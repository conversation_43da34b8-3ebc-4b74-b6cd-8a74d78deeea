const path = require('path');
const webpack = require('webpack');
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
const CrossoriginWebpackPlugin = require('crossorigin-webpack-plugin');
const InlineScriptPlugin = require('./scripts/inline-script-plugin.js');
const ReactRefreshWebpackPlugin = require('@pmmmwh/react-refresh-webpack-plugin');
const MFEntryPlugin  = require('./scripts/mf-entry-plugin.js')


const pxToVwConfig = require('./px-to-vw.config.js');
const fs = require('fs');

const envHost = {
  prod: 'me.ewt360.com',
  pre: 'staging-me.ewt360.com',
  sit: 'me.test.ewt360.com',
  dev: 'me.dev.ewt360.com',
  local: 'me.local.ewt360.com',
};

const proxyHost = {
  prod: 'gateway.ewt360.com',
  pre: 'staging-gateway.ewt360.com',
  sit: 'gateway.test.ewt360.com',
  dev: 'gateway.dev.ewt360.com',
};

const isDevelopment = process.env.NODE_ENV === 'development';
const APP_DIR = '/ewtcustomerh5';

module.exports = {
  webpack: {
    devtool: 'source-map',
    entry: {
      main: './src/index',
      // 部分代码需要 inline 到 HTML 中
      inline: './src/inline',
    },
    publicPath: process.env.ASSET_PATH || '/',
    html: {
      template: './src/index.html',
      excludeChunks: ['inline'],
      templateParameters: {
        env: process.env.DEPLOYMENT_ENV,
      },
    },
    copyWebpackPlugin: {
      patterns: [
        { from: `public`, to: 'public' },
      ],
    },
    alias: {
      components: './src/components',
      assets: './src/assets',
      '~': './src/',
      '@': './src/',
    },
    postcss: {
      plugins: ['postcss-import', ['postcss-px-to-viewport', pxToVwConfig]],
    },
    define: {
      __DEV__: process.env.BUILD_ENV === 'dev',
      __PROD__: process.env.BUILD_ENV === 'prod',
      __PRE__: process.env.BUILD_ENV === 'pre',
      __TEST__: process.env.BUILD_ENV === 'test',
      __MOCK__: process.env.PROXY_ENV === 'mock',
      'process.env.DEPLOYMENT_ENV': JSON.stringify(process.env.DEPLOYMENT_ENV || 'dev'),
      __BUILD_TIME__: `"${Date.now()}"`,
      __APP_DIR__: JSON.stringify(APP_DIR),
      'process.env.PUBLIC_PATH': JSON.stringify(
        `${process.env.ASSET_PATH || '/'}public/`
      ),
    },
    babel: {
      plugins: [isDevelopment && require.resolve('react-refresh/babel')].filter(Boolean),
    },
    chain: (config) => {
      config.plugin("MFEntryPlugin").use(MFEntryPlugin);
      // moment 只保留中文的国际化配置，减小包体积
      config
        .plugin('ContextReplacementPlugin')
        .use(webpack.ContextReplacementPlugin, [/moment[/\\]locale$/, /zh-cn/]);
      if (process.env.USE_ANALYZER === 'analyzer') {
        config.plugin('BundleAnalyzerPlugin').use(BundleAnalyzerPlugin);
      }
      /** 取消压缩: 排查兼容性问题的时候，可以跑这个命令 */
      if (process.env.NO_MINIMIZE) {
        config.optimization.minimize(false);
      }
      // 给注入的 script 标签增加 crossorigin 属性，减少 script error 的产生
      config.output.crossOriginLoading('anonymous');
      config.plugin('CrossoriginWebpackPlugin').use(CrossoriginWebpackPlugin);
      if (isDevelopment) {
        config.plugin('react-refresh').use(ReactRefreshWebpackPlugin);
      }
      // 分包
      config.optimization.merge({
        splitChunks: {
          cacheGroups: {
            vendors: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
              minChunks: 1,
            },
            antdMobile: {
              test: /[\\/]node_modules[\\/](antd-mobile)/,
              name: 'antdMobile',
              chunks: 'all',
              priority: 1,
              minChunks: 1,
            },
            coreJs: {
              test: /[\\/]node_modules[\\/](core-js|abortcontroller-polyfill)/,
              name: 'coreJs',
              chunks: 'all',
              priority: 41,
              minChunks: 1,
            },
          },
        },
      });
      // 把兼容性不好的 npm 包过 babel
      config.module
        .rule('js')
        .oneOf('js')
        .exclude.clear()
        .end()
        .include.add(path.join(__dirname, 'src'))
        .add(
          /node_modules[\\/](antd-mobile|@floating-ui|@use-gesture|runes2|staged-components|@react-spring|react-router|@remix-run|@ewt[\\/]request)|dom7|swiper|ssr-window|mst-js-bridge|react-window|@dnd-kit|solarlunar/,
        )
        .end();
      // 解决某些包只有符合 commonjs 规范的产物的解析问题，比如 react-is
      config.module
        .rule('js')
        .oneOf('js')
        .use('babel-loader')
        .tap((options) => ({ sourceType: 'unambiguous', ...options }));
      // 把 inline.js 注入到 HTML 中
      if (!isDevelopment) {
        // 把 inline.js 注入到 HTML 中
        config.plugin('InlineScriptPlugin')
          .use(InlineScriptPlugin);
      }
    },
  },
  server: {
    proxy: [
      {
        context: [
        ],
        target: 'http://yapi.235.mistong.com/mock/1809',
        changeOrigin: true,
        secure: false
      },
      {
        context: ['/api', '/customerApi'],
        target: `http://${proxyHost[process.env.DEPLOYMENT_ENV] || proxyHost.sit}`,
        changeOrigin: true,
        secure: false,
        pathRewrite: { '^/customerApi': '' },
      },
    ],
    host: envHost[process.env.DEPLOYMENT_ENV] || envHost.local,
    hot: true,
    port: 3003,
    open: [APP_DIR],
    server: 'http',
    allowedHosts: 'all',
    client: {
      overlay: {
        errors: true,
        warnings: false,
        runtimeErrors: false,
      },
    },
    historyApiFallback: true,
  },
};
