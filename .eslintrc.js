module.exports = {
  ignorePatterns: ['node_modules', 'dist'],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    parser: 'babel-eslint',
  },
  env: {
    browser: true,
    es2021: true
  },
  extends: [
    'plugin:react/recommended',
    'plugin:prettier/recommended'
  ],
  overrides: [
  ],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  plugins: [
    'react'
  ],
  globals: {
  },
  rules: {
    // "quotes": ["error", "single"]
  },
  settings: {
    react: {
      "version": "detect"
    }
  }
}
