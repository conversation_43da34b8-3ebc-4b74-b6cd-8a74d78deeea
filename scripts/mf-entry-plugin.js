const webpack = require("webpack");
const { ModuleFederationPlugin } = webpack.container;
class MFEntryPlugin {
  apply(compiler) {
    const remoteName = "bendH5";
    // 本地测试模式下使用默认版本配置 如需用最新的联系 B端同学
    const defaultVersion = "0-0-6";
    const entry = {
      dev: `https://cdn.dev.ewt360.com/resources/app/dev/bend-h5/dist/entry.${defaultVersion}.js`,
      sit: `https://cdn.test.ewt360.com/resources/app/sit/bend-h5/dist/entry.${defaultVersion}.js`,
      pre: "https://cdn.ewt360.com/resources/app/pre/bend-h5/dist/entry.0-0-7.03865c10.js",
      prod: "https://cdn.ewt360.com/resources/app/prod/bend-h5/dist/entry.0-0-7.1a64097f.js",
    }[process.env.DEPLOYMENT_ENV];
    compiler.options.plugins.push(
      new ModuleFederationPlugin({
        name: "customerH5",
        remotes: {
          bendH5: `
          promise new Promise(function(resolve) {
            function loadEntry() {
              var script = document.createElement('script')
              script.src = '${entry}'
              script.onload = function() {
                var proxy = {
                  get: function(request)  {
                    try {
                      return window.${remoteName}.get(request)
                    } catch(e) {
                     console.log('federation',e)
                    }
                  },
                  init: function(arg) {
                    try {
                      return window.${remoteName}.init(arg)
                    } catch(e) {
                     console.log('federation',e)
                    }
                  }
               }
               resolve(proxy)
              }
              script.onerror = resolve
              document.head.appendChild(script)
            }
            loadEntry()
          })
          `.trim(),
        },
        shared: {
          react: {
            singleton: true,
            // eager: true,
            requiredVersion: "18.2.0",
          },
          "react-dom": {
            singleton: true,
            // eager: true,
            requiredVersion: "18.2.0",
          },
          "mst-analytics": {
            singleton: true,
            // eager:true，
            requiredVersion: "1.3.4",
          },
        },
      }),
    );
  }
}
module.exports = MFEntryPlugin;
