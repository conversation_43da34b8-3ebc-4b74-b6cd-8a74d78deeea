const HtmlWebpackPlugin = require("html-webpack-plugin");

class InlineScriptPlugin {
  apply(compiler) {
    compiler.hooks.compilation.tap("InlineScriptPlugin", (compilation) => {
      HtmlWebpackPlugin.getHooks(compilation).beforeEmit.tapAsync(
        "InlineScriptPlugin",
        (data, cb) => {
          // 查找以 'inline.' 开头的文件（hash 版本）
          const scriptFileName = Object.keys(compilation.assets).find((file) =>
            /inline\..*js$/.test(file),
          );

          if (scriptFileName) {
            const inlineScript = compilation.assets[scriptFileName].source();
            // 把 script 直接插入到 HTML
            data.html = data.html.replace("<!-- inline -->", inlineScript);
            // 防止生成多余的 JS 文件
            delete compilation.assets[scriptFileName];
          }

          cb(null, data);
        },
      );
    });
  }
}
module.exports = InlineScriptPlugin;
