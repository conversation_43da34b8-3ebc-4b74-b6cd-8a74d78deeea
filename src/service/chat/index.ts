import request from "@/utils/request";

export type TRes<T> = Promise<{ success?: boolean; data: T }>;

export function getStartNode(params) {
  return request.get("/api/studyprod/desk/diagnostic/learning/startNode");
}

export function getNextQuestionAll(params): TRes<any> {
  return request.get(
    "/api/studyprod/desk/diagnostic/learning/nextQuestionAll",
    {
      params,
    },
  );
}

export function updateUserEdition(data): TRes<boolean> {
  return request.post(
    "/api/studyprod/desk/diagnostic/learning/updateUserEdition",
    data,
  );
}

/** 小计划合成一个大计划 */
export function postSynthesisPlan(data): TRes<any> {
  return request.post(
    "/api/studyprod/desk/diagnostic/learning/synthesisPlan",
    data,
  );
}
