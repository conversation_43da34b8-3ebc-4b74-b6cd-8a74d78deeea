import request from "@/utils/request";

/**
 * 查询用户信息
 * @param params
 */
export const getUserRole = async (params?: any) => {
  const { data } = await request({
    url: "/api/studyprod/holiday/content/turorial/getUserRole",
    method: "get",
    params,
  });
  return data;
};

/**
 * 查询学科的枚举
 * @param params
 */
export const getSubjectList = async (params?: any, headers: any = {}) => {
  const { data } = await request({
    url: "/api/studyprod/holiday/content/turorial/getSubjectListV2",
    method: "get",
    params,
    headers,
  });
  return data;
};

/**
 * 查询我的默认教程版本
 * @param params
 */
export const getMyTutorialVersion = async (params?: any) => {
  const { data } = await request({
    url: "/api/studyprod/holiday/content/turorial/getMyTutorialVersion",
    method: "get",
    params,
  });
  return data;
};

/**
 * 根据学科、年级、教程、等级查询资源列表
 * @param params
 */
export const getTutorialVersionResourceList = async (params?: any) => {
  const { data } = await request({
    url: "/api/studyprod/holiday/content/turorial/getTutorialVersionResourceListV2",
    method: "get",
    params,
  });
  return data;
};

/**
 * 周末智学 --- 根据学科、年级、教程、等级查询资源列表
 * @param params
 */
export const getTutorialVersionResourceListForWeekStudy = async (
  data?: any,
) => {
  const { data: responseData } = await request({
    url: "/api/studyprod/istudy/plan/resource/app/getTutorialVersionResourceList",
    method: "post",
    data,
  });
  return responseData;
};

/**
 * 获取试卷的答题情况
 * @param params
 */
export const getTutorialVersionPaperStatus = async (params?: any) => {
  const { data } = await request({
    url: "/api/studyprod/holiday/content/turorial/paper/status",
    method: "get",
    params,
  });
  return data;
};

/**
 * 获取试卷的答题情况
 * @param params
 */
export const getPeriodicalBySubject = async (params?: any) => {
  const { data } = await request({
    url: "/api/studyprod/holiday/content/turorial/getPeriodicalList",
    method: "get",
    params,
  });
  return data;
};
