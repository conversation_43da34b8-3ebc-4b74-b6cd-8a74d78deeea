import request from "@/utils/request";
import { TRes } from "@/service/home";
import { IMedalInfo, IPackagePrize } from "@/typing";

// 获取小e伴读的鼓励文案列表
export function getSupportText(): TRes<string[]> {
  return request.get("/api/studyprod/self/study/quote/list");
}

/** 推荐内容 - 学科列表 信息 */
export interface IRecommendContentSubjectList {
  /** 学科名称 */
  subjectName: string;
  /** 学科对应的目录id */
  categoryId: number;
}
/** 获取小e伴读的鼓励文案列表 */
export function getRecommendSubjectList(params: {
  ignoreError: boolean;
}): TRes<IRecommendContentSubjectList[]> {
  return request.get("/api/studyprod/self/study/recommend/subject/info", {
    params,
  });
}

/** 课包信息 */
export interface IRecommendPackageInfo {
  /** 课包标题 */
  title: string;
  /** 封面图 */
  coverImgUrl: string;
  /** 背景图 */
  uploadImgUrl: string;
  /** 作者列表 */
  authorList: string[];
  /** 选择人数 */
  chooseCount: number;
  /** 描述 */
  description: string;
  /** 讲数 */
  lessonCount: number;
  /** 课程报id */
  courseId: string;
  /** 是否添加到计划 */
  added2Plan: boolean;
  /** 课包是否完成了 */
  coursePackageHasFinished: boolean;
}

/** 推荐内容 - 课包列表 信息 */
export interface IRecommendPackageList {
  /** 是否还有下一页 */
  haveNextPage: boolean;
  /** 课包信息列表 */
  data: IRecommendPackageInfo[];
}
/** 获取推荐的课包列表信息，需要参数：页数、条数、目录id */
export function getRecommendPackageList(params: {
  pageIndex: number;
  pageSize: number;
  categoryId: number;
  ignoreError: boolean;
}): TRes<IRecommendPackageList> {
  return request.get("/api/studyprod/self/study/recommend/subject/courseList", {
    params,
  });
}

/** 将课包加入到计划，需要课包id参数 */
export function postAddPackageToPlan(data: {
  courseId: string;
  ignoreError?: boolean;
}): TRes<boolean> {
  return request.post("/api/studyprod/self/study/user/plan/addCourse", data);
}

/** 将课包移出计划，需要课包id参数 */
export function postRemovePackageByPlan(data: {
  courseId: string;
}): TRes<boolean> {
  return request.post("/api/studyprod/self/study/user/removeCourse", data);
}

/** 获取课包是否已加入计划 */
export function getPackageIsAddedPlan(params: {
  courseId: string;
  ignoreError: boolean;
}): TRes<boolean> {
  return request.get("/api/studyprod/self/study/user/plan/checkCourse", {
    params,
  });
}

/** 课程讲状态，0-失效，1正常 */
export enum ELessonStatus {
  normal = 1,
  loseEfficacy = 0,
}

/** 已完成的课程讲信息 */
export interface ICompletedLessonInfo {
  /** 包id */
  courseId: string;
  /** 讲id */
  lessonId: string;
  /** 讲名称 */
  lessonName: number;
  /** 时长 */
  duration: number;
  /** 0 失效  1正常 */
  lessonStatus: ELessonStatus;
  /** 时间戳 到毫秒 */
  finishTime: number;
}

/** 推荐内容 - 课包列表 信息 */
export interface ICompletedLessonList {
  /** 是否还有下一页 */
  haveNextPage: boolean;
  /** 课包信息列表 */
  data: ICompletedLessonInfo[];
}

/** 查询本周已完成的任务列表，分页形式 */
export function getCompletedLessonList(params: {
  pageIndex: number;
  pageSize: number;
}): TRes<ICompletedLessonList> {
  return request.get("/api/studyprod/self/study/user/week/current/finished", {
    params,
  });
}

/** 课包详情信息 */
export interface INoAddPackageInfo {
  /** 标题 */
  title: string;
  /** 选择人数 */
  chooseCount: number;
  /** 封面图 */
  coverImgUrl: string;
  /** 推荐理由 */
  description: string;
  /** 总数量 */
  lessonCount: number;
  /** 作者列表 */
  authorList: string[];
  /** 是否90天前完成的 */
  earlierFinish: boolean;
}

/** 已完成的报课类型，多了完成数量 */
export type IDetailPackageInfo = INoAddPackageInfo & {
  /** 完成数量 */
  completeCount?: number;
};

/** 获取已经加入计划的课包信息 */
export function getAddedPackageInfo(params: {
  courseId: string;
  ignoreError: boolean;
}): TRes<IDetailPackageInfo> {
  return request.get("/api/studyprod/self/study/user/planDetail/courseInfo", {
    params,
  });
}

/** 课程讲状态, -1-失效，0-没看过，1-已看过，2-已看完 */
export enum EAddedLessonStatus {
  lose = -1,
  noSee = 0,
  watched = 1,
  completed = 2,
}

/** 未加入计划的列表信息 */
export interface IDetailForLessonBase {
  /** lessonId */
  lessonId: string;
  /** lesson名称 */
  title: string;
  /** 讲时长 */
  duration: string;
  /** 视频时长文案，如：共6分钟 */
  durationDisplay: string;
}

/** 已加入计划的课包详情信息 */
export type IDetailLessonInfo = IDetailForLessonBase & {
  /** 课讲状态 */
  status?: EAddedLessonStatus;
};

/** 未加入计划的讲列表信息 */
export interface IAddedLessonList {
  /** 是否还有下一页 */
  haveNextPage: boolean;
  /** 列表 */
  data: IDetailLessonInfo[];
}

/** 获取已经加入计划的讲列表 */
export function getAddedLessonList(params: {
  courseId: string;
  pageIndex: number;
  pageSize: number;
  ignoreError: boolean;
}): TRes<IAddedLessonList> {
  return request.get(
    "/api/studyprod/self/study/user/planDetail/lessonListPage",
    { params },
  );
}

/** 获取未加入计划的课包信息 */
export function getNoAddPackageInfo(params: {
  courseId: string;
  ignoreError: boolean;
}): TRes<IDetailPackageInfo> {
  return request.get(
    "/api/studyprod/self/study/plan/detail/notAdd/courseInfo",
    { params },
  );
}

/** 未加入计划的讲列表信息 */
export interface INoAddLessonList {
  /** 是否还有下一页 */
  haveNextPage: boolean;
  /** 列表 */
  data: IDetailLessonInfo[];
}

/** 获取未加入计划的讲列表 */
export function getNoAddLessonList(params: {
  courseId: string;
  pageIndex: number;
  pageSize: number;
  ignoreError: boolean;
}): TRes<INoAddLessonList> {
  return request.get(
    "/api/studyprod/self/study/plan/detail/notAdd/lessonList",
    { params },
  );
}

/** 新手引导弹窗的回调 */
export function postGuidPopupCallback(): TRes<boolean> {
  return request.post("/api/studyprod/self/study/user/tutorialPop/callback");
}

/** 今日打卡弹窗的回调 */
export function getCheckInPopupCallback(): TRes<boolean> {
  return request.get("/api/studyprod/self/study/user/dailyCyclePop/callback");
}

// 本周达标弹窗回调
export function getWeekCompleteCallback(): TRes<string[]> {
  return request.get(
    "/api/studyprod/self/study/user/firstCompleteWeekPlanPop/callback",
    { params: { ignoreError: true } },
  );
}

// 打卡达标弹窗回调
export function getClockInCompletedCallback(): TRes<string[]> {
  return request.get(
    "/api/studyprod/self/study/user/completeMilestoneTaskPop/callback",
    { params: { ignoreError: true } },
  );
}

// 课包达标弹窗回调
export function getPackageCompletedCallback(): TRes<string[]> {
  return request.get(
    "/api/studyprod/self/study/user/completeCourseTaskPop/callback",
    { params: { ignoreError: true } },
  );
}

export interface IClockInPrizeList {
  /** 各个等级达成的阈值 */
  targetValue: number;
  /** 各个等级奖励的步数 */
  rewardValue: number;
  /** 所属等级 */
  level: number;
  /** 勋章图片 */
  mainImgUrl: string;
}

export interface IClockInPrizeConfigRes {
  /** 累计打卡天数 */
  processNum: number;
  /** 任务id */
  taskId: string;
  /** 节点id */
  nodeId: string;
  /** 任务配置的最大等级 */
  maxLevel: number;
  /** 已完成的最大等级 */
  finishedMaxLevel: number;
  /** 已领取的最大等级 */
  receivedMaxLevel: number;
  /** 当前任务奖励等级id */
  taskRewardLevelId: string;
  /** 任务各个等级的配置信息 ,MilestoneLevelConfigVO */
  milestoneLevelConfigVOs: IClockInPrizeList[];
}

/** 获取奖励抽屉的配置和基础信息 */
export function getClockInPrizeConfig(params: {
  eventCode: string;
  ignoreError?: boolean;
}): TRes<IClockInPrizeConfigRes> {
  return request.get(
    "/api/activitycenter/app/usersystem/new/outer/getEventTaskProgressDetail",
    { params },
  );
}

export function getPrizeDetail(params: {
  taskId: string;
}): TRes<IStepPrizeRes> {
  return request.get(
    "/api/activitycenter/app/usersystem/new/getTaskRewardDetail",
    { params },
  );
}

export interface IStepPrizeRes {
  /** 获取成功情况 */
  awardSuccess: boolean;
  /** 任务id */
  taskId: number;
  /** 任务名称 */
  taskName: string;
  /** 步数奖励 */
  stepAward: number;
  /** 勋章奖励 ,MedalInfoVO */
  medalAward: IMedalInfo;
  /** 超过用户占比（已经算好X100） */
  exceedProportion: number;
  /** 当前领取步数奖励的nodeId */
  nodeId: number;
}

/** 查询步数相关奖励 */
export function postStepPrize(data: {
  nodeId: string;
  taskId: string;
  taskRewardLevelId: string;
  processNum: number;
  isAwardOnce: number;
  ignoreError: boolean;
}): TRes<IStepPrizeRes> {
  return request.post(
    "/api/activitycenter/app/usersystem/new/markerTask/award",
    data,
  );
}

/** 查询福袋奖励 */
export function postPackagePrize(data: {
  nodeId: number;
  ignoreError: boolean;
}): TRes<IPackagePrize[]> {
  return request.post(
    "/api/activitycenter/app/usersystem/new/package/award",
    data,
  );
}

export enum EPackageInPrizeType {
  medal = 1,
  shiWu = 2,
  image = 3,
}

export interface IPackageInPrize {
  /** 奖品类型1勋章2实物发放3形象 */
  prizeType: EPackageInPrizeType;
  /** 奖品名称 */
  prizeName: string;
  /** 奖品图片url */
  prizeUrl: string;
}

/** 领取福袋 */
export function postOpenPackageById(data: {
  bagId: number;
}): TRes<IPackageInPrize> {
  return request.post("/api/activitycenter/app/usersystem/prize/award", data);
}

/** 检测包状态信息，确认是否更新了 */
export function getCheckPackageStatus(params: {
  courseId: string;
  ignoreError: boolean;
}): TRes<{ updateStatus: boolean }> {
  return request.get(
    "/api/studyprod/self/study/user/planDetail/checkLessonUpdate",
    { params },
  );
}

/** 更新课包状态 */
export function postUpdateCourseStatus(data: {
  courseId: string;
}): TRes<boolean> {
  return request.post(
    "/api/studyprod/self/study/user/planDetail/lessonUpdate",
    data,
  );
}

/** 删除失效的讲 */
export function postRemoveLoseLesson(data: {
  courseId: string;
}): TRes<boolean> {
  return request.post(
    "/api/studyprod/self/study/user/remove/invalidLessons",
    data,
  );
}

export interface IWatchedListParams {
  /** 是否需要课程包信息 */
  needCoursePackage: boolean;
  /** 记录id */
  recordId: number;
  /** 注释 */
  courseId: string;
  /** 课程讲ID */
  lessonId: string;
  /** 每页显示条数 */
  pageSize: number;
  /** 当前页码 */
  pageIndex: number;
  /** 前端作为判断是否有下一页的标识，自用字段 */
  haveNextPage: boolean;
}

export interface IWatchedLesson {
  /** 课程讲ID */
  lessonId?: number;
  /** 课程讲名称 */
  lessonName?: string;
  /** 视频播放时长，格式：00:00:00 */
  videoPlayTime?: number;
  /** 视频播放时长，格式：共6分钟 */
  videoPlayTimeDisplay?: number;
  /** 上一次观看的时间点，单位：秒 */
  lastVideoPlayedProgress?: number;
  /** 上一次观看日期 */
  lastViewTime?: number;
  /** 该字段是业务内自定义的，前端标识是否选中该课程 */
  selected?: boolean;
}

export interface IWatchedLessonList {
  /** 记录id */
  recordId?: number;
  /** 讨论后新增：根据此字段判断是否有下一页 */
  haveNextPage: boolean;
  /** 课程包ID */
  coursePackageId?: string;
  /** 课程包名称 */
  coursePackageName?: string;
  /** 课程包背景图 */
  coverImgUrl?: string;
  /** 选中的课程讲 */
  selectWatchedLesson?: IWatchedLesson;
  /** 其他看过的课程讲列表 */
  otherWatchedLessonList?: IWatchedLesson[];
}

export type ICurrentLessonInfo = IWatchedLesson & {
  /** 课程包ID */
  coursePackageId?: string;
  /** 课程包名称 */
  coursePackageName?: string;
  /** 课程包背景图 */
  coverImgUrl?: string;
};

/** 标记完成底抽 - 查看已看过课程列表 */
export function getWatchedLessonList(
  params: IWatchedListParams,
): TRes<IWatchedLessonList> {
  return request.get(
    "/api/studyprod/self/study/user/confirmWatched/lessonList",
    { params },
  );
}

/** 批量标记完成课程讲 */
export function postBatchFinishLesson(data: {
  lessonIds: number[];
}): TRes<boolean> {
  return request.post(
    "/api/studyprod/self/study/user/batch/finish/lesson",
    data,
  );
}

/** 奖励的枚举 */
export enum EReceiveReward {
  receiveReward = "RECEIVE_REWARD",
  noReward = "NO_REWARD",
  viewReward = "VIEW_REWARD",
}

export interface IHolidayCheckInDetail {
  /** 累计打卡天数 */
  days: number;
  /** 领取奖励状态 EReceiveReward */
  rewardStatus: EReceiveReward;
  /** 再打卡天数，-1 不用展示，0 表示全部领取，其他则正常展示 */
  checkInDayCount: number;
  /** 已完成任务数 */
  completedTaskCount: number;
}

/** 首页 - 获取假期的打卡天数和奖励状态 */
export function getHolidayDaysAndRewardStatus(params: {
  scene: number;
  queryType: string;
}): TRes<IHolidayCheckInDetail> {
  return request.get(
    "/api/studyprod/self/study/user/accumulativeDaysAndRewardStatus/byScene",
    { params },
  );
}
