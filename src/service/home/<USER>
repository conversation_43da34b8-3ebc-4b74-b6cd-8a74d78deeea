import request from "@/utils/request";
import { EPackageStatus, EPopupType } from "@/routes/self-learning-home/common";

export type TRes<T> = Promise<{ data: T }>;
/** 周达标、里程碑、包完成弹窗回调 */
interface IFeedbackPopCallbackReq {
  courseId?: number;
  type: EPopupType;
}

const popupType = {
  [EPopupType.weekComplete]: "weekComplete",
  [EPopupType.milestoneComplete]: "milestoneComplete",
  [EPopupType.courseComplete]: "courseComplete",
};

export function feedbackPopCallback(data: IFeedbackPopCallbackReq) {
  return request.post("/api/studyprod/self/study/user/feedbackPop/callback", {
    ...data,
    type: popupType[data.type],
  });
}

/**
 * 学习场景
 * 自主学习计划 1
 * 2025寒假 2
 */
export enum EStudyScene {
  selfLearning = 1,
  winterHoliday = 2,
}

/**
 * 学习场景
 * @param selfLearning 自主学习计划
 * @param winterHoliday 2025寒假
 */
export const studySceneName = {
  [EStudyScene.selfLearning]: "自主学习计划",
  [EStudyScene.winterHoliday]: "2025寒假",
};

/** 计划详情 */
export interface IPlanDetailRes {
  flagId: string;
  flagContent: string;
  /** 默认每周任务数 */
  defaultPlanCount: number;
  /** 每周最小任务数（包含） */
  minPlanCount: number;
  /** 每周最大任务数（包含） */
  maxPlanCount: number;
  /** 用户设置的周计划数 */
  userPlanCount: number;
  /** 学习场景 */
  studyScene: EStudyScene;
  /** 需要学习多久才可以打卡 */
  dailyCheckInDuration: number;
}
export function getUserPlanDetail(): TRes<IPlanDetailRes> {
  return request.get("/api/studyprod/self/study/user/plan/detail");
}

/** 欢迎勋章 是否需要展示、以及勋章图 */

export interface IWelcomeModelInfo {
  imgUrl: string;
  /** 是否弹窗 */
  popup: boolean;
}

export function getWelcomeModelInfo(): TRes<IWelcomeModelInfo> {
  return request.post("/api/studyprod/self/study/user/turorialPopup", {
    ignoreError: true,
  });
}

/** 周计划弹窗相关 */
export interface IWeekPopInfo {
  /** 是否弹窗 */
  popup?: boolean;
  /** 完成当周任务量 */
  finishCount: number;
}

export function getWeekPopInfo(): TRes<IWeekPopInfo> {
  return request.get(
    "/api/studyprod/self/study/user/firstCompleteWeekPlanPop",
    { params: { ignoreError: true } },
  );
}

/** 里程碑弹窗相关 */
export interface IMilestonePopInfo {
  /** 是否弹窗 */
  popup?: boolean;
  /** 累计天数 */
  dayCount?: number;
}

export function checkMilestonePop(): TRes<IMilestonePopInfo> {
  return request.get(
    "/api/studyprod/self/study/user/completeMilestoneTaskPop",
    { params: { ignoreError: true } },
  );
}

/** 包完成弹窗相关 */
export interface ICompleteCourseTaskPopRes {
  /** 是否弹窗 */
  popup?: boolean;
  /** 图片地址 */
  imgUrl?: string;
  /** 标题 */
  courseName?: string;
  /** 课程包id */
  courseId: string;
}

export function getCompleteCourseTaskPopInfo(): TRes<ICompleteCourseTaskPopRes> {
  return request.get("/api/studyprod/self/study/user/completeCourseTaskPop", {
    params: {
      ignoreError: true,
    },
  });
}

/** 获取今日学习时长以及打卡状态 */
export interface IStudyTimeAndCheckInStatus {
  /** 今日学习时长，单位分钟 */
  studyDuration?: number;
  /** 今日打卡状态true：已打卡，false：未打开 */
  checkInStatus?: boolean;
  /** 未打卡状态是否能打卡 */
  canCheckInStatus?: boolean;
  /** 1.2新增: 打卡对应的e游记任务是否完成标识，完成了才能弹出领奖弹窗，否则仅toast */
  finishedFlag?: boolean;
}
export function getStudyTimeAndCheckInStatus(): TRes<IStudyTimeAndCheckInStatus> {
  return request.get(
    "/api/studyprod/self/study/user/today/studyTimeAndCheckInStatus",
  );
}

export interface ICheckInPrefixState {
  /** 奖励领取状态true:已领取 false：未领取 */
  rewardFlag: boolean;
  /** 弹窗标识 true:已弹窗 false：未弹窗 */
  popFlag: boolean;
  /** 打卡标识true:已打卡 false：未打卡 */
  checkInFlag: boolean;
  /** 任务完成标识true:已完成 false：未完成 */
  finishedFlag: boolean;
}

export function getCheckInPrefixState(): TRes<ICheckInPrefixState> {
  return request.get("/api/studyprod/self/study/user/checkInPrefixState");
}

/** 自动打卡 */

export function checkInWithToday(): TRes<boolean> {
  return request.post("/api/studyprod/self/study/user/today/checkIn");
}

// 累计打卡打状态枚举
export enum ECheckInDayCount {
  /** 不需要展示 */
  noShow = -1,
  /** 已经全部领取了 */
  received = 0,
  /** 其他枚举值均正常展示 还需打卡多少天 */
}

/** 累计打卡时长以及奖励状态 */
export interface IGetAccumulativeDaysAndRewardStatus {
  /** 累计打卡天数 */
  days?: number;
  /** 领取奖励状态,RECEIVE_REWARD领取奖励,VIEW_REWARD查看奖励,NO_REWARD无奖励 */
  rewardStatus?: string;
  /** 已完成任务数 */
  completedTaskCount?: number;
  /** 再打卡天数，-1 不用展示，0 表示已全部领取，其他则正常展示 */
  checkInDayCount?: number;
}

export function getAccumulativeDaysAndRewardStatus(): TRes<IGetAccumulativeDaysAndRewardStatus> {
  return request.get(
    "/api/studyprod/self/study/user/accumulativeDaysAndRewardStatus",
  );
}

/** 获取 flag 列表 */
export interface IFlagItem {
  /** id */
  id?: string;
  /** flag文案 */
  flag?: string;
}

export function getFlagList(): TRes<IFlagItem[]> {
  return request.get("/api/studyprod/self/study/flagList");
}

/** 更改周计划数 */
export interface IUpdateWeekPlanCountReq {
  weekPlanCount: number;
}

export function updateWeekPlanCount(data: IUpdateWeekPlanCountReq) {
  return request.post(
    "/api/studyprod/self/study/user/updateWeekPlanCount",
    data,
  );
}

/** 更新 flag */
export interface IUpdateFlagReq {
  flagContent: string;
  flagId: string;
}

export function updateFlag(data: IUpdateFlagReq) {
  return request.post("/api/studyprod/self/study/user/updateFlag", data);
}

/** 获取用户昵称、学习人数、目标、自习报告 */
export interface IUserInfoRes {
  /** 昵称 */
  nickName?: string;
  /** 学习人数 */
  learnCount?: number;
  /** flag自习目标 */
  flagContent?: string;
  /** 自习报告路由 */
  studyReportRouter: string;
}

export function getUserInfo(): TRes<IUserInfoRes> {
  return request.get("/api/studyprod/self/study/home/<USER>");
}

export interface IPostAllPlanReq {
  pageSize?: number;
  pageIndex?: number;
  /** 状态 */
  status?: EPackageStatus;
  subjectInfo?: {
    // 一级分类
    categoryCode: number;
    // 学科id
    subjectId: number;
  };
}

export interface IPostAllPlanRes {
  /** 注释 */
  allCourseCount?: number;
  /** 注释 */
  listPage?: IListPage;
}

export interface IListPage {
  /** 总页数 */
  totalPages?: number;
  /** 当前页码 */
  pageIndex?: number;
  /** 每页条数 */
  pageSize?: number;
  /** 总条数兼容移动端出参 */
  total?: number;
  /** 总页码兼容移动端出参 */
  totalPage?: number;
  /** 总记录数 */
  totalRecords?: number;
  /** 分页结果集 ,T */
  data?: IPackageItem[];
  /** 注释 */
  havePrePage?: boolean;
  /** 注释 */
  haveNextPage?: boolean;
}

export interface IPackageItem {
  /** 封面图 */
  coverImgUrl?: string;
  /** 科目id */
  subjectId?: number;
  courseId: number;
  /** 科目 */
  subjectName?: string;
  /** 标题 */
  title?: string;
  /** 总讲数 */
  totalCount?: number;
  /** 完成讲数 */
  finishCount?: number;
  /** 包完成状态 */
  finishStatus?: EPackageStatus;
  /** 添加时间时间戳格式 */
  addTime?: string;
  /** 是否失效 */
  invalidation?: boolean;
  /** 作者 */
  authorList: string[];
}
export function postAllPlan(data: IPostAllPlanReq): TRes<IPostAllPlanRes> {
  return request.post("/api/studyprod/self/study/user/allPlan", data);
}

export interface IWeekPlanRes {
  /** 周计划任务数 */
  weekCompleteCount?: number;
  /** 周计划任务数 */
  weekPlanCount?: number;
  /** 列表 ,PageQueryResultExt */
  courseLessonInfoList?: CourseLessonInfoList;
}

export interface CourseLessonInfoList {
  /** 总页数 */
  totalPages?: number;
  /** 当前页码 */
  pageIndex?: number;
  /** 每页条数 */
  pageSize?: number;
  /** 总条数兼容移动端出参 */
  total?: number;
  /** 总页码兼容移动端出参 */
  totalPage?: number;
  /** 总记录数 */
  totalRecords?: number;
  /** 分页结果集 ,T */
  data?: ICourseItem[];
  /** 注释 */
  havePrePage?: boolean;
  /** 注释 */
  haveNextPage?: boolean;
}

export interface ICourseItem {
  /** 课程包id */
  courseId: string;
  /** 包封面 */
  coverImgUrl: string;
  /** 包名称 */
  courseName: string;
  /** 包科目 */
  subjectName: string;
  /** 注释 */
  lessonInfo: LessonInfo;
}

export interface LessonInfo {
  /** 时长， 单位分钟 */
  duration: number;
  /** 视频的总时长文案，例：共6分钟 */
  durationDisplay: string;
  /** 讲名称 */
  lessonName: string;
  /** 讲id */
  lessonId: string;
  /** 是否有看课记录 */
  watched: boolean;
  /** 课程完成时间，在本周已完成列表内才显示 */
  finishTime?: number;
}

export interface IGetWeekListReq {
  /** 页码 */
  pageIndex: number;
  /** 每页条目数 */
  pageSize: number;
}

export function getWeekList(data: IGetWeekListReq): TRes<IWeekPlanRes> {
  return request.get("/api/studyprod/self/study/user/week/current", {
    params: data,
  });
}

/** 移除计划 */
export interface IRemovePackage {
  courseId: number;
}

export function removePackage(data: IRemovePackage): TRes<boolean> {
  return request.post("/api/studyprod/self/study/user/removeCourse", data);
}

/** 获取科目列表 */
export interface ISubjectItem {
  code: number;
  parentCode: number;
  name: string;
}

export function getSubjectListApi(params?: Record<string, any>): TRes<{
  categorySubjectList: ISubjectItem[];
}> {
  return request.get("/api/studyprod/self/study/categorySubjectInfo", {
    params,
  });
}

/** 获取90天前完成的课程包 */
export interface IPostEarlierCompletePlanReq extends IPostAllPlanReq {}

export function postEarlierCompletePlan(
  data: IPostEarlierCompletePlanReq,
): TRes<IPostAllPlanRes> {
  return request.post(
    "/api/studyprod/self/study/user/earlierCompletePlan",
    data,
  );
}

/** 判断用户是否有E游记的权限 */
export function checkActivityAuth(): TRes<boolean> {
  return request.get("/api/studyprod/self/study/check/activity/auth");
}

/** 获取下一讲的课程 */
interface IGetNextLessonReq {
  courseId: string;
}
export function getNextLesson(data: IGetNextLessonReq): TRes<ICourseItem> {
  return request.get("/api/studyprod/self/study/user/course/nextLesson", {
    params: data,
  });
}

export interface IGetSelfLearningOfflineConfigRes {
  tipSwitch: boolean;
  dayNum: number;
  offLineVersion: string;
  offLineTimestamp: string;
}

/** 获取自习计划下线配置 */
export function getSelfLearningOfflineConfig(): TRes<IGetSelfLearningOfflineConfigRes> {
  return request.get("/api/studyprod/self/study/offLineTips", {
    params: {
      ignoreError: true,
    },
  });
}
