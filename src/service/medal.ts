import request, { RequestResponse } from "@/utils/request";

/**
 * 勋章
 * @params code string 勋章编号
 * @params name string 勋章名称
 * @params mainImgUrl string 勋章图片地址
 * @params avatarImgUrl string 勋章头像地址
 * @params frameImgUrl string 勋章头像边框地址
 * @params level number 勋章等级
 * @params medalNumber number 获取勋章的个数
 * @params medalDescribe string 勋章说明
 * @params awardDate number 获取的时间，时间戳
 * @params isReceived number 是否获得 1 已获取 0 未获取
 * @params groupId string 勋章分组id，用于查询勋章组详情
 * @params labelId string 分类id,list页面用
 * @params isWear boolean 是否佩戴中，false未佩戴，true为佩戴中
 */
export interface IMedalData {
  code: string;
  name: string;
  mainImgUrl: string;
  avatarImgUrl: string;
  frameImgUrl: string;
  level: number;
  medalNumber: number;
  medalDescribe: string;
  awardDate: number;
  isReceived: number;
  groupId: string;
  labelId: string;
  isWear: boolean;
}

/**
 * 根据勋章id、勋章组查询勋章详情
 */
export function getMedalDetailByCodeOrGroupId(params: {
  medalId: string;
  groupId?: string;
}) {
  return request({
    url: "/api/activitycenter/app/usersystem/getMyMedalByGroupId",
    method: "get",
    params,
  }) as RequestResponse<IMedalData[]>;
}

/**
 * 佩戴勋章
 */
export function postWearMedal(params: { medalId: string }) {
  return request({
    url: "/api/activitycenter/app/medal/updateUserCurrentMedal",
    method: "post",
    data: params,
  });
}

/**
 * 取消佩戴
 */
export function postCancelWearMedal(params: { medalId: string }) {
  return request({
    url: "/api/activitycenter/app/medal/cancelWear",
    method: "post",
    data: params,
  });
}

/**
 * 获取勋章墙开关
 */
export function getMedalSwitch(params: { ignoreError?: boolean }) {
  return request({
    url: "/api/activitycenter/app/medal/getMedalSwitch",
    method: "get",
    params,
  }) as RequestResponse<boolean>;
}
