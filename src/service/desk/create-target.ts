import request from "@/utils/request";
import { TRes } from "@/service/home";

export interface ICreateTargetBaseDataRes {
  /** 学科ID */
  subjectId: number;
  /** 学科名称 */
  subjectName: string;
  /** 动作ID */
  actionId: number;
  /** 动作名称 */
  actionName: string;
  /** 数量ID */
  amountId: number;
  /** 数量名称 */
  amountName: string;
  /** 数量单位 */
  unit: string;
  /** 是否完成 */
  completeStatus: boolean;
}

// 创建目标 - 获取基础数据
export function getDeskCreateTargetBaseData(): TRes<
  ICreateTargetBaseDataRes[]
> {
  return request.get("/api/studyprod/desk/app/target/optionList");
}

// 创建目标,成功后会返回目标id
export function postDeskCreateTarget(data: {
  endTime: number;
  subjectId: number;
  actionId: number;
  amountId: number;
}): TRes<number> {
  return request.post("/api/studyprod/desk/app/target/add", data);
}

// 编辑目标,成功后会返回目标id
export function postDeskEditTarget(data: {
  targetId?: number;
  endTime: number;
  subjectId: number;
  actionId: number;
  amountId: number;
}): TRes<number> {
  return request.post("/api/studyprod/desk/app/target/edit", data);
}

export type TDeskTargetDetailRes = ICreateTargetBaseDataRes & {
  targetId: number;
  endTime: number;
};

// 获取计划广场学科资源列表
export function getDeskTargetDetail(params: {
  targetId?: number | string;
  ignoreError?: boolean;
}): TRes<TDeskTargetDetailRes> {
  return request.get("/api/studyprod/desk/app/target/detail", {
    params,
  });
}

// 完结目标
export function postDeskCompleteTarget(data: {
  targetId: number;
}): TRes<number> {
  return request.post("/api/studyprod/desk/app/target/complete", data);
}
