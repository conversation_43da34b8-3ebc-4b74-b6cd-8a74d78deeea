import request from "@/utils/request";
import { TRes } from "@/service/home";
import { IPageData, ISubjectItem } from "@/service/common";

export interface IGradeItem {
  gradeId: number;
  gradeName: string;
}

export interface IPlanSquareSubjectListRes {
  subjectList: ISubjectItem[];
  gradeList: IGradeItem[];
}

// 获取计划广场学科列表
export function getPlanSquareSubjectList(): TRes<IPlanSquareSubjectListRes> {
  return request.get("/api/studyprod/desk/app/templatePlan/optionList");
}

export interface IPlanSquareResListData {
  /** 模版计划id */
  templatePlanId: number;
  /** 计划背景图 */
  backgroundImg: string;
  /** 模版计划名称 */
  name: string;
  /** 模版计划描述 */
  desc: string;
  /** 学习周数 */
  weekCount: number;
  /** 学习时间，学习时长，单位：分钟 */
  learnTime?: number;
  /** 学科列表 ,SubjectVO */
  subjectList?: ISubjectItem[];
  /** 阶段 */
  stage?: string;
  /** 计划id,值大于0，则说明该模版已加入计划 */
  planId?: number;
  /** 学习模式 */
  learnMode?: number;
}

export interface IPlanSquareResourceListRes {
  /** 标题可配置，计划广场 */
  title?: string;
  /** 背景图 */
  backgroundImg?: string;
  /** 当前时间, 服务器时间，使用场景是：周模式时需要计算距离下周差几天 */
  currentTime?: number;
  /** 模版计划列表 ,PageQueryResultExt */
  list?: IPageData<IPlanSquareResListData[]>;
}

// 获取计划广场学科资源列表
export function getPlanSquareResourceList(params: {
  subjectId: number;
  pageIndex: number;
  pageSize: number;
  grade: number | string;
}): TRes<IPlanSquareResourceListRes> {
  return request.get("/api/studyprod/desk/web/templatePlan/list", {
    params,
  });
}
