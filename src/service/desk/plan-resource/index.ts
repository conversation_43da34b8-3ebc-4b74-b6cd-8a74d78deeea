import request from "@/utils/request";
import { TRes } from "@/service/home";

export interface IPageData<T> {
  /** 总页数 */
  totalPages?: number;
  /** 当前页码 */
  pageIndex?: number;
  /** 每页条数 */
  pageSize?: number;
  /** 总条数兼容移动端出参 */
  total?: number;
  /** 总页码兼容移动端出参 */
  totalPage?: number;
  /** 总记录数 */
  totalRecords?: number;
  /** 分页结果集 ,T */
  data?: T[];
  /** 注释 */
  havePrePage?: boolean;
  /** 注释 */
  haveNextPage?: boolean;
}

export interface IWeekOrDayList {
  /** 开始时间 */
  startTime: number;
  /** 结束时间 */
  endTime: number;
  /** 任务总数 */
  taskCount?: number;
  /** 完成任务数 */
  finishCount?: number;
  /** 是否是当前周或今天 */
  currentWeekOrToday?: boolean;
}

/** 学习模式 */
export enum LearnModelEnum {
  /** 周计划 */
  WEEK = 1,
  /** 日计划 */
  DAY = 2,
  /** 无周期计划 */
  NO_CYCLE = 3,
}

/** 计划来源 */
export const planSourceMap = {
  1: "自主创建",
  2: "计划广场",
} as const;

export const LearnModelTitleMap = {
  [LearnModelEnum.WEEK]: "本周",
  [LearnModelEnum.DAY]: "今天",
  // 无周期的不需要值，因为不会显示周期的列表
  [LearnModelEnum.NO_CYCLE]: "",
} as const;

export interface IPlanInfoProcessRes {
  /** 计划名称 */
  planName: string;
  /** 完成任务数 */
  completeTaskCount: number;
  /** 任务总数 */
  taskCount: number;
  /** 学习模式,1周计划，2日计划，3无周期计划 */
  learnModel: LearnModelEnum;
  /** 是否允许编辑资源 */
  allowResourceEdit?: boolean;
  /** 自然周列表 ,NaturalWeekOrDayVO */
  weekOrDayList: IWeekOrDayList[];
  /** 当前时间 */
  currentTime: string;
  /** 计划来源 */
  planSource: number;
  /** 首个重点学科ID */
  firstSubjectId?: number;
  /** 是否已删除 */
  hasDeleted?: boolean;
}

/**
 * 根据planId获取任务进度+周列表
 */
export function getDeskPlanInfoProcess(params: {
  planId: string;
  ignoreError?: boolean;
}): TRes<IPlanInfoProcessRes> {
  return request.get("/api/studyprod/desk/app/plan/info/process", {
    params,
  });
}

/** 讲 */
export interface ILessonVO {
  /** 包ID */
  courseId?: string;
  /** 讲ID */
  lessonId?: string;
  /** 讲名称 */
  lessonName?: string;
  /** 时长秒 */
  duration?: number;
  /** 用户看课的进度0～1小数 */
  progressTime?: number;
  /** 试卷id-快照ID */
  paperId?: string;
  /** 试卷是否完成：false未完成true已完成 */
  hasFinish?: boolean;
  /** 报告id */
  reportId?: string;
  /** 是否已批改，true已批改；false未批改 */
  correctResult?: boolean;
  /** 正确率0-1小数 */
  rightRate?: number;
}

/** 试卷 */
export interface IPaperVO {
  /** 试卷ID-快照ID */
  paperId?: string;
  /** 试卷名称 */
  paperName?: string;
  /** 试卷时长分钟 */
  duration?: number;
  /** 试卷题目数 */
  questionNum?: number;
  /** 试卷作答码 */
  bizCode?: number;
  /** 平台 */
  platform?: number;
  /** 报告id */
  reportId?: string;
  /** 正确率0～1小数 */
  rightRate?: number;
  /** 是否已批改true已批改；false未批改 */
  correctResult?: boolean;
}

/** fm */
export interface IFmVO {
  /** FMid */
  fmId?: string;
  /** FM名称 */
  fmName?: string;
  /** 播放时长 */
  duration?: number;
}

export enum EResourceType {
  /** 讲 */
  lesson = 1,
  /** 试卷 */
  paper = 2,
  /** FM */
  fm = 3,
}
export interface IPlanResourceListRes {
  /** 资源记录ID */
  recordId: string;
  /** 资源类型 */
  resourceType?: EResourceType;
  /** 学科ID */
  subjectId?: number;
  /** 学科名称 */
  subjectName?: string;
  /** 资源状态：false失效true有效 */
  resourceStatus?: boolean;
  /** 完成状态：false未完成true已完成 */
  finishStatus?: boolean;
  /** 是否禁用：false否true是 */
  hasForbid?: boolean;
  /** 讲信息 */
  lessonVO?: ILessonVO;
  /** 试卷信息 */
  paperVO?: IPaperVO;
  /** FM信息 */
  fmVO?: IFmVO;
  /** 页码,前端自行添加的，使用场景是从app看课回来后刷新当提起那 */
  pageIndex?: number;
}

export interface IPlanResourceListNewRes {
  finishedCount: number;
  queryResultExt: IPageData<IPlanResourceListRes>;
}

// 查询场景
export enum FromTypeEnum {
  /** 看课、试卷等场景返回 */
  visibilityChange = 0,
  /** 其他 */
  other = 1,
}

/**
 * 指定天或者周计划的分页列表
 */
export function getDeskPlanResourceList(params: {
  planId: string;
  pageSize: number;
  pageIndex: number;
  startTime: number;
  endTime: number;
  channel: string;
  ignoreError?: boolean;
  fromType?: FromTypeEnum;
}): TRes<IPlanResourceListNewRes> {
  return request.get("/api/studyprod/desk/app/plan/info/resource/list", {
    params,
  });
}

/** 弹窗类型 */
export enum PositiveFeedbackEnum {
  /** 今天 */
  today = 1,
  /** 未来 */
  future = 2,
  /** 历史 */
  history = 3,
}

// 根据recordId删除资源
export function postRemoveResourceByRecordId(data: {
  recordId: string;
}): TRes<boolean> {
  return request.post(
    "/api/studyprod/desk/app/plan/resource/removeResource",
    data,
  );
}

/** 获取反馈问题 */
export function getSummaryInfo(params): TRes<any[]> {
  return request.get("/api/studyprod/desk/diagnostic/learning/summaryInfo", {
    params,
  });
}

/** 更新反馈问题 */
export function postUpdateSummaryOptions(data): TRes<boolean> {
  return request.post(
    "/api/studyprod/desk/diagnostic/learning/updateSummaryOptions",
    data,
  );
}
export interface IPlanPositiveFeedbackRes {
  /** 时间戳，冗余字段，暂时业务内未使用 */
  date: string;
  /** 资源类型 */
  popUpType: PositiveFeedbackEnum;
}

/** 获取计划正向反馈列表 */
export function getDeskPlanPositiveFeedback(params: {
  planId: string;
  ignoreError?: boolean;
}): TRes<IPlanPositiveFeedbackRes[]> {
  return request.get("/api/studyprod/desk/app/plan/positiveFeedbackList", {
    params,
  });
}
