import request from "@/utils/request";

/**
 * 创建计划
 * yapi http://yapi.235.mistong.com/project/1181/interface/api/163358
 * @param data
 * @returns
 */
export function planAdd(
  data: Desk.plan.add.Request,
): Promise<Desk.plan.add.Response> {
  return request.post("/api/studyprod/desk/app/plan/add", data);
}

/**
 * 生成计划名称
 * yapi http://yapi.235.mistong.com/project/1181/interface/api/164415
 * @param data
 * @returns
 */
export function generatePlanName(
  data: Desk.plan.generatePlanName.Request,
): Promise<Desk.plan.generatePlanName.Response> {
  return request.post("/api/studyprod/desk/app/plan/generatePlanName", data);
}

/**
 * 用户计划列表（不含周末智学）
 * yapi http://yapi.235.mistong.com/project/1181/interface/api/163344
 * @param data
 * @returns
 */
export function getPlanList(params): Promise<Desk.plan.list.Response> {
  return request.get("/api/studyprod/desk/app/plan/list", {
    params,
  });
}

/**
 * 单个删除计划
 * yapi http://yapi.235.mistong.com/project/1181/interface/api/163498
 * @param data
 * @returns
 */
export function postPlanRemove(
  data: Desk.plan.remove.Request,
): Promise<Desk.plan.remove.Response> {
  return request.post("/api/studyprod/desk/app/plan/remove", data);
}

/**
 * 周末智学入口
 * yapi http://yapi.235.mistong.com/project/1181/interface/api/164436
 * @param data
 * @returns
 */
export function getIstudy(
  params: Desk.plan.isStudy.Request,
): Promise<Desk.plan.isStudy.Response> {
  return request.get("/api/studyprod/desk/app/plan/istudy", {
    params,
  });
}

/**
 * 模版计划详情
 * yapi http://yapi.235.mistong.com/project/1181/interface/api/164394
 * @param params
 * @returns
 */
export function getTemplatePlanDetail(
  params: Desk.templatePlan.detail.Request,
): Promise<Desk.templatePlan.detail.Response> {
  return request.get("/api/studyprod/desk/app/templatePlan/detail", {
    params,
  });
}

/**
 * 用户生成的计划详情
 * yapi http://yapi.235.mistong.com/project/1181/interface/api/168222
 * @param params
 * @returns
 */
export function getSynthesisPlanDetail(
  params: Desk.templatePlan.synthesisDetail.Request,
): Promise<Desk.templatePlan.synthesisDetail.Response> {
  return request.get(
    "/api/studyprod/desk/diagnostic/learning/synthesisPlan/detail",
    {
      params,
    },
  );
}

/**
 * 模版计划资源列表（分页）
 * yapi http://yapi.235.mistong.com/project/1181/interface/api/164401
 * @param params
 * @returns
 */
export function getResourceList(
  params: Desk.templatePlan.resourceList.Request,
): Promise<Desk.templatePlan.resourceList.Response> {
  return request.get("/api/studyprod/desk/app/templatePlan/resourceList", {
    params,
  });
}

/**
 * 计划数量统计
 * yapi http://yapi.235.mistong.com/project/1181/interface/api/165547
 * @param params
 * @returns
 */
export function getPlanCount(
  params?: Desk.plan.planCount.Request,
): Promise<Desk.plan.planCount.Response> {
  return request.get("/api/studyprod/desk/app/plan/planCount", {
    params,
  });
}
