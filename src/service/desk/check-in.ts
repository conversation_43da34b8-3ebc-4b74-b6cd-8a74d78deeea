import request from "@/utils/request";
import { TRes } from "@/service/home";

export interface ICheckInSystemConfigRes {
  /** 系统时间 */
  systemTime: number;
  /** 打卡激励任务的起始时间 */
  configTime: number;
}

// 书桌-获取系统配置
export function getDeskSystemConfig(): TRes<ICheckInSystemConfigRes> {
  return request.get(
    "/api/studyprod/desk/pc/plan/month/configAndServiceTimeInfo",
  );
}

export interface ICheckInListAwardConfig {
  /** 阈值：打卡累计天数 */
  targetValue: string;
  /** 勋章ID */
  medalId: number;
  /** 奖励等级 */
  medalLevel: number;
  /** 奖励领取状态（0：未领取，1：已领取） */
  awardStatus: number;
  /** 勋章名称	 */
  medalName: string;
  /** 勋章图片 */
  mainImgUrl: string;
}

export interface ICheckInListRes {
  monthCheckInCount: number;
  awardConfigList: ICheckInListAwardConfig[];
}

// 书桌-获取打卡列表
export function getDeskCheckInList(params: {
  startTime: number;
  endTime: number;
}): TRes<ICheckInListRes> {
  return request.get("/api/studyprod/desk/pc/plan/checkInList", {
    params,
  });
}

// 书桌-获取打卡状态
export function getDeskCheckInStatus(params: { ignoreError: boolean }): TRes<{
  checkInStatus: boolean;
  targetDuration: number;
}> {
  return request.get("/api/studyprod/desk/pc/plan/getCheckInStatus", {
    params,
  });
}

export interface IPostDeskCheckInRes {
  /** 打卡状态（true-打卡成功,false-打卡失败）*/
  checkInStatus: boolean;
  /** 已学时长 */
  studyDuration: number;
  /** 目标时长 */
  targetDuration: number;
}

// 书桌-打卡
export function postDeskCheckIn(data: {
  ignoreError: boolean;
}): TRes<IPostDeskCheckInRes> {
  return request.post("/api/studyprod/desk/pc/plan/checkIn", {
    data,
  });
}

export interface IDaily {
  /** 日签id */
  id: string;
  /** 排版日 */
  layoutDate: string;
  /** 引用语 */
  quote: string;
  /** 语出处 */
  source: string;
  /** 背景图 */
  backgroundImage: string;
  /** 跳转类型 */
  linkType: string;
  /** 二维码url */
  qrCodeUrl: string;
  /** 跳转路由 */
  routeUrl: string;
  /** 内容 */
  content?: string;
}

export interface IMedalAward {
  /** 勋章id */
  id: string;
  /** 勋章名称	 */
  name: string;
  /** 勋章图（主图片URL）	 */
  mainImgUrl: string;
  /** 勋章等级 */
  level: string;
  /** 勋章头像图 */
  avatarImgUrl: string;
  /** 勋章头像边框 */
  frameImgUrl: string;
  /** 获取时间 */
  awardDate: string;
  /** 勋章描述 */
  medalDescribe?: string;
}

export interface IPostDeskReceiveCheckInPrizeRes {
  /** 注释 */
  medalAward: IMedalAward;
  /** 注释 */
  daily: IDaily;
}

// 书桌 - 打卡 - 领取奖励
export function postDeskReceiveCheckInPrize(data?: {
  ignoreError?: boolean;
}): TRes<IPostDeskReceiveCheckInPrizeRes> {
  return request.post("/api/studyprod/desk/pc/plan/rewarded", {
    data,
  });
}
