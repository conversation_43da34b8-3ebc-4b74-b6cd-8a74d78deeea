import request from "@/utils/request";

export type TRes<T> = Promise<{ data: T }>;

/** 获取答题记录 */
export enum EPaperID {
  // 生涯大事件 - 排序
  careerSort = 1,
  // 生涯性格测评
  careerMBTI,
}
export type TAnswer = {
  paperId: EPaperID;
  questionId: string;
  answers: string;
};

export function getAnswers(
  paperId: EPaperID,
): TRes<Omit<TAnswer, "paperId">[]> {
  return request.get("/api/activitycenter/page/question/app/getAnswers", {
    params: {
      paperId,
    },
  });
}

/** 保存答题记录 */
export function saveAnswer(data: TAnswer): TRes<boolean> {
  return request.post(
    "/api/activitycenter/page/question/app/saveAnswers",
    data,
  );
}

/** 清空页面题答题记录 */
export function clearRecord(data: { paperId: EPaperID }): TRes<boolean> {
  return request.post(
    "/api/activitycenter/page/question/app/clearAnswers",
    data,
  );
}

/** 获取人格类型推荐专业列表 */

export interface IMajorInfo {
  recommendList: TMajor[];
  answerTaskId: string;
}

export type TMajor = {
  id: number;
  name: string;
};
export function getMajorListByType(params: { type: string }): TRes<IMajorInfo> {
  return request.get(
    "/api/activitycenter/page/question/app/mbti/getMajorListByType",
    {
      params,
    },
  );
}
