import request, { RequestResponse } from "@/utils/request";

export interface IBaseEdition {
  /** 教材id */
  baseEditionId: string;
  /** 教材名称 */
  editionName: string;
  /** 图片地址 */
  imgUrl: string;
}

export interface ISubjectBaseEditionList {
  /** 学科id */
  subjectId: number;
  /** 学科名称 */
  subjectName: string;
  /** 教材列表 */
  editionList: IBaseEdition[];
}

// 获取 所有学科 全部教材版本列表
export const getAllSubjectEditionList = () =>
  request.get("/api/studyprod/common/edition/getAllList") as RequestResponse<
    ISubjectBaseEditionList[]
  >;

export interface IUserSubjectBaseEdition {
  /** 学科id */
  subjectId: number;
  /** 基础目录id, 如果没有返回null */
  baseEditionId?: string;
}

// 获取 用户 所有学科 选中的 教材版本
export const getUserSubjectEdition = () =>
  request.get(
    "/api/studyprod/common/edition/user/getAllSubjects",
  ) as RequestResponse<IUserSubjectBaseEdition[]>;

// 保存 用户 某些学科 选中的 教材版本
export const saveUserSubjectEdition = (data: {
  subjectEditionList: IUserSubjectBaseEdition[];
}) =>
  request.post(
    "/api/studyprod/common/edition/saveAllSubjects",
    data,
  ) as RequestResponse<boolean>;
/** 学科 */
export interface ISubjectItem {
  /** 学科id */
  subjectId: number;
  /** 学科名称 */
  subjectName: string;
}

/** 分页数据 */
export interface IPageData<T> {
  /** 总页数 */
  totalPages?: number;
  /** 当前页码 */
  pageIndex?: number;
  /** 每页条数 */
  pageSize?: number;
  /** 总条数兼容移动端出参 */
  total?: number;
  /** 总页码兼容移动端出参 */
  totalPage?: number;
  /** 总记录数 */
  totalRecords?: number;
  /** 分页结果集 ,T */
  data?: T[];
  /** 注释 */
  havePrePage?: boolean;
  /** 注释 */
  haveNextPage?: boolean;
}
