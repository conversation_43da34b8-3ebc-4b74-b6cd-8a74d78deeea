import { slsUpload, switchRouterInfo } from "./utils";

function main() {
  // 应用的根路径
  const base = __APP_DIR__;
  // 转换 history 路由前，有使用的 hash 路由
  const hashPaths = ["/svip", "/self-learning"];

  try {
    const info = switchRouterInfo(base, hashPaths, true, window.location);
    if (info.toPath) {
      const fromPath = window.location.href;
      window.history.replaceState({}, "", info.toPath);
      // 当前项目访问量不大，故先全量做埋点
      slsUpload({
        event: "switch-router-info-success",
        toType: "history",
        fromPath,
        toPath: info.toPath,
      });
    }
  } catch (e) {
    console.log("info error: ", e);
    slsUpload(
      {
        event: "switch-router-info-failed",
        toType: "history",
        error: e,
      },
      "error",
    );
  }
}

main();
