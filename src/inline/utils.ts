// 上报 SLS 日志自定义方法，不依赖额外包，减少体积
export function slsUpload(
  message: Record<string, any>,
  level: string = "info",
) {
  try {
    const gp = JSON.stringify({
      location: window.location.href,
      environment: process.env.DEPLOYMENT_ENV || "local",
      release: window.__MST_LABEL__ || "baseline",
      build_time: __BUILD_TIME__ || "0",
    });

    const recordContent = JSON.stringify(message);

    const image = new Image();
    const slsRecordUrl = `https://sls-ewt-tech-infra-fe.cn-hangzhou.log.aliyuncs.com/logstores/ewt-web-log/track_ua.gif?APIVersion=0.6.0&__topic__=ewtCustomerH5&__source__=html&gp=${encodeURIComponent(gp)}&content=${encodeURIComponent(recordContent)}&level=${level}`;
    image.src = slsRecordUrl;
  } catch (error) {
    console.warn("图片形式上报sls出错", error);
  }
}

/**
 * 合并字符串开头的多个斜杠为一个
 * @param str 需要处理的字符串
 * @returns 处理后的字符串
 */
function mergeSlash(str: string) {
  if (!str) {
    return str;
  }
  let i = 0;
  while (str[i] === "/" && str[i + 1] === "/") {
    i++;
  }
  return i ? str.substring(i) : str;
}

/**
 * hash 路由和 history 路由的切换
 * @param base 根路径
 * @param hashPaths hash 路由的路径，用来判断是否为 hash 路由
 * @param toHistory 是否切换为 history 路由
 * @returns
 */
export function switchRouterInfo(
  base: string,
  hashPaths: string[],
  toHistory: boolean,
  location: Location,
) {
  const pathname = location.pathname;
  const search = location.search;
  const hash = location.hash;
  // 去掉 # 后，切分路由
  const hashArr = hash.length > 1 ? hash.slice(1).split("?") : [];
  const historyInfo = {
    // 去掉 跟路径信息
    pathname: mergeSlash(pathname.split(base)[1]) || "/",
    // 去掉 问号
    search: search.length > 1 ? search.slice(1) : "",
  };
  const hashInfo = {
    pathname: mergeSlash(hashArr[0]) || "/",
    search: hashArr[1] || "",
  };

  let isHistory = true;
  if (historyInfo.pathname === "/") {
    if (hashInfo.pathname !== "/") {
      // 判断是否历史的 hash 路由
      for (let i = 0; i < hashPaths.length; i++) {
        if (hashInfo.pathname.toLowerCase().indexOf(hashPaths[i]) === 0) {
          isHistory = false;
          break;
        }
      }
    } else if (hashInfo.search.length > historyInfo.search.length) {
      // 如果两种路由都是 /，则谁的 search 长，就当做那种模式
      isHistory = false;
    }
  }

  // 最终需要跳转的路径
  let toPath = "";
  // 部分场景完全无需跳转
  if (
    hashInfo.pathname !== "/" ||
    hashInfo.search.length > 0 ||
    historyInfo.pathname !== "/" ||
    historyInfo.search.length > 0
  ) {
    if (toHistory && !isHistory) {
      toPath = base + hashInfo.pathname;
      if (hashInfo.search) {
        toPath += "?" + hashInfo.search;
      }
    } else if (!toHistory && isHistory) {
      toPath = base + "/#" + historyInfo.pathname;
      if (historyInfo.search) {
        toPath += "?" + historyInfo.search;
      }
    }
  }

  return {
    isHistory,
    historyInfo,
    hashInfo,
    toPath,
  };
}
