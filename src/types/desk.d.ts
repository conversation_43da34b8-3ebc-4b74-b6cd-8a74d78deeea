declare namespace Desk {
  namespace plan {
    namespace remove {
      interface Request {}

      interface Response {
        /** 注释 */
        success?: boolean;
        /** 注释 */
        code?: string;
        /** 注释 */
        msg?: string;
        /** data */
        data?: boolean;
      }
    }
    namespace add {
      interface Request {}

      interface Response {
        /** 注释 */
        success?: boolean;
        /** 注释 */
        code?: string;
        /** 注释 */
        msg?: string;
        /** data */
        data?: number;
      }
    }

    namespace generatePlanName {
      interface Request {}

      interface Response {
        /** 注释 */
        success?: boolean;
        /** 注释 */
        code?: string;
        /** 注释 */
        msg?: string;
        /** data */
        data?: number;
      }
    }

    namespace isStudy {
      interface Request {
        needProcess: boolean;
      }

      interface Response {
        /** 注释 */
        success?: boolean;
        /** 注释 */
        code?: string;
        /** 注释 */
        msg?: string;
        /** IStudyVO */
        data?: Data;
      }

      interface Data {
        /** 名称 */
        planName: string;
        /** 计划描述 */
        desc: string;
        /** 周期内任务总数量 */
        cycleTaskCount?: number;
        /** 周期内完成数量 */
        cycleFinishCount?: number;
        /** 学习模式：1周计划，2日计划，3无周期计划,,@seecom.ewt360.learning.service.common.enums.LearnModelEnum */
        learnMode: number;
        /** 路由 */
        route: string;
        h5Route: string;
      }
    }
    namespace list {
      interface Request {
        /** 页容量(Integer) */
        pageSize: string;
        /** 页码(Integer) */
        pageIndex: string;
        /** 状态：0 进行中，1 已完成(Integer) */
        status?: string;
      }

      interface Response {
        /** 注释 */
        success?: boolean;
        /** 注释 */
        code?: string;
        /** 注释 */
        msg?: string;
        /** PageQueryResultExt */
        data?: Data2;
      }

      interface Data2 {
        /** 总页数 */
        totalPages?: number;
        /** 当前页码 */
        pageIndex?: number;
        /** 每页条数 */
        pageSize?: number;
        /** 总条数兼容移动端出参 */
        total?: number;
        /** 总页码兼容移动端出参 */
        totalPage?: number;
        /** 总记录数 */
        totalRecords?: number;
        /** 分页结果集 ,T */
        data?: Data[];
        /** 注释 */
        havePrePage?: boolean;
        /** 注释 */
        haveNextPage?: boolean;
      }

      interface Data {
        /** 业务类型 */
        bizCode: string;
        /** 计划ID */
        planId: number;
        /** 计划名称 */
        planName: string;
        /** 计划开始时间戳 */
        startTime?: number;
        /** 计划结束时间戳 */
        endTime?: number;
        /** 任务总数量 */
        taskAllCount?: number;
        /** 完成数量 */
        finishAllCount?: number;
        /** 完成状态 */
        completeStatus?: boolean;
        /** 最近更新时间 */
        lastUpdateTime?: string;
        /** 计划创建时间 */
        planCreateTime?: string;
        /** 来源类型，1自习计划，2模板计划 */
        sourceType?: number;
        /** 学科名称 */
        subjectName?: string;
      }
    }
    namespace planCount {
      interface Request {}

      interface Response {
        /** 注释 */
        success?: boolean;
        /** 注释 */
        code?: string;
        /** 注释 */
        msg?: string;
        /** AllPlanCountVO */
        data?: Data;
      }

      interface Data {
        /** 进行中计划数 */
        inProgressCount?: number;
        /** 未开始计划数 */
        notStartedCount?: number;
        /** 已截止计划数 */
        expireCount?: number;
      }
    }
  }
  namespace templatePlan {
    namespace detail {
      interface Request {
        /** 模版计划id(Long) */
        templatePlanId?: string;
        /** 是否忽略错误 */
        ignoreError?: boolean;
      }

      interface Response {
        /** 注释 */
        success?: boolean;
        /** 注释 */
        code?: string;
        /** 注释 */
        msg?: string;
        /** TemplatePlanDetailVO */
        data?: Data;
      }

      interface Data {
        /** 当前时间 */
        currentTime?: number;
        /** 模版计划资源分组 ,ResourceGroupVO */
        resourceGroupList?: ResourceGroupList[];
        /** 模版计划id */
        templatePlanId: string;
        /** 计划背景图 */
        backgroundImg: string;
        /** 模版计划名称 */
        name: string;
        /** 模版计划描述 */
        desc: string;
        /** 学习周数 */
        weekCount: number;
        /** 学习时间，学习时长，单位：分钟 */
        learnTime?: number;
        /** 学习模式，1 周计划, 2 日计划, 3 无周期计划 */
        learnMode: number;
        /** 学科列表 ,SubjectVO */
        subjectList?: SubjectList[];
        /** 阶段 */
        stage?: string;
        /** 计划id,值大于0，则说明该模版已加入计划 */
        planId?: number;
      }

      interface SubjectList {
        /** 注释 */
        subjectId?: number;
        /** 注释 */
        subjectName?: string;
      }

      interface ResourceGroupList {
        /** 计划id */
        templatePlanId?: number;
        /** 组id */
        groupId?: number;
        /** 组名 */
        groupName?: string;
      }
    }
    namespace resourceList {
      interface Request {
        /** 模版计划id(Long) */
        templatePlanId: string;
        /** 分组id(Integer) */
        groupId: number;
        /** 页码(Integer) */
        pageIndex: number;
        /** 页大小(Integer) */
        pageSize: number;
        /** (String) */
        channel: string | number;
      }

      interface Response {
        /** 注释 */
        success?: boolean;
        /** 注释 */
        code?: string;
        /** 注释 */
        msg?: string;
        /** PageQueryResultExt */
        data?: Data2;
      }

      interface Data2 {
        /** 总页数 */
        totalPages?: number;
        /** 当前页码 */
        pageIndex?: number;
        /** 每页条数 */
        pageSize?: number;
        /** 总条数兼容移动端出参 */
        total?: number;
        /** 总页码兼容移动端出参 */
        totalPage?: number;
        /** 总记录数 */
        totalRecords?: number;
        /** 分页结果集 ,T */
        data?: Data[];
        /** 注释 */
        havePrePage?: boolean;
        /** 注释 */
        haveNextPage?: boolean;
      }

      interface Data {
        /** 记录ID */
        recordId: string;
        /** 资源类型,@seecom.ewt360.learning.service.common.enums.SelfResourceTypeEnum */
        resourceType?: number;
        /** 学科ID */
        subjectId?: number;
        /** 学科名称 */
        subjectName?: string;
        /** 是否禁用：false否true是 */
        hasForbid?: boolean;
        /** 讲resourceType=1有 ,TemplateLessonVO */
        lessonVO?: LessonVO;
        /** 试卷resourceType=2有 ,TemplatePaperVO */
        paperVO?: PaperVO;
        /** fmresourceType=3有 ,TemplateFmVO */
        fmVO?: FmVO;
      }

      interface FmVO {
        /** FMid */
        fmId?: string;
        /** FM名称 */
        fmName?: string;
        /** 播放时长 */
        duration?: number;
      }

      interface PaperVO {
        /** 试卷ID-快照ID */
        paperId?: string;
        /** 试卷名称 */
        paperName?: string;
        /** 试卷时长分钟 */
        duration?: number;
        /** 试卷题目数 */
        questionNum?: number;
        /** 试卷作答码 */
        bizCode?: number;
        /** 平台 */
        platform?: number;
      }

      interface LessonVO {
        /** 包ID */
        courseId?: string;
        /** 讲ID */
        lessonId?: string;
        /** 讲名称 */
        lessonName?: string;
        /** 时长秒 */
        duration?: number;
      }
    }
    namespace synthesisDetail {
      export interface Request {
        /** 生成的模版计划id(Long) */
        diagnosticTemplatePlanId: string;
      }

      export interface Response {
        /** 注释 */
        success?: boolean;
        /** 注释 */
        code?: string;
        /** 注释 */
        msg?: string;
        /** TemplatePlanDetailVO */
        data?: Data;
      }

      export interface Data {
        /** 当前时间 */
        currentTime?: number;
        /** 模版计划资源分组 ,ResourceGroupVO */
        resourceGroupList?: ResourceGroupList[];
        /** 模版计划id */
        templatePlanId: number;
        /** 计划背景图 */
        backgroundImg: string;
        /** 模版计划名称 */
        name: string;
        /** 模版计划描述 */
        desc: string;
        /** 学习周数 */
        weekCount: number;
        /** 学习时间，学习时长，单位：分钟 */
        learnTime?: number;
        /** 学科列表 ,SubjectVO */
        subjectList?: SubjectList[];
        /** 学习模式，1周计划,2日计划,3无周期计划 */
        learnMode?: number;
        /** 计划id,值大于0，则说明该模版已加入计划 */
        planId?: number;
      }

      export interface SubjectList {
        /** 注释 */
        subjectId?: number;
        /** 注释 */
        subjectName?: string;
      }

      export interface ResourceGroupList {
        /** 实际的模板计划id */
        templatePlanId: number;
        /** 组id */
        groupId: number;
        /** 组名， 拼接的组名 */
        groupName: string;
      }
    }
  }
}
