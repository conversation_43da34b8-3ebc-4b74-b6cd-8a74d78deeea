// 垂直渐变背景
@mixin linear_gradient($startColor, $endColor) {
  background: -webkit-linear-gradient(top, $startColor, $endColor); /* Safari 5.1 - 6.0 */
  background: -o-linear-gradient(bottom, $startColor, $endColor); /* Opera 11.1 - 12.0 */
  background: -moz-linear-gradient(bottom, $startColor, $endColor); /* Firefox 3.6 - 15 */
  background: linear-gradient(to bottom, $startColor, $endColor); /* 标准的语法 */
}

// 水平渐变背景
@mixin linear_gradient_x($startColor, $endColor) {
  background: -webkit-linear-gradient(left, $startColor, $endColor); /* Safari 5.1 - 6.0 */
  background: -o-linear-gradient(right, $startColor, $endColor); /* Opera 11.1 - 12.0 */
  background: -moz-linear-gradient(right, $startColor, $endColor); /* Firefox 3.6 - 15 */
  background: linear-gradient(to right, $startColor, $endColor); /* 标准的语法 */
}

// 现代浏览器滚动条
@mixin modern_scroll($width:4px, $background:#94999c) {
  &::-webkit-scrollbar {
    width: $width;
  }
  &::-webkit-scrollbar-thumb { /*滚动条里面小方块*/
    //border-radius: 2px;
    background: $background;
  }
  &::-webkit-scrollbar-track { /*滚动条里面轨道*/
    //border-radius: 2px;
    background: transparent;
  }
}

@mixin modern_scroll_x($height:6px, $background:#94999c) {
  &::-webkit-scrollbar {
    height: $height;
  }
  &::-webkit-scrollbar-thumb { /*滚动条里面小方块*/
    border-radius: 6px;
    background: $background;
  }
  &::-webkit-scrollbar-track { /*滚动条里面轨道*/
    background: transparent;
  }
}

// 鼠标hover显示蒙层效果
// 添加该效果的目标元素需指定position属性
@mixin hover_cover_effect($background:rgba(255,255,255,.15)) {
  &:after {
    content: '';
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: $background;
  }
}

// 鼠标hover显示box-shadow
@mixin hover_shadow_effect($background:rgba(0,0,0,.1)) {
  box-shadow: 0 2px 7px 0 $background;
}

// 多行省略号
@mixin multi_lines_ellipsis($lines) {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $lines;
  overflow: hidden;
}

@function str-split($string, $separator) {
  $split-list: ();
  $index: str-index($string, $separator);

  @while $index != null {
    $item: str-slice($string, 1, $index - 1);
    $split-list: append($split-list, $item);
    $string: str-slice($string, $index + 1);
    $index: str-index($string, $separator);
  }

  $split-list: append($split-list, $string);
  @return $split-list;
}

@function scale-calc($value, $scale) {
  $str: inspect($value);

  @if str-index($str, 'calc(') {
    $inner: str-slice($str, 6, str-length($str) - 1);
    $tokens: str-split($inner, ' ');
    $new-tokens: ();

    @each $t in $tokens {
      @if str-index($t, 'px') {
        // 直接在字符串里拼倍率
        $num-str: str-slice($t, 1, str-length($t) - 2);
        $new-tokens: append($new-tokens, $num-str + 'px * #{$scale}', space);
      } @else {
        $new-tokens: append($new-tokens, $t, space);
      }
    }

    @return unquote('calc(#{$new-tokens})');
  }

  @return $value;
}

/*
 * 数值倍率转换
 * @param {number} $value - 输入的值
 * @param {number} $multiplier - 倍率
 * @return {number} - 乘法结果
 */
 @function multiply-value($value, $multiplier) {

  $value-str: inspect($value);
  // ---- calc() ----
  @if str-index($value-str, 'calc(') {
    @return scale-calc($value-str, $multiplier);
  }

  @if type-of($value) == list {
    $result: ();
    @each $val in $value {
      @if type-of($val) == number and not unitless($val) {
        $result: append($result, $val * $multiplier, space);
      } @else {
        $result: append($result, $val, space);
      }
    }
    @return $result;

  } @else if type-of($value) == string {
    @if str-index($value, 'drop-shadow(') {
      // drop-shadow 处理逻辑同之前
      $content: str-slice($value, 13, -2);
      $parts: str-split($content, ' ');
      $processed: ();
      @each $part in $parts {
        @if str-index($part, 'px') {
          $num: str-slice($part, 1, str-length($part) - 2);
          $processed: append($processed, #{($num * $multiplier)}px, space);
        } @else {
          $processed: append($processed, $part, space);
        }
      }
      @return unquote('drop-shadow(#{$processed})');

    } @else {
      @return $value;
    }

  } @else if type-of($value) == number and not unitless($value) {
    @return $value * $multiplier;

  } @else {
    @return $value;
  }
}

@function process-value-with-important($value, $multiplier) {
  $important: null;
  $actual-value: $value;

  // 检查是否包含 !important
  @if type-of($value) == list and length($value) > 1 {
    $last-item: nth($value, -1);
    @if type-of($last-item) == string and $last-item == '!important' {
      $important: '!important';
      $actual-value: slice($value, 1, -1);
    }
  }

  // 处理实际的值
  $processed-value: multiply-value($actual-value, $multiplier);

  // 如果有 !important，重新添加
  @if $important {
    @if type-of($processed-value) == list {
      @return append($processed-value, $important);
    } @else {
      @return ($processed-value, $important);
    }
  } @else {
    @return $processed-value;
  }
}

/**
  @ description: 适配最大宽度(仅传需要转换的属性、px才转换)
  @ param $properties: 属性列表
  @ param $min-width: 最小宽度 默认800
  @ param $scale: 缩放比例 默认1.5
**/

@mixin adaptive-max($properties, $min-width: 540px, $scale: 540 / 375) {
  @each $prop, $value in $properties {
    #{$prop}: $value;
  }

  @media (min-width: $min-width) {
    $multiplier: $scale;

    @each $prop, $value in $properties {
      #{$prop}: process-value-with-important($value, $multiplier);
    }
  }
}
