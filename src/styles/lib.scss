// 垂直渐变背景
@mixin linear_gradient($startColor, $endColor) {
  background: -webkit-linear-gradient(top, $startColor, $endColor); /* Safari 5.1 - 6.0 */
  background: -o-linear-gradient(bottom, $startColor, $endColor); /* Opera 11.1 - 12.0 */
  background: -moz-linear-gradient(bottom, $startColor, $endColor); /* Firefox 3.6 - 15 */
  background: linear-gradient(to bottom, $startColor, $endColor); /* 标准的语法 */
}

// 水平渐变背景
@mixin linear_gradient_x($startColor, $endColor) {
  background: -webkit-linear-gradient(left, $startColor, $endColor); /* Safari 5.1 - 6.0 */
  background: -o-linear-gradient(right, $startColor, $endColor); /* Opera 11.1 - 12.0 */
  background: -moz-linear-gradient(right, $startColor, $endColor); /* Firefox 3.6 - 15 */
  background: linear-gradient(to right, $startColor, $endColor); /* 标准的语法 */
}

// 现代浏览器滚动条
@mixin modern_scroll($width:4px, $background:#94999c) {
  &::-webkit-scrollbar {
    width: $width;
  }
  &::-webkit-scrollbar-thumb { /*滚动条里面小方块*/
    //border-radius: 2px;
    background: $background;
  }
  &::-webkit-scrollbar-track { /*滚动条里面轨道*/
    //border-radius: 2px;
    background: transparent;
  }
}

@mixin modern_scroll_x($height:6px, $background:#94999c) {
  &::-webkit-scrollbar {
    height: $height;
  }
  &::-webkit-scrollbar-thumb { /*滚动条里面小方块*/
    border-radius: 6px;
    background: $background;
  }
  &::-webkit-scrollbar-track { /*滚动条里面轨道*/
    background: transparent;
  }
}

// 鼠标hover显示蒙层效果
// 添加该效果的目标元素需指定position属性
@mixin hover_cover_effect($background:rgba(255,255,255,.15)) {
  &:after {
    content: '';
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: $background;
  }
}

// 鼠标hover显示box-shadow
@mixin hover_shadow_effect($background:rgba(0,0,0,.1)) {
  box-shadow: 0 2px 7px 0 $background;
}

// 多行省略号
@mixin multi_lines_ellipsis($lines) {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $lines;
  overflow: hidden;
}

/**
  @ description: 适配最大宽度
  @ param $properties: 属性列表
  @ param $min-width: 最小宽度 默认800
  @ param $scale: 缩放比例 默认1.5
**/
@mixin adaptive-max($properties, $min-width: 700px, $scale: 1.87) {
  // 基础样式 (自动写入)
  @each $prop, $value in $properties {
    #{$prop}: $value;
  }

  // 缩放样式（min-width 下）(生成媒体查询)
  @media (min-width: #{$min-width}) {
    @each $prop, $value in $properties {
      @if type-of($value) == list {
        $multiplied-values: ();
        @each $val in $value {
          @if type-of($val) == number and unitless($val) == false {
            $multiplied-values: append($multiplied-values, $val * $scale, space);
          } @else {
            $multiplied-values: append($multiplied-values, $val, space);
          }
        }
        #{$prop}: $multiplied-values;
      } @else if type-of($value) == string and str-index($value, 'calc(') {
        // 注意：这里假设 calc() 字符串不做单位检查
        #{$prop}: calc(#{$value} * #{$scale});
      } @else if type-of($value) == number and unitless($value) == false {
        #{$prop}: $value * $scale;
      } @else {
        #{$prop}: $value;
      }
    }
  }
}
