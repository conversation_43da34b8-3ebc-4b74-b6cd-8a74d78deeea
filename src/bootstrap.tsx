import "abortcontroller-polyfill";
import "web-animations-js";
import React from "react";
import { createRoot } from "react-dom/client";
import { RouterProvider } from "react-router-dom";
import routes from "@/routes";
import MstAnalytics from "mst-analytics";
import initArms from "./utils/arms";
import initRum from "./utils/rum";

import "@/styles/reset.scss";
import "@/assets/font/common.css";
import "@/assets/font";

const MOUNT_NODE = document.getElementById("root");
const root = createRoot(MOUNT_NODE);
const render = () => {
  root.render(<RouterProvider router={routes} />);
};

// 初始化埋点
try {
  MstAnalytics.init({
    appKey: "wg22vlfgaqtfqywspolipkqt",
    // dev: true, // 调试模式开关
    isHistoryMode: true, // history模式开关
    pageConfig: {
      // 路由路径  pageCode 为必传参数  其他课选参数可以放后面
      // hash  模式为 #后面的  /xxx/xx
      // history 模式也是 需要上报的页面作为 key 值
      // 生涯大事件排序
      "/high-school-journey": {
        pageCode: "ewt_h5_base_operation_career_sort_view",
      },
      // 生涯性格测试
      "/mbti": {
        pageCode: "ewt_h5_base_operation_career_personality_view",
      },
      "/self-learning/completed-course": {
        pageCode:
          "ewt_h5_study_course_self_learning_completed_course_list_view",
      },
      "/self-learning/home": {
        pageCode: "ewt_h5_study_course_self_learning_home_view",
      },
      "/self-learning/recommend": {
        pageCode: "ewt_h5_study_course_self_learning_recommend_page_view",
      },
      "/self-learning/completed-list": {
        pageCode: "ewt_h5_study_course_self_learning_completed_list_view",
      },
      "/subject-channel/textbook": {
        pageCode: "ewt_h5_study_channel_textbook_view",
      },
      "/subject-channel/key-points": {
        pageCode: "ewt_h5_study_channel_key_points_view",
      },
      "/subject-channel/college-exam": {
        pageCode: "ewt_h5_study_channel_college_exam_view",
      },
      "/subject-channel/study-exam": {
        pageCode: "ewt_h5_study_channel_study_exam_view",
      },
      "/subject-channel/literacy": {
        pageCode: "ewt_h5_study_channel_literacy_view",
      },
      // 书桌-计划广场
      "/desk/plan-square": {
        pageCode: "ewt_h5_base_plan_desk_plan_square_view",
      },
      // 书桌-全部计划
      "/desk/plan-list": {
        pageCode: "ewt_h5_base_plan_desk_plan_list_view",
      },
      // 书桌-创建计划
      "/desk/create-plan": {
        pageCode: "ewt_h5_base_plan_desk_create_plan_view",
      },
      // 书桌-创建目标
      "/desk/create-target": {
        pageCode: "ewt_h5_base_plan_desk_create_target_view",
      },
      // 书桌-目标
      "/desk/target-info": {
        pageCode: "ewt_h5_base_plan_desk_target_info_view",
      },
      // 书桌-打卡
      "/desk/check-in": {
        pageCode: "ewt_h5_base_plan_desk_check_in_page_view",
      },
      // 书桌-打卡
      "/desk/unfinished-course": {
        pageCode: "ewt_h5_base_plan_desk_unfinished_course_page_view",
      },
      // 营地-排行榜
      "/camp/rank": {
        pageCode: "ewt_h5_base_operation_camp_rank_view",
      },
    },
  });
} catch (e) {
  console.log("埋点初始化失败", e);
}

initArms();
initRum();
render();
