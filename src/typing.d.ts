/** 推荐计划内容的来源 */
export enum ERecommendContentSourceFrom {
  /** 推荐计划 */
  recommend = "推荐计划页",
  guid = "新手引导",
  week = "本周计划",
  allPlan = "全部计划",
}

/** 勋章信息，这个是通用的 */
export interface IMedalInfo {
  /** 勋章编号 */
  code: string;
  /** 勋章名称 */
  name: string;
  /** 勋章图 */
  mainImgUrl: string;
  /** 勋章头像图 */
  avatarImgUrl: string;
  /** 勋章头像边框 */
  frameImgUrl: string;
}

/** 福袋的奖励，这个是通用的 */
export interface IPackagePrize {
  /** 福袋名称 */
  packageName: string;
  /** id */
  packageId: number;
  /** 福袋图片 */
  packageImg: string;
  /** '开奖类型：0：立即开奖1：定时开奖' */
  openType: number;
  /** 福袋领取状态，1：已获得，2：已错失，3：未获得,已获得：步数达到要求同时已发放福袋；,已错失：步数达到要求但该福袋为定时福袋，已错过开奖时间，无法获得福袋；,未获得：步数未达到要求，未得到对应福袋； */
  status: number;
  /** 开奖时间 */
  openDate: string;
  /** 福袋过期时间（只针对立即开奖的福袋而言） */
  expirationTime: string;
}

/** 来源的页面名称 */
export enum ESourceFromForText {
  home = "自习计划首页",
  planDetail = "计划详情页",
  recommend = "推荐计划页",
  currentWeekComplete = "本周已完成页",
  NewComerGuidance = "新手引导页",
}

/**
 * 批量请求的返回状态
 */
export enum AllSettledEnum {
  /** 成功 fulfilled */
  success = "fulfilled",
  /** 失败 rejected */
  error = "rejected",
}

declare module '*.scss';