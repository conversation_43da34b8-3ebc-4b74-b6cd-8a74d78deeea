import React, { memo, useEffect, useRef, useState } from "react";
import styles from "./index.module.scss";
import LoadingBlock from "../loading-block";
import { cls } from "@/utils/tool";

enum LoadingStatusEnum {
  LOADING = 0,
  READY = 1,
  ERROR = 2,
}

const statusClass = {
  [LoadingStatusEnum.LOADING]: "",
  [LoadingStatusEnum.ERROR]: styles.error,
  [LoadingStatusEnum.READY]: styles.ready,
};

// 包含加载与失败状态的 图片渲染组件
const LoadingImage = memo(function LoadingImage({
  src,
  fallback,
  className = "",
}: {
  src: string;
  fallback: string;
  className?: string;
}) {
  const [realSrc, setRealSrc] = useState("");
  const [loadingState, setLoadingState] = useState(LoadingStatusEnum.READY);
  const loadingTimerRef = useRef<NodeJS.Timeout | number>(0);
  const loadedRef = useRef<Record<string, boolean>>({});

  const stopLoadingTimer = () => {
    if (loadingTimerRef.current) {
      clearTimeout(loadingTimerRef.current);
      loadingTimerRef.current = 0;
    }
  };

  const onLoad = (loadedSrc: string) => {
    stopLoadingTimer();
    setLoadingState(LoadingStatusEnum.READY);
    loadedRef.current[loadedSrc] = true;
  };

  const onError = () => {
    stopLoadingTimer();
    setLoadingState(LoadingStatusEnum.ERROR);
  };

  useEffect(() => {
    // 先 ready 状态，再 loading 状态，避免闪屏
    setLoadingState(LoadingStatusEnum.READY);
    const needSrc = src || fallback;
    if (!loadedRef.current[needSrc]) {
      loadingTimerRef.current = setTimeout(() => {
        setLoadingState(LoadingStatusEnum.LOADING);
      }, 200);
    }
    setRealSrc(needSrc);
    return () => {
      stopLoadingTimer();
    };
  }, [src]);

  return (
    <div className={cls([styles.base, statusClass[loadingState], className])}>
      {loadingState === LoadingStatusEnum.LOADING && (
        <LoadingBlock className={styles.loading_block} />
      )}
      {!!realSrc &&
        (loadingState !== LoadingStatusEnum.ERROR ? (
          <img src={realSrc} onLoad={() => onLoad(realSrc)} onError={onError} />
        ) : (
          <img src={fallback} />
        ))}
    </div>
  );
});

export default LoadingImage;
