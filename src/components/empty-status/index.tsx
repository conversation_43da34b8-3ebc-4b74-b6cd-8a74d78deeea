import React, { ReactNode } from "react";
import { ErrorBlock } from "antd-mobile";
import DefaultEmptyImg from "@/assets/common/no-content.png";
import NetworkErrorImg from "@/assets/common/error.png";
import styles from "./style.module.scss";
import { cls } from "@/utils/tool";
import { Button, IButton } from "../button";

/**
 * 按钮的配置选项
 */
interface IButtonOption extends Partial<IButton> {
  icon?: string;
  /** 是否展示按钮 */
  showIcon?: boolean;
  /** 按钮点击回调 */
  handleClick?: () => void;
  /** 图标的样式类 */
  iconClassName?: string;
}

interface IEmptyProps {
  image?: React.ReactElement | string;
  text?: string | ReactNode;
  className?: string;
  showButton?: boolean;
  buttonOption?: IButtonOption;
  children?: React.ReactNode;
}

/**
 * 通用空态
 */
const EmptyStatus: React.FC<IEmptyProps> = (props) => {
  const {
    image = DefaultEmptyImg,
    text = "暂无数据",
    className = "",
    showButton = false,
    buttonOption,
    children,
    ...rest
  } = props;

  const {
    showIcon,
    icon: buttonIcon,
    text: buttonText,
    className: buttonClassName,
    iconClassName,
    handleClick,
    ...buttonRest
  } = buttonOption || {
    showIcon: false,
    icon: "",
    text: "",
    className: "",
    iconClassName: "",
    handleClick: () => {},
  };

  const title = (
    <div className={styles["multiple-light"]}>
      {Array.isArray(text)
        ? text.map((item: string) => <p key={item}>{item}</p>)
        : text}
    </div>
  );

  return (
    <div className={styles["empty-container"]}>
      <ErrorBlock
        image={image}
        title={title}
        description={""}
        className={cls([styles["empty-medal-img"], className])}
        {...rest}
      />
      {buttonText && (
        <Button
          text={buttonText}
          className={buttonClassName}
          icon={
            showIcon && (
              <img
                src={buttonIcon}
                alt="icon"
                className={cls([styles["button-icon"], iconClassName])}
              />
            )
          }
          onClick={handleClick}
          {...buttonRest}
        />
      )}
      {props.children}
    </div>
  );
};

export default React.memo(EmptyStatus);

export const NetworkErrorStatus: React.FC<IEmptyProps> = (props) => {
  const { className = "", buttonOption, text } = props;

  return (
    <EmptyStatus
      className={cls([styles["empty-error-img"], className])}
      image={NetworkErrorImg}
      text={text || "糟糕，遇到了点问题，点击“重新加载”试试"}
      buttonOption={{
        text: "重新加载",
        handleClick: () => buttonOption?.handleClick?.(),
      }}
    />
  );
};
