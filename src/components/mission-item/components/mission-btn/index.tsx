import React from "react";
import classNames from "classnames";

import styles from "./index.module.scss";

const MissionBtn = (props: {
  isDone: boolean;
  text: string;
  desc?: string;
  showDoneIcon?: boolean;
  percent?: number;
  className?: string;
  textClassName?: string;
  hiddenRightIcon?: boolean;
  onClick?: () => void;
}) => {
  const {
    text,
    desc,
    showDoneIcon,
    isDone,
    percent,
    className,
    textClassName,
    hiddenRightIcon,
    onClick,
  } = props;
  return (
    <div
      className={classNames(
        styles.container,
        {
          [styles.done]: isDone,
        },
        className,
      )}
      onClick={onClick}
    >
      <div className={classNames(styles.word, textClassName)}>
        {text}
        {!!desc && <span>({desc})</span>}
      </div>
      {!isDone && !!percent && <div className={styles.percent}>{percent}%</div>}
      {isDone && showDoneIcon && (
        <div className={styles.done_icon}>
          <i className="iconfont iconyijiajihua"></i>
        </div>
      )}
      {!hiddenRightIcon && <i className="iconfont iconjinru"></i>}
    </div>
  );
};

export default MissionBtn;
