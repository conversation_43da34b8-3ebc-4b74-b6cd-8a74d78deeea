/**
 * 没有做课后习题的提示弹窗
 */

import React from "react";

import Popup, { IPopup } from "@/components/popup";
import Button, { EType } from "@/components/aButton";

import styles from "./style.module.scss";

interface IAITipModal extends IPopup {
  onClose: () => void;
}

const AITipModal: React.FC<IAITipModal> = (props) => {
  const { onClose } = props;
  return (
    <Popup onClose={onClose} {...props}>
      <div className={styles["content"]}>
        当你完成课后习题，AI会自动分析错题类型，生成专属「同类训练题包」，帮你精准攻克薄弱环节！现在习题还没有完成哦，马上开始做题吧！
      </div>
      <div className={styles["footer"]}>
        <Button
          className={styles["button"]}
          onClick={onClose}
          type={EType.BLUE}
          text="我知道了"
        />
      </div>
    </Popup>
  );
};

export default AITipModal;
