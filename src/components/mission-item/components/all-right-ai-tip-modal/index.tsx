/**
 * 课后习题全对的提示弹窗
 */

import React from "react";

import Popup, { IPopup } from "@/components/popup";
import Button, { EType } from "@/components/aButton";

import styles from "./style.module.scss";

interface IAllRightAITipModal extends IPopup {
  onClose: () => void;
  toPractice: () => void;
}

const AllRightAITipModal: React.FC<IAllRightAITipModal> = (props) => {
  const { onClose, toPractice } = props;
  return (
    <Popup onClose={onClose} {...props}>
      <div className={styles["content"]}>
        <span style={{ color: "#2F86FF" }}>
          学霸稳了！AI检测到你的课后习题「全对通关」
        </span>
        ，已生成【超稳训练包】～（悄悄说：对你来说可能 so
        easy你也可以选择去学习其他内容。)
      </div>
      <div className={styles["footer"]}>
        <Button
          className={styles["button"]}
          onClick={onClose}
          type={EType.WHITE}
          text="学习其他"
        />
        <Button
          className={styles["button"]}
          onClick={toPractice}
          type={EType.BLUE}
          text="巩固加练"
        />
      </div>
    </Popup>
  );
};

export default AllRightAITipModal;
