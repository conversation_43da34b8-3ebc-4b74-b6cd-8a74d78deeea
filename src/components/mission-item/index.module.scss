.container {
  position: relative;
  margin-bottom: 12px;

  .actions {
    display: flex;
    align-items: center;
    flex-grow: 0;
    flex-shrink: 0;
    padding-right: 18px;
    height: 38px;
    line-height: 38px;

    .offline {
      font-weight: bold;
      font-size: 14px;
      color: #333333;
      margin-left: 10px;
    }

    .delete {
      font-size: 12px;
      color: #FF4D4F;
      margin-left: 10px;
      cursor: pointer;
    }
  }

  .main {
    .main_top {
      height: 40px;
      background: url(@/assets/image/self-learning-for-subject/mission-subject.png) left/contain no-repeat, #FFFFFF;
      border: 1px solid #021E66;
      border-radius: 8px 8px 0 0;
      display: flex;
      align-items: center;
      border-bottom: 0;

      .subject {
        // min-width: 38px;
        height: 38px;
        margin-right: 4px;
        flex-grow: 0;
        flex-shrink: 0;
        position: relative;

        img {
          width: 38px;
          height: 38px;
        }

        .subject_name {
          height: 22px;
          padding: 0 4px;
          background: #FFFFFF;
          border: 1px solid #021E66;
          box-shadow: 4px 4px 0 0 rgba(2, 30, 102, 0.15);
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          font-size: 12px;
          color: #021E66;
          // position: absolute;
          // top: -5px;
          // left: -4px;
          margin-top: -5px;
          margin-left: -4px;
        }
      }

      .title {
        line-height: 38px;
        font-size: 14px;
        color: #021E66;
        font-weight: bold;
        flex-grow: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .empty {
        visibility: hidden;
      }

      &.subject_1 {
        background: url(@/assets/image/subjectIcon/mission-subject-1.png) left/contain no-repeat, #FFFFFF;
      }

      &.subject_2 {
        background: url(@/assets/image/subjectIcon/mission-subject-2.png) left/contain no-repeat, #FFFFFF;
      }

      &.subject_3 {
        background: url(@/assets/image/subjectIcon/mission-subject-3.png) left/contain no-repeat, #FFFFFF;
      }

      &.subject_4 {
        background: url(@/assets/image/subjectIcon/mission-subject-4.png) left/contain no-repeat, #FFFFFF;
      }

      &.subject_5 {
        background: url(@/assets/image/subjectIcon/mission-subject-5.png) left/contain no-repeat, #FFFFFF;
      }

      &.subject_6 {
        background: url(@/assets/image/subjectIcon/mission-subject-6.png) left/contain no-repeat, #FFFFFF;
      }

      &.subject_7 {
        background: url(@/assets/image/subjectIcon/mission-subject-7.png) left/contain no-repeat, #FFFFFF;
      }

      &.subject_8 {
        background: url(@/assets/image/subjectIcon/mission-subject-8.png) left/contain no-repeat, #FFFFFF;
      }

      &.subject_9 {
        background: url(@/assets/image/subjectIcon/mission-subject-9.png) left/contain no-repeat, #FFFFFF;
      }

      &.subject_10 {
        background: url(@/assets/image/subjectIcon/mission-subject-10.png) left/contain no-repeat, #FFFFFF;
      }

      &.subject_11 {
        background: url(@/assets/image/subjectIcon/mission-subject-11.png) left/contain no-repeat, #FFFFFF;
      }

    }

    .main_bottom {
      height: 39px;
      background: #FFFFFF;
      border: 1px solid #021E66;
      border-top: 0;
      border-radius: 0 0 8px 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .info {
        display: flex;
        align-items: center;
        .info_icon {
          padding: 6px 8px;
          height: 38px;
          line-height: 1;
          display: flex;
          align-items: center;
          justify-content: center;

          img {
            width: 28px;
            height: 28px;
          }
        }

        .info_name {
          font-weight: bold;
          font-size: 12px;
          color: rgba(2, 30, 102, 0.85);
        }

        .info_desc {
          font-size: 12px;
          color: rgba(2, 30, 102, 0.85);
          margin-left: 8px;
        }
      }

      .btns {
        display: flex;
        align-items: center;
        padding-right: 10px;

        .btn_split {
          width: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          i {
            font-size: 6px;
            color: #021E66;
          }
        }
      }
    }

  }

  .top {
    position: absolute;
    top: 1px;
    right: 0;

    .done {
      position: absolute;
      width: 22px;
      height: 22px;
      top: -7px;
      right: -6px;
    }
  }
}

// AI 推题按钮
.ai-paper-btn {
  margin-left: 5px;
  padding-right: 8px !important;
  &-text {
    font-size: 12px !important;
  }
}

.ai-paper-btn-lottie {
  width: 62px;
  margin-left: 5px;
  display: flex;
}

.ai-paper-btn-disabled {
  opacity: 0.5;
}