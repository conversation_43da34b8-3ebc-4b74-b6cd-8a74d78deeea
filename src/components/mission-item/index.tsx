import React from "react";
import classNames from "classnames";
import {
  IAIRecommendPaperItem,
  ICreateRecommendPaper,
  IPlanListItem,
  IResourceItem,
  postFmComplete,
  ResourceTypeEnum,
} from "@/routes/week-study/apis";
import Lottie from "@/components/lottie-box";
import AITipModal from "./components/ai-tip-modal";
import AllRightAITipModal from "./components/all-right-ai-tip-modal";
import UnSelfEditingAiTipModal from "./components/un-self-editing-ai-tip-modal";
import MissionLessonPng from "@/assets/image/self-learning-for-subject/mission-lesson.png";
import MissionPaperPng from "@/assets/image/self-learning-for-subject/mission-paper.png";
import MissionFmPng from "@/assets/image/self-learning-for-subject/mission-fm.png";
import MissionDonePng from "@/assets/image/self-learning-for-subject/mission-done.png";
import aiPaperBtnJSON from "@/assets/json/lottie-json/ai-paper-btn.json";
import styles from "./index.module.scss";
import MissionBtn from "./components/mission-btn";
import {
  cls,
  makeAnswerReportUrl,
  makeAnswerUrl,
  openRoute,
  openWebView,
} from "@/utils/tool";
import { Dialog, Toast } from "antd-mobile";
import { AICommend, EResourceType } from "@/routes/week-study/common";

const BASE_INFOS = {
  [ResourceTypeEnum.LESSON]: {
    icon: MissionLessonPng,
    name: "视频课",
    bg: "#FFF1B8",
  },
  [ResourceTypeEnum.FM]: {
    icon: MissionFmPng,
    name: "FM",
    bg: "#FFF1B8",
  },
  [ResourceTypeEnum.PAPER]: {
    icon: MissionPaperPng,
    name: "试卷",
    bg: "#CFE8FF",
  },
};

const formatPercent = (percent: number) => {
  return Math.min(100, Math.max(0, Math.floor((percent || 0) * 100)));
};

const MissionItem = (props: {
  aiRecommendPaper?: IAIRecommendPaperItem;
  resourceItem: IResourceItem;
  wrapClassName?: string;
  disableDelete?: boolean;
  currentPlan: IPlanListItem;
  toPractice: (
    paper: IAIRecommendPaperItem,
    data: Omit<ICreateRecommendPaper, "planId">,
  ) => void;
  onDelete?: () => void;
  lessonRouteParams?: {
    planId: string | number;
    subjectId: number;
    planName: string;
    startTime?: number | null;
    endTime?: number | null;
  };
  onToAppCallback?: (id: string) => void;
}) => {
  const {
    aiRecommendPaper,
    resourceItem,
    wrapClassName,
    onDelete,
    disableDelete,
    currentPlan,
    toPractice,
    lessonRouteParams,
    onToAppCallback,
  } = props;
  const {
    resourceStatus,
    finishStatus,
    resourceType,
    lessonVO,
    fmVO,
    paperVO,
    subjectName,
    hasForbid,
    subjectIcon,
    recordId,
  } = resourceItem;
  // ai 提示弹窗 - 课后习题未做
  const [aiTipModalVis, setAITipModalVis] = React.useState(false);
  // ai 提示弹窗 - 课后习题全对
  const [allRightAITipModalVis, setAllRightAITipModalVis] =
    React.useState(false);
  // ai 提示弹窗 - 课后习题未自批
  const [unSelfEditingAiTipModalVis, setUnSelfEditingAiTipModalVis] =
    React.useState(false);
  // ai 推题去答题|看报告
  function toAIPaperAnswerOrReport() {
    toPractice(aiRecommendPaper, {
      reportId: lessonVO.reportId,
      paperId: lessonVO.paperId,
      resourceId: lessonVO.lessonId,
      resourceType: EResourceType.package,
      hasWrong: lessonVO.rightRate !== 1,
    });
  }
  const isDisable = !resourceStatus || !!hasForbid;
  const disableWord = !resourceStatus ? "已下架" : "校园版App不支持";
  const infos = BASE_INFOS[resourceType] || { name: "", icon: "", bg: "" };
  let title = "";
  let extInfo = "";
  let nodes = [];
  // 统一的第一个按钮点击处理函数
  const handleFirstBtnClick = (
    resourceType: ResourceTypeEnum,
    lessonPaper?: boolean,
  ) => {
    if (resourceType === ResourceTypeEnum.LESSON && lessonVO) {
      onToAppCallback?.(recordId);
      openRoute({
        domain: "media",
        action: "commonPlayer",
        params: {
          playVideoId: lessonVO.lessonId,
          isOffLineMode: false,
          ...(lessonRouteParams || {}),
        },
      });
    } else if (resourceType === ResourceTypeEnum.FM && fmVO) {
      onToAppCallback?.(recordId);
      if (!finishStatus) {
        postFmComplete({
          contentId: fmVO.fmId,
          contentType: 3,
        });
      }
      openRoute({
        domain: "fm",
        action: "open_detail",
        params: {
          id: `${fmVO.fmId}`,
        },
      });
    } else if (resourceType === ResourceTypeEnum.PAPER) {
      onToAppCallback?.(recordId);
      const config = {
        paperId: lessonPaper ? lessonVO.paperId : paperVO.paperId,
        reportId: lessonPaper
          ? lessonVO.reportId || undefined
          : paperVO.reportId || undefined,
        bizCode: lessonPaper ? 102 : paperVO.bizCode,
        platform: lessonPaper ? 4 : paperVO.platform,
        ...(lessonPaper
          ? {}
          : {
              homeworkId: currentPlan.planId,
            }),
        isRepeat: 1,
      };
      if (lessonPaper) {
        openWebView(
          lessonVO.hasFinish && lessonVO.reportId
            ? makeAnswerReportUrl(config)
            : makeAnswerUrl(config),
        );
      } else {
        openWebView(
          finishStatus && paperVO.reportId
            ? makeAnswerReportUrl(config)
            : makeAnswerUrl(config),
        );
      }
    }
  };
  // 视频课
  if (resourceType === ResourceTypeEnum.LESSON && lessonVO) {
    title = lessonVO.lessonName || "";
    // 仅处理成了分钟
    extInfo = lessonVO.duration
      ? `${Math.max(1, Math.round(lessonVO.duration / 60))}分钟`
      : "";
    if (lessonVO.paperId) {
      // 既有课程讲，又有习题
      nodes.push(
        <MissionBtn
          key="lesson"
          text="学"
          isDone={finishStatus}
          percent={formatPercent(lessonVO.progressTime)}
          showDoneIcon
          onClick={() => {
            handleFirstBtnClick(ResourceTypeEnum.LESSON);
          }}
        />,
      );

      // 分割
      nodes.push(
        <div key="split" className={styles.btn_split}>
          <i className="iconfont iconsanjiaoxing" />
        </div>,
      );

      // 习题。已交卷，未自批时，不显示打勾
      nodes.push(
        <MissionBtn
          key="lesson_paper"
          text="练"
          isDone={lessonVO.hasFinish}
          showDoneIcon={lessonVO.correctResult}
          onClick={() => {
            handleFirstBtnClick(ResourceTypeEnum.PAPER, true);
          }}
        />,
      );
      // AI 推题
      if (aiRecommendPaper?.hasRecommend) {
        // 课后习题批改完成 & AI推题未完成时，展示 lottie 按钮
        if (lessonVO.hasFinish && !aiRecommendPaper.hasFinish) {
          nodes.push(
            <Lottie
              loop
              onClick={() => {
                // 已经组过试卷了就直接跳转到试卷或答题页面
                if (aiRecommendPaper.paperId) {
                  toAIPaperAnswerOrReport();
                } else if (!lessonVO.correctResult) {
                  // 课后习题未自批，就弹窗提示去自批
                  setUnSelfEditingAiTipModalVis(true);
                } else if (lessonVO.rightRate === 1) {
                  // 课后习题已经自批过且全部答对，就弹窗让用户选择是否继续作答
                  setAllRightAITipModalVis(true);
                } else {
                  // 课后习题已经自批过且有错
                  toAIPaperAnswerOrReport();
                }
              }}
              // lottie 里图片地址前缀
              assetsPath={`${process.env.PUBLIC_PATH}ai-btn-lottie/images/`}
              dataJson={aiPaperBtnJSON}
              className={styles["ai-paper-btn-lottie"]}
            />,
          );
        } else {
          // 课后习题未完成或者AI推题已完成
          nodes.push(
            <MissionBtn
              key="AI_paper"
              text="AI推题"
              hiddenRightIcon
              className={classNames(
                styles["ai-paper-btn"],
                // 练习未完成不可点
                !lessonVO.hasFinish && styles["ai-paper-btn-disabled"],
              )}
              textClassName={styles["ai-paper-btn-text"]}
              isDone={aiRecommendPaper.hasFinish}
              // 批改完成之后展示完成图标
              // showDoneIcon={aiRecommendPaper.correctResult}
              onClick={() => {
                // 课后练习还没做，点击AI推题，提示去做课后练习
                if (!lessonVO.hasFinish) {
                  setAITipModalVis(true);
                  // AI 推题已经完成, 直接跳转去报告
                } else {
                  toAIPaperAnswerOrReport();
                }
              }}
            />,
          );
        }
      }
    } else {
      // 只有课程讲的情况
      nodes.push(
        <MissionBtn
          key="lesson"
          text={finishStatus ? "已完成" : "去学习"}
          isDone={finishStatus}
          percent={formatPercent(lessonVO.progressTime)}
          showDoneIcon
          onClick={() => {
            handleFirstBtnClick(ResourceTypeEnum.LESSON);
          }}
        />,
      );
    }
  } else if (resourceType === ResourceTypeEnum.FM && fmVO) {
    title = fmVO.fmName || "";
    // 仅处理成了分钟
    extInfo = fmVO.duration
      ? `${Math.max(1, Math.round(fmVO.duration / 60))}分钟`
      : "";
    nodes.push(
      <MissionBtn
        key="fm"
        text={finishStatus ? "已完成" : "去收听"}
        isDone={finishStatus}
        onClick={() => {
          handleFirstBtnClick(ResourceTypeEnum.FM);
        }}
      />,
    );
  } else if (resourceType === ResourceTypeEnum.PAPER && paperVO) {
    title = paperVO.paperName || "";
    extInfo = [
      paperVO.duration ? `${paperVO.duration}分钟` : "",
      paperVO.questionNum ? `${paperVO.questionNum}题` : "",
    ]
      .filter(Boolean)
      .join("/");
    nodes.push(
      <MissionBtn
        key="paper"
        text={
          finishStatus
            ? paperVO.correctResult
              ? "已完成"
              : "已交卷"
            : "测一测"
        }
        desc={
          finishStatus
            ? paperVO.correctResult
              ? `正确率${formatPercent(paperVO.rightRate)}%`
              : "去自批"
            : ""
        }
        isDone={finishStatus}
        onClick={() => {
          handleFirstBtnClick(ResourceTypeEnum.PAPER);
        }}
      />,
    );
  }

  return (
    <>
      <div
        className={classNames(styles.container, wrapClassName)}
        onClick={() => {
          if (resourceStatus && hasForbid) {
            Toast.show({
              content: "该资源不支持校园版App播放，请于学生版App查看",
            });
          }
        }}
      >
        <div
          className={styles.main}
          style={{
            opacity: isDisable ? 0.3 : 1,
            pointerEvents: isDisable ? "none" : "unset",
          }}
        >
          <div
            className={cls([
              styles.main_top,
              subjectIcon && styles[`subject_${subjectIcon}`],
            ])}
          >
            <div className={styles.subject}>
              <div className={styles.subject_name}>{subjectName}</div>
            </div>
            <div
              className={styles.title}
              onClick={() => {
                handleFirstBtnClick(resourceType);
              }}
            >
              {title}
            </div>
            <div className={classNames(styles.actions, styles.empty)}>
              {isDisable && <div className={styles.offline}>{disableWord}</div>}
              {!disableDelete && <div className={styles.delete}>移出</div>}
            </div>
          </div>
          <div
            className={styles.main_bottom}
            style={{ backgroundColor: finishStatus ? "#E5F5DD" : infos.bg }}
          >
            <div className={styles.info}>
              <div className={styles.info_icon}>
                <img src={infos.icon} />
              </div>
              <div className={styles.info_name}>{infos.name}</div>
              <div className={styles.info_desc}>{extInfo}</div>
            </div>
            <div className={styles.btns}>{nodes}</div>
          </div>
        </div>
        <div className={classNames(styles.actions, styles.top)}>
          {isDisable && <div className={styles.offline}>{disableWord}</div>}
          {!disableDelete && (
            <div
              className={styles.delete}
              onClick={(e) => {
                Dialog.confirm({
                  content: "确定要从计划中移出该内容吗？",
                  onConfirm: onDelete,
                });
                e.preventDefault();
                e.stopPropagation();
              }}
            >
              移出
            </div>
          )}
          {finishStatus && <img src={MissionDonePng} className={styles.done} />}
        </div>
      </div>
      {/* 只有视频课资源才有AI推题 */}
      {!!lessonVO && (
        <>
          {/* 课后习题还没做 */}
          <AITipModal
            title={AICommend}
            onClose={() => setAITipModalVis(false)}
            visible={aiTipModalVis}
          />
          {/* 课后习题全对且还未进行AI组卷 */}
          <AllRightAITipModal
            title={AICommend}
            onClose={() => setAllRightAITipModalVis(false)}
            toPractice={() => {
              setAllRightAITipModalVis(false);
              toAIPaperAnswerOrReport();
            }}
            visible={allRightAITipModalVis}
          />
          {/* 课后习题未自批 */}
          <UnSelfEditingAiTipModal
            visible={unSelfEditingAiTipModalVis}
            title={AICommend}
            onClose={() => {
              setUnSelfEditingAiTipModalVis(false);
            }}
            // 去自批
            toSelfEdit={() => {
              if (!lessonVO) {
                return;
              }
              // 去课后习题自批
              const config = {
                paperId: lessonVO.paperId,
                reportId: lessonVO.reportId || undefined,
                bizCode: 102,
                platform: 4,
                isRepeat: 1,
              };
              openWebView(
                lessonVO.hasFinish && lessonVO.reportId
                  ? makeAnswerReportUrl(config)
                  : makeAnswerUrl(config),
              );
              setUnSelfEditingAiTipModalVis(false);
            }}
          />
        </>
      )}
    </>
  );
};

export default MissionItem;
