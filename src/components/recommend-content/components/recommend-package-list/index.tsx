import * as React from "react";
import { Ellipsis } from "antd-mobile";
import { IRecommendPackageInfo } from "@/service/self-learning/drawer";
import {
  clickPv,
  createURLByType,
  EJumpType,
  handleNumberToTenThousand,
  openUrlInWebView,
} from "@/utils/tool";

import Style from "./style.module.scss";
import { ERecommendContentSourceFrom } from "@/typing";
import { PackageOperateButton } from "./components/package-operate-button";
import { useLocation } from "react-router-dom";

/**
 * 课包列表
 * @param data 课包列表
 */
export interface IRecommendPackageList {
  sourceFrom: ERecommendContentSourceFrom;
  data: IRecommendPackageInfo[];
  handleOperateClick?: (item: IRecommendPackageInfo) => void;
  selectedSubjectName: string;
  globalCourseRef: any;
}

export const RecommendPackageList: React.FC<IRecommendPackageList> = (
  props,
) => {
  const {
    data,
    handleOperateClick,
    sourceFrom,
    selectedSubjectName,
    globalCourseRef,
  } = props;
  const location = useLocation();

  return (
    <React.Fragment>
      {data.map((item: IRecommendPackageInfo, index: number) => (
        <div
          className={Style["course-package-item"]}
          key={`${item.courseId}_${index}`}
        >
          {/* 课包的背景大图，如果没有的话就显示默认的渐变背景色 */}
          {item.uploadImgUrl ? (
            <img src={item.uploadImgUrl} alt={item.title} />
          ) : (
            <div className={Style["default-bg-box"]} />
          )}
          {/* 课包右上角的操作按钮 */}
          <PackageOperateButton
            item={item}
            sourceFrom={sourceFrom}
            selectedSubjectName={selectedSubjectName}
            handleOperateClick={handleOperateClick}
          />

          <div
            className={Style["course-package-info-box"]}
            onClick={() => {
              clickPv(
                "ewt_h5_study_course_self_learning_recommend_plan_component_resource_detail_button_click",
                {
                  sourceFrom,
                  subject: selectedSubjectName,
                  courseId: item.courseId,
                  courseName: item.title,
                },
              );
              globalCourseRef.current.courseId = item.courseId;
              // 新开webview去查看详情
              openUrlInWebView(
                createURLByType({
                  path: "/self-learning/plan-detail",
                  type: EJumpType.outside,
                  originSearch: location.search,
                  addQueryObject: {
                    courseId: item.courseId,
                  },
                }),
              );
            }}
          >
            <div className={Style["base-info-container"]}>
              <div className={Style["base-info-box"]}>
                <div className={Style["small-image-box"]}>
                  <img
                    src={item.coverImgUrl}
                    alt=""
                    className={Style["covert-image"]}
                  />
                  <span>共{item.lessonCount}讲</span>
                  <span className={Style["teacher-name"]}>
                    {item.authorList?.join(",")}
                  </span>
                </div>
                <span>{handleNumberToTenThousand(item.chooseCount)}人已选</span>
              </div>
              <p className={Style["course-page-title"]}>{item.title}</p>
              {item.description && (
                <Ellipsis
                  content={`推荐理由：${item.description}`}
                  rows={1}
                  direction="end"
                />
              )}
            </div>
          </div>
        </div>
      ))}
    </React.Fragment>
  );
};
