.subject-change-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.course-package-item {
  position: relative;
  z-index: 1;
  margin-bottom: 10px;

  & > img {
    width: 100%;
    height: 175px;
    border-radius: 8px;
    display: block;
  }

  .default-bg-box {
    width: 100%;
    height: 175px;
    background: linear-gradient(90deg, #7EB5FF, #FFDAB7);
    border-radius: 8px;
  }

  .add-to-plan-button {
    position: absolute;
    right: 8px;
    top: 8px;
    padding: 8px 12px;
    border: 1px solid #2D86FE;

    & > div {
      color: #2D86FE;
      font-size: 12px;
      line-height: 12px;

      i {
        font-size: 12px;
        margin-right: 4px;
      }
    }

    &.added {
      background-color: #FAAD14;
      border: 1px solid #fff;

      :global {
        .svg-icon {
          margin-right: 3px;
        }
      }

      & > div {
        color: #fff;
      }
    }

    &.package-finished-button {
      background-color: rgba(0, 0, 0, .25);
      border: none;

      & > div {
        color: #fff;
      }
    }
  }
}

.course-package-info-box {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, .6);
  border-radius: 0 0 8px 8px;
  color: rgba(255, 255, 255, 0.85);
  min-height: 80px;

  .base-info-container{
    padding: 0 10px 10px;
    margin-top: -22px;

    .base-info-box {
      display: flex;
      align-items: baseline;
      justify-content: space-between;
      margin-bottom: 2px;

      .small-image-box {
        display: flex;
        align-items: baseline;

        span {
          margin-left: 16px;
        }

        .teacher-name {
          max-width: 108px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }

      .covert-image {
        width: 80px;
        height: 45px;
        border-radius: 4px;
        background: linear-gradient(90deg, #7EB5FF, #FFDAB7);
      }
    }

    .course-page-title {
      font-size: 15px;
      font-weight: bold;
      color: #fff;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
