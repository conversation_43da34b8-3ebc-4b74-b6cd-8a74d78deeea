import * as React from "react";
import { clickPv, cls } from "@/utils/tool";
import { IRecommendPackageInfo } from "@/service/self-learning/drawer";

import Style from "../../style.module.scss";
import { Button, EButtonType, IconSvg } from "@/components";
import { Toast } from "antd-mobile";
import { ERecommendContentSourceFrom } from "@/typing";

/**
 * 课包右上角的操作按钮
 * @param item 当前课包
 * @param sourceFrom 页面来源
 * @param selectedSubjectName 选择的学科名称
 * @param handleOperateClick 操作按钮点击事件
 */
export interface IPackageOperateButton {
  item: IRecommendPackageInfo;
  sourceFrom: ERecommendContentSourceFrom;
  selectedSubjectName: string;
  handleOperateClick: (item: IRecommendPackageInfo) => void;
}

export const PackageOperateButton: React.FC<IPackageOperateButton> = (
  props,
) => {
  const { item, sourceFrom, selectedSubjectName, handleOperateClick } = props;

  return (
    <Button
      type={EButtonType.grey}
      text={
        item?.coursePackageHasFinished
          ? "已完成"
          : item.added2Plan
            ? "移出计划"
            : "加入计划"
      }
      className={cls([
        Style["add-to-plan-button"],
        item.added2Plan && Style["added"],
        item?.coursePackageHasFinished && Style["package-finished-button"],
      ])}
      icon={
        <IconSvg
          name={
            item?.coursePackageHasFinished
              ? "icon-yijiajihua"
              : item.added2Plan
                ? "icon-jianjihua"
                : "icon-jiajihua"
          }
        />
      }
      onClick={(event: any) => {
        event?.stopPropagation();
        if (item?.coursePackageHasFinished) {
          Toast.show("你已完成该计划");
          return;
        }
        // 按钮点击key的基础字符串
        const clickPvBasicStr =
          "ewt_h5_study_course_self_learning_recommend_plan_component_resource_";
        // 按钮类型根据包状态给不同的值
        const clickPvKey = `${clickPvBasicStr}${item?.added2Plan ? "remove_button_click" : "add_button_click"}`;
        clickPv(clickPvKey, {
          sourceFrom,
          subject: selectedSubjectName,
          courseId: item.courseId,
          courseName: item.title,
        });
        // 最后调用业务的回调
        handleOperateClick(item);
      }}
    />
  );
};
