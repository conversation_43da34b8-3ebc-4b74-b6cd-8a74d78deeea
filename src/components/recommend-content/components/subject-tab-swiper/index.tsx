import * as React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { cls } from "@/utils/tool";
import { IRecommendContentSubjectList } from "@/service/self-learning/drawer";

import Style from "./style.module.scss";

/**
 * 学科列表swiper
 * @param onSwiper swiper初始化完毕回调函数，会返回swiper对象
 * @param data 学科列表
 * @param active 需要激活的tab
 */
export interface ISubjectTabSwiper {
  onSwiper: (swiper: any) => void;
  data: IRecommendContentSubjectList[];
  active?: number;
  handleClick?: (item: IRecommendContentSubjectList, index: number) => void;
}

export const SubjectTabSwiper: React.FC<ISubjectTabSwiper> = (props) => {
  const { onSwiper, data, active, handleClick } = props;

  const items = () =>
    data?.map((item: IRecommendContentSubjectList, index: number) => (
      <SwiperSlide key={`${item.categoryId}_${index + 1}`}>
        <div
          className={cls([
            Style["subject-name-box"],
            item.categoryId === active && Style["active"],
            index === 0 && Style["first-child"],
          ])}
          onClick={() => handleClick?.(item, index)}
        >
          {item.subjectName}
        </div>
      </SwiperSlide>
    ));

  return (
    <Swiper
      slidesPerView={"auto"}
      centeredSlides={false}
      pagination={false}
      onSwiper={onSwiper}
      className={Style["recommend-content-swiper"]}
    >
      {items()}
    </Swiper>
  );
};
