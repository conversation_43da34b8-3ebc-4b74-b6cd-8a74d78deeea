import React, { useEffect, useRef, useState } from "react";
import { <PERSON>Loading, InfiniteScroll, Toast } from "antd-mobile";
import { Spin } from "../spin";
import EmptyStatus, { NetworkErrorStatus } from "../empty-status";
import { StudyRecommendPopup } from "@/components";
import CustomCenterPopup from "../custom-center-popup";
import { InfiniteScrollBottom } from "../infinites-scroll-bottom";

import Logger from "@/utils/safe-logger";
import {
  clickPv,
  expPv,
  throttle,
  debounce,
  isIgnoreError,
} from "@/utils/tool";
import type {
  IRecommendContentSubjectList,
  IRecommendPackageInfo,
} from "@/service/self-learning/drawer";
import {
  getAddedPackageInfo,
  getNoAddPackageInfo,
  getPackageIsAddedPlan,
  getRecommendPackageList,
  getRecommendSubjectList,
  postAddPackageToPlan,
  postRemovePackageByPlan,
} from "@/service/self-learning/drawer";
import { useVisibilitychange } from "@/hooks";

import { ERecommendContentSourceFrom } from "@/typing.d";

import "swiper/css";
import styles from "./style.module.scss";
import { TipPopup } from "../tip-popup";
import { SubjectTabSwiper } from "./components/subject-tab-swiper";
import { RecommendPackageList } from "./components/recommend-package-list";
import { ErrorCodeEnum } from "@/utils/constant";

const logger = new Logger("self-learning-recommend");

/** 推荐方案的内容组件参数 */
interface IRecommendContentProps {
  /** 来源，上报时区分场景作用 */
  sourceFrom: ERecommendContentSourceFrom;
  emptyHeight?: string;
  networkErrorHeight?: string;
  subjectListLoaded?: (subjectList: IRecommendContentSubjectList[]) => void;
}

/** 推荐方案的内容组件 */
const RecommendContent: React.FC<IRecommendContentProps> = (props) => {
  const { sourceFrom, emptyHeight, networkErrorHeight, subjectListLoaded } =
    props;
  /** 是否加载中。第一次进入时使用 */
  const [loading, setLoading] = useState(true);
  const [operateLoading, setOperateLoading] = useState(false);
  // 移出计划二次确认框
  const [removeTipVisible, setRemoveTipVisible] = useState(false);
  const [isChangeSubject, setIsChangeSubject] = useState(false);
  const [packageLoading, setPackageLoading] = useState(false);
  /** 无推荐内容时推荐e讲堂的信息展示 */
  const [studyVisible, setStudyVisible] = useState(false);
  /** 选中的学科id*/
  const [selectedSubject, setSelectedSubject] = useState(-1);
  /** 选中的学科名称，上报QT用 */
  const [selectedSubjectName, setSelectedSubjectName] = useState("");
  /** 学科列表 */
  const [subjectList, setSubjectList] = useState<
    IRecommendContentSubjectList[]
  >([]);
  /** 课包的信息 */
  const [operatePackage, setOperatePackage] = useState<IRecommendPackageInfo>();
  /** 学科下对应的课包列表 */
  const [packageList, setPackageList] = useState<IRecommendPackageInfo[]>([]);
  /** 包列表分页信息，默认在第一页且有下一页数据，后续根据接口来覆盖 */
  const packagePageInfo = useRef({
    haveNextPage: true,
    pageIndex: 1,
    pageSize: 10,
  });
  const [planTooMuchInfo, setPlanTooMuchInfo] = useState({
    visible: false,
    content: "",
  });
  // 存储点击的包id信息，用于更新单包时
  const globalCourseInfo = useRef({
    packageList: [],
    courseId: "",
  });
  /** 学科列表的 swiper 对象 */
  const swiperRef = useRef<any>(null);
  // 学科列表网络错误标识
  const [subjectNetWorkError, setSubjectNetWorkError] = useState(false);
  // 包列表的网络错误标识
  const [packageListNetWork, setPackageListNetWork] = useState(false);
  // 请求时序控制，防止展示数据和筛选条件不符合
  const requestFlag = React.useRef<number>(0);
  const [removePackageVisible, setRemovePackageVisible] = useState(false);

  /** 根据学科的 categoryId 获取绑定的包列表 */
  /** */
  /**
   * 根据学科的 categoryId 获取绑定的包列表
   * @param categoryId 学科/目录id
   * @param isChangeSubject 来源是否为切换学科
   */
  const queryPackageListByCategoryId = async (
    categoryId?: number,
    isChangeSubject?: boolean,
  ) => {
    requestFlag.current++;
    const nowReqFlag = requestFlag.current;
    setPackageLoading(true);
    // 第一次查询时需要指定id，只是页数不同时取选中的学科即可
    const reqCategoryId = categoryId || selectedSubject;

    // 设置网络错误的提示和上报
    const setNetError = (params: any) => {
      packagePageInfo.current.haveNextPage = false;
      setPackageListNetWork(true);
      setIsChangeSubject(false);
      logger.warn("self-learning-recommend-package-list-error", {
        reason: "自主学习计划-推荐列表-课程包列表出错",
        ...params,
      });
    };

    try {
      const { data } = await getRecommendPackageList({
        ...packagePageInfo.current,
        categoryId: reqCategoryId,
        ignoreError: true,
      });

      if (nowReqFlag !== requestFlag.current) {
        return;
      }

      // 判断数据是否符合预期，包含分页数据才能继续
      if (data?.data) {
        // 如果是自动加载的下一页，就拼接数组显示
        // 如果是切换学科就只显示当前数据
        const newList = categoryId
          ? [...data.data]
          : [...packageList, ...data.data];
        packagePageInfo.current.haveNextPage = !!data?.haveNextPage;
        globalCourseInfo.current.packageList = newList;
        setPackageList(newList);
        setPackageListNetWork(false);
        setIsChangeSubject(false);
      } else {
        setPackageList([]);
        setNetError({
          ...packagePageInfo.current,
          categoryId: reqCategoryId,
          resData: data,
        });
      }

      // 给资源点加载的时间
      window.setTimeout(() => {
        try {
          if (isChangeSubject) {
            const dom = document.getElementById("self-learning-recommend-page");
            if (dom) {
              dom.scrollTop = 0;
            }
          }
        } catch (error) {
          console.warn("推荐计划容器组件-滚动到顶部出错", error);
        }
      }, 300);
    } catch (error) {
      if (nowReqFlag !== requestFlag.current) {
        return;
      }
      setPackageList([]);
      setNetError({
        error,
        ...packagePageInfo.current,
        categoryId: reqCategoryId,
      });
    } finally {
      if (nowReqFlag !== requestFlag.current) {
        return;
      }
      setPackageLoading(false);
      setLoading(false);
      setIsChangeSubject(false);
    }
  };
  /** 获取学科列表 */
  const querySubjectList = async () => {
    // 设置网络错误的提示和上报
    const setNetError = (error: any, isNetworkError?: boolean) => {
      if (isNetworkError) {
        setSubjectNetWorkError(true);
      }
      setLoading(false);
      logger.warn("self-learning-recommend-subject-warn", {
        reason: "自主学习计划-推荐列表-学科信息列表为空",
        error,
      });
    };

    try {
      setLoading(true);
      const { data } = await getRecommendSubjectList({
        ignoreError: true,
      });
      if (data?.length) {
        const [firstSubject] = data;
        subjectListLoaded?.([...data]);
        setSelectedSubject(firstSubject.categoryId);
        setSelectedSubjectName(firstSubject.subjectName);
        setSubjectList([...data]);
        queryPackageListByCategoryId(firstSubject.categoryId);
        setSubjectNetWorkError(false);
        return;
      } else {
        // 异常没数据就提示网络错误，让用户重新刷新
        setNetError(data);
        subjectListLoaded?.([]);
      }
    } catch (error) {
      // 异常没数据就提示网络错误，让用户重新刷新
      setNetError(error, true);
      subjectListLoaded?.([]);
    }
  };

  const handleSubjectItemClick = (
    subjectItem: IRecommendContentSubjectList,
    index: number,
  ) => {
    if (subjectItem.categoryId === selectedSubject) {
      return;
    }
    setIsChangeSubject(true);
    setSelectedSubject(subjectItem.categoryId);
    setSelectedSubjectName(subjectItem.subjectName);
    // 切换学科时将页数重置为 1 和有下一页的状态
    packagePageInfo.current = {
      ...packagePageInfo.current,
      pageIndex: 1,
      haveNextPage: true,
    };
    if (swiperRef?.current) {
      // 因为学科名称内容容器偏小，且中间间距较大，容易看不到上一个选项，因此多移动一个
      swiperRef.current?.slideTo(index - 1);
    }
    setPackageList([]);
    queryPackageListByCategoryId(subjectItem.categoryId, true);
  };

  const autoQueryPackageList = async () => {
    if (
      packagePageInfo.current.haveNextPage &&
      !loading &&
      subjectList?.length &&
      !isChangeSubject &&
      !packageLoading
    ) {
      packagePageInfo.current = {
        ...packagePageInfo.current,
        pageIndex: packagePageInfo.current.pageIndex + 1,
      };
      await queryPackageListByCategoryId();
    }
  };

  const throttleLoadMore = throttle(autoQueryPackageList, 600);

  const handleLoadMore = async () => {
    throttleLoadMore();
  };

  const handleAddButtonClick = async (callbackItem?: IRecommendPackageInfo) => {
    try {
      event?.stopPropagation();
      const currentPackage = callbackItem || operatePackage;
      if (operateLoading || !currentPackage) {
        return;
      }
      setOperateLoading(true);
      const targetApi = currentPackage.added2Plan
        ? postRemovePackageByPlan
        : postAddPackageToPlan;
      const { data: isSuccess } = await targetApi({
        courseId: currentPackage.courseId,
        ...(!currentPackage.added2Plan ? { ignoreError: true } : {}),
      });
      if (isSuccess) {
        // 操作成功后关闭提示框
        setRemoveTipVisible(false);
        Toast.show(
          currentPackage?.added2Plan ? "计划移出成功" : "加入计划成功",
        );
        packageList.some((item: IRecommendPackageInfo) => {
          if (item.courseId === currentPackage.courseId) {
            item.added2Plan = !currentPackage.added2Plan;
            // 如果加入计划就+1，移出计划就-1
            if (item.added2Plan) {
              item.chooseCount++;
            } else {
              item.chooseCount--;
            }
            return true;
          }
        });
        setRemovePackageVisible(false);
        setPackageList([...packageList]);
      }
    } catch (error) {
      if (error?.code) {
        const code = Number(error.code);
        // 加入计划过多
        if (code === ErrorCodeEnum.TOO_MANY_LESSON && error?.msg) {
          setPlanTooMuchInfo({
            visible: true,
            content: error?.msg,
          });
        } else {
          if (error?.msg) {
            Toast.show(error.msg);
          }
          if (!isIgnoreError(error, [ErrorCodeEnum.LESSON_IN_PLAN])) {
            logger.error("recommend-add-plan-error", {
              error,
            });
          }
        }
      }
    } finally {
      setOperateLoading(false);
    }
  };

  const debounceAddOrUpdateClick = debounce(handleAddButtonClick);

  useEffect(() => {
    try {
      expPv("ewt_h5_study_course_self_learning_recommend_plan_component_expo", {
        sourceFrom,
      });
    } catch (error) {
      console.warn("推荐计划的内容组件上报曝光失败", error);
    }
    querySubjectList();
  }, []);

  // 更新单个课程包状态
  const updatePackageStatus = async () => {
    try {
      // 如果没有报信息就不刷新了
      if (!globalCourseInfo.current.courseId) {
        return;
      }
      const reqCourseId = globalCourseInfo.current.courseId;
      // 使用完毕就清空，防止重新请求
      globalCourseInfo.current.courseId = "";
      const { data: isAddedPlan } = await getPackageIsAddedPlan({
        courseId: reqCourseId,
        ignoreError: true,
      });
      const infoService = isAddedPlan
        ? getAddedPackageInfo
        : getNoAddPackageInfo;
      const { data: newPackageInfo } = await infoService({
        courseId: reqCourseId,
        ignoreError: true,
      });

      for (let courseItem of globalCourseInfo.current.packageList) {
        if (courseItem?.courseId === reqCourseId) {
          courseItem.added2Plan = isAddedPlan;
          courseItem.lessonCount = newPackageInfo.lessonCount;
          courseItem.chooseCount = newPackageInfo.chooseCount;
          if (isAddedPlan) {
            courseItem.coursePackageHasFinished =
              newPackageInfo?.lessonCount === newPackageInfo?.completeCount;
          }
        }
      }
      // 刷新数据
      setPackageList([...globalCourseInfo.current.packageList]);
    } catch (error) {
      console.warn("更新课程包状态失败", error);
    }
  };

  // 返回后重新刷新包状态
  const handleRefreshConfig = (isShow: boolean) =>
    isShow && updatePackageStatus();

  // 页面的可见/不可见监控，变更后查询最新的奖励状态
  useVisibilitychange({
    handleVisibilitychange: handleRefreshConfig,
  });

  return (
    <div className={styles["recommend-content-container"]}>
      {/* 学科列表swiper */}
      {!!subjectList?.length && (
        <SubjectTabSwiper
          onSwiper={(swiper: any) => (swiperRef.current = swiper)}
          data={subjectList}
          active={selectedSubject}
          handleClick={handleSubjectItemClick}
        />
      )}
      {/* 如果学科列表或者包列表网络错误，那就显示重新加载的错误提示 */}
      {(subjectNetWorkError || packageListNetWork) &&
        !loading &&
        !studyVisible && (
          <div style={{ height: networkErrorHeight || `calc(100vh - 31vw)` }}>
            <NetworkErrorStatus
              buttonOption={{
                handleClick: () => querySubjectList(),
              }}
            />
          </div>
        )}
      {/* 学科列表存在，不是两种网络错误，包列表是空, 不是推荐添加，展示无数据空态 */}
      {!subjectNetWorkError &&
        !packageListNetWork &&
        !studyVisible &&
        !!subjectList?.length &&
        !packageList?.length &&
        !isChangeSubject &&
        !loading && (
          <div style={{ height: emptyHeight || `calc(100vh - 40vw)` }}>
            <EmptyStatus />
          </div>
        )}
      {/* 接口正常但是学科信息为空的话显示推荐的界面 */}
      {!subjectNetWorkError &&
        !loading &&
        !subjectList?.length &&
        !studyVisible && (
          <div style={{ height: `calc(100vh - 31vw)` }}>
            <EmptyStatus
              text="暂无符合你的推荐内容，可前往e讲堂进行添加～"
              buttonOption={{
                text: "查看如何添加",
                handleClick: () => setStudyVisible(true),
              }}
            />
          </div>
        )}
      {/* 如果在e讲堂添加课程的介绍 */}
      {studyVisible && <StudyRecommendPopup />}
      {/* 学科切换loading态 */}
      {isChangeSubject && (
        <div className={styles["changing-subject-box"]}>
          <span>加载中</span>
          <DotLoading color="currentColor" />
        </div>
      )}
      {/* 课包列表 */}
      {!!packageList?.length && (
        <div className={styles["recommend-course-package-list-box"]}>
          <RecommendPackageList
            data={packageList}
            selectedSubjectName={selectedSubjectName}
            sourceFrom={sourceFrom}
            handleOperateClick={(item: IRecommendPackageInfo) => {
              if (item.added2Plan) {
                // 新手引导不展示二次确认框，其他场景移出时都展示
                if (sourceFrom === ERecommendContentSourceFrom.guid) {
                  debounceAddOrUpdateClick(item);
                } else {
                  setOperatePackage(item);
                  setRemoveTipVisible(true);
                }
              } else {
                debounceAddOrUpdateClick(item);
              }
            }}
            globalCourseRef={globalCourseInfo}
          />

          {!subjectNetWorkError &&
            !loading &&
            subjectList?.length === 0 &&
            !studyVisible && (
              <div style={{ height: `calc(100vh - 31vw)` }}>
                <EmptyStatus
                  text="暂无符合你的推荐内容，可前往e讲堂进行添加～"
                  buttonOption={{
                    text: "查看如何添加",
                    handleClick: () => setStudyVisible(true),
                  }}
                />
              </div>
            )}

          {studyVisible && <StudyRecommendPopup />}
          <InfiniteScroll
            loadMore={handleLoadMore}
            hasMore={packagePageInfo.current.haveNextPage}
          >
            {!!packageList?.length && (
              <InfiniteScrollBottom
                hasMore={packagePageInfo.current.haveNextPage}
              />
            )}
          </InfiniteScroll>
        </div>
      )}

      <CustomCenterPopup
        visible={planTooMuchInfo.visible}
        title="已加计划过多"
        content={planTooMuchInfo?.content}
        onClose={() =>
          setPlanTooMuchInfo({
            visible: false,
            content: "",
          })
        }
      />

      <TipPopup
        visible={removeTipVisible}
        title="是否确定移出计划"
        onClose={() => {
          setOperatePackage(null);
          setRemoveTipVisible(false);
        }}
        onOk={(e: any) => {
          clickPv(
            "ewt_h5_study_course_self_learning_recommend_plan_remove_package_confirm_button_click",
            {
              courseId: operatePackage.courseId,
            },
          );
          debounceAddOrUpdateClick();
        }}
      />
      {loading && <Spin />}

      <TipPopup
        title="是否确定把该课程移出计划？"
        visible={removePackageVisible}
        onClose={() => setRemovePackageVisible(false)}
        onOk={() => handleAddButtonClick()}
      />
    </div>
  );
};

export default React.memo(RecommendContent);
