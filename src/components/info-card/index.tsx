/** 信息卡片 */

import * as React from "react";

import { SpinLoading } from "antd-mobile";

import { IconSvg } from "@/components";

import Style from "./style.module.scss";
import { cls } from "@/utils/tool";

interface IInfoCard {
  // 信息块标题
  title?: string;
  // 整个容器的类名
  className?: string;
  infoClassName?: string;
  statusClassName?: string;
  // 标题的类名
  titleClassName?: string;
  actionText?: string;
  value?: string | number;
  unit?: string;
  statusText?: string | React.ReactNode;
  /** 右上角操作 loading */
  actionLoading?: boolean;
  // 允许自定义底部
  bottom?: React.ReactElement;
  // 点击问号图标触发
  handleExplain?: () => void;
  /** 状态区不可交互 */
  statusNoAction?: boolean;
  handleStatus?: () => void;
  handleAction?: () => void;
}

export const InfoCard: React.FC<IInfoCard> = (props) => {
  const {
    actionLoading,
    title,
    actionText,
    unit,
    className,
    bottom,
    value,
    statusClassName,
    infoClassName,
    titleClassName,
    statusText,
    statusNoAction,
    handleExplain,
    handleAction,
    handleStatus,
  } = props;
  return (
    <div className={cls([Style["info-card"], className])}>
      <div className={cls([Style["info"], infoClassName])}>
        {!!title && (
          <div className={Style["top"]}>
            <div className={cls([Style["title"], titleClassName])}>{title}</div>
            {actionText ? (
              <div
                className={cls([
                  Style["action"],
                  actionLoading && Style["action-loading"],
                ])}
                onClick={handleAction}
              >
                {actionLoading ? <SpinLoading color="green" /> : actionText}
              </div>
            ) : null}
          </div>
        )}
        {!bottom ? (
          <div
            className={cls([
              Style["bottom"],
              !value && value != 0 && Style["no-value"],
            ])}
          >
            <div className={Style["value-container"]}>
              <div className={Style["value"]}>
                {value ?? <div className={Style["empty"]} />}
              </div>
              <div className={Style["unit"]}>{unit}</div>
            </div>
            {handleExplain ? (
              <IconSvg
                className={Style["explain"]}
                name="icon-xingzhuangjiehe"
                onClick={handleExplain}
              />
            ) : null}
          </div>
        ) : (
          bottom
        )}
      </div>
      <div
        className={cls([
          Style["status"],
          !statusNoAction && Style["allow-click"],
          statusClassName,
        ])}
        onClick={handleStatus}
      >
        <div>{statusText}</div>
        {!statusNoAction ? (
          <div>
            <IconSvg name="icon-jinru" />
          </div>
        ) : null}
      </div>
      {props.children}
    </div>
  );
};
