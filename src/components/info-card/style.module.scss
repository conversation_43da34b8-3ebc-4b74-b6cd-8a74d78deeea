.info-card {
  width: 100%;
  box-sizing: border-box;

  .info {
    width: 100%;
    height: 64px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background-color: #FFFFFF;
    border-radius: 8px;
    box-sizing: border-box;
    padding: 8px 10px 10px 10px;

    .top {
      display: flex;
      justify-content: space-between;
      .title {
        flex: 1;
        width: 48px;
        font-size: 12px;
        color: #333333;
      }

      .action {
        flex: 0 0 auto;
        font-size: 12px;
        color: #2D86FE;
      }
    }

    .bottom {
      display: flex;
      align-items: baseline;
      height: 22px;

      .value-container {
        height: 100%;
        display: flex;
        flex: 1;
        align-items: baseline;
      }

      .value {
        height: 100%;
        padding-top: 4px;
        box-sizing: border-box;
        font-weight: bold;
        font-size: 28px;
        color: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;

        .empty {
          width: 15px;
          height: 3px;
          background-color: rgba(0, 0, 0, 0.8);
        }
      }

      .unit {
        font-size: 12px;
        color: #999999;
        margin-left: 5px;
      }

      .explain {
        flex: 0 0 auto;
        font-size: 13px;
        padding-right: 1px;
        color: rgba(0, 0, 0, 0.45)
      }
    }
    .no-value {
      align-items: center;
    }
  }

  .status {
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    font-size: 12px;
    color: #000;
  }

  .allow-click {
    display: flex;
    font-size: 12px;
    justify-content: space-around;
  }

  .action-loading {
    color: green;
    --size: 16px;

    :global {
      .adm-spin-loading {
        width: 16px;
        height: 16px;
      }
    }
  }
}
