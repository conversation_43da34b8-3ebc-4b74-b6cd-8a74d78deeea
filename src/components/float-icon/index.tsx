import React, { useEffect, useState } from "react";
import { FloatingBubble, Popover } from "antd-mobile";
import { getSupportText } from "@/service/self-learning/drawer";
import styles from "./style.module.scss";
import EGifImg from "@/assets/common/e-gif.gif";
import GifCloseImg from "@/assets/common/gif-close.png";
import { clickPv, throttle } from "@/utils/tool";

let banDuTimer = null;
/**
 * 浮动icon配置定义
 */
interface IFloatIcon {
  /** 浮动元素的图片，必传 */
  img?: string;
  /** 默认的初始化位置，默认是隐藏半个 */
  defaultOffset?: any;
  /** 允许移动的范围，x轴或者y轴，默认是y轴 */
  axis?: "x" | "y" | "xy" | "lock";
  /** 自动磁吸到边界,默认x轴 */
  magnetic?: "y" | "x";
  /** 自定义样式，参考antd */
  style?: any;
  /** 点击气泡后的回调 */
  handleClick?: () => void;
  /** 自动磁吸的等待时间，默认4500毫秒，单位是毫秒值 */
  autoMagneticTime?: number;
}

const FloatIcon: React.FC<IFloatIcon> = (props) => {
  const {
    defaultOffset,
    img = EGifImg,
    axis = "y",
    magnetic = "x",
    style = {
      "--initial-position-bottom": "0",
      "--initial-position-left": "0",
      "--edge-distance": "0",
      "--size": "72px 96px",
    },
    handleClick,
    autoMagneticTime = 4500,
  } = props;

  const [offset, setOffset] = useState(defaultOffset || { x: -36, y: -43 });
  const [popContent, setPopContent] = useState(""); // 弹出气泡的文案
  const [visible, setVisible] = useState(false);
  const [popTextList, setPopTextList] = useState([]); // 循环的文案列表
  /** 是否显示小e伴读，默认显示，用户可手动关闭 */
  const [isShow, setIsShow] = useState(true);

  /** 隐藏小e伴读定时器 */
  const hideBanDu = () => {
    clearTimeout(banDuTimer);
    banDuTimer = window.setTimeout(() => {
      hideFloat();
      if (popTextList?.length) {
        setPopContent("");
        setVisible(false);
      }
    }, autoMagneticTime);
  };

  /** 隐藏浮动 */
  const hideFloat = () => {
    const newOffset = {
      x: -36,
    };
    setOffset(newOffset);
  };

  /** 减少调用次数，对隐藏的动作节流，原因是内部有定时器 */
  const throttledHideBanDu = throttle(hideBanDu, 500);

  /** 显示浮动 */
  const showFloat = () => {
    const newOffset = {
      ...offset,
      x: 0,
    };
    setOffset(newOffset);
  };

  /** 点击小e伴读回调 */
  const banDuClick = () => {
    showFloat();
    /** 如果有传入外部回调 */
    handleClick?.();
    clickPv("ewt_h5_study_course_self_learning_home_float_icon_click", {
      type: "点击小e",
    });
    if (popTextList.length) {
      const randomIndex = Math.floor(Math.random() * popTextList.length);
      const popText = popTextList[randomIndex];
      /** 保存取得随机内容 */
      setPopContent(popText);
      /** 延迟让pop文案出现，原因是有位置的变化，需要等最终位置确定后再出现 */
      window.setTimeout(() => setVisible(true), 300);
      /** 最后启动隐藏小e伴读的倒计时 */
      hideBanDu();
    }
  };

  // 初始化需要展示的文案列表，只有存在内容才会显示
  const initData = async () => {
    try {
      const { data } = await getSupportText();
      setPopTextList([...(data || [])]);
    } catch (error) {
      console.warn("小e伴读-获取内容list出错", error);
    }
  };

  /** 点击空白区域时关闭小e伴读、关闭文案pop */
  const handleDocumentClick = (event: MouseEvent) => {
    const targetElement = document.getElementById("self-learning-float-gif");
    const isClickInsideElement = targetElement?.contains(event.target as Node);
    if (!isClickInsideElement) {
      clearTimeout(banDuTimer);
      hideFloat();
      setVisible(false);
      setPopContent("");
    }
  };

  useEffect(() => {
    initData();
    document.addEventListener("click", handleDocumentClick);
    return () => {
      document.removeEventListener("click", handleDocumentClick);
    };
  }, []);

  return isShow ? (
    <FloatingBubble
      className={styles["float-box"]}
      axis={axis}
      magnetic={magnetic}
      style={style}
      onOffsetChange={(offset) => {
        setOffset(offset);
        throttledHideBanDu();
        if (visible) {
          setVisible(false);
          setPopContent("");
        }
      }}
      offset={offset}
      onClick={banDuClick}
    >
      <Popover
        visible={visible}
        content={popContent}
        trigger="click"
        placement="right"
        mode="dark"
        className={styles["popover-container"]}
      >
        <div className={styles["float-content-box"]}>
          <img
            src={img}
            alt=""
            className={styles["float-icon-img"]}
            id="self-learning-float-gif"
          />
          <img
            src={GifCloseImg}
            alt=""
            className={styles["close-button"]}
            onClick={(event: any) => {
              // 阻止冒泡
              event.stopPropagation();
              clickPv(
                "ewt_h5_study_course_self_learning_home_float_icon_click",
                {
                  type: "关闭小e",
                },
              );
              setIsShow(false);
            }}
          />
        </div>
      </Popover>
    </FloatingBubble>
  ) : null;
};

export default React.memo(FloatIcon);
