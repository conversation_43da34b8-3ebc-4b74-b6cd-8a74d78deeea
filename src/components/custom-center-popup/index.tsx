import React, { ReactNode } from "react";
import { CenterPopup } from "antd-mobile";
import styles from "./style.module.scss";

interface ICustomCenterPopupProps {
  visible?: boolean;
  title?: string;
  /** 必须有内容 */
  content: string | ReactNode;
  onClose?: () => void;
  buttonText?: string;
}

const CustomCenterPopup: React.FC<ICustomCenterPopupProps> = (props) => {
  const {
    visible,
    title = "温馨提示",
    content = "",
    onClose = () => {},
    buttonText = "我知道了",
  } = props;

  return content ? (
    <CenterPopup visible={visible}>
      <div className={styles["custom-center-popup"]}>
        <div className={styles["title"]}>{title}</div>
        <div className={styles["content"]}>{content}</div>
        <div className={styles["footer"]} onClick={onClose}>
          {buttonText}
        </div>
      </div>
    </CenterPopup>
  ) : null;
};

export default CustomCenterPopup;
