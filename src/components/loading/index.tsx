import React from "react";
import <PERSON><PERSON><PERSON><PERSON> from "@/assets/lottie/loading.json";
import Lottie from "lottie-react";
import styles from "./index.module.scss";
const Loading = () => {
  return (
    <div className={styles.container}>
      <Lottie
        loop={true}
        autoplay={true}
        animationData={LottieJson}
        assetsPath={`${process.env.PUBLIC_PATH}loading/images/`}
        className={styles.icon}
      />
      <div className={styles.text}>加载中...</div>
    </div>
  );
};

export default Loading;
