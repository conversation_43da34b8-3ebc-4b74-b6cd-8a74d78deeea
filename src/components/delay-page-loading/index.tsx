import React, { useEffect, useRef, useState } from "react";
import { SpinLoading } from "antd-mobile";

import styles from "./index.module.scss";
import { createPortal } from "react-dom";
import { cls } from "@/utils/tool";

const DelayPageLoading = ({ loading }: { loading: boolean }) => {
  const timerRef = useRef<NodeJS.Timeout | number>(0);
  const [opacity, setOpacity] = useState(0);

  useEffect(() => {
    if (loading) {
      timerRef.current = setTimeout(() => {
        setOpacity(1);
      }, 300);
    } else {
      setOpacity(0);
      clearTimeout(timerRef.current);
    }
    return () => {
      clearTimeout(timerRef.current);
    };
  }, [loading]);

  return createPortal(
    <div
      className={cls([styles.container, !!loading && styles.loading])}
      style={{ opacity }}
      onClick={(e) => {
        if (loading) {
          e.preventDefault();
          e.stopPropagation();
        }
      }}
    >
      <div className={styles.center}>
        <SpinLoading color="primary" />
      </div>
    </div>,
    document.body,
  );
};

export default DelayPageLoading;
