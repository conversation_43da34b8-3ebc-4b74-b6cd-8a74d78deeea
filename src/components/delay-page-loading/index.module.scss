.container {
  transition: opacity 0.3s linear;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  pointer-events: none;
  user-select: none;
  opacity: 0;
  z-index: 1001;
  background-color: rgba(0, 0, 0, 0.55);

  .center {
    position: absolute;
    top: 50%;
    left: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    transform: translate(-50%, -50%);
    background: #fff;
    border-radius: 16px;
  }

  &.loading {
    pointer-events: auto;
  }
}
