import React, { Fragment, useEffect, useRef, useState } from "react";
import { SpinLoading, Toast } from "antd-mobile";
import { Popup } from "../pop-up";
import Style from "./style.module.scss";
import { Button, EButtonType } from "../button";
import {
  getWatchedLessonList,
  ICurrentLessonInfo,
  IWatchedLesson,
  postBatchFinishLesson,
} from "@/service/self-learning/drawer";
import { clickPv, cls, debounce, expPv } from "@/utils/tool";
import PageLoading from "../page-loading";
import { CurrentCourseInfo } from "./components/current-course-info";
import { OtherWatchedLessonComp } from "./components/other-watched-lesson-list";
import { ESourceFromForText } from "@/typing";

// 标记课程完成参数
interface ISignCompleteLessonPopup {
  /** 是否需要开始标记流程 */
  visible: boolean;
  /** 点击关闭按钮、取消按钮时回调 */
  onCancel: () => void;
  /** 课包id */
  courseId: string;
  /** 课讲id */
  lessonId: string;
  /** 保存完成后回调函数, 参数是选择的课讲id集合 */
  onConfirm: (lessonIds?: number[]) => void;
  /** 来源 */
  sourceFrom: ESourceFromForText;
}

export const SignCompleteLessonPopup: React.FC<ISignCompleteLessonPopup> = (
  props,
) => {
  const { visible, onCancel, onConfirm, courseId, lessonId, sourceFrom } =
    props;
  // 内部loading状态, 初始值是外部的显示诉求,主动关闭能力是组件内部来维护
  const [preLoading, setPreLoading] = useState(visible);
  // 标记课程完成的popup弹窗开关,默认是关闭的
  // 哪怕外部告知要弹出底抽了,此时也需要根据接口返回后数据状态来变更此值
  // 如果接口失败了不会变更,如果接口成功了且至少有所选课讲信息，此时才会变更此值为true
  const [popVisible, setPopVisible] = useState(false);
  // 90天内已看过列表的数据加载中状态,用户控制显示加载中还是结果状态
  const [nextLoading, setNextLoading] = useState(false);
  // 是否正在保存中，一方面是用户状态显示，一方面是控制多次请求
  const [isSaving, setIsSaving] = useState(false);

  // 所有看过且可以标记完成的课讲列表
  const [allLessonList, setAllLessonList] = useState<IWatchedLesson[]>([]);
  // 用户选中的课讲, 第一个课讲必然是用户所选的课讲,后续是列表内选中的
  const [selectedList, setSelectedList] = useState<IWatchedLesson[]>([]);
  // 用户所选的课包、课讲信息
  const [currentLesson, setCurrentLesson] = useState<ICurrentLessonInfo>();

  // 查询90天内课讲列表所需要的参数信息
  const queryParamsRef = useRef({
    pageSize: 20,
    pageIndex: 1,
    lessonId,
    courseId,
    recordId: null,
    needCoursePackage: true,
    /** 由recordId变更为通过此字段来判断是否有下一页 */
    haveNextPage: true,
  });

  const handleLessonClick = (lesson: IWatchedLesson, isSelect: boolean) => {
    const newSelectedList = [currentLesson];
    const newAllList = JSON.parse(JSON.stringify(allLessonList));
    newAllList.map((item: IWatchedLesson) => {
      if (item.lessonId === lesson.lessonId) {
        item.selected = isSelect;
      }
      if (item.selected) {
        newSelectedList.push(item);
      }
    });
    setAllLessonList(newAllList);
    setSelectedList(newSelectedList);
  };

  const queryLessonList = async () => {
    try {
      // 如果没有下一页就不再发起请求了
      if (!queryParamsRef.current.haveNextPage) {
        return;
      }
      let reqParams = { ...queryParamsRef.current };
      setNextLoading(true);
      const { data } = await getWatchedLessonList(reqParams);
      if (data) {
        const {
          otherWatchedLessonList,
          selectWatchedLesson,
          recordId,
          haveNextPage,
          ...rest
        } = data;
        queryParamsRef.current = {
          ...queryParamsRef.current,
          recordId,
          haveNextPage,
          pageIndex: queryParamsRef.current.pageIndex + 1,
          needCoursePackage: false, // 第一次之后就不许再需要拿包的信息了
        };
        // 如果当前的课包信息没有且返回数据有信息,就保存一次
        if (
          !currentLesson?.coursePackageId &&
          selectWatchedLesson?.lessonName
        ) {
          setCurrentLesson({
            ...selectWatchedLesson,
            ...rest,
          });
          // 同时将当前课包信息保存到已选择里
          setSelectedList([{ ...selectWatchedLesson }]);
        }
        // 将新的数据合并到原数组内
        const newAll = allLessonList.concat(otherWatchedLessonList);
        setAllLessonList(newAll);
        if (selectWatchedLesson?.lessonName || currentLesson?.lessonName) {
          setPopVisible(true);
        }
      }
      setNextLoading(false);
    } catch (error) {
      console.log("error", error);
      /** 增加一个缓冲，避免闪一下什么也没有的场景 */
      window.setTimeout(() => setNextLoading(false), 300);
    } finally {
      setPreLoading(false);
    }
  };

  const initData = async () => {
    try {
      queryParamsRef.current = {
        ...queryParamsRef.current,
        pageIndex: 1,
        recordId: null,
        haveNextPage: true,
        needCoursePackage: true,
      };
      queryLessonList();
    } catch (error) {}
  };

  // 确定完成回调
  const handleOnConfirm = async () => {
    try {
      if (isSaving) {
        return;
      }
      const lessonIds = selectedList?.map(
        (item: IWatchedLesson) => item.lessonId,
      );
      await postBatchFinishLesson({ lessonIds });
      Toast.show(`课程已标记完成,火焰+${lessonIds.length}`);
      setPopVisible(false);
      onConfirm?.(lessonIds);
    } catch (error) {
      console.error(error);
    } finally {
      setIsSaving(false);
    }
  };
  // 确定完成回调 - 防止用户过快点击
  const debounceBatchFinish = debounce(handleOnConfirm);

  // 底抽关闭不可见时重置数据
  const resetData = () => {
    queryParamsRef.current = {
      ...queryParamsRef.current,
      pageIndex: 1,
      recordId: null,
      needCoursePackage: true,
    };
    setSelectedList([]);
    setAllLessonList([]);
    setCurrentLesson(null);
  };

  // 取消、关闭按钮点击回调
  const handlePopClose = () => {
    setPopVisible(false);
    onCancel?.();
  };

  useEffect(() => {
    // 满足条件再触发逻辑，不满足就不做后续处理
    if (visible && courseId && lessonId) {
      queryParamsRef.current = {
        ...queryParamsRef.current,
        courseId,
        lessonId,
      };
      expPv(
        "ewt_h5_study_course_self_learning_lesson_batch_complete_popup_expo",
        {
          sourceFrom,
          courseId,
        },
      );
      setPreLoading(true);
      initData();
    }
    if (!visible) {
      resetData();
    }
  }, [visible, courseId, lessonId]);

  return (
    <Fragment>
      {/* 正式数据返回前的loading态 */}
      <PageLoading visible={!!preLoading} />
      <Popup
        title="确认课程完成"
        destroyOnClose={true}
        visible={popVisible}
        onClose={handlePopClose}
      >
        <div className={Style["complete-lesson-pop"]}>
          <div className={Style["complete-tip-text"]}>
            你是否已掌握以下课程，并将其标记为已完成状态？
          </div>
          <div className={Style["scroll-box"]}>
            {/* 用户所选课包信息 */}
            <CurrentCourseInfo courseInfo={currentLesson} />

            {/* 90天内其他已看过课程列表 */}
            <OtherWatchedLessonComp
              data={allLessonList}
              onLoadMore={queryLessonList}
              onLessonClick={handleLessonClick}
              nextFlag={queryParamsRef?.current.haveNextPage}
              nextLoading={nextLoading}
            />
          </div>

          {/* 底抽底部按钮 */}
          <div className={Style["buttons"]}>
            <Button
              className={Style["cancel"]}
              type={EButtonType.grey}
              text="取消"
              onClick={() => {
                clickPv(
                  "ewt_h5_study_course_self_learning_lesson_batch_complete_cancel_button_click",
                  {
                    sourceFrom,
                    courseId,
                  },
                );
                handlePopClose();
              }}
            />
            <Button
              icon={isSaving ? <SpinLoading /> : null}
              className={cls([Style["ok"], isSaving && Style["saving-button"]])}
              text={`确认完成${selectedList.length}个课程`}
              onClick={() => {
                setIsSaving(true);
                clickPv(
                  "ewt_h5_study_course_self_learning_lesson_batch_complete_ok_button_click",
                  {
                    sourceFrom,
                    courseId,
                    selectedCount: selectedList.length,
                    // 可选数量长度加上必选的一个讲，得到总的课完成数量
                    // 这里可以理解为可批量完成的总数量
                    canSelectCount: allLessonList.length + 1,
                  },
                );
                debounceBatchFinish();
              }}
            />
          </div>
        </div>
      </Popup>
    </Fragment>
  );
};
