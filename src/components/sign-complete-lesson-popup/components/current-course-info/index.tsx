import React from "react";
import moment from "moment";
import { ICurrentLessonInfo } from "@/service/self-learning/drawer";

import Style from "./style.module.scss";

interface ICurrentCourseInfo {
  courseInfo: ICurrentLessonInfo;
}

export const CurrentCourseInfo: React.FC<ICurrentCourseInfo> = (props) => {
  const { courseInfo } = props;

  return courseInfo ? (
    <div className={Style["selected-lesson-box"]}>
      <div className={Style["package-image-and-title"]}>
        <img
          src={courseInfo.coverImgUrl}
          alt=""
          onError={(e: any) =>
            (e.target.style.background =
              "linear-gradient(90deg, #7EB5FF, #FFDAB7)")
          }
        />
        <p>{courseInfo.coursePackageName}</p>
      </div>
      <div className={Style["lesson-info"]}>
        <p className={Style["lesson-name"]}>{courseInfo.lessonName}</p>
        <div className={Style["lesson-time-and-progress"]}>
          <span className={Style["lesson-total-time"]}>
            {courseInfo.videoPlayTimeDisplay}
          </span>
          {courseInfo.lastViewTime && courseInfo.lastVideoPlayedProgress && (
            <span className={Style["lesson-progress"]}>
              最近于
              {moment(courseInfo.lastViewTime).format("YYYY年MM月DD日")}
              观看到
              {courseInfo.lastVideoPlayedProgress}
            </span>
          )}
        </div>
      </div>
    </div>
  ) : null;
};
