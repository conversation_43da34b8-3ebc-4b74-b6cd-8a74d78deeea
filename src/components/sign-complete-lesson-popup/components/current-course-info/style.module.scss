.selected-lesson-box {
  margin: 16px;
  padding: 12px 16px;
  background-color: #fff;
  border: 1px solid #2E86FF;
  border-radius: 8px;

  .package-image-and-title {
    display: flex;
    align-items: center;
    padding-bottom: 12px;
    border-bottom: 1px dashed rgba(0, 0, 0, .15);
    margin-bottom: 8px;

    img {
      width: 48px;
      height: 27px;
      border-radius: 3px;

      & + p {
        max-width: 245px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 13px;
        color: #455A72;
        margin-left: 8px;
      }
    }
  }

  .lesson-info {
    margin-bottom: 0;

    .lesson-name {
      color: rgba(0, 0, 0, .85);
      font-size: 15px;
      max-width: 300px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.lesson-time-and-progress {
  margin-top: 6px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: flex-start;

  .lesson-total-time {
    color: rgba(0, 0, 0, .45);
  }

  .lesson-progress {
    color: #2D86FE;
    margin-left: 16px;
  }
}
