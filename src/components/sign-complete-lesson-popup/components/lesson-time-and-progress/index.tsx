import React from "react";
import { IWatchedLesson } from "@/service/self-learning/drawer";
import moment from "moment";
import { IconSvg } from "@/components";
import { cls, covertSecondToProgress } from "@/utils/tool";

import Style from "./style.module.scss";

interface ILessonTimeAndProgress {
  lessonInfo: IWatchedLesson;
  onClick: (lesson: IWatchedLesson, isSelect: boolean) => void | Event;
}

export const LessonTimeAndProgress: React.FC<ILessonTimeAndProgress> = (
  props,
) => {
  const { lessonInfo, onClick } = props;

  return lessonInfo ? (
    <div
      className={cls([
        Style["lesson-info"],
        lessonInfo.selected && Style["selected"],
      ])}
      onClick={() => onClick(lessonInfo, !lessonInfo.selected)}
    >
      <p className={Style["lesson-name"]}>{lessonInfo.lessonName || ""}</p>
      <div className={Style["lesson-time-and-progress"]}>
        <span className={Style["lesson-total-time"]}>
          {lessonInfo.videoPlayTimeDisplay || "-"}
        </span>
        {!!lessonInfo.lastViewTime && !!lessonInfo.lastVideoPlayedProgress && (
          <span className={Style["lesson-progress"]}>
            最近于{moment(lessonInfo.lastViewTime).format("YYYY年MM月DD日")}
            观看到
            {lessonInfo.lastVideoPlayedProgress}
          </span>
        )}
      </div>
      <div
        className={cls([
          Style["selected-icon"],
          lessonInfo.selected && Style["selected"],
        ])}
      >
        <IconSvg name="icon-yijiajihua" />
      </div>
    </div>
  ) : null;
};
