.lesson-info {
  padding: 11px 16px;
  background: #FFFFFF;
  border: 1px solid #DDD;
  border-radius: 8px;
  margin-bottom: 12px;
  position: relative;

  &.selected {
    border: 1px solid #2D86FE;
  }

  .lesson-name {
    color: rgba(0, 0, 0, .85);
    font-size: 15px;
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .lesson-time-and-progress {
    margin-top: 6px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    .lesson-total-time {
      color: rgba(0, 0, 0, .45);
    }

    .lesson-progress {
      color: #2D86FE;
      margin-left: 16px;
    }
  }

  .selected-icon {
    position: absolute;
    right: -1px;
    bottom: -1px;
    width: 16px;
    height: 16px;
    border-radius: 8px 0 8px 0;
    background-color: #DDD;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;

    &.selected {
      background-color: #2D86FE;
    }

    svg {
      font-size: 10px;
    }
  }
}
