import React from "react";
import { IWatchedLesson } from "@/service/self-learning/drawer";
import { LessonTimeAndProgress } from "../lesson-time-and-progress";
import Style from "./style.module.scss";

import { cls } from "@/utils/tool";
import { InfiniteScroll } from "antd-mobile";

interface IOtherWatchedLessonComp {
  data: IWatchedLesson[];
  onLessonClick: (lesson: IWatchedLesson, isSelect: boolean) => void | Event;
  nextFlag: boolean;
  nextLoading: boolean;
  onLoadMore: () => Promise<void>;
}

export const OtherWatchedLessonComp: React.FC<IOtherWatchedLessonComp> = (
  props,
) => {
  const { data, onLessonClick, nextFlag, nextLoading, onLoadMore } = props;

  return data?.length ? (
    <div className={Style["other-lesson-box"]}>
      <div className={Style["earlier-tip-text"]}>
        以下课程在加入计划后的近90天内看过，是否也标记完成？
      </div>
      <div className={Style["lesson-list-box"]}>
        {data?.map((item: IWatchedLesson, index: number) => {
          return (
            <LessonTimeAndProgress
              key={`${index}`}
              onClick={onLessonClick}
              lessonInfo={item}
            />
          );
        })}

        <InfiniteScroll
          loadMore={onLoadMore}
          hasMore={nextFlag}
          threshold={30}
          style={{ paddingTop: 0 }}
        >
          <span
            className={cls([
              Style["list-footer-button"],
              Style["no-more-button"],
            ])}
          >
            {nextLoading ? "加载中..." : nextFlag ? "" : "没有更多课程"}
          </span>
        </InfiniteScroll>
      </div>
    </div>
  ) : null;
};
