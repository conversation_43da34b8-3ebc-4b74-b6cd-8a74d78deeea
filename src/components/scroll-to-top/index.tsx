/** 回到顶部 */

import * as React from "react";

import scrollToTopPNG from "@/assets/common/scroll-to-top.png";
import { cls, throttle } from "@/utils/tool";

import Style from "./style.module.scss";

interface IScrollToTop {
  /** 滚动元素的类名 */
  scrollElementClass: string;
  /** 容器类名 */
  className?: string;
  /** 滚动阈值，超过这个值显示回到顶部图标 */
  scrollThreshold?: number;
}

export const ScrollToTop: React.FC<IScrollToTop> = (props) => {
  const { scrollElementClass, className, scrollThreshold = 100 } = props;
  const [show, setShow] = React.useState<boolean>(false);
  const scrollEleRef = React.useRef<Element>();
  React.useEffect(() => {
    if (!scrollElementClass) {
      return;
    }
    const scrollElement = document.querySelector(`.${scrollElementClass}`);
    if (!scrollElement) {
      return;
    }
    /** 把这个 dom 存下来，回到顶部的时候要用 */
    scrollEleRef.current = scrollElement;
    const scroll = throttle(function scroll(e: Event) {
      try {
        setShow((e.target as Element).scrollTop > scrollThreshold);
      } catch (error) {
        console.error(error);
      }
    });
    scrollElement.addEventListener("scroll", scroll);
    return () => {
      scrollElement.removeEventListener("scroll", scroll);
    };
  }, [scrollElementClass]);
  function scrollToTop() {
    if (scrollEleRef.current) {
      /** scrollTo 兼容性不太好，这边做个降级逻辑 */
      scrollEleRef.current.scrollTo
        ? scrollEleRef.current.scrollTo({
            top: 0,
            left: 0,
            behavior: "smooth",
          })
        : (scrollEleRef.current.scrollTop = 0);
    }
  }
  return (
    show && (
      <div className={cls([className, Style["wrapper"]])} onClick={scrollToTop}>
        <img src={scrollToTopPNG} />
      </div>
    )
  );
};

export default ScrollToTop;
