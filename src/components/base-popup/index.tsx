import React from "react";
import { Popup } from "antd-mobile";
import { IconSvg } from "@/components";
import { cls } from "@/utils/tool";

import styles from "./index.module.scss";

const BasePopup = ({
  title,
  children,
  open,
  onClose,
  height,
  closeOnMaskClick,
  showConfirm,
  onConfirm,
  confirmText,
}: {
  title: string;
  children?: React.ReactNode;
  open: boolean;
  height?: string;
  showConfirm?: boolean;
  closeOnMaskClick?: boolean;
  onClose: () => void;
  onConfirm?: () => void;
  confirmText?: string;
}) => {
  return (
    <Popup
      bodyClassName={styles.popup}
      bodyStyle={{ height }}
      position="bottom"
      visible={open}
      closeOnMaskClick={closeOnMaskClick}
      onClose={onClose}
    >
      <div className={styles.container}>
        <div className={styles.title}>{title}</div>
        <div className={styles.close} onClick={onClose}>
          <IconSvg name="icon-guanbi" />
        </div>
        <div className={styles.content}>
          <div className={styles.top}></div>
          {children}
          <div
            className={cls([styles.bottom, showConfirm && styles.action])}
          ></div>
        </div>
        {showConfirm && (
          <div className={styles.action_container}>
            <div className={styles.confirm} onClick={onConfirm}>
              {confirmText || "确定"}
            </div>
          </div>
        )}
      </div>
    </Popup>
  );
};

export default BasePopup;
