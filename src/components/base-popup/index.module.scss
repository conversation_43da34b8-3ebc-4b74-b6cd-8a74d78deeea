.popup {
  border-radius: 16px 16px 0 0;

  .container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .title {
    height: 57px;
    text-align: center;
    font-weight: bold;
    font-size: 18px;
    color: #333333;
    padding-top: 20px;
    flex: 0 0 auto;
  }

  .content {
    flex: 1;
    overflow-x: hidden;
    overflow-y: auto;

    .top {
      height: 5px;
    }

    .bottom {
      box-sizing: content-box;
      height: 19px;
      padding-bottom: constant(safe-area-inset-bottom);
      padding-bottom: env(safe-area-inset-bottom);
    }

    .action {
      height: 0;
      padding-bottom: 0;
    }
  }

  .action_container {
    flex: 0 0 auto;
    height: 72px;
    box-sizing: content-box;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    background: #ffffff;

    .confirm {
      width: 343px;
      height: 40px;
      background: #2f86ff;
      border: 1px solid #021e66;
      box-shadow: 0 4px 0 0 rgba(2, 30, 102, 0.15);
      border-radius: 20px;
      font-weight: bold;
      font-size: 16px;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .close {
    position: absolute;
    top: 4px;
    right: 0;
    font-size: 14px;
    width: 46px;
    height: 46px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #a7acb9;
  }
}
