import React from "react";
import {
  InfiniteScroll,
  List as Antd<PERSON>ist,
  InfiniteScrollProps,
} from "antd-mobile";

import Style from "./style.module.scss";
import { cls } from "@/utils/tool";

interface IList<T> extends Partial<InfiniteScrollProps> {
  data: T[];
  keyAttr?: string;
  className?: string;
  scrollClassName?: string;
  /** 有些数据嵌套的太深了，不好通过直接取属性的方式，获取key, 这里支持方法方法 */
  getKey?: (item: T) => number | string;
  renderItem: (item: T, isLast: boolean) => React.ReactElement;
}

export function List<T>(props: IList<T>) {
  const {
    hasMore,
    className,
    scrollClassName,
    data,
    keyAttr = "id",
    getKey,
    loadMore,
    renderItem,
  } = props;

  return (
    <>
      <AntdList className={cls([className, Style["list"]])}>
        {(data || []).map((item, idx) => {
          return (
            <div key={getKey ? getKey(item) : item[keyAttr]}>
              {renderItem(item, idx === data.length - 1)}
            </div>
          );
        })}
      </AntdList>
      {loadMore && (
        <InfiniteScroll
          className={cls([scrollClassName, Style["scroll"]])}
          loadMore={loadMore}
          hasMore={hasMore}
        >
          {props.children}
        </InfiniteScroll>
      )}
    </>
  );
}
