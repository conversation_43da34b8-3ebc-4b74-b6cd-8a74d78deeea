import React, { CSSProperties } from "react";
import EmptyPng from "@/assets/common/empty.png";
import { cls } from "@/utils/tool";

import styles from "./index.module.scss";

const EmptyInfo = ({
  style,
  text,
  className,
  wordStyle,
}: {
  style?: CSSProperties;
  text?: string;
  wordStyle?: CSSProperties;
  className?: string;
}) => {
  return (
    <div className={cls([styles.container, className])} style={style}>
      <img src={EmptyPng} />
      <div className={styles.word} style={wordStyle}>
        {text || "暂无数据"}
      </div>
    </div>
  );
};
export default EmptyInfo;
