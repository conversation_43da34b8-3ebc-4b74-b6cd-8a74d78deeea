/** 统一封装的 popup */

import * as React from "react";

import { Popup as AntPopup, PopupProps } from "antd-mobile";

import Style from "./style.module.scss";

export interface IPopup extends PopupProps {
  title: string;
  titleClassName?: string;
}

const Popup = (props: IPopup) => {
  const { title, children, titleClassName, ...rest } = props;
  return (
    <AntPopup
      showCloseButton
      bodyStyle={{
        borderTopLeftRadius: "16px",
        borderTopRightRadius: "16px",
      }}
      closeOnMaskClick
      {...rest}
    >
      <div className={`${Style["title"]} ${titleClassName}`}>{title}</div>
      {children}
    </AntPopup>
  );
};

export default Popup;
