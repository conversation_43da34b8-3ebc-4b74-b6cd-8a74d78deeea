import React, { useEffect, useRef } from "react";
import { ProgressCircle as ProgressC, ProgressCircleProps } from "antd-mobile";
import { v4 as uuid } from "uuid";

interface IProgressCircleProps extends ProgressCircleProps {
  className?: string;
  style?: React.CSSProperties;
  fillColor?: "fill" | "linearGradient";
  linearGradientStops?: {
    offset: string;
    stopColor: string;
  }[];
}

export const ProgressCircle: React.FC<IProgressCircleProps> = (props) => {
  const {
    className,
    style,
    fillColor = "fill",
    linearGradientStops,
    children,
    ...p
  } = props;

  const ProgressRef = useRef<HTMLDivElement>(null);
  const uId = useRef(uuid());
  const elementS = useRef("");

  useEffect(() => {
    if (ProgressRef.current) {
      if (fillColor === "fill") return;
      if (linearGradientStops?.length <= 0) return;
      const svg = ProgressRef.current?.querySelector(
        ".adm-progress-circle-svg",
      );
      if (!elementS.current) {
        elementS.current = svg.innerHTML;
      }

      const linearStopS = linearGradientStops
        ?.map(
          (v) => `<stop offset=${v.offset} stop-color=${v.stopColor}></stop>`,
        )
        ?.join("");

      const htmlS = `
      <defs>
        <linearGradient id="gradient_${uId.current}">
          ${linearStopS}
        </linearGradient>
      </defs>
      ${elementS.current}
      `;
      svg.innerHTML = htmlS;
      const fill = svg?.querySelector(".adm-progress-circle-fill");
      fill.setAttribute("style", `stroke: url(#gradient_${uId.current})`);
    }
  }, [ProgressRef, linearGradientStops]);

  return (
    <div className={className} ref={ProgressRef}>
      <ProgressC
        style={{
          "--track-width": "7px",
          "--fill-color":
            fillColor === "fill" ? "var(--adm-color-danger)" : "transparent",
          ...style,
        }}
        {...p}
      >
        {children}
      </ProgressC>
    </div>
  );
};
