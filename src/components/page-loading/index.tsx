import React from "react";
import { DotLoading, Mask, SpinLoading } from "antd-mobile";
import styles from "./style.module.scss";
import { cls } from "@/utils/tool";

interface LoadingProps {
  visible?: boolean;
  warpClassName?: string;
  className?: string;
  color?: string;
}

const PageLoading: React.FC<LoadingProps> = (props) => {
  const { visible, color, warpClassName, className } = props;
  return (
    <Mask className={className} visible={visible}>
      <div className={cls([styles.overlayContent, warpClassName])}>
        <SpinLoading color={color ?? "#fff"} />
      </div>
    </Mask>
  );
};

export const RowLoading: React.FC = () => {
  return (
    <div className={styles["row-loading"]}>
      加载中 <DotLoading />
    </div>
  );
};

export default PageLoading;
