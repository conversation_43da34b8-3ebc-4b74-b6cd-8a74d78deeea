import * as React from "react";
import { Button } from "../button";
import { TipPopup } from "../tip-popup";

import Style from "./style.module.scss";

interface ICourseNotCompletePopupProps {
  visible: boolean;
  onCancel: () => void;
  onOk: () => void;
}

export const CourseNotCompletePopup: React.FC<ICourseNotCompletePopupProps> = (
  props,
) => {
  const { visible, onCancel, onOk } = props;

  return (
    <TipPopup
      onClose={onCancel}
      hiddenCancel={true}
      visible={visible}
      closeOnMaskClick={true}
      okText="我知道了"
      title="课程还未观看过"
      onOk={onOk}
    >
      <p className={Style["course-not-complete-popup"]}>
        加入计划后，只有在最近90天内观看该课程1分钟以上才能将其标记为已完成状态哦～
        <span className={Style["orange-text"]}>
          注：如果你刚看过课程，看课数据未刷新请耐心等会1-2分钟～
        </span>
      </p>
    </TipPopup>
  );
};
