import * as React from "react";
import { NavBar } from "antd-mobile";
import type { NavBarProps } from "antd-mobile";

import {
  ILayoutContext,
  LayoutContext,
} from "@/components/layout/layout-context";

import { cls } from "@/utils/tool";

import { Back } from "./back";
import Style from "./style.module.scss";

export interface IHeader extends NavBarProps {
  backIconUrl?: string;
  onBack?: () => void;
}

export const Header: React.FC<IHeader> = (props) => {
  const { children, className, backIconUrl, onBack, ...rest } = props;
  const layoutContext = React.useContext<ILayoutContext>(LayoutContext);
  function handleBack() {
    onBack && onBack();
  }
  return (
    <NavBar
      className={cls([Style["header"], className])}
      onBack={handleBack}
      back={<Back imgUrl={backIconUrl} />}
      backArrow={false}
      {...rest}
    >
      {children}
    </NavBar>
  );
};

export * from "./back";
