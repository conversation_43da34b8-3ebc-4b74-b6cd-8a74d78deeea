/**
 * 返回按钮
 */
import * as React from "react";

import { cls } from "@/utils/tool";

import backPng from "@/assets/common/back.png";

import Style from "./style.module.scss";

interface IBack {
  imgUrl?: string;
  className?: string;
  style?: React.CSSProperties;
  onBack?: () => void;
}

export function Back(props: IBack) {
  const { className, style = {}, imgUrl, onBack } = props;
  return (
    <div
      onClick={() => {
        onBack?.();
      }}
      className={cls([Style["back"], className])}
      style={style}
    >
      <img src={imgUrl || backPng} />
    </div>
  );
}
