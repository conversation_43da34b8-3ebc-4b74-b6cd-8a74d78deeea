import React, { Fragment, useEffect, useState } from "react";
import Style from "./style.module.scss";
import { <PERSON><PERSON><PERSON>, <PERSON> } from "antd-mobile";
import { Button, EButtonType, IconSvg } from "@/components";
import LottieCom from "@/components/lottie-box";
import <PERSON><PERSON><PERSON><PERSON> from "@/assets/json/sun-json/data";
import { EPopupType } from "@/routes/self-learning-home/common";
import { clickPv, cls, expPv } from "@/utils/tool";

import { typeImgMap, typeNameMap, typeServiceMap } from "./context";
import FinishImg from "@/assets/common/finish-package.png";
import { ICheckInPrefixState } from "@/service/home";

/** 课包名称，课包图片 */
export interface IPackagePopupInfo {
  courseId: string;
  title: string;
  image: string;
}

interface IAchieveGoalPopupProps {
  type: EPopupType;
  count?: number;
  packageInfo?: IPackagePopupInfo;
  onClose?: () => void;
  checkInPrefixState?: ICheckInPrefixState;
}

export interface IPopupRef {
  setVisible?: React.Dispatch<React.SetStateAction<boolean>>;
}

export const AchieveGoalPopup = React.forwardRef<
  IPopupRef,
  IAchieveGoalPopupProps
>(function Popups(props, ref) {
  const { type, count = 0, packageInfo, onClose, checkInPrefixState } = props;
  const [visible, setVisible] = useState(false);

  /** 把弹窗的调度能力暴露出去 */
  React.useImperativeHandle(ref, () => {
    return {
      setVisible,
    };
  }, []);

  const uploadStatus = async () => {
    try {
      typeServiceMap[type]?.();
    } catch (error) {
      console.warn(`${typeNameMap[type]}上报异常`, error);
    }
  };

  /** 点击关闭 */
  const handleCloseClick = () => {
    const buttonText = makeButtonText();
    clickPv("ewt_h5_study_course_self_learning_incentive_popup_ok_click", {
      type: typeNameMap[type],
      buttonText,
    });
    onClose?.();
    setVisible(false);
  };

  const makeSubTitle = () => {
    if ([EPopupType.weekComplete, EPopupType.checkIn].includes(type)) {
      return type === EPopupType.checkIn
        ? "今日打卡成功"
        : "完成了本周的学习计划";
    }
    if (type === EPopupType.milestoneComplete) {
      return `累计进行打卡${count}天`;
    }
    if (packageInfo?.title && type === EPopupType.courseComplete) {
      return `完成了"${packageInfo?.title}"的学习任务`;
    }
    return "";
  };

  useEffect(() => {
    if (visible) {
      expPv("ewt_h5_study_course_self_learning_incentive_popup_expo", {
        type: typeNameMap[type],
      });
      // 显示时上报状态
      uploadStatus();
    }
  }, [visible]);

  const makeButtonText = () => {
    if (EPopupType.checkIn === type) {
      return checkInPrefixState?.rewardFlag ? "查看奖励" : "点击领奖";
    } else {
      return EPopupType.milestoneComplete === type ? "点击领奖" : "继续努力";
    }
  };

  return (
    <Mask visible={visible}>
      {/* 这里如果是打卡类型的需要蓝色背景，否则就是激励的黄色渐变 */}
      <div
        className={cls([
          Style["achieve-goal-popup-box"],
          EPopupType.checkIn === type ? Style["blue-bg"] : Style["yellow-bg"],
        ])}
      >
        <div className={Style["content-box"]}>
          {/* lottie动画 */}
          {visible && (
            <LottieCom dataJson={LottieJson} className={Style["lottie-box"]} />
          )}
          {/* 主要内容 */}
          <div className={Style["centre-content"]}>
            {/* 标题 */}
            <p className={Style["first-title"]}>恭喜你</p>
            {/* 副标题 */}
            <Ellipsis
              className={Style["sub-title"]}
              content={makeSubTitle()}
              rows={2}
              direction="end"
            />
            {/* 奖励内容 */}
            <div
              className={cls([
                Style["award-box"],
                EPopupType.courseComplete !== type && Style["circle-award-box"],
              ])}
            >
              {[EPopupType.weekComplete, EPopupType.milestoneComplete].includes(
                type,
              ) && (
                <Fragment>
                  <div
                    className={cls([
                      Style["img-center-box"],
                      Style["radius-box"],
                    ])}
                  >
                    <img src={typeImgMap[type]} alt="奖励" />
                  </div>
                  {EPopupType.checkIn !== type && (
                    <div className={Style["other-into-box"]}>
                      <IconSvg name="icon-guanbi" />
                      <span>{count}</span>
                    </div>
                  )}
                </Fragment>
              )}
              {[EPopupType.checkIn, EPopupType.courseComplete].includes(
                type,
              ) && (
                <div
                  className={
                    type === EPopupType.checkIn
                      ? Style["clock-in-box"]
                      : Style["course-box"]
                  }
                >
                  {type === EPopupType.courseComplete ? (
                    <img
                      src={packageInfo?.image || ""}
                      alt=""
                      className={Style["course-covert-img"]}
                      onError={(e: any) =>
                        (e.target.style.background =
                          "linear-gradient(90deg, #7EB5FF, #FFDAB7)")
                      }
                    />
                  ) : (
                    <img
                      src={typeImgMap[type]}
                      alt="打卡"
                      className={Style["clock-in-img"]}
                    />
                  )}
                  <img
                    src={FinishImg}
                    alt="完成"
                    className={Style["finish-img"]}
                  />
                </div>
              )}
            </div>
            {/* 今日打卡是特殊的提示语,其他弹窗保持原本文案 */}
            <p className={Style["tip-text"]}>
              {EPopupType.checkIn === type
                ? "请再接再厉，坚持学习"
                : "给自己点个赞吧"}
            </p>
            <Button
              onClick={handleCloseClick}
              className={Style["start-button"]}
              text={makeButtonText()}
              type={EButtonType.grey}
            />
          </div>
        </div>
      </div>
    </Mask>
  );
});
