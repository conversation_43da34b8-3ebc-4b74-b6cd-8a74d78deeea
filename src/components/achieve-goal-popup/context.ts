import { EPopupType } from "@/routes/self-learning-home/common";
import FireImg from "@/assets/common/icon-finish-icon.png";
import ClockInImg from "@/assets/common//clock-in.png";
import DayImg from "@/assets/common/day.png";
import {
  getCheckInPopupCallback,
  getClockInCompletedCallback,
  getPackageCompletedCallback,
  getWeekCompleteCallback,
} from "@/service/self-learning/drawer";

// 弹窗类型中文名称Map，主要为上报QT使用
export const typeNameMap = {
  [EPopupType.weekComplete]: "周任务完成",
  [EPopupType.courseComplete]: "课程包完成",
  [EPopupType.milestoneComplete]: "累计打卡",
  [EPopupType.checkIn]: "每日打卡",
};
// 弹窗类型对应的图片展示
export const typeImgMap = {
  [EPopupType.weekComplete]: FireImg,
  [EPopupType.milestoneComplete]: DayImg,
  [EPopupType.checkIn]: ClockInImg,
};
// 三种类型弹窗的回调，周达标、打卡达标、课包达标
export const typeServiceMap = {
  [EPopupType.weekComplete]: getWeekCompleteCallback,
  [EPopupType.milestoneComplete]: getClockInCompletedCallback,
  [EPopupType.courseComplete]: getPackageCompletedCallback,
  [EPopupType.checkIn]: getCheckInPopupCallback,
};
