.achieve-goal-popup-box {
  width: 310px;
  height: 400px;
  max-height: 90vh;
  overflow: auto;
  border-radius: 8px;
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 99;

  &.yellow-bg {
    background-image: linear-gradient(180deg, #FFC45A 0%, #F96D05 100%);

    .sub-title, .first-title {
      color: #863900;
    }

    .start-button > div {
      color: #863900;
    }
  }

  &.blue-bg {
    background: #2D86FE;

    .sub-title, .first-title {
      color: #fff;
    }

    .start-button > div {
      color: #2D86FE;
    }
  }

  .content-box {
    position: relative;
    z-index: 100;
    width: 100%;
    height: 100%;

    .lottie-box {
      width: 310px;
      height: 400px;
      position: absolute;
      z-index: 101;
      left: 0;
      top: 0;
      overflow: hidden;
    }

    .centre-content {
      text-align: center;
      position: absolute;
      left: 0;
      top: 0;
      z-index: 102;
      width: 100%;
      height: 100%;

      .first-title {
        font-size: 32px;
        line-height: 32px;
        font-weight: bold;
        margin-top: 24px;
      }

      .sub-title {
        font-size: 14px;
        line-height: 22px;
        max-width: 220px;
        margin: 15px auto 0;
        height: 44px;
      }

      .award-box {
        position: relative;
        z-index: 1;
        margin-top: 30px;
        display: flex;
        align-items: center;
        justify-content: center;

        &.circle-award-box {
          width: 108px;
          height: 108px;
          margin: 30px auto 0;
        }

        .other-into-box {
          position: absolute;
          top: 50%;
          left: 116px;
          color: #fff;
          font-size: 20px;
          font-weight: bold;
          transform: translate(0, -50%);
          display: flex;
          align-items: baseline;

          span{
            font-size: 32px;
            margin-left: 7px;
          }
        }

        .img-center-box {
          width: 108px;
          height: 108px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          background-color: #fff;
          position: relative;

          img {
            height: 48px;
          }
        }

        .course-box {
          width: 160px;
          height: 90px;
          position: relative;
          border: 1px solid transparent;
          margin-bottom: 24px;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .clock-in-box {
          width: 108px;
          height: 108px;
          border-radius: 50%;
          background-color: #fff;
          position: relative;
          display: flex;
          justify-content: center;
          align-items: center;

          .clock-in-img {
            width: 77px;
            height: 43px;
          }
        }

        .finish-img {
          position: absolute;
          bottom: -12px;
          left: 50%;
          transform: translateX(-50%);
          width: 24px;
          height: 24px;
        }

        .course-covert-img {
          width: 160px;
          height: 90px;
        }
      }

      .tip-text {
        color: #fff;
        font-size: 14px;
        margin-top: 57px;
      }

      .start-button {
        width: 278px;
        height: 44px;
        margin: 9px auto 0;
      }
    }
  }
}
