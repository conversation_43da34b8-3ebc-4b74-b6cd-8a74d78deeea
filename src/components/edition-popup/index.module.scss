@import "~@/styles/lib.scss";

.edition_list_container {
  // padding-left: 17px;
  @include adaptive-max((
    padding-left: 17px,
  ));

  .subject {
    font-weight: bold;
    // font-size: 16px;
    color: #2a333a;
    // line-height: 16px;
    // margin-bottom: 16px;
    @include adaptive-max((
      font-size: 16px,
      line-height: 16px,
      margin-bottom: 16px,
    ));
  }

  .editions {
    display: flex;
    flex-wrap: wrap;
    // padding-bottom: 8px;
    @include adaptive-max((
      padding-bottom: 8px,
    ));

    .edition {
      // width: 106px;
      // height: 94px;
      // margin-right: 12px;
      // margin-bottom: 12px;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      @include adaptive-max((
        width: 106px,
        height: 94px,
        margin-right: 12px,
        margin-bottom: 12px,
      ));

      &::before {
        content: " ";
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        // height: 74px;
        background: #f5f7fb;
        // border-radius: 8px;
        z-index: -1;
        @include adaptive-max((
          height: 74px,
          border-radius: 8px,
        ));
      }

      .cover {
        // width: 79px;
        // height: 48px;
        // border-radius: 2px;
        @include adaptive-max((
          width: 79px,
          height: 48px,
          border-radius: 2px,
        ));
      }

      .name {
        font-weight: bold;
        // font-size: 14px;
        color: #2a333a;
        // height: 46px;
        // line-height: 46px;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        // max-width: 100px;
        @include adaptive-max((
          font-size: 14px,
          height: 46px,
          line-height: 46px,
          max-width: 100px,
        ));
      }

      .selected_icon {
        position: absolute;
        // width: 19px;
        // height: 16px;
        right: 0;
        bottom: 0;
        background: url(../../assets/subject-channel/book-selected.png)
          center/contain no-repeat;
        display: none;
        @include adaptive-max((
          width: 19px,
          height: 16px,
        ));
      }

      &.active {
        pointer-events: none;

        &::before {
          background: #e1efff;
          // border: 1.5px solid #2e86ff;
          @include adaptive-max((
            border: 1.5px solid #2e86ff,
          ));
        }

        .name {
          color: #558bff;
        }

        .selected_icon {
          display: block;
        }
      }
    }

    div.edition:nth-child(3n + 3) {
      margin-right: 0;
    }
  }
}
