import React, { useState, useRef } from "react";
import SafeLogger from "@/utils/safe-logger";
import {
  getAllSubjectEditionList,
  getUserSubjectEdition,
  ISubjectBaseEditionList,
  saveUserSubjectEdition,
} from "@/service/common";
import { ISubjectEditionList, IUserSubjectEditionMap } from ".";

interface IUseBaseEdition {
  logger?: SafeLogger;
  // 对科目进行自定义处理，比如有些场景只需要设置9大学科的教材版本
  transformAllSubjectEdition?: (
    lists: ISubjectBaseEditionList[],
  ) => ISubjectBaseEditionList[];
}

const useBaseEdition = ({
  logger,
  transformAllSubjectEdition,
}: IUseBaseEdition) => {
  const [loading, setLoading] = useState(false);
  const [isError, setIsError] = useState(false);
  const allSubjectEditionRef = useRef<ISubjectEditionList[]>([]);
  const userSubjectEditionRef = useRef<IUserSubjectEditionMap>({});

  // 初始化 所有学科 的 教材版本 列表，以及 用户当前选中的 教材版本
  const initEditions = async () => {
    if (allSubjectEditionRef.current.length) {
      return true;
    }
    setLoading(true);
    try {
      const [editionListRes, currentEditionRes] = await Promise.all([
        getAllSubjectEditionList(),
        getUserSubjectEdition(),
      ]);
      if (!editionListRes.data?.length) {
        throw new Error("学科教材版本返回为空");
      }
      const userMap = {};
      currentEditionRes.data.forEach((item) => {
        userMap[item.subjectId] = item.baseEditionId;
      });
      const selectedMap = {};
      allSubjectEditionRef.current = (
        transformAllSubjectEdition
          ? transformAllSubjectEdition(editionListRes.data)
          : editionListRes.data
      ).map((item) => {
        if (userMap[item.subjectId]) {
          const selectedVersion = item.editionList.find(
            (edition) => edition.baseEditionId === userMap[item.subjectId],
          );
          if (selectedVersion) {
            selectedMap[item.subjectId] = {
              ...selectedVersion,
              editionId: selectedVersion.baseEditionId,
              subjectId: item.subjectId,
              subjectName: item.subjectName,
            };
          }
        }
        return {
          ...item,
          editionList: item.editionList.map((edition) => ({
            ...edition,
            editionId: edition.baseEditionId,
          })),
        };
      });
      userSubjectEditionRef.current = selectedMap;
      setIsError(false);
      setLoading(false);
      return true;
    } catch (error) {
      logger?.error("user-subject-editions-error", {
        error,
      });
      setLoading(false);
      setIsError(true);
      return false;
    }
  };

  const onSaveEdition = async (userSelectedMap: IUserSubjectEditionMap) => {
    setLoading(true);
    try {
      await saveUserSubjectEdition({
        subjectEditionList: Object.keys(userSelectedMap).map((subjectId) => ({
          subjectId: +subjectId,
          baseEditionId: userSelectedMap[subjectId].editionId,
        })),
      });
      userSubjectEditionRef.current = { ...userSelectedMap };
      setLoading(false);
      return true;
    } catch (error) {
      logger?.error("save-user-edition-error", { error });
      setLoading(false);
      return false;
    }
  };
  return {
    loading,
    isError,
    initEditions,
    allSubjectEditionRef,
    userSubjectEditionRef,
    onSaveEdition,
  };
};

export default useBaseEdition;
