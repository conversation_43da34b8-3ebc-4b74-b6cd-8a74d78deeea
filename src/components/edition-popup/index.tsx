import React, { useEffect, useState } from "react";
import BasePopup from "@/components/base-popup";
import { cls } from "@/utils/tool";
import LoadingImage from "@/components/loading-image";
import NoVersionPng from "@/assets/subject-channel/no_version.png";
import ErrorInfo from "../error-info";

import styles from "./index.module.scss";

export interface IUserSubjectEdition {
  subjectId: number;
  subjectName: string;
  editionId: string;
  editionName: string;
}

export interface IEdition {
  editionId: string;
  editionName: string;
  imgUrl: string;
}

export interface ISubjectEditionList {
  subjectId: number;
  subjectName: string;
  editionList: IEdition[];
}

export type IUserSubjectEditionMap = Record<number, IUserSubjectEdition>;

const EditionPopup = ({
  open,
  list,
  userSelected,
  isError,
  showSubjectId,
  onRetry,
  onClose,
  onConfirm,
}: {
  open: boolean;
  list: ISubjectEditionList[];
  userSelected: IUserSubjectEditionMap;
  isError: boolean;
  showSubjectId?: number;
  onRetry: () => void;
  onClose: () => void;
  onConfirm: (userSelected: IUserSubjectEditionMap) => void;
}) => {
  const [userSelectedMap, setUserSelectedMap] =
    useState<IUserSubjectEditionMap>(userSelected);

  const onItemClick = (edition: IEdition, subject: ISubjectEditionList) => {
    const map = { ...userSelectedMap };
    map[subject.subjectId] = {
      ...edition,
      subjectId: subject.subjectId,
      subjectName: subject.subjectName,
    };
    setUserSelectedMap(map);
  };

  const onClickConfirm = async () => {
    // 判断是否有信息修改
    const isDataChange = Object.keys(userSelectedMap).some(
      (subjectId) =>
        userSelectedMap[subjectId].editionId !==
        userSelected[subjectId]?.editionId,
    );
    // 无修改，直接关闭弹窗
    if (!isDataChange) {
      onClose();
      return;
    }
    onConfirm(userSelectedMap);
  };

  useEffect(() => {
    const map: IUserSubjectEditionMap = {};
    list.forEach((subject) => {
      const pre = userSelected[subject.subjectId];
      if (pre) {
        map[subject.subjectId] = { ...pre };
      } else {
        map[subject.subjectId] = {
          ...subject.editionList[0],
          subjectId: subject.subjectId,
          subjectName: subject.subjectName,
        };
      }
    });
    setUserSelectedMap(map);
  }, [userSelected, list]);

  // 打开时，默认聚焦到特殊学科 ID
  useEffect(() => {
    if (open && list?.length > 0 && showSubjectId) {
      setTimeout(() => {
        const item = document.getElementById(`edition-popup-${showSubjectId}`);
        if (item) {
          item.scrollIntoView();
        }
      }, 200);
    }
  }, [open, list]);

  return (
    <BasePopup
      height="80vh"
      open={open}
      onClose={onClose}
      onConfirm={onClickConfirm}
      title="请选择各学科的教材版本"
      showConfirm={!isError}
    >
      {isError ? (
        <ErrorInfo type="light" onRetry={onRetry} />
      ) : (
        <div className={styles.edition_list_container}>
          {list.map((subject) => (
            <div
              key={subject.subjectId}
              id={`edition-popup-${subject.subjectId}`}
            >
              <div className={styles.subject}>{subject.subjectName}</div>
              <div className={styles.editions}>
                {subject.editionList.map((edition) => (
                  <div
                    key={edition.editionId}
                    className={cls([
                      styles.edition,
                      edition.editionId ===
                        userSelectedMap[subject.subjectId]?.editionId &&
                        styles.active,
                    ])}
                    onClick={() => onItemClick(edition, subject)}
                  >
                    <LoadingImage
                      className={styles.cover}
                      src={edition.imgUrl}
                      fallback={NoVersionPng}
                    />
                    <div className={styles.name}>{edition.editionName}</div>
                    <div className={styles.selected_icon}></div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}
    </BasePopup>
  );
};
export default EditionPopup;
