.item {
  text-align: center;
  width: 330px;
  cursor: pointer;
  height: 40px;
  background: #EEF1F6;
  border-radius: 21px;
  line-height: 40px;
  position: relative;
  margin: 0 auto;
  margin-top: 12px;
  font-size: 14px;
  color: #666666;
  box-sizing: border-box;

  div {
    margin: 0 auto;
    width: 283px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &:first-child {
    margin-top: 0;
  }

  .beSelected-icon {
    width: 19px;
    height: 16px;
    top: 23px;
    right: 0;
    position: absolute;
    z-index: 1;
  }
}

.title {
  line-height: 66px;
  height: 66px;
  text-align: center;
  font-weight: 600;
  font-size: 18px;
  color: #021E66;
}

.beSelected-item {
  background: #FFFFFF;
  border: 1px solid #2D86FE;
  border-radius: 21px;
  font-weight: bold;
  font-size: 14px;
  color: #2D86FE;
}

.error-btn {
  span {
    font-weight: 400;
  }
}

.content {
  min-height: 30vh;
  max-height: 60vh;
  overflow: scroll;
}
