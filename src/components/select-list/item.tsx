/** 单个元素组件 */

import * as React from 'react';
import cls from 'classnames';

import beSelectedIcon from '~/assets/image/self-learning-for-subject/be-choosed.png';

import Style from './style.module.scss';

export interface IItem {
  beSelected: boolean;
  item: unknown;
  textKey: string;
  renderItem?: (item: unknown) => React.ReactNode;
  onSelected: (item: unknown) => void;
}

const Item = React.memo(function FlagItem(props: IItem) {
  const { item, beSelected, textKey, onSelected, renderItem } = props;
  return (
    <div className={cls(Style['item'], beSelected && Style['beSelected-item'])}>
      <div onClick={() => onSelected(item)}>
        {renderItem ? renderItem(item) : item[textKey]}
      </div>
      {beSelected ? (
        <img className={Style['beSelected-icon']} src={beSelectedIcon} />
      ) : null}
    </div>
  );
});

export default Item;
