/** 可选中列表 */

import * as React from 'react';
import cls from 'classnames';
import Item from './item';

import Style from './style.module.scss';
import { render } from 'react-dom';

export interface ISelectList {
  className?: string;
  data: unknown[];
  textKey: string;
  keyName?: string;
  // 初始值
  value?: unknown;
  checkIfSelect: (item: unknown) => boolean;
  // 选中后的回调
  onSelect: (item: unknown) => void;
  renderItem: (item: unknown) => React.ReactNode;
}
export const SelectList: React.FC<ISelectList> = (props) => {
  const {
    className,
    keyName,
    data,
    textKey,
    renderItem,
    checkIfSelect,
    onSelect,
  } = props;

  return (
    <div className={cls(className, Style['content'])}>
      {(data || []).map((item, idx) => (
        <Item
          textKey={textKey}
          key={item[keyName || 'id'] || idx}
          beSelected={checkIfSelect(item)}
          item={item}
          onSelected={onSelect}
          renderItem={renderItem}
        />
      ))}
    </div>
  );
};
