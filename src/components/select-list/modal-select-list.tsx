/** 弹窗样式的可选中列表 */

import * as React from 'react';

import { Popup, PopupProps } from 'antd-mobile';

import { ISelectList, SelectList } from './index';

import Style from './style.module.scss';

export interface IModalSelectList extends PopupProps {
  title: string;
  listClassName?: string;
  className?: string;
  visible: boolean;
  data: unknown[];
  textKey?: string;
  renderItem: (item: unknown) => React.ReactNode;
  checkIfSelect: (item: unknown) => boolean;
  onSelect?: (item: unknown) => void;
}

const ModalSelectList = React.memo(function FlagItem(props: IModalSelectList) {
  const {
    title,
    visible,
    listClassName,
    className,
    data,
    textKey,
    onSelect,
    checkIfSelect,
    renderItem,
    ...rest
  } = props;
  return (
    <Popup
      showCloseButton
      bodyStyle={{
        borderTopLeftRadius: '16px',
        borderTopRightRadius: '16px',
        paddingBottom: 12,
      }}
      className={className}
      visible={visible}
      closeOnMaskClick
      {...rest}
    >
      <div className={Style['title']}>{title}</div>
      <SelectList
        renderItem={renderItem}
        checkIfSelect={checkIfSelect}
        onSelect={onSelect}
        textKey={textKey}
        data={data}
        className={listClassName}
      />
    </Popup>
  );
});

export default ModalSelectList;
