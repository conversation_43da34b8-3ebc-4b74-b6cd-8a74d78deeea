import React from "react";
import { Popup } from "antd-mobile";

import styles from "./index.module.scss";

interface ProgressModalProps {
  open: boolean;
  onClose: () => void;
  onOk: () => void;
  showNoRemindModal?: boolean;
  onChange?: (checked: boolean) => void;
}

const ProgressModal = (props: ProgressModalProps) => {
  const { open, onClose, showNoRemindModal = false, onChange, onOk } = props;

  return (
    <Popup visible={open} onClose={onClose} className={styles.modal}>
      <div className={styles.container}>
        <div className={styles.title}>关于学习进度</div>
        <div className={styles.desc}>
          <p>
            1、若某些课程一加入就有看课进度，是因为加计划前60天内你观看过该课程，进度被同步了；
          </p>
          <p>2、在站内任意场景观看已加计划的课程，看课进度都会同步到计划内。</p>
        </div>

        <div className={styles.btn} onClick={() => onOk()}>
          我知道了
        </div>
        <div className={styles.safe}></div>
        <div className={styles.close} onClick={onClose}>
          <i className="iconfont iconguanbi1" />
        </div>
      </div>
    </Popup>
  );
};

export default ProgressModal;
