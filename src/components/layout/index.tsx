/**
 * 统一的布局组件
 */

import * as React from "react";
import { SafeArea, SafeAreaProps } from "antd-mobile";

import { cls } from "@/utils/tool";
import { Header, IHeader, Spin as PageLoading, ISpin } from "@/components";

import { LayoutContext } from "./layout-context";
import mstJsBridge from "mst-js-bridge";

import Style from "./style.module.scss";

export interface ILayout {
  pageLoadingProps?: ISpin;
  pageLoading?: boolean;
  className?: string;
  bgClassName?: string;
  bgProps?: any;
  wrapperHidden?: boolean;
  children: React.ReactNode;
  showHeader?: boolean;
  headerProps?: IHeader;
  topSafeAreaProps?: SafeAreaProps;
  /** 支持设置页面容器高度 */
  setChildrenContainerHeight?: (
    v: ILayoutRef["safeAreaHeight"],
  ) => number | string;
  childrenContainerClassName?: string;
  bgChildren?: React.ReactNode;
}

export interface ILayoutRef {
  safeAreaHeight: { top?: number; bottom?: number };
  setPageLoading: (visible: boolean) => void;
}

export const Layout = React.forwardRef<ILayoutRef, ILayout>(
  function Layout(props, ref) {
    const {
      children,
      className,
      bgClassName,
      bgProps,
      wrapperHidden,
      showHeader,
      pageLoading = false,
      pageLoadingProps = {},
      headerProps = {},
      topSafeAreaProps = {},
      setChildrenContainerHeight,
      childrenContainerClassName,
      bgChildren,
      ...rest
    } = props;
    /** 页面级的 loading */
    const [loading, setLoading] = React.useState<boolean>(false);
    const [safeAreaHeight, setSafeAreaHeight] = React.useState<
      ILayoutRef["safeAreaHeight"]
    >({});

    React.useImperativeHandle(ref, () => {
      return {
        safeAreaHeight,
        setPageLoading: (visible) => setLoading(visible),
      };
    }, [safeAreaHeight]);
    // 获取安全区的高度
    async function getSafeHeight() {
      try {
        const {
          data: { top },
        } = await mstJsBridge.getNativeData({
          domain: "systemInfo",
          action: "getDevicePortraitSafeArea",
          params: {
            safe_area: "top,bottom",
          },
        });
        const DomTop = (
          document.querySelector(
            ".ewt-customer-top-safe-area",
          ) as HTMLDivElement
        ).offsetHeight;
        setSafeAreaHeight({
          top: Math.max(top ?? 0, DomTop ?? 0),
          bottom: 0,
        });
      } catch (err) {
        console.error(err);
      }
    }
    React.useEffect(() => {
      getSafeHeight();
    }, []);

    return (
      <React.Fragment>
        {(bgClassName || bgProps) &&
          (wrapperHidden ? (
            <div
              className={cls([Style["self-header-bg-wrapper"]])}
              style={{ overflow: "hidden" }}
            >
              <div
                {...bgProps}
                className={cls([Style["self-header-bg"], bgClassName])}
              >
                {bgChildren}
              </div>
            </div>
          ) : (
            <div
              {...bgProps}
              className={cls([Style["self-header-bg"], bgClassName])}
            >
              {bgChildren}
            </div>
          ))}
        <div {...rest} className={cls([Style["layout"], className])}>
          <SafeArea
            {...topSafeAreaProps}
            className={cls([
              "ewt-customer-top-safe-area",
              (topSafeAreaProps as SafeAreaProps).className,
            ])}
            position="top"
            style={
              safeAreaHeight?.top ? { paddingTop: safeAreaHeight.top } : {}
            }
          />
          <LayoutContext.Provider
            value={{
              safeAreaBottomHeight: safeAreaHeight.bottom || 0,
              safeAreaTopHeight: safeAreaHeight.top || 0,
              showPageLoading: setLoading,
            }}
          >
            <div
              style={{
                position: "relative",
                height: setChildrenContainerHeight
                  ? setChildrenContainerHeight(safeAreaHeight)
                  : "unset",
              }}
              className={childrenContainerClassName}
            >
              {showHeader ? (
                <Header
                  {...headerProps}
                  style={{
                    ...(headerProps.style || {}),
                    top: safeAreaHeight.top ?? 0,
                    "--height": "12.8vw",
                  }}
                />
              ) : null}
              {children}
            </div>
            <PageLoading
              {...pageLoadingProps}
              visible={loading || pageLoading}
            />
          </LayoutContext.Provider>
        </div>
      </React.Fragment>
    );
  },
);
