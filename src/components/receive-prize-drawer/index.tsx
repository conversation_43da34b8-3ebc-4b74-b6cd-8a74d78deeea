import React, { useEffect, useReducer, useRef, useState } from "react";
import { SpinLoading, Toast } from "antd-mobile";
import { Popup } from "../pop-up";
import Style from "./style.module.scss";
import HorizontalTimeLine, {
  IHorizontalTimeLineConfig,
} from "../horizontal-time-line";
import { Button } from "../button";
import FinishImg from "@/assets/common/finish-package.png";
import ReceivedPrizeIconImg from "@/assets/common/received-prize-icon.png";
import {
  clickPv,
  cls,
  expPv,
  openRoute,
  openUrlInWebView,
  versionCompare,
} from "@/utils/tool";
import SafeLogger from "@/utils/safe-logger";
import {
  getClockInPrizeConfig,
  IClockInPrizeConfigRes,
  IClockInPrizeList,
} from "@/service/self-learning/drawer";
import {
  ReceivePrizeCommonPopup,
  IPopupRef as IReceiveCommonRef,
} from "../receive-prize-common-popup";
import { NetworkErrorStatus } from "../empty-status";
import { useVisibilitychange } from "@/hooks";
import { MedalModal } from "./medal-modal";
import { EStudyScene, studySceneName } from "@/service/home";
import mstJsBridge from "mst-js-bridge";
import { ErrorCodeEnum } from "@/utils/constant";

interface IReceivePrizeDrawerProps {
  closeBefore?: () => void;
  processNum: number;
  scene?: EStudyScene;
  isHolidayReward: boolean;
}

export interface IPopupRef {
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
}

export const ReceivePrizeDrawerComponent = React.forwardRef<
  IPopupRef,
  IReceivePrizeDrawerProps
>(function Popups(props, ref) {
  const { closeBefore, processNum, isHolidayReward } = props;
  const [visible, setVisible] = useState(false);
  // 奖励的配置列表数据
  const [showList, setShowList] = useState<IHorizontalTimeLineConfig[]>();
  // 通用领奖popup ref
  const commonReceiveRef = React.useRef<IReceiveCommonRef>();
  const [receiveBaseInfo, setReceiveBaseInfo] = useState<any>();
  const [isNetworkError, setIsNetworkError] = useState(false);
  const [loading, setLoading] = useState(false);
  const [updateState, forceUpdate] = useReducer((x: number) => x + 1, 0); // 刷新数据信息，使ref内的list信息变更
  // app version
  const pageBaseInfo = useRef({
    version: "",
    initialSlide: 0,
  });
  const medalModalRef = useRef<any>();
  const [medalModalInfo, setMedalModalInfo] = useState({
    image: "",
    isGrey: false,
  });
  // 没有e游记nodeId节点类型错误，默认为false，根据getClockInPrizeConfig接口返回决定
  const [noNodeIdError, setNoNodeIdError] = useState(false);
  // 场景 - 中文
  const strKey = isHolidayReward
    ? EStudyScene.winterHoliday
    : EStudyScene.selfLearning;
  const originScene = studySceneName[strKey];

  /** 把弹窗的调度能力暴露出去 */
  React.useImperativeHandle(ref, () => {
    return {
      setVisible,
    };
  }, []);

  const handleClose = () => {
    /** 先调用外部传入的，把数据传出去 */
    closeBefore?.();
    // 再内部闭环关闭抽屉
    setVisible(false);
  };

  // 判断是否能领奖
  // 奖励等级小于等于能领取的最大等级，且等于已完成的下一级
  // 代表之前的都已经领取，并且当前奖励未领取
  const canIReceivePrize = (
    item: IClockInPrizeList,
    rest: IClockInPrizeConfigRes,
  ) =>
    item.level > rest.receivedMaxLevel && item.level <= rest.finishedMaxLevel;

  // 生成领取按钮文案
  const makeReceiveButtonText = (
    item: IClockInPrizeList,
    rest: IClockInPrizeConfigRes,
  ) => {
    if (canIReceivePrize(item, rest)) {
      return "领奖";
    }
    if (item.level <= rest.receivedMaxLevel) {
      return "已领";
    }
    return "未达标";
  };

  const makePrizeList = (receiveInfo: IClockInPrizeConfigRes) => {
    const { milestoneLevelConfigVOs, ...rest } = receiveInfo;
    const newList = [];
    const dataList = Array.isArray(milestoneLevelConfigVOs)
      ? milestoneLevelConfigVOs
      : [];
    // 后端返回的列表顺序有可能不是正排的，使用sort做任务等级的简单排序
    dataList.sort((a, b) => a?.level - b?.level);
    // 组装时间轴需要的数据list
    dataList.forEach((item: IClockInPrizeList, index: number) => {
      const havePrizeStatus = canIReceivePrize(item, receiveInfo);

      // 如果当前奖励等级大于已领取的等级，就是灰色的
      const imageIsGrey = item.level > rest.receivedMaxLevel;

      // 如果有奖励，且不是第一个，需要定位到指定的位置
      if (havePrizeStatus && !pageBaseInfo.current.initialSlide) {
        pageBaseInfo.current.initialSlide = index;
      }

      // 时间轴上的标志点为状态，如果当前等级小于等于已完成的最大等级，就都是对勾
      // 至于是灰色还是绿色用其他字段判断
      const circleStatus =
        item.level <= rest.finishedMaxLevel ? FinishImg : null;
      // 完成标志是否灰色：当前等级小于等于已经领取的等级
      const circleIsGrey = item.level > rest.finishedMaxLevel;

      newList.push({
        topContent: (
          <div
            className={cls([
              Style["prize-box"],
              imageIsGrey && Style["no-get"],
            ])}
          >
            <span>打卡{item.targetValue}天</span>
            <div className={Style["medal-box"]}>
              <img
                src={item?.mainImgUrl}
                alt=""
                onClick={() => {
                  setMedalModalInfo({
                    image: item?.mainImgUrl,
                    isGrey: imageIsGrey,
                  });
                  medalModalRef?.current?.setVisible(true);
                }}
              />
            </div>
          </div>
        ),
        bottomContent: (
          <Button
            onClick={() => handlePrizeClick(item, receiveInfo)}
            text={makeReceiveButtonText(item, receiveInfo)}
            className={cls([
              Style["receive-prize-button"],
              havePrizeStatus ? Style["have-prize"] : Style["no-prize"],
              item.level <= rest.receivedMaxLevel && Style["received-green"],
            ])}
          />
        ),
        customCircle: circleStatus && (
          <img
            src={circleIsGrey ? ReceivedPrizeIconImg : circleStatus}
            alt="完成"
            className={cls([
              Style["finish-img"],
              circleIsGrey && Style["grey"],
            ])}
          />
        ),
        otherData: {
          ...item,
          ...rest,
        },
      });
    });

    return newList;
  };

  // 数据初始化
  const initData = async () => {
    const eventCode = isHolidayReward ? "800013" : "800012";
    try {
      setLoading(true);
      const { data } = await getClockInPrizeConfig({
        eventCode,
        ignoreError: true,
      });
      if (
        data?.taskId ||
        data?.nodeId ||
        !!data?.milestoneLevelConfigVOs?.length
      ) {
        const timelineList = makePrizeList(data);
        setIsNetworkError(false);
        setShowList(timelineList);
      } else {
        // 如果没有任务、节点、配置信息的话，就显示网络异常，因为后续没法走流程了
        setIsNetworkError(true);
      }
    } catch (error) {
      setIsNetworkError(true);
      if (error?.code === ErrorCodeEnum.NO_NODE_ID) {
        setNoNodeIdError(true);
      } else {
        Toast.show(error?.msg || error?.message || error);
      }
      // 上报日志
      SafeLogger.baseLogger.error("receive-prize-config-error", {
        error,
        eventCode,
      });
    } finally {
      setLoading(false);
    }
  };

  // 领奖+未达标点击事件
  const handlePrizeClick = (prizeInfo: IClockInPrizeList, restConfig: any) => {
    if (canIReceivePrize(prizeInfo, restConfig)) {
      if (prizeInfo.level !== restConfig?.receivedMaxLevel + 1) {
        Toast.show("不要着急，奖品要按顺序一个个领取哦～");
        return;
      }
      // 能领取的情况下上报埋点和弹出领奖
      clickPv(
        "ewt_h5_study_course_self_learning_prize_drawer_receive_prize_button_click",
        {
          count: prizeInfo.targetValue,
          scene: originScene,
        },
      );

      // 如果在app内，且拿到了版本号，且版本号是目标版本, 且有节点信息
      if (
        mstJsBridge.isInMstApp() &&
        pageBaseInfo.current.version &&
        versionCompare(pageBaseInfo.current.version, "10.6.0") &&
        restConfig?.nodeId
      ) {
        const { nodeId, taskId, taskRewardLevelId } = restConfig;
        openRoute({
          domain: "user",
          action: "task_rewards",
          params: {
            // 接口如果没返回就传递一个0，原因是app内部没有对这个字段的缺省做处理，不传会导致app的crash
            nodeId: nodeId || 0,
            taskId,
            // 原因同上
            taskRewardLevelId: taskRewardLevelId || 1,
            processNum: processNum || 0,
            moduleName: "学生自主学习计划",
            rewardStatus: 1,
          },
        });
        // 如果去app领取就不继续执行h5的领取了
        return;
      }

      setReceiveBaseInfo({
        ...restConfig,
        ...prizeInfo,
      });
      if (commonReceiveRef?.current) {
        commonReceiveRef.current?.setVisible(true);
      }
    }
  };

  // 获取app版本
  const getAppVersion = async () => {
    try {
      // 这里不用通用的方法，以为是需要异步即可，不用同步锁
      const { data } = await mstJsBridge.getAppVersion();
      if (data?.version) {
        pageBaseInfo.current.version = data.version;
      }
    } catch (error) {
      // 不是核心链路，失败了就从h5领取，因此通过arms收集即可
      console.warn("获取app版本号失败", error);
    }
  };

  // 每次打开时奖品都可能变化，因此每次显示的时候都去请求最新的奖品信息
  useEffect(() => {
    // 页面可见了才去初始化数据
    if (visible) {
      expPv("ewt_h5_study_course_self_learning_prize_drawer_expo", {
        scene: originScene,
      });
      // 如果没有app版本号，且抽屉显示了就去请求app版本号
      if (!pageBaseInfo.current.version) {
        getAppVersion();
      }
      initData();
    } else {
      // 页面不可见需要清理下数据，已最新的为准
      setShowList([]);
    }
  }, [visible, updateState]);

  // 打开app后再回来刷新下奖励状态
  const handleRefreshConfig = (isShow: boolean) => isShow && forceUpdate();

  // 页面的可见/不可见监控，变更后查询最新的奖励状态
  useVisibilitychange({
    handleVisibilitychange: handleRefreshConfig,
  });

  return (
    <Popup
      bodyClassName={Style["receive-prize-drawer-box"]}
      visible={visible}
      onClose={() => handleClose()}
      title={isHolidayReward ? "假期限时自习打卡活动" : "查看奖励"}
      {...props}
    >
      {isHolidayReward ? (
        <p className={Style["holiday-rule-text"]}>
          2025/1/10-2/13寒假期间，自习打卡5天、8天、15天，可领寒假自习专属勋章！快喊你的小伙伴一起来自习打卡吧~
          <span
            className={Style["jump-rule-page"]}
            onClick={() =>
              openUrlInWebView(
                "https://web.ewt360.com/themeTemplateClient/index.html?id=1876174079872405505",
              )
            }
          >
            规则说明
          </span>
        </p>
      ) : (
        <p className={Style["rule-remark-text"]}>
          坚持进行每日学习打卡，完成以下阶梯任务，即可获得精美勋章和 e
          游记步数，快行动起来吧～
        </p>
      )}

      {/* loading */}
      {loading && (
        <div className={Style["loading-box"]}>
          <SpinLoading color="primary" />
          <span>加载中...</span>
        </div>
      )}

      {/* 如果是网络错误、数据不符合预期就展示错误界面等待刷新 */}
      {isNetworkError && !loading ? (
        <NetworkErrorStatus
          text={noNodeIdError ? "任务暂不支持查看和领取奖励，请稍后重试" : ""}
          buttonOption={{
            handleClick: () => initData(),
          }}
        />
      ) : null}

      {/* 如果数据正常，且加载完毕就展示横向时间轴的奖励配置 */}
      {!isNetworkError && !!showList?.length && !loading && (
        <HorizontalTimeLine
          initialSlide={pageBaseInfo.current.initialSlide}
          list={showList}
          className={Style["self-learning-prize"]}
        />
      )}

      {/* 前端H5的领取奖励抽屉 */}
      <ReceivePrizeCommonPopup
        ref={commonReceiveRef}
        receiveBaseInfo={receiveBaseInfo}
        closeBefore={() => initData()}
      />

      <MedalModal ref={medalModalRef} {...medalModalInfo} />
    </Popup>
  );
});
