.receive-prize-drawer-box {
  padding-bottom: 50px;

  // 奖励规则说明
  .rule-remark-text {
    font-size: 14px;
    line-height: 22px;
    color: #666;
    text-align: center;
    padding: 0 20px 30px;
  }

  .holiday-rule-text {
    font-size: 14px;
    line-height: 22px;
    color: #666;
    padding: 5px 20px 30px;

    .jump-rule-page {
      color: #2D86FE;
      cursor: pointer;
      margin-left: 15px;
    }
  }

  // 领奖按钮
  .receive-prize-button {
    padding: 5px 12px;

    // 按钮内部的容器
    & > div {
      font-size: 16px;
      font-weight: bold;
    }

    // 可以领奖状态
    &.have-prize {
      background-color: #FF605E;
    }

    // 未达标状态
    &.no-prize {
      background-color: transparent;

      & > div {
        color: #BFC8CF;
      }
    }
    &.received-green {
      background-color: transparent;

      & > div {
        color: #73D13D;
      }
    }
  }
}

// 奖品容器
.prize-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  // 勋章盒子
  .medal-box {
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;

    // 勋章图片
    & > img {
      max-width: 40px;
      max-height: 40px;
      display: block;
    }
  }

  // 没有获得时的灰色
  &.no-get img {
    filter: grayscale(100%);
  }
}

// 针对领奖的时间轴容器想做偏移
.self-learning-prize > div {
  padding-left: 15px;
}

// 时间轴上已完成的图片样式
.finish-img {
  width: 24px;
  height: 24px;
  border: 2px solid #fff;

  // 完成且已领取过的灰色样式
  &.grey {
    border: 0;
  }
}

.network-error {
  img {
    width: 160px!important;
    height: auto!important;
  }
}

.replay-button {
  padding: 5px 26px;
  margin-top: 10px;
}

.loading-box {
  width: 100%;
  height: 129px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  & > span {
    margin-top: 10px;
  }
}
