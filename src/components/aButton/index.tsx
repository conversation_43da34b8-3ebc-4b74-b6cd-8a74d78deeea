/** 统一封装的按钮组件 */

import * as React from "react";
import cls from "classnames";

import Style from "./style.module.scss";

export enum EType {
  BLUE = "blue",
  WHITE = "white",
}

export interface IButton {
  className?: string;
  // 按钮类型
  type: EType;
  text: string;
  onClick: () => void;
}

const Button = (props: IButton) => {
  const { className, type, text, onClick } = props;
  return (
    <div
      className={cls(
        Style["button"],
        {
          [Style["blue"]]: type === EType.BLUE,
          [Style["white"]]: type === EType.WHITE,
        },
        className,
      )}
      onClick={onClick}
    >
      {text}
    </div>
  );
};

export default Button;
