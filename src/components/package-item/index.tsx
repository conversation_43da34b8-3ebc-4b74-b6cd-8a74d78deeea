/** 全部计划、90天前已完成课程包的 item */

import * as React from "react";
import { ProgressBar } from "antd-mobile";

import { IPackageItem as IPackageDetailItem } from "@/service/home";
import finishPNG from "@/assets/common/icon-finish-icon.png";
import { EPackageStatus } from "@/routes/self-learning-home/common";

import { IconSvg } from "../icon-svg";

import Style from "./style.module.scss";
import { cls } from "@/utils/tool";

interface IPackageItem {
  className?: string;
  item: IPackageDetailItem;
  handleClick: (item: IPackageDetailItem) => void;
  hiddenProgress?: boolean;
}
export const PackageItem: React.FC<IPackageItem> = (props) => {
  const { item, className, handleClick, hiddenProgress = false } = props;
  return (
    <div
      className={cls([
        Style["package-item"],
        item.invalidation && Style["grey"],
        className,
      ])}
      onClick={() => handleClick(item)}
    >
      <div className={Style["package-item-left"]}>
        <div className={Style["package-item-left-img-wrapper"]}>
          <img src={item.coverImgUrl} />
        </div>
        <div className={Style["package-item-left-subjectName"]}>
          {item.subjectName}
        </div>
      </div>
      <div className={Style["package-item-right"]}>
        <div className={Style["package-item-right-name"]}>{item.title}</div>
        <div className={Style["package-item-right-info"]}>
          {item.invalidation ? (
            <div className={Style["invalidation"]}>计划已失效</div>
          ) : hiddenProgress ? null : item.finishStatus ===
            EPackageStatus.done ? (
            <div>共{item.totalCount ?? "-"}讲</div>
          ) : (
            <ProgressBar
              className={Style["progress"]}
              percent={(item.finishCount / item.totalCount) * 100}
              style={{
                "--fill-color":
                  "linear-gradient(270deg, #F96D05 0%, #FFC45A 100%)",
              }}
              text={`${item.finishCount || 0}/${item.totalCount}讲`}
            />
          )}
        </div>
        <div className={Style["package-item-right-teacher"]}>
          <div
            className={Style["name"]}
          >{`讲师：${(item.authorList || []).join("，")}`}</div>
          <IconSvg name="icon-jinru" />
        </div>
      </div>
    </div>
  );
};
