.package-item {
  width: 351px;
  height: 75px;
  background: #FFFFFF;
  display: flex;
  margin: 0 auto;
  margin-top: 12px;
  border-radius: 8px;
  padding: 5px 10px 5px 5px;

  &-left {
    width: 80px;
    height: 65px;
    border-radius: 4px;
    margin-right: 10px;

    &-img-wrapper {
      width: 80px;
      height: 45px;
      background: linear-gradient(90deg, #7EB5FF, #FFDAB7);
      border-radius: 4px 4px 0 0;
      img {
        width: 100%;
        height: 100%;
        border-radius: 4px 4px 0 0;
      }
    }

    &-subjectName {
      font-size: 12px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #DBE8F5;
      border-radius: 0 0 4px 4px;
    }
  }

  &-right {
    flex: 1;
    &-name {
      line-height: 22px;
      max-width: 236px;
      width: 236px;
      font-weight: bold;
      font-size: 14px;
      color: #333333;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    &-info {
      display: flex;
      line-height: 20px;
      font-weight: 400;
      font-size: 12px;
      color: #666666;
      margin-bottom: 3px;

      .progress {
        height: 20px;
        flex: 1;

        :global {
          .adm-progress-bar-trail {
            flex: 0 0 72px;
          }
          .adm-progress-bar-text {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 33px;
            font-size: 12px;
            color: #666666;
            line-height: 20px;
          }
        }
      }
    }
    &-teacher {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: 400;
      font-size: 12px;
      color: #666666;

      .name {
        flex: 1;
        line-height: 20px;
        max-width: 230px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      svg {
        font-size: 16px;
        flex: 0 0 auto;
      }
    }
  }
}


.grey {
  opacity: 0.3;
}
