import React from "react";
import classNames from "classnames";
import { Button } from "antd-mobile";
import styles from "./index.module.scss";
import ProgressIconPng from "@/assets/image/self-learning-for-subject/mission-progress-icon.png";

export interface MissionProgressProps {
  /** 进度百分比，范围 0-100 */
  percent: number;
  /** 是否显示问号图标 */
  showQuestion?: boolean;
  /** 问号图标点击事件 */
  onQuestionClick?: () => void;
  /** 自定义样式类名 */
  className?: string;
  /** 是否显示底部内容 */
  showBottom?: boolean;
  /** 总任务数 */
  total?: number;
  /** 已完成任务数 */
  done?: number;
  /** 是否显示刷新按钮 */
  showRefresh?: boolean;
  /** 是否正在刷新 */
  isRefreshing?: boolean;
  /** 刷新按钮点击事件 */
  onRefresh?: () => void;
  /** 是否显示添加按钮 */
  showAdd?: boolean;
  /** 添加按钮点击事件 */
  onClickAdd?: () => void;
  /** 是否显反馈按钮 */
  showFeedback?: boolean;
  /** 反馈按钮点击事件 */
  onClickFeedback?: () => void;
  /** 是否显示当周状态按钮 */
  showWeekStatus?: boolean;
  /** 当周状态按钮是否禁用 */
  disableWeekStatus?: boolean;
  /** 当周状态按钮点击事件 */
  onWeekStatusClick?: () => void;
  /** 图标颜色 */
  iconColor?: string;
  /** 图标大小 */
  iconSize?: number;
}

const MissionProgress: React.FC<MissionProgressProps> = ({
  percent,
  showQuestion = true,
  onQuestionClick,
  className,
  showBottom = false,
  total = 0,
  done = 0,
  showRefresh = false,
  isRefreshing = false,
  onRefresh,
  showAdd = false,
  onClickAdd,
  showFeedback = false,
  onClickFeedback,
  showWeekStatus = false,
  disableWeekStatus = false,
  onWeekStatusClick,
  iconColor = "",
}) => {
  // 确保百分比在 0-100 之间
  const formatPercent = Math.min(100, Math.max(0, percent));
  const showWord = `${done}/${total}`;

  return (
    <div className={classNames(styles.container, className)}>
      <div className={styles.top_container}>
        <div className={styles.top}>
          <div className={styles.per_bg}>
            <div
              className={styles.per_bar}
              style={{ width: `${formatPercent}%` }}
            ></div>
          </div>
          <div
            className={styles.per_bar_top}
            style={{ width: `${formatPercent}%` }}
          >
            <img
              src={ProgressIconPng}
              className={styles.bar_icon}
              style={{
                transform: `translate(${88 - 0.76 * formatPercent}% ,-50%)`,
              }}
            />
          </div>
        </div>
        {showQuestion && (
          <div className={styles.question} onClick={onQuestionClick}>
            <i className="iconfont iconwenhao1" />
          </div>
        )}
      </div>
      {showBottom && (
        <div className={styles.bottom}>
          <div className={styles.bottom_left}>
            <div
              className={styles.word_icon}
              style={iconColor ? { color: iconColor } : {}}
            >
              <i className="iconfont iconxingzhuangjiehe" />
            </div>
            <div className={styles.word}>
              {!total ? "无进度" : `计划总进度已完成`}
            </div>
            {total > 0 && (
              <div
                key={showWord}
                className={styles.percent}
                data-text={showWord}
              >
                {showWord}
              </div>
            )}
            {showRefresh && (
              <div
                className={styles.refresh}
                style={{ cursor: isRefreshing ? "unset" : "pointer" }}
                onClick={() => {
                  if (!isRefreshing) {
                    onRefresh?.();
                  }
                }}
              >
                {!isRefreshing ? (
                  <span>刷新进度</span>
                ) : (
                  <span>正在刷新...</span>
                )}
              </div>
            )}
            {showFeedback && (
              <Button
                color="primary"
                fill="none"
                onClick={onClickFeedback}
                className={styles.feedback_btn}
              >
                反馈
              </Button>
            )}
          </div>
          <div className={styles.bottom_right}>
            {showAdd && (
              <Button
                color="primary"
                fill="none"
                onClick={onClickAdd}
                className={styles.add_btn}
              >
                添加内容
              </Button>
            )}
            {showWeekStatus && (
              <Button
                color="primary"
                fill="none"
                onClick={onWeekStatusClick}
                disabled={disableWeekStatus}
                className={classNames(styles.week_btn, {
                  [styles.disable]: disableWeekStatus,
                })}
              >
                当周状态
              </Button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default MissionProgress;
