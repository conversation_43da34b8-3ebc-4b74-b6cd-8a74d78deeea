.container {
  width: 100%;
}

.top_container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 24px;
}

.top {
  flex: 1;
  position: relative;
  height: 8px;
  background: #f5f5f5;
  border-radius: 4px;
}

.per_bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  border-radius: 4px;
  border: 1px solid #021e66;
}

.per_bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: #1890ff;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.per_bar_top {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: #1890ff;
  border-radius: 4px;
  transition: width 0.3s ease;
  border: 1px solid #021e66;
}

.bar_icon {
  position: absolute;
  top: 50%;
  right: 0;
  width: 20px;
  height: 24px;
  transform: translateY(-50%);
  transition: transform 0.3s ease;
}

.question {
  margin-left: 8px;
  cursor: pointer;
  color: #999;
  font-size: 12px;
  :global {
    .iconfont {
      font-size: 12px;
    }

    .iconwenhao1 {
      color: #021e66;
      opacity: 0.5;
    }
  }
}

.bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.bottom_left {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
}

.word_icon {
  display: flex;
  align-items: center;
  :global {
    .iconfont {
      font-size: 12px;
      color: rgb(2, 30, 102, 0.25);
    }
  }
}

.word {
  color: #333;
  font-size: 12px;
}

.percent {
  color: #ffe776;
  font-size: 14px;
  font-weight: bold;
  text-shadow:
    -1px -1px 0 #000,
    1px -1px 0 #000,
    -1px 1px 0 #000,
    1px 1px 0 #000;
}

.refresh {
  color: #2F86FF;
  cursor: pointer;
  font-size: 12px;
  margin-left: 8px;
}

.bottom_right {
  display: flex;
  gap: 8px;
}

.add_btn,
.feedback_btn,
.week_btn {
  cursor: pointer;
  color: #2F86FF;
  padding: 0;
  font-size: 12px;
}
