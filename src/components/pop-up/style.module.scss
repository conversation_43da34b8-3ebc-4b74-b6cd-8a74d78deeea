.body {
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;

  .header {
    height: 60px;
    position: relative;
    box-sizing: border-box;
    padding-top: 16px;
    .close-icon {
      position: absolute;
      top: 16px;
      right: 20px;
      z-index: 1;
      font-size: 14px;
    }
    .title {
      top: 24px;
      left: 50%;
      width: 100%;
      transform: translateX(-50%);
      position: absolute;
      text-align: center;
      font-weight: bold;
      font-size: 18px;
      color: #333333;
    }
  }
}
