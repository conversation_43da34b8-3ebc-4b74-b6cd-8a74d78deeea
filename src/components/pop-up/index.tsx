/**
 * 统一的底抽
 */

import * as React from "react";
import { Popup as AntPopup, PopupProps } from "antd-mobile";

import { cls } from "@/utils/tool";
import { IconSvg } from "@/components";

import Style from "./style.module.scss";

export interface IPopup extends PopupProps {
  title?: string;
  /** 是否影藏头部 */
  hiddenHeader?: boolean;
  hiddenCancel?: boolean;
  onClose: () => void;
}

export const Popup: React.FC<IPopup> = (props) => {
  const {
    children,
    title,
    bodyClassName,
    hiddenHeader,
    onClose,
    closeOnMaskClick,
    hiddenCancel,
    ...rest
  } = props;

  return (
    <AntPopup
      showCloseButton={false}
      destroyOnClose
      position="bottom"
      onMaskClick={() =>
        typeof closeOnMaskClick === "undefined" || closeOnMaskClick
          ? onClose?.()
          : () => {}
      }
      closeOnMaskClick={closeOnMaskClick}
      bodyClassName={cls([Style["body"], bodyClassName])}
      {...rest}
    >
      {!hiddenHeader ? (
        <div className={Style["header"]}>
          {!hiddenCancel && (
            <div
              className={Style["close-icon"]}
              onClick={() => {
                onClose && onClose();
              }}
            >
              <IconSvg name="icon-guanbi" />
            </div>
          )}
          <div className={Style["title"]}>{title}</div>
        </div>
      ) : null}
      {children}
    </AntPopup>
  );
};
