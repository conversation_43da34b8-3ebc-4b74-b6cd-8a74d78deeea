/** 课程失效底抽 */

import * as React from "react";

import { Popup, But<PERSON> } from "@/components";
import type { IPopup } from "@/components";

import Style from "./style.module.scss";

interface ICourseInvalidatePopup extends IPopup {
  onOk: () => void;
}

export const CourseInvalidatePopup: React.FC<ICourseInvalidatePopup> = (
  props,
) => {
  const { onOk, ...rest } = props;
  return (
    <Popup title="课程失效" destroyOnClose {...rest}>
      <div className={Style["remove-package-pop"]}>
        该课程已失效，将从计划中移除
        <div className={Style["button"]}>
          <Button className={Style["ok"]} text="知道了" onClick={onOk} />
        </div>
      </div>
    </Popup>
  );
};
