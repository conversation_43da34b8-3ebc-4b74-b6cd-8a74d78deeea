/** 科目列表 */

import * as React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";

import type { ISubjectItem } from "@/service/home";
import { cls } from "@/utils/tool";

import Style from "./style.module.scss";
export interface ISubjectList {
  /** 受控模式 */
  value?: ISubjectItem;
  className?: string;
  itemClassName?: string;
  subjectList: ISubjectItem[];
  onChange?: (item: ISubjectItem) => void;
}

export const SubjectList: React.FC<ISubjectList> = (props) => {
  const { value, className, subjectList, itemClassName, onChange } = props;
  const swiperRef = React.useRef<Record<string, any>>();

  /** 选中某个学科 */
  function selectSubject(item: ISubjectItem, idx: number) {
    onChange?.(item);
    if (swiperRef?.current) {
      swiperRef.current?.slideTo(idx - 1);
    }
  }

  return (
    <Swiper
      slidesPerView={"auto"}
      centeredSlides={false}
      pagination={false}
      onSwiper={(swiper) => {
        swiperRef.current = swiper;
      }}
      className={cls([Style["subjects-swiper"], className])}
    >
      {(subjectList || []).map?.((item: ISubjectItem, index: number) => (
        <SwiperSlide key={`${item.parentCode}-${item.code}`}>
          <div
            className={cls([
              itemClassName,
              Style["subject-item"],
              value?.parentCode === item.parentCode &&
                value?.code === item.code &&
                Style["beSelected"],
            ])}
            onClick={() => selectSubject(item, index)}
          >
            {item.name}
          </div>
        </SwiperSlide>
      ))}
    </Swiper>
  );
};
