import React, { CSSProperties } from "react";
import ErrorPng from "@/assets/common/error.png";
import { cls } from "@/utils/tool";

import styles from "./index.module.scss";

const ErrorInfo = ({
  icon,
  style,
  type,
  desc,
  retryText,
  confirmText,
  onRetry,
  onConfirm,
}: {
  icon?: string;
  style?: CSSProperties;
  type?: "light" | "dark";
  desc?: string;
  retryText?: string;
  confirmText?: string;
  onRetry: () => void;
  onConfirm?: () => void;
}) => {
  const isLight = type === "light";
  return (
    <div
      className={cls([styles.container, isLight && styles.light])}
      style={style}
    >
      <img src={icon || ErrorPng} />
      <div className={styles.word}>
        {desc || "啊哦～发生了未知异常，请稍后重试"}
      </div>
      <div className={styles.btns_container}>
        <div className={styles.btn} onClick={onRetry}>
          {retryText || "重新加载"}
        </div>
        {!!onConfirm && (
          <div className={styles.btn} onClick={onConfirm}>
            {confirmText || "我知道了"}
          </div>
        )}
      </div>
    </div>
  );
};
export default ErrorInfo;
