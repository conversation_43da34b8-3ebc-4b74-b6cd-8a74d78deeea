import React, { ReactNode, useEffect } from "react";
import { NavBar } from "antd-mobile";
import BackArrowImg from "@/assets/image/subject-exercise/back-arrow.png";
import { useNavigate } from "react-router-dom";
// import { useStores } from "@/hooks";
import styles from "./index.modules.scss";

/**
 * 顶部的导航栏props
 * @param props
 * @params children ReactNode 子元素
 * @params backArrow ReactNode 返回按钮
 * @params left ReactNode 返回按钮右侧内容
 * @params right ReactNode 最右侧区域内容
 * @params className string class样式
 * @params back string | null 返回区域的文字，如果为 null 的话，backArrow 也不会渲染
 * @params onClose function 关闭view时触发的回调
 *
 */
interface TopNavBarProps {
  back?: string | null;
  backArrow?: boolean | ReactNode;
  children?: ReactNode;
  left?: ReactNode;
  onlyCallBack?: boolean;
  noAndroidBack?: boolean;
  onlyCallClose?: boolean;
  onBack?: () => void;
  right?: ReactNode;
  className?: string;
  onClose?: () => void;
}

/**
 * 默认的箭头
 */
const defaultBackArrow = () => (
  <img src={BackArrowImg} className={styles.backArrowImg} />
);

const TopNavBar: React.FC<TopNavBarProps> = (props) => {
  const {
    children,
    backArrow = defaultBackArrow(),
    left,
    right,
    onBack,
    className = "",
    back,
    onlyCallBack,
    onlyCallClose,
    noAndroidBack,
    onClose,
  } = props;
  const history = useNavigate();
  // const tools = useStores("tools");

  /**
   * 处理返回逻辑
   * 如果调用者传参了则已参数为准，否则默认逻辑为回退，当历史记录到底时会提示关闭webview？
   */
  const handleBack = () => {
    if (onlyCallClose) {
      onClose && onClose();
      return;
    }
    // tools.popHistory();
    // const historyList = tools.getHistory(); // 当前记录下来的浏览历史
    // 历史列表为空（刷新也会被清空，同样关闭）
    // if (historyList.length <= 0) {
    //   try {
    //     onClose && onClose();
    //     // 通知客户端要刷新页面了
    //     // 在安卓上这里有可能出现：webview都退出了还没执行此代码，因此每次监听到物理返回按键都通知下刷新，正常关闭也通知
    //     window.mstJsBridge.getNativeData({
    //       domain: "systemInfo",
    //       action: "nativeRefresh",
    //       params: {},
    //     });

    //     // 调用多个路由时延迟100毫秒关闭，给上一个路由留一点时间
    //     window.setTimeout(() => {
    //       window.mstJsBridge.closeWebview();
    //     }, 100);
    //   } catch (error) {}
    // } else {
    //   onBack && onBack();
    //   if (onlyCallBack) {
    //     return;
    //   }
    //   // history.goBack();
    // }
  };

  const listenerAndroidBackkey = (event: any) => {
    if (noAndroidBack) {
      return;
    }
    // event对象包含当前全局bridge对象 event.bridge
    try {
      window.mstJsBridge.getNativeData({
        domain: "systemInfo",
        action: "nativeRefresh",
        params: {},
      });
    } catch (error) {
      console.log("通知刷新出错");
    }
    handleBack();
  };

  // 全局监听安卓物理返回按键
  useEffect(() => {
    document.addEventListener("androidBackKey", listenerAndroidBackkey, false);
    return () => {
      document.removeEventListener(
        "androidBackKey",
        listenerAndroidBackkey,
        false,
      );
    };
  }, []);

  return (
    <div className={styles.topNavBarContainer}>
      <NavBar
        back={back}
        backArrow={backArrow}
        left={left}
        right={right}
        onBack={handleBack}
        className={className}
      >
        {children}
      </NavBar>
    </div>
  );
};

export default TopNavBar;
