import React from "react";
import { Mask, SpinLoading } from "antd-mobile";
import { cls } from "@/utils/tool";

import styles from "./index.modules.scss";

interface LoadingProps {
  className?: string;
  visible?: boolean;
  maskStyle?: any;
  overlayContentClassName?: string;
  getContainer?: () => HTMLElement;
}

const PageLoading: React.FC<LoadingProps> = (props) => {
  const {
    className,
    visible,
    maskStyle,
    overlayContentClassName,
    getContainer,
  } = props;
  return (
    <Mask
      className={className}
      visible={visible}
      style={maskStyle}
      getContainer={getContainer}
    >
      <div className={cls([styles.overlayContent, overlayContentClassName])}>
        <SpinLoading color="primary" />
      </div>
    </Mask>
  );
};

export default PageLoading;
