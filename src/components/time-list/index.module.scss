.container {
  width: 100%;
  height: 100%;
  background: #fff;
  box-sizing: border-box;

  :global {
    .swiper {
      width: 100%;
      height: 100%;
    }

    .swiper-slide {
      width: auto;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.item {
  min-width: 102px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  position: relative;
  padding: 0 8px;
  cursor: pointer;
  background-color: #E2EEFF;
  border-radius: 8px;
  margin-right: 8px;
  margin-bottom: 8px;

  &_finish {
    width: 16px;
    height: 16px;
    position: absolute;
    top: 0;
    right: 0;
  }

  &_title {
    font-size: 16px;
    color: rgb(2, 30, 102, .65);
    text-align: center;
    font-weight: bold;
  }

  &_desc {
    font-size: 14px;
    color: rgb(2, 30, 102, .65);
    text-align: center;
  }

  &.active {
    background-color: #fff;
    border: 1px solid #021E66;
    box-shadow: 4px 4px 0 0 #021e6626;

    .item_title {
      color: #021E66;
      font-weight: bold;
    }

    .item_desc {
      color: #1890ff;
    }
  }
}

.more {
  font-size: 14px;
  color: #999;
  padding: 0 8px;
  text-align: center;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}