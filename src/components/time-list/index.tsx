import React, { useEffect, useRef } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import classNames from "classnames";
import dayjs from "dayjs";
import styles from "./index.module.scss";
import { LearnModelEnum } from "@/service/desk/plan-resource";

export interface ITimeListItem {
  /** 标题 */
  title: string;
  /** 开始时间戳 */
  startTime?: number;
  /** 结束时间戳 */
  endTime?: number;
  /** 天数 */
  day?: number;
  /** 是否完成 */
  isDone?: boolean;
  /** 自定义数据 */
  [key: string]: any;
}

export interface TimeListProps {
  /** 列表数据 */
  list: ITimeListItem[];
  /** 当前选中的项 */
  currentItem: ITimeListItem;
  /** 是否显示完成图标 */
  showDoneIcon?: boolean;
  /** 完成图标 */
  doneIcon?: string;
  /** 点击事件 */
  onChange: (item: ITimeListItem) => void;
  /** 自定义样式类名 */
  className?: string;
  /** 是否显示最后的更多提示 */
  showMore?: boolean;
  /** 当前时间标题, 默认是今天，周模式是本周 */
  currentTimeTitle?: string;
}

const formatDay = (timestamp: number) => {
  return dayjs(Number(timestamp)).format("MM-DD");
};

const TimeList: React.FC<TimeListProps> = ({
  list,
  currentItem,
  showDoneIcon = true,
  doneIcon,
  onChange,
  className,
  showMore = false,
  currentTimeTitle = "今天",
}) => {
  const swiperRef = useRef<any>();
  const count = list.length;

  const onItemClick = (index: number, item: ITimeListItem) => {
    swiperRef.current.slideTo(index);
    onChange?.(item);
  };

  useEffect(() => {
    setTimeout(() => {
      if (swiperRef.current) {
        const index = list.findIndex(
          (item) => item.startTime === currentItem.startTime,
        );
        if (index > -1) {
          swiperRef.current.slideTo(index);
        }
      }
    }, 100);
  }, []);

  // 渲染描述
  const renderDesc = (item: ITimeListItem) => {
    // 天模式
    if (item.mode === LearnModelEnum.DAY) {
      // 如果是当前天，那么展示默认的文案，否则展示天的值
      return item.isCurrent ? currentTimeTitle : formatDay(item.startTime);
    }
    // 周模式
    if (item.startTime && item.endTime) {
      // 同 天模式
      return item.isCurrent
        ? currentTimeTitle
        : `${formatDay(item.startTime)}至${formatDay(item.endTime)}`;
    }
    return "";
  };

  return (
    <div className={classNames(styles.container, className)}>
      <Swiper
        onSwiper={(swr) => {
          swiperRef.current = swr;
        }}
        slidesPerView="auto"
        centeredSlides
        centeredSlidesBounds
        threshold={10}
      >
        {list.map((item, index) => (
          <SwiperSlide key={index} virtualIndex={index}>
            <div
              key={index}
              className={classNames(styles.item, {
                [styles.active]: item.startTime === currentItem.startTime,
              })}
              onClick={() => onItemClick(index, item)}
            >
              {showDoneIcon && item.isDone && (
                <img
                  src={doneIcon}
                  className={styles.item_finish}
                  alt="已完成"
                />
              )}
              <div className={styles.item_title}>{item.title}</div>
              <div className={styles.item_desc}>{renderDesc(item)}</div>
            </div>
          </SwiperSlide>
        ))}
        {showMore && (
          <SwiperSlide key={count} virtualIndex={count}>
            <div className={styles.more}>更远的事情未来再考虑吧~</div>
          </SwiperSlide>
        )}
      </Swiper>
    </div>
  );
};

export default TimeList;
