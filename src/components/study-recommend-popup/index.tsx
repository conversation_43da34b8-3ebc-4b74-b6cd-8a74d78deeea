import React from "react";
import styles from "./style.module.scss";
import RecommendImg from "@/assets/common/eClass-guidance.png";
import { Button } from "@/components";
import { cls, openRoute } from "@/utils/tool";

export interface IStudyRecommendPopup {
  onOk?: () => void;
  imgSrc?: string;
  noPadding?: boolean;
  showTip?: boolean;
  showButton?: boolean;
  onlyOnOk?: boolean;
  buttonText?: string;
  buttonClassName?: string;
}

export const StudyRecommendPopup: React.FC<IStudyRecommendPopup> = (props) => {
  const {
    onOk,
    noPadding,
    showTip = true,
    showButton = true,
    imgSrc,
    onlyOnOk = false,
    buttonText = "学会了，前往E讲堂添加",
    buttonClassName,
  } = props;
  return (
    <div
      className={cls([
        styles["study-recommend-popup-content-box"],
        noPadding && styles["no-padding"],
      ])}
    >
      {showTip && (
        <div className={styles["tip-text"]}>
          此添加方式只支持版本号为10.6.5的升学E网通APP
        </div>
      )}
      <img src={imgSrc || RecommendImg} alt="loading..." />
      {showButton && (
        <Button
          text={buttonText}
          className={cls([styles["to-study-button"], buttonClassName])}
          onClick={() => {
            try {
              onOk?.();
              if (!onlyOnOk) {
                openRoute({
                  domain: "main_page",
                  action: "open_main_page_tab",
                  params: {
                    type: 1,
                    subjectId: 1,
                  },
                });
              }
            } catch (error) {}
          }}
        />
      )}
    </div>
  );
};
