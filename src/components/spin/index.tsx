/** 统一的 loading 组件 */

import * as React from "react";
import { SpinLoading, Mask } from "antd-mobile";
import type { MaskProps } from "antd-mobile";

import Style from "./style.module.scss";

export interface ISpin extends MaskProps {}

export const Spin: React.FC<ISpin> = (props) => {
  return (
    <Mask className={Style["spin"]} color="white" destroyOnClose {...props}>
      <SpinLoading />
    </Mask>
  );
};
