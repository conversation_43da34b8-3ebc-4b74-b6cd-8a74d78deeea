import { Mask } from "antd-mobile";
import React from "react";
import styles from "./index.module.scss";
import CloseImg from "@/assets/common/close-black-aircle.png";
import MedalDetail from "./medal-detail";
import { IMedalData } from "@/service/medal";

interface ICustomizeMedalModal {
  visible?: boolean;
  onCancel: () => void;
  medalGroupId?: string; // 勋章组id，不传代表单个勋章
  medalCode: string; // 勋章编号
  onRefreshMedalList?: (medal: IMedalData) => void;
}

const CustomizeMedalModal: React.FC<ICustomizeMedalModal> = (props) => {
  const { visible, onCancel, medalCode, medalGroupId, onRefreshMedalList } =
    props;

  return (
    <Mask visible={visible}>
      <div className={styles.customizeMedalModalContainer}>
        <img
          className={styles.closeBtn}
          src={CloseImg}
          alt="关闭"
          onClick={onCancel}
        />
        <div className={styles.outerBox}>
          <div className={styles.innerLayerBox}>
            {!!medalCode && (
              <MedalDetail
                key={medalCode}
                medalCode={medalCode}
                medalGroupId={medalGroupId}
                onRefreshMedalList={onRefreshMedalList}
              />
            )}
          </div>
        </div>
      </div>
    </Mask>
  );
};

export default CustomizeMedalModal;
