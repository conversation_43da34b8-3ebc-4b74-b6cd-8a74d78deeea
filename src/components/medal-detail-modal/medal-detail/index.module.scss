.medal-list-box {
  height: 100%;
  position: relative;

  .medal-detail-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    padding-top: 34px;
    padding-bottom: 24px;

    .medal-image-box {
      width: 240px;
      height: 240px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;

      .medal-img {
        max-width: 240px;
        max-height: 240px;
        background-color: transparent;

        &.grey {
          filter: grayscale(100%);
        }
      }

      .level-frame-box {
        width: 48px;
        height: 18px;
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        z-index: 1;

        .level-frame {
          width: 48px;
          height: 18px;
          position: absolute;
          left: 50%;
          bottom: 0;
          transform: translateX(-50%);

          &+span {
            position: absolute;
            bottom: 0;
            display: block;
            left: 50%;
            width: 48px;
            height: 18px;
            transform: translateX(-50%);
            text-align: center;
            line-height: 18px;
            font-size: 14px;
            color: #fff;
            font-weight: bold;
          }
        }
      }
    }

    .medal-info {
      font-size: 14px;
      text-align: center;
      margin-top: 20px;

      .medal-name-box {
        display: flex;
        align-items: center;
        justify-content: center;
        max-width: 280px;

        .medal-name {
          font-weight: bold;
          font-size: 18px;
          color: rgba(0, 0, 0, .8);
          line-height: 18px;

          &+span {
            display: block;
            padding: 0 5px;
            height: 18px;
            line-height: 18px;
            color: #fff;
            margin-left: 4px;
            background-image: linear-gradient(270deg, #FD5E40 1%, #FE3649 99%);
            border-radius: 4px;
          }
        }
      }

      .medal-code {
        color: rgba(0, 0, 0, .45);
        line-height: 12px;
        min-height: 14px;
        font-size: 12px;
        margin-top: 8px;
      }

      .medal-describe {
        color: rgba(0, 0, 0, .8);
        margin-top: 24px;
        max-width: 280px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        &.link-text {
          color: #FE3649;
        }
      }

      .medal-get-time {
        color: rgba(0, 0, 0, .45);
        font-size: 12px;
        margin-top: 4px;
        min-height: 16.8px;
      }

      .no-get-medal {
        height: 84px;
      }
    }
  }

  .medal-group-swiper {
    width: 100%;
    height: 240px;
    flex-shrink: 0;

    &>div>div {
      width: 240px !important;
    }
  }

  .wear-medal-ing {
    display: inline-block;
    padding: 4px 10px;
    border-radius: 0px 0px 8px 8px;
    color: #fffffffc;
    transform: scaleY(-1);
    font-size: 16px;
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #FE3649;
    z-index: 3;
  }

  // 确认按钮
  .confirm-button {
    outline: none;
    width: 180px;
    height: 48px;
    border-radius: 24px;
    background-color: rgba(0, 0, 0, .5);
    border: 1px solid rgba(255, 255, 255, 0.8);
    color: #fff;
    font-size: 18px;
    font-weight: bold;
    position: absolute;
    z-index: 2;
    left: 50%;
    bottom: -94px;
    transform: translate(-50%, -50%);

    &.active {
      background-image: linear-gradient(270deg, #FD5E40 1%, #FE3649 99%);
      box-shadow: 0 0 12px 0 #FD5C41;
    }

    &.cancel {
      background-color: rgba(0, 0, 0, 0.8);
      border-width: 1px;
      border-style: solid;
      border-color: rgba(255, 255, 255, 0.8);
      border-radius: 24px;
      color: rgba(255, 255, 255, 0.8);
      font-weight: bold;
      font-size: 18px;
    }
  }
}