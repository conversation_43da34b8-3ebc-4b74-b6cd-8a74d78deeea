import React, { useEffect, useRef, useState } from "react";
import {
  getMedalDetailByCodeOrGroupId,
  postWearMedal,
  postCancelWearMedal,
  IMedalData,
} from "@/service/medal";
import styles from "./index.module.scss";
import { cls } from "@/utils/tool";
import dayjs from "dayjs";
import { Swiper, SwiperSlide } from "swiper/react";
import LevelFrameImg from "@/assets/medal/level-frame.png";
import "swiper/css";
import { Toast } from "antd-mobile";
import SafeLogger from "@/utils/safe-logger";
import { openWebView, refreshUserMedal } from "@/utils/bridge-utils";
import LoadingImage from "@/components/loading-image";
import MedalDefaultPng from "@/assets/medal/medal-default.png";

interface IMedalDetail {
  medalGroupId?: string; // 勋章组id，不传代表单个勋章
  medalCode: string; // 勋章编号
  onRefreshMedalList?: (medal: IMedalData) => void;
}

const MedalDetail: React.FC<IMedalDetail> = (props) => {
  const { medalCode, medalGroupId, onRefreshMedalList } = props;
  const [medalList, setMedalList] = useState<IMedalData[]>([]);
  const [currentMedalInfo, setCurrentMedalInfo] = useState<IMedalData>();
  const [initialSlide, setInitialSlide] = useState(0); // 默认显示第0个
  const [currentSwiperIndex, setCurrentSwiperIndex] = useState(0);
  const [swiperObj, setSwiperObj] = useState<any>();
  const loggerRef = useRef<SafeLogger>();
  const wearMedalIngRef = useRef(false); // 佩戴勋章执行中，默认false

  // 查询勋章详情
  const getMedalDetail = async (toIndex?: number) => {
    try {
      const listRes = await getMedalDetailByCodeOrGroupId({
        medalId: medalCode,
        groupId: medalGroupId,
      });
      const list = listRes?.data || [];
      if (list?.length) {
        // 遍历寻找是否有获得并且勋章等级是最大的，没找到就是 -1
        const resultObj = (list || []).reduce(
          (acc, current, index) => {
            if (!!current.isReceived && current.level > acc.maxLevel) {
              return {
                maxLevel: current.level,
                maxIndex: index,
                isWearLevel: current.isWear ? index : acc.isWearLevel,
              };
            } else {
              return acc;
            }
          },
          { maxLevel: -1, maxIndex: -1, isWearLevel: -1 },
        );

        // 默认位置未当前索引，如果toIndex在代表刷新，到指定位置，如果佩戴等级不为空则定位到佩戴的
        // 如果获得的最大等级不为空，则定位到最大等级的
        let positionIndex = currentSwiperIndex;
        if (toIndex) {
          positionIndex = toIndex;
        } else if (resultObj.isWearLevel !== -1) {
          positionIndex = resultObj.isWearLevel;
        } else if (resultObj.maxIndex !== -1) {
          positionIndex = resultObj.maxIndex;
        }

        const showInfo = (list[positionIndex] || {}) as IMedalData; // 指定位置的信息
        setCurrentMedalInfo({ ...showInfo });
        setInitialSlide(positionIndex);
        setMedalList(list);
      } else {
        loggerRef.current?.error("medal-detail-response-error", {
          error: list,
        });
      }
    } catch (error: any) {
      // 加载勋章列表出错时上报sls
      loggerRef.current?.error("load-medal-detail-error", { error }); // 加载勋章列表出错时上报sls
    }
  };

  const medalGroupChange = (swiper: any) => {
    const currentIndx = swiper.activeIndex; // swiper返回的索引值，从0开始
    const curMedalInfo = medalList[currentIndx];
    setCurrentMedalInfo(curMedalInfo);
    setCurrentSwiperIndex(currentIndx);
  };

  /**
   * 勋章Item
   * @param item 勋章对象
   */
  const medalGroupItem = (item: IMedalData) => (
    <div className={styles.medalImageBox}>
      <LoadingImage
        src={item.mainImgUrl}
        fallback={MedalDefaultPng}
        className={cls([styles.medalImg, !item.isReceived && styles.grey])}
      />

      {/* 勋章组时才显示勋章等级 */}
      {Boolean(item.level) && (
        <div className={styles.levelFrameBox}>
          <img src={LevelFrameImg} alt="" className={styles.levelFrame} />
          <span>LV{item.level}</span>
        </div>
      )}
    </div>
  );

  /**
   * 佩戴勋章按钮点击
   */
  const handleWearButtonClick = async () => {
    try {
      if (currentMedalInfo) {
        const { isWear, isReceived, code } = currentMedalInfo;
        if (!isReceived) {
          return; // 暂未获得点击时不处理
        }
        if (wearMedalIngRef.current) {
          return;
        }
        wearMedalIngRef.current = true; // 点击就改变状态，防止多次请求
        const targetApi = isWear ? postCancelWearMedal : postWearMedal;
        await targetApi({
          medalId: code,
        }); // 佩戴 / 取消 勋章
        Toast.show(isWear ? "勋章已取下" : "佩戴成功");
        currentMedalInfo.isWear = !isWear; // 佩戴状态取反操作
        setCurrentMedalInfo(currentMedalInfo);
        wearMedalIngRef.current = false; // 佩戴完成后重置加载中的状态，以供下次请求

        // 成功后的下方列表刷新和当前勋章刷新时机提前，无需等app的调用，以防时间过长影响页面操作
        onRefreshMedalList?.(currentMedalInfo);
        getMedalDetail(currentSwiperIndex);

        refreshUserMedal({
          ...currentMedalInfo,
          currentWear: currentMedalInfo.isWear,
        });
      }
    } catch (error: any) {
      Toast.show("佩戴失败");
      wearMedalIngRef.current = false; // 完成动作后重置状态
      // 加载勋章列表出错时上报sls
      loggerRef.current?.error("load-medal-wear-error", { error });
    }
  };

  // 判断文本是否为url链接
  const checkIsUrl = (text: string) => {
    const newText = text?.trim() || ""; // 去除前后空格，以防格式错误
    const reg = /^https?:\/\//;
    return !!newText && reg.test(newText);
  };

  // 勋章说明转换，链接类型说明转换为固定文案：查看倾向
  const medalDescribeCovert = (text: string) => {
    const isUrl = checkIsUrl(text);
    return isUrl ? "点击查看详情>" : text;
  };

  // 勋章说明点击
  const handleDescClick = () => {
    const urlText = currentMedalInfo?.medalDescribe || "";
    const newUrl = urlText?.trim();
    const isUrl = checkIsUrl(newUrl);
    if (isUrl) {
      openWebView(newUrl); // 这里以落地页的名称为主（周一确认是否均为专题模板）
    }
  };

  // swiper的初始化完毕事件，可以拿到swiper对象
  const onSwiper = (swiper: any) => setSwiperObj(swiper);

  useEffect(() => {
    loggerRef.current = new SafeLogger("my-medal-list");
    getMedalDetail();
  }, []);

  // 定位最大等级
  useEffect(() => {
    // 只有在勋章列表已请求结束，并且swiper初始化完毕时执行
    if (medalList?.length && swiperObj) {
      try {
        const curInfo = medalList[initialSlide];
        setCurrentMedalInfo(curInfo);
        swiperObj.slideTo(initialSlide, 0);
      } catch (error) {
        console.log("定位出错", error);
      }
    }
  }, [medalList, initialSlide, swiperObj]);

  return (
    <div className={styles.medalListBox}>
      <div className={styles.medalDetailContainer}>
        {/* 勋章图、勋章等级展示区域 */}
        <Swiper
          initialSlide={initialSlide}
          slidesPerView="auto"
          spaceBetween={0}
          centeredSlides={true}
          className={styles.medalGroupSwiper}
          onSlideChangeTransitionStart={medalGroupChange}
          onSwiper={onSwiper}
        >
          {medalList.map((item: IMedalData, index: number) => (
            <SwiperSlide key={`${item.code}_${item.groupId}`}>
              {medalGroupItem(item)}
            </SwiperSlide>
          ))}
        </Swiper>

        {/* 勋章信息区域，包括名称、说明、编号等 */}
        {currentMedalInfo && (
          <div className={styles.medalInfo}>
            <p className={styles.medalNameBox}>
              <span className={styles.medalName}>{currentMedalInfo.name}</span>
              {currentMedalInfo.medalNumber > 1 && (
                <span>x{currentMedalInfo.medalNumber}</span>
              )}
            </p>

            <p className={styles.medalCode}>
              {!!currentMedalInfo.isReceived
                ? `勋章编号：${currentMedalInfo.code}`
                : ""}
            </p>
            <p
              className={cls([
                styles.medalDescribe,
                checkIsUrl(currentMedalInfo.medalDescribe || "") &&
                  styles.linkText,
              ])}
              onClick={handleDescClick}
            >
              {medalDescribeCovert(currentMedalInfo.medalDescribe)}
            </p>
            <p className={styles.medalGetTime}>
              {!!currentMedalInfo.isReceived
                ? dayjs(+currentMedalInfo.awardDate).format("YYYY.MM.DD")
                : ""}
            </p>
          </div>
        )}
      </div>
      {/* 佩戴中状态 */}
      {currentMedalInfo?.isWear && (
        <span className={styles.wearMedalIng}>佩戴中</span>
      )}
      {/* 佩戴按钮 */}
      <button
        onClick={handleWearButtonClick}
        className={cls([
          styles.confirmButton,
          !currentMedalInfo?.isWear &&
            !!currentMedalInfo?.isReceived &&
            styles.active,
          currentMedalInfo?.isWear && styles.cancel,
        ])}
      >
        {!!currentMedalInfo &&
          (!!currentMedalInfo.isWear
            ? "取消佩戴"
            : !!currentMedalInfo.isReceived
              ? "佩戴勋章"
              : "暂未获得")}
      </button>
    </div>
  );
};

export default React.memo(MedalDetail);
