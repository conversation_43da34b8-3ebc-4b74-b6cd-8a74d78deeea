/** 统一封装的按钮组件 - 蓝、白风格 */

import * as React from "react";
import { cls } from "@/utils/tool";

import Style from "./blue-white-style.module.scss";

export enum EType {
  BLUE = "blue",
  WHITE = "white",
}

export interface IButton {
  className?: string;
  // 按钮类型
  type: EType;
  text: string | React.ReactNode;
  onClick: () => void;
}

const Button = (props: IButton) => {
  const { className, type, text, onClick } = props;
  return (
    <div
      className={cls([
        Style["button"],
        type === EType.BLUE && Style["blue"],
        type === EType.WHITE && Style["white"],
        className,
      ])}
      onClick={onClick}
    >
      {text}
    </div>
  );
};

export default Button;
