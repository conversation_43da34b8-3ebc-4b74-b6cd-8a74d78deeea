/**
 * 统一的按钮组件
 */
import * as React from "react";

import { SpinLoading } from "antd-mobile";

import { cls } from "@/utils/tool";

import Style from "./style.module.scss";

export enum EButtonType {
  Blue,
  grey,
}
export interface IButton {
  id?: string;
  icon?: React.ReactNode;
  loading?: boolean;
  className?: string;
  type?: EButtonType;
  // 按钮文案
  text: string | React.ReactNode;
  onClick?: (e: any) => void;
}
export const Button: React.FC<IButton> = (props) => {
  const {
    text,
    className,
    type = EButtonType.Blue,
    onClick,
    icon,
    id = "",
    loading = false,
  } = props;
  const isGrey = type === EButtonType.grey;
  return (
    <div
      className={cls([Style["button"], isGrey && Style["grey"], className])}
      onClick={(e: any) => {
        if (loading) return;
        onClick?.(e);
      }}
      id={id}
    >
      <div className={cls([Style["text"], isGrey && Style["grey-text"]])}>
        {icon}
        {loading ? (
          <SpinLoading
            style={{ "--size": "18px", marginRight: 6 }}
            color={isGrey ? "#666" : "#fff"}
          />
        ) : null}
        <span>{text}</span>
      </div>
    </div>
  );
};
