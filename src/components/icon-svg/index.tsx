import * as React from "react";

import { cls } from "@/utils/tool";
interface IIconSvg extends React.SVGProps<SVGSVGElement> {
  /** icon svg 的名字，直接从 iconfont 上复制就行 */
  name: string;
}

export const IconSvg: React.FC<IIconSvg> = (props) => {
  const { name, className, ...rest } = props;
  return (
    <svg className={cls(["svg-icon", className])} aria-hidden="true" {...rest}>
      <use xlinkHref={`#${name}`}></use>
    </svg>
  );
};
