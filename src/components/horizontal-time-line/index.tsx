import React, { ReactNode, useRef } from "react";
import { cls } from "@/utils/tool";
import { Swiper, SwiperSlide } from "swiper/react";
import Style from "./index.module.scss";

/** 每一项的定义 */
export interface IHorizontalTimeLineConfig {
  topContent?: ReactNode | string;
  bottomContent?: ReactNode | string;
  customCircle?: ReactNode;
  className?: string;
  /** 如果需要传递其他参数的数据，可以使用此属性 */
  otherData?: any;
}

export interface IHorizontalTimeLineParam {
  list: IHorizontalTimeLineConfig[];
  className?: string;
  handleClick?: (item: any, index: number) => void;
  initialSlide?: number;
}

/** 横向时间轴 */
const HorizontalTimeLine: React.FC<IHorizontalTimeLineParam> = (props) => {
  const { list = [], className, handleClick, initialSlide } = props;
  /** swiper 对象 */
  const swiperRef = useRef<any>(null);
  if (!list?.length) {
    // 没数据就不渲染了
    return null;
  }

  const items = () =>
    list.map((item: IHorizontalTimeLineConfig, index: number) => {
      const {
        topContent = "",
        bottomContent = "",
        customCircle,
        className = "",
      } = item;
      return (
        <SwiperSlide key={`${index + 1}`}>
          <div
            className={cls([
              Style["swiper-slide-box"],
              index === 0 && Style["first-child"],
              className,
            ])}
            onClick={() => handleClick?.(item?.otherData, index)}
          >
            {topContent}
            <div className={Style["split-box"]}>
              <hr
                className={cls([
                  Style["hr-line"],
                  index === 0 && Style["first-child"],
                ])}
              />
              {customCircle || <i className={Style["circle-point"]} />}
              <hr
                className={cls([
                  Style["hr-line"],
                  index === list.length - 1 && Style["last-child"],
                ])}
              />
            </div>
            {bottomContent}
          </div>
        </SwiperSlide>
      );
    });

  return (
    <div className={cls([Style["horizontal-time-line-box"], className])}>
      <Swiper
        initialSlide={initialSlide || 0}
        slidesPerView={"auto"}
        centeredSlides={false}
        pagination={false}
        onSwiper={(swiper) => {
          swiperRef.current = swiper;
        }}
        className={Style["horizontal-time-line-swiper"]}
      >
        {items()}
      </Swiper>
    </div>
  );
};

export default HorizontalTimeLine;
