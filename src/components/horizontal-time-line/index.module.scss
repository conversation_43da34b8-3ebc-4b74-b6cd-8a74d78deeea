.horizontal-time-line-box {

  .horizontal-time-line-swiper {
    // 将slide的样式设置为格局内容来
    :global {
      .swiper-slide {
        width: auto;
      }
    }

    // 每个slide容器的设置
    .swiper-slide-box {
      display: flex;
      justify-content: center;
      flex-direction: column;
      align-items: center;
      min-width: 80px;

      // 中间的分割容器，有点和线
      .split-box {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        padding: 4px 0  8px;

        // 左右两边的线
        .hr-line {
          flex: 1;
          background-color: #BFC8CF;
          height: 4px;
          border: 0;

          // 第一个和最后一个的线用透明色代替，但空间要留着
          &.first-child, &.last-child {
            background-color: transparent;
          }
        }

        // 中间圆点的样式
        .circle-point {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          border: 6px solid #FFFFFF;
          background-color: #BFC8CF;
        }
      }
    }
  }
}
