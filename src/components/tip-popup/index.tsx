import * as React from "react";
import { Popup } from "../pop-up";
import styles from "./style.module.scss";
import { Button, EButtonType } from "../button";
import { cls } from "@/utils/tool";

interface ITipPopupProps {
  onClose?: () => void;
  onOk?: (e?: Event) => void;
  title?: string;
  children?: any;
  visible: boolean;
  cancelText?: string;
  okText?: string;
  hiddenCancel?: boolean;
  /** popup的其他参数 */
  [key: string]: any;
}

export const TipPopup: React.FC<ITipPopupProps> = (props) => {
  const {
    onClose = () => {},
    onOk = () => {},
    title = "",
    visible,
    children,
    cancelText = "取消",
    okText = "确定",
    hiddenCancel = false,
    ...rest
  } = props;

  return (
    <Popup
      title={title}
      visible={visible}
      onClose={() => onClose()}
      hiddenCancel={hiddenCancel}
      {...rest}
    >
      <div className={styles["content-box"]}>{children}</div>
      <div className={styles["button-box"]}>
        {!hiddenCancel && (
          <Button
            type={EButtonType.grey}
            text={cancelText}
            onClick={onClose}
            className={styles["cancel-button"]}
          />
        )}
        <Button
          text={okText}
          onClick={onOk}
          className={cls([
            styles["ok-button"],
            hiddenCancel && styles["only-one-button"],
          ])}
        />
      </div>
    </Popup>
  );
};
