import React from "react";
import { Modal } from "antd-mobile";
import { debounce } from "lodash";
import Styles from "./style.module.scss";

interface ConfirmModalProps {
  title?: string;
  content: string;
  onConfirm: () => void;
  onCancel?: () => void; // 取消按钮回调
  onClose?: () => void; // 点击外部关闭回调
  className?: string;
  bodyClassName?: string;
  debounceTime?: number; // 防抖时间，默认 300ms
  confirmText?: string; // 确认按钮文字
  cancelText?: string; // 取消按钮文字
  actions?: any;
}

const ConfirmModal = ({
  title = "提示",
  content,
  onConfirm,
  onCancel,
  onClose,
  className,
  bodyClassName,
  debounceTime = 300,
  confirmText = "确认",
  cancelText = "取消",
  actions,
}: ConfirmModalProps) => {
  // 防抖处理
  const debouncedOnConfirm = debounce(onConfirm, debounceTime);
  const debouncedOnCancel = onCancel
    ? debounce(onCancel, debounceTime)
    : undefined;
  const debouncedOnClose = onClose
    ? debounce(onClose, debounceTime)
    : undefined;

  return Modal.show({
    className: className || Styles["confirm-modal"],
    content,
    title,
    showCloseButton: true,
    bodyClassName: bodyClassName || Styles["confirm-modal-body"],
    onClose: debouncedOnClose,
    actions: actions || [
      {
        key: "cancel",
        text: cancelText,
        onClick: debouncedOnCancel,
      },
      {
        key: "confirm",
        text: confirmText,
        onClick: debouncedOnConfirm,
        primary: true,
      },
    ],
  });
};

export default ConfirmModal;
