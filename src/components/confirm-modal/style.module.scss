.confirm-modal {
  :global {
    .adm-plain-anchor {
      right: 16px!important;
    }
    .adm-modal-wrap {
      border-radius: 20px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
      max-width: 340px;
      width: 90vw;
      background: #fff;
    }
    .adm-modal-content {
      padding: 0;
      padding-top: 8px;
      font-size: 14px;
      font-weight: normal;
      color: #666;
      text-align: center;
      line-height: 22px;
    }
    .adm-modal-title {
      font-size: 20px;
      font-weight: 600;
      color: #222;
      text-align: center;
      margin-bottom: 12px;
    }
    .adm-modal-footer {
      display: flex;
      margin-top: 24px;
      flex-direction: row;
      justify-content: space-evenly;
      padding: 0;

      .adm-space-item {
        width: 40%;
      }

      .adm-button {
        border: 1px solid #021E66;
        border-radius: 30px;

        &.adm-button-fill-none {
          color: #021E66;
        }
      }
    }
    .adm-modal-button {
      flex: 1;
      height: 44px;
      border-radius: 24px;
      font-size: 16px;
      font-weight: 500;
      box-shadow: none;
      margin: 0;
      padding: 0;
      box-shadow: 0 4px 0 0 #021e6626;
    }
    .adm-modal-button-default {
      background: #fff;
      color: #2468f2;
      border: 1.5px solid #2468f2;
    }
    .adm-modal-button-primary {
      background-color: #2F86FF!important;
      color: #fff;
      border: none;
    }
    .adm-modal-close {
      top: 18px;
      right: 18px;
      width: 20px;
      height: 20px;
      color: #bcbcbc;
      font-size: 20px;
    }
  }
}

.confirm-modal-body {
  padding: 24px 20px 0 20px;
  width: 343px;
  border-radius: 24px;
  padding-top: 24px!important;
}