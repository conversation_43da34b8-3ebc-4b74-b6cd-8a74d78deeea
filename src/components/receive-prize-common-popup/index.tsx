import React, { Fragment, useEffect, useState } from "react";
import moment from "moment";
import { Popover, SpinLoading } from "antd-mobile";
import { Swiper, SwiperSlide } from "swiper/react";

import { Popup } from "../pop-up";
import { LayoutContext } from "@/components/layout/layout-context";
import { Header } from "../header";
import { IconSvg } from "../icon-svg";
import Loading from "./components/loading";
import {
  EPackageInPrizeType,
  getPrizeDetail,
  postOpenPackageById,
  postPackagePrize,
  postStepPrize,
} from "@/service/self-learning/drawer";
import { NetworkErrorStatus } from "../empty-status";
import { IPackagePrize } from "@/typing";
import { cls } from "@/utils/tool";
import { Button } from "../button";
import SafeLogger from "@/utils/safe-logger";
import EmptyPrizeImg from "@/assets/common/empty-prize.png";
import EYouJiImg from "@/assets/common/e-you-ji.png";
import LeftHuaImg from "@/assets/common/left-hua.png";
import RightHuaImg from "@/assets/common/right-hua.png";
import StepGifImg from "@/assets/common/step.gif";

import "swiper/css";
import Style from "./style.module.scss";
import { boxBgList, titleMap } from "./context";

export enum ERewardStatus {
  receive = 1,
  view = 2,
}

interface IReceivePrizeCommonPopupProps {
  closeBefore?: () => void;
  receiveBaseInfo: any;
  rewardStatus?: ERewardStatus;
}

enum EPrizeType {
  step = "step",
  medal = "medal",
  package = "package",
  image = "image",
  prize = "prize",
}

export interface IPopupRef {
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
}

export const ReceivePrizeCommonPopup = React.forwardRef<
  IPopupRef,
  IReceivePrizeCommonPopupProps
>(function Popups(props, ref) {
  const {
    closeBefore = () => {},
    receiveBaseInfo,
    rewardStatus = ERewardStatus.receive,
  } = props;
  const [visible, setVisible] = useState(false);
  const { safeAreaTopHeight } = React.useContext(LayoutContext) || {};
  const [isLoading, setIsLoading] = useState(false);
  const [isNetWorkError, setIsNetWorkError] = useState(false);
  const [prizeList, setPrizeList] = useState<any[]>([]);

  // 开福袋的映射，要存储和读取每一个开奖动作
  const [openPackageMap, setOpenPackageMap] = useState<any>({});
  // popover 的受控索引
  const [popIndex, setPopIndex] = useState(-1);

  /** 把弹窗的调度能力暴露出去 */
  React.useImperativeHandle(ref, () => {
    return {
      setVisible,
    };
  }, []);

  const handleClose = () => {
    /** 先调用外部传入的，把数据传出去 */
    closeBefore();
    // 再内部闭环关闭抽屉
    setVisible(false);
  };

  /** 加载数据 */
  const initData = async () => {
    if (isLoading || !receiveBaseInfo?.nodeId) {
      return; // 加载中就不要一直点了
    }
    const { nodeId, taskId, taskRewardLevelId } = receiveBaseInfo;
    let prizeList: any[] = [];
    let firstStepIsError = false;

    // 如果是查看奖励则仅仅请求步数信息，其他不做处理
    if (rewardStatus === ERewardStatus.view) {
      try {
        const { data } = await getPrizeDetail({
          taskId,
        });
        setPrizeList([
          {
            type: EPrizeType.step,
            value: data.stepAward,
            title: `+${data.stepAward}步`,
          },
        ]);
        setIsNetWorkError(false);
      } catch (error) {
        // 如果标识为false，代表接口内部也错误了
        setIsNetWorkError(true);
        setIsLoading(false);
      }
      return;
    }

    try {
      setIsLoading(true);
      // 这里的接口必须是顺序调用，先领取步数和勋章，然后再去查询领取步数后时是否触发了福袋掉落
      const { data: stepData } = await postStepPrize({
        nodeId,
        taskId,
        taskRewardLevelId,
        // 这里后端说不会用，但是字段必传，让传个0即可
        processNum: 0,
        isAwardOnce: 0,
        ignoreError: true,
      });
      // 服务说可能存在接口200了，但是标识为false，这时候实际也失败了
      // 奖励的步数是必须的数据，因此严格判断来保证
      if (stepData?.awardSuccess && stepData?.stepAward) {
        prizeList = [
          {
            type: EPrizeType.step,
            value: stepData.stepAward,
            title: `+${stepData.stepAward}步`, // 这个勋章是必须存在的
          },
        ];
        // 勋章有可能不存在，只返回时才加入
        if (stepData?.medalAward) {
          prizeList.push({
            type: EPrizeType.medal,
            value: stepData.medalAward.mainImgUrl,
            title: stepData.medalAward.name, // 这个勋章是必须存在的
          });
        }
        setPrizeList(prizeList);
        setIsNetWorkError(false);
        // 如果接口请求成功了，isLoading状态先不重置，等待下个福袋接口状态变更
      } else {
        // 如果标识为false，代表接口内部也错误了
        setIsNetWorkError(true);
        setIsLoading(false);
        firstStepIsError = true;
        SafeLogger.baseLogger.error("receive-prize-error", {
          ...receiveBaseInfo,
          response: { ...stepData },
          reason: "api return error, awardSuccess is false",
        });
      }
    } catch (error) {
      // 出现网络等其他异常也出Error提示
      setIsNetWorkError(true);
      setIsLoading(false);
      firstStepIsError = true;
    }

    // 第一个接口错误了不继续请求第二个接口了
    if (firstStepIsError) {
      return;
    }

    try {
      // 福袋信息是个数组，可能有多个福袋
      const { data: packageData } = await postPackagePrize({
        nodeId,
        ignoreError: true,
      });
      // 福袋有可能么有，也有可能存在1-n个
      if (packageData?.length) {
        packageData.forEach((item: IPackagePrize) =>
          prizeList.push({
            type: EPrizeType.package,
            value: item.packageImg,
            title: item.packageName,
            packageType: item.openType,
            openTime: item.openDate,
            packageStatus: item.status,
            packageId: item.packageId,
          }),
        );
        setPrizeList(prizeList);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      SafeLogger.baseLogger.warn("receive-medal-warn", {
        error,
        ...receiveBaseInfo,
      });
    }
  };

  // 开福袋
  const handlePackageOpen = async (packageInfo: any) => {
    // 如果里边已经有了就不继续开了
    if (!openPackageMap[packageInfo.packageId]) {
      try {
        setOpenPackageMap({
          ...openPackageMap,
          [packageInfo.packageId]: {
            loading: true,
          },
        });

        const { data } = await postOpenPackageById({
          bagId: packageInfo.packageId,
        });

        let newPrizeObj = {};
        if (data?.prizeUrl) {
          newPrizeObj = {
            type:
              data.prizeType === EPackageInPrizeType.image
                ? EPrizeType.image
                : EPrizeType.medal,
            value: data.prizeUrl,
            title: data.prizeName,
          };
        } else {
          newPrizeObj = {
            type: EPrizeType.prize,
            value: EmptyPrizeImg,
            title: (
              <div>
                <p>与奖品</p>
                <p>擦肩而过</p>
              </div>
            ),
          };
        }

        const packageIndex = prizeList.findIndex(
          (item: any) => item.packageId === packageInfo.packageId,
        );
        prizeList[packageIndex] = newPrizeObj;
        setPrizeList(prizeList);
        setOpenPackageMap({
          ...openPackageMap,
          [packageInfo.packageId]: {
            loading: false,
            ...data,
          },
        });
      } catch (error) {
        console.warn("打开福袋接口出错", error);
        setOpenPackageMap({
          ...openPackageMap,
          [packageInfo.packageId]: {
            loading: false,
          },
        });
      }
    }
  };

  const makePopoverContent = (item: any) => {
    if (item.type === EPrizeType.step) {
      return "e游记进行各城市游历的核心能量";
    }

    if (item.type === EPrizeType.medal || item.type === EPrizeType.image) {
      return (
        <Fragment>
          <p>升学e网通的虚拟奖励</p>
          <p>
            {item.type === EPrizeType.medal
              ? "前往“我的-勋章墙”查看"
              : "前往“e游记-个人名片”查看"}
          </p>
        </Fragment>
      );
    }

    // 奖品 - 福袋
    if (item.type === EPrizeType.package) {
      return (
        <Fragment>
          {item.packageType === 1 ? (
            <p>开奖时间: {moment(item.openTime).format("YYYY-MM-DD")}</p>
          ) : (
            <p>e游记城市游历时获得的奖励</p>
          )}
          <p>前往“e游记-我的福袋”查看</p>
        </Fragment>
      );
    }
  };

  useEffect(() => {
    if (visible) {
      initData();
    }
  }, [visible]);

  return (
    <Popup
      hiddenHeader
      bodyClassName={Style["receive-prize-common-popup"]}
      bodyStyle={{ paddingTop: safeAreaTopHeight }}
      visible={visible}
      onClose={() => handleClose()}
      {...props}
    >
      <Header
        back={<IconSvg name="icon-guanbichouti" />}
        onBack={handleClose}
        className={Style["header-box"]}
      />
      <div
        className={Style["father-box"]}
        style={{
          maxHeight: `calc(100vh - 12.8vw - ${safeAreaTopHeight}px)`,
          height: `calc(100vh - 12.8vw - ${safeAreaTopHeight}px)`,
          overflow: "auto",
        }}
      >
        {/* loading */}
        {isLoading && <Loading />}

        {(isNetWorkError || !prizeList?.length) && !isLoading && (
          <NetworkErrorStatus
            buttonOption={{
              handleClick: () => initData(),
              className: Style["replay-button"],
            }}
          />
        )}

        {/* e游记的flag图片 */}
        {!isNetWorkError && !isLoading && !!prizeList?.length && (
          <Fragment>
            <img
              src={EYouJiImg}
              alt=""
              className={Style["flag-img"]}
              style={{
                top: `calc(100vh - 12.8vw - ${safeAreaTopHeight}px)`,
              }}
            />

            <div>
              {/* 小人的占位容器 */}
              <div className={Style["empty-image-box"]}>
                <img src={StepGifImg} alt="" />
              </div>

              <div className={Style["hua-box"]}>
                <img src={LeftHuaImg} alt="" className={Style["left-hua"]} />
                <p>恭喜你获得了</p>
                <img src={RightHuaImg} alt="" className={Style["right-hua"]} />
              </div>

              {!!prizeList?.length && (
                <Swiper
                  slidesPerView={"auto"}
                  centeredSlides={false}
                  pagination={false}
                  className={cls([
                    Style["receive-prize-common-swiper"],
                    prizeList.length <= 2 && Style["center"],
                  ])}
                  onSlideChange={() => setPopIndex(-1)}
                >
                  {prizeList.map((item: any, index: number) => (
                    <SwiperSlide key={item.id}>
                      <div
                        className={cls([
                          Style["prize-item"],
                          boxBgList[index % boxBgList.length],
                        ])}
                      >
                        {![EPrizeType.prize].includes(item.type) && (
                          <Popover
                            trigger="click"
                            mode="dark"
                            placement="top"
                            content={makePopoverContent(item)}
                            onVisibleChange={() => setPopIndex(index)}
                            visible={popIndex === index}
                          >
                            <IconSvg
                              name="icon-xingzhuangjiehe"
                              className={Style["wen-hao"]}
                            />
                          </Popover>
                        )}
                        <div className={Style["type-title"]}>
                          {titleMap[item.type]}
                        </div>
                        {item.type === EPrizeType.step && (
                          <div className={Style["step-number"]}>
                            {item.value}
                          </div>
                        )}
                        {[
                          EPrizeType.medal,
                          EPrizeType.prize,
                          EPrizeType.image,
                        ].includes(item.type) && (
                          <img
                            src={item.value}
                            alt=""
                            className={Style["prize-img"]}
                          />
                        )}
                        {item.type === EPrizeType.package && (
                          <Fragment>
                            {openPackageMap?.[item.packageId]?.loading && (
                              <SpinLoading color="primary" />
                            )}

                            {!openPackageMap?.[item.packageId] && (
                              <img
                                src={item.value}
                                alt=""
                                className={Style["prize-img"]}
                              />
                            )}

                            {!openPackageMap?.[item.packageId]?.loading &&
                            !!openPackageMap?.[item.packageId]?.prizeUrl ? (
                              <img
                                src={openPackageMap[item.packageId]?.prizeUrl}
                                alt=""
                                className={Style["prize-img"]}
                              />
                            ) : null}
                          </Fragment>
                        )}
                        <div className={Style["prize-desc"]}>{item.title}</div>
                        {EPrizeType.package === item.type &&
                          item.packageType === 0 && (
                            <Button
                              text="立即领奖"
                              className={Style["now-receive-prize-button"]}
                              onClick={() => handlePackageOpen(item)}
                            />
                          )}
                      </div>
                    </SwiperSlide>
                  ))}
                </Swiper>
              )}
            </div>
            <Button
              text="继续学习"
              className={Style["bottom-button"]}
              onClick={handleClose}
            />
          </Fragment>
        )}
      </div>
    </Popup>
  );
});
