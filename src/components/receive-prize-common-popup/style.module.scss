.header-box {
  position: sticky !important;
  top: -1px;

  :global {
    .adm-nav-bar-title {
      color: #2A333A;
    }
    .adm-nav-bar-left {
      font-size: 20px;
    }
  }
}

.receive-prize-common-popup {
  background-color: #fff;

  // 父容器
  .father-box {
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    // 网络错误场景
    .network-error {
      img {
        width: 160px!important;
        height: auto!important;
      }
    }

    // 网络异常时重新加载按钮
    .replay-button {
      padding: 5px 26px;
      margin-top: 10px;
    }

    // e游记的 flag
    .flag-img {
      width: 146px;
      height: 40px;
      display: block;
      margin: 0 auto;
    }

    // 占据位置的空容器，里边只有一个示例动画
    .empty-image-box {
      width: 200px;
      height: 200px;
      margin: 0 auto;
      border-radius: 50%;
      background: #e3f5ff80;
      text-align: center;

      img {
        width: 114px;
        height: 176px;
      }
    }

    // 左右两边花的父容器，中间是欢迎你
    .hua-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: -24px;

      .left-hua, .right-hua {
        width: 100px;
        height: 110px;
      }

      p {
        font-size: 20px;
        color: #000;
        font-weight: bold;
      }
    }

    // 底部的按钮
    .bottom-button {
      width: 343px;
      height: 44px;
      margin: 0 auto 16px;
    }
  }
}

.package-popover-box {
  font-size: 12px;
}

// 获得的奖品容器
.receive-prize-common-swiper {
  padding-left: 20px;
  margin-top: -15px;

  &.center{
    :global {
      .swiper-wrapper {
        width: auto;
        display: flex;
        justify-content: center;
      }
    }
  }

  :global {
    .swiper-slide {
      width: auto;
      padding-bottom: 8px;
    }
  }

  .prize-item {
    width: 102px;
    height: 164px;
    border-radius: 8px;
    margin-right: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    padding: 8px 0 8px;
    position: relative;
    text-align: center;
    margin-bottom: 10px;

    .wen-hao {
      position: absolute;
      top: 5px;
      right: 5px;
      width: 16px;
      height: 16px;
      cursor: pointer;
      color: #B3A89D;
    }

    .type-title {
      color: #000;
      font-size: 16px;
    }

    .step-number {
      font-size: 40px;
      color: #35547E;
      font-weight: bold;
    }

    .prize-img {
      max-width: 72px;
      max-height: 72px;
      display: block;
    }

    .prize-desc {
      font-size: 14px;
      color: rgba(0, 0, 0, .65);
      padding: 8px;
      padding-bottom: 0!important;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      line-clamp: 2;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .now-receive-prize-button {
      background-image: linear-gradient(270deg, #FD5E40 1%, #FE3649 99%);
      border-radius: 12px;
      width: 72px;
      height: 24px;
      position: absolute;
      bottom: -18px;
      left: 50%;
      transform: translate(-50%);
      padding: 0;

      & > div {
        font-size: 14px;
        color: #fff;
      }
    }

    &.step {
      background-image: linear-gradient(180deg, #EAF6FF 0%, #CEE6FF 98%);
      border: 0.5px solid rgba(0,0,0,.08);
    }

    &.medal {
      background-image: linear-gradient(180deg, #FFF3E5 0%, #FFD1C3 100%);
      border: 0.5px solid rgba(0,0,0,.14);
    }

    &.package {
      background-image: linear-gradient(180deg, #FFF5FE 0%, #FFDAFF 100%);
      border: 0.5px solid rgba(0,0,0,.14);
    }
  }
}
