import React from "react";
import { LottieOptions, useLottie } from "lottie-react";

interface ILottieBoxProps extends Omit<LottieOptions, "animationData"> {
  onComplete?: () => void;
  dataJson: any;
  className?: string;
  loop?: boolean;
  // 其他组件参数
  [key: string]: any;
}

const LottieBox: React.FC<ILottieBoxProps> = (props) => {
  const { onComplete, dataJson, className, loop = true } = props;

  const options = {
    ...props,
    animationData: dataJson,
    loop,
    autoPlan: true,
    onComplete: () => onComplete?.(),
  };

  const { View } = useLottie(options);
  return <div className={className}>{View}</div>;
};

export default LottieBox;
