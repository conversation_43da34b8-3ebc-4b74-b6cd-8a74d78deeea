@keyframes skeleton-loading {
  0% {
    transform: translate(-37.5%);
  }

  to {
    transform: translate(37.5%);
  }
}

.block {
  position: absolute;
  left: 0;
  top: 0;
  background: #6f8afa;
  border-radius: 2px;
  overflow: hidden;

  &::after {
    position: absolute;
    left: -150%;
    right: -150%;
    top: 0;
    bottom: 0;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0.2) 25%,
      rgba(255, 255, 255, 0.5) 37%,
      rgba(255, 255, 255, 0.2) 63%
    );
    animation: skeleton-loading 1.4s ease infinite;
    content: " ";
  }
}
