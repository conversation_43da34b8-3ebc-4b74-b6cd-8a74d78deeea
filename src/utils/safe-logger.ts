const TOPIC = "ewtCustomerH5";
const BASE_SOURCE = "base";

interface ISafeLogger {
  error: (event: string, data) => void;
  info: (event: string, data) => void;
  warn: (event: string, data) => void;
}

/**
 * 封装更稳定的logger，减少三方sls产生的影响
 * 封装了error、warn、info 方法
 */
class SafeLogger implements ISafeLogger {
  _logger;
  _source;
  _loggerClass;
  static baseLogger: ISafeLogger;
  /**
   * 构造方法
   * @param {*} source 源
   */
  constructor(source) {
    this._source = source || "unknown";
    try {
      const Logger = this._getLoggerClass();
      this._logger = new Logger(TOPIC, {
        topic: TOPIC,
        source: this._source,
        environment: process.env.DEPLOYMENT_ENV,
      });
    } catch (e) {
      console.log(`sls初始化失败：${source}`, e);
    }
  }

  _getLoggerClass() {
    if (!this._loggerClass) {
      this._loggerClass = require("@ewt/sls-web-track");
      this._loggerClass.setGlobalProps({
        environment: process.env.DEPLOYMENT_ENV,
        release: window.__MST_LABEL__ || "baseline",
        build_time: __BUILD_TIME__ || "0",
      });
    }
    return this._loggerClass;
  }

  /**
   * error日志
   * @param {string} event 事件
   * @param {object} more
   */
  error(event, more = {}) {
    this._safe("error", { event, ...more });
  }

  /**
   * info日志
   * @param {string} event 事件
   * @param {object} more
   */
  info(event, more = {}) {
    this._safe("info", { event, ...more });
  }

  /**
   * warn日志
   * @param {string} event 事件
   * @param {object} more
   */
  warn(event, more = {}) {
    this._safe("warn", { event, ...more });
  }

  _safe(type, data) {
    try {
      if (this._logger) {
        if (data && data.error) {
          const error = data.error || {};
          data.msg = error.msg || error.message || error.name || "";
          // 忽略纯网络错误
          if (
            [
              "Network Error",
              "timeout of 30000ms exceeded",
              "Request aborted",
            ].includes(data.msg)
          ) {
            return;
          }
        }
        this._logger[type](data);
      }
    } catch (e) {
      console.log("sls上报失败", e);
    }
  }
}

// 创建全局基础logger，供全局使用
SafeLogger.baseLogger = new SafeLogger(BASE_SOURCE);

export default SafeLogger;
