export const envFlag = process.env.DEPLOYMENT_ENV || "dev";
export const IS_DEV = envFlag === "dev"; // 开发环境
export const IS_SIT = envFlag === "sit"; // 测试环境
export const IS_PRE = envFlag === "pre"; // 预发环境
export const IS_PROD = envFlag === "prod"; // 线上环境

// 转换预发域名，前缀加了staging-，因此只传递关键域名host，其余host可定制
export const translateStagingDomain = (originDomain = "", originHost = "") => {
  return `//staging-${originDomain}${originHost || ".ewt360.com"}`;
};

// 根据指定的域名前缀生成通用的完整域名
export const makeUnifyDomain = (
  originDomain: string,
  stagingDomain?: string,
) => {
  let urlHost = `//${originDomain}.ewt360.com`;
  if (IS_SIT || IS_DEV)
    urlHost = `//${originDomain}.${IS_DEV ? "dev" : "test"}.ewt360.com`;
  if (IS_PRE) urlHost = translateStagingDomain(originDomain, stagingDomain);
  return urlHost;
};

// 域名host生成
export const web = makeUnifyDomain("web"); // 静态资源 host
export const gateway = makeUnifyDomain("gateway"); // api-gateway host
