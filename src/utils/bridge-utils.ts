import mstJsBridge from "mst-js-bridge";

/**
 * 版本比较 versionCmp
 * @param {String} s1 当前版本.
 * @param {String} s2 比较版本.
 * @return {Boolean} 当前版本大于或等于返回true, 否则false
 * versionCompare('6.3', '5.2.5'); // true.
 * versionCompare('6.1', '6.1'); // true.
 * versionCompare('6.1.5', '6.2'); // false.
 */
export const versionCompare = (s1: string, s2: string) => {
  const s2i = (s: string) => {
    let num = 0;
    let factor = 1;
    for (let i = s.length - 1; i >= 0; i--) {
      const code = s.charCodeAt(i);
      // eslint-disable-next-line yoda
      if (48 <= code && code < 58) {
        num += (code - 48) * factor;
        factor *= 10;
      }
    }
    return num;
  };
  const a = s1.split(".").map(s2i);
  const b = s2.split(".").map(s2i);
  const n = Math.min(a.length, b.length);
  for (let i = 0; i < n; i++) {
    if (a[i] < b[i]) {
      return false;
      // eslint-disable-next-line no-else-return
    } else if (a[i] > b[i]) {
      return true;
    }
  }
  if (a.length < b.length) return false;
  if (a.length > b.length) return true;
  // eslint-disable-next-line no-bitwise
  const last1 = s1.charCodeAt(s1.length - 1) | 0x20;
  // eslint-disable-next-line no-bitwise
  const last2 = s2.charCodeAt(s2.length - 1) | 0x20;
  // eslint-disable-next-line no-unneeded-ternary
  return last1 > last2 ? true : last1 < last2 ? false : true;
};

/**
 * 调用mst bridge
 * @param {*} route
 */
export function openRoute(route: any) {
  try {
    mstJsBridge && mstJsBridge.openNative(route);
  } catch (e) {
    console.log(e);
  }
}

/**
 * 待用native， 并获取返回值
 * @param {*} route
 * @returns
 */
export async function getNative(route: any) {
  try {
    if (mstJsBridge) {
      return await mstJsBridge.getNativeData(route);
    }
  } catch (e) {
    console.log("调用mstJsBridge异常", e);
  }
  return null;
}

/**
 * 关闭webview
 */
export function closeWebview() {
  try {
    mstJsBridge && mstJsBridge.closeWebview();
  } catch (e) {
    console.log(e);
  }
}

/**
 * 通过Native来新开webview
 * @param {string} url 链接
 * @param {object} config 更多参数
 * - title 页面标题
 * - removeCurrentPage 关闭当前页面: 1 表示关闭
 * - style 页头样式
 * 0 : 导航栏是蓝底、白字的样式、标题居中
 * 1 : 导航栏是白底、黑字、标题左对齐
 * 2 : 导航栏是白底、黑字、标题居中对齐
 * 3 : 导航栏是白底、黑字、标题居中对齐 返回按钮是关闭
 */
export function openWebView(
  url: string,
  config: { title?: string; removeCurrentPage?: number; style?: number } = {},
) {
  const params = { url, ...config };
  openRoute({
    domain: "web",
    action: "open_webview",
    params,
  });
}

/**
 * 通过Native来新开webview，并尝试关闭当前 webview
 * @param {string} url 链接
 * @param {object} config 更多参数
 * - title 页面标题
 * - style 页头样式
 * 0 : 导航栏是蓝底、白字的样式、标题居中
 * 1 : 导航栏是白底、黑字、标题左对齐
 * 2 : 导航栏是白底、黑字、标题居中对齐
 * 3 : 导航栏是白底、黑字、标题居中对齐 返回按钮是关闭
 */
export function closeCurrentAndOpenWebView(
  url: string,
  config: { title?: string; style?: number } = {},
) {
  if (getIsInMSTApp()) {
    const { isSupportRemoveCurrentPage } = getAppSupportInfo();
    const agentName = getAppAgentName();
    if (isSupportRemoveCurrentPage) {
      // removeCurrentPage 不能传 bool，Android 不认
      openWebView(url, { ...config, removeCurrentPage: 1 });
    } else if (agentName === "android") {
      // 历史经验上，在低版本 Android App 上这样的做法没什么问题
      closeWebview();
      openWebView(url, config);
    } else {
      // 鸿蒙之类的需要延时
      closeWebview();
      setTimeout(() => {
        openWebView(url, config);
      }, 300);
    }
  } else {
    window.location.replace(url);
  }
}

/**
 * 获取安全区域顶部空间
 */
export async function getSafeAreaTop() {
  const { isSupportDeviceSafeArea } = getAppSupportInfo();
  if (isSupportDeviceSafeArea) {
    const res = await getNative({
      domain: "systemInfo",
      action: "getDevicePortraitSafeArea",
    });
    return (res && res.data && +res.data.top) || 0;
  }
  return 0;
}

/**
 * 同步接口，判断是否在App中
 * 参照mst-bridge代码处理
 * @returns {boolean}
 */
export function getIsInMSTApp(): boolean {
  const {
    navigator: { userAgent },
  } = window;
  return userAgent.indexOf("EWT/") > -1;
}

/**
 * 同步接口，返回App的版本号，如果不在App中，返回空字符串
 * 参照mst-bridge代码处理
 */
export function getAppVersion() {
  const {
    navigator: { userAgent },
  } = window;

  const ewtFlagIndex = userAgent.indexOf("EWT/");
  const ewtFlagEndIndex = userAgent.indexOf("/com.mistong.ewt");
  if (ewtFlagIndex > -1 && ewtFlagEndIndex > -1) {
    return userAgent.substring(ewtFlagIndex + 4, ewtFlagEndIndex);
  }
  return "";
}

/**
 * 获取 App 所属平台：ios、android、harmonyos、others
 */
export function getAppAgentName() {
  try {
    if (mstJsBridge) {
      return mstJsBridge.getAgentName().toLowerCase();
    }
  } catch (e) {
    console.log(e);
  }
  return "others";
}

/**
 * 获取App支持情况
 */
export function getAppSupportInfo() {
  const info = {
    // 是否支持新开 Webview 时关闭当前 Webview，iOS 都可以，Android 需要 10.8.5
    isSupportRemoveCurrentPage: false,
    // 是否支持设备安全区域
    isSupportDeviceSafeArea: false,
    // 是否支持修改状态栏颜色
    isSupportChangeStatusBarStyle: false,
    // 是否支持 通知App 事件
    isSupportNotifyAppEvent: false,
  };
  const version = getAppVersion();
  const agentName = getAppAgentName();
  if (version) {
    info.isSupportRemoveCurrentPage =
      agentName === "ios" ||
      (agentName === "android" && versionCompare(version, "10.8.5"));
    info.isSupportDeviceSafeArea = versionCompare(version, "9.9.7");
    info.isSupportChangeStatusBarStyle = versionCompare(version, "10.4.0");
    info.isSupportNotifyAppEvent = versionCompare(version, "10.8.5");
  }
  return info;
}

/**
 * 修改webview容器标题
 * @param {string} title
 */
export function changeMstTitle(title: string) {
  try {
    mstJsBridge &&
      mstJsBridge.call("changeMstTitle", {
        title,
      });
  } catch (e) {
    console.log(e);
  }
}

/**
 * 修改状态栏颜色
 * @param {number} style 1：白色、2：黑色
 */
export function changeStatusBarStyle(style: 1 | 2) {
  const { isSupportChangeStatusBarStyle } = getAppSupportInfo();
  if (isSupportChangeStatusBarStyle) {
    getNative({
      domain: "web",
      action: "changeStatusBarStyle",
      params: {
        style,
      },
    });
  }
}

/**
 * 通知App事件
 * @param {string} eventCode 事件编码
 * @param {object} params 事件参数
 */
export function notifyAppEvent(eventCode: string, params: any) {
  const { isSupportNotifyAppEvent } = getAppSupportInfo();
  if (isSupportNotifyAppEvent) {
    getNative({
      domain: "systemInfo",
      action: "sendEventToNative",
      params: {
        eventCode,
        param: params || {},
      },
    });
  }
}

/**
 * 通知 App 科目变化
 * @param subjectId 学科ID
 */
export function notifyAppSubjectId(subjectId: number) {
  notifyAppEvent("eClassMainPageSubjectChanged", {
    subjectId,
  });
}

/**
 * 打开 课程包 详情页（播放页）
 * @param courseId 课程包 ID
 * @param lessonId 课程讲 ID ，可不传
 */
export function openCourse(
  courseId: number | string,
  lessonId?: number | string,
) {
  const params: any = {
    id: `${courseId}`,
  };
  if (lessonId) {
    // App Native 参数名字就是写错的了...
    params.lessionID = `${lessonId}`;
  }
  openRoute({
    domain: "course",
    action: "open_detail",
    params,
  });
}

/**
 * 打开 搜索页
 * search_from：搜索类型，用于区分搜索业务场景（11.1.5 版本之前需要），本次需求App会设置默认值，默认全站搜索，App根据渠道区别显示全站搜索和校园版搜索
 * 1. home 全站搜索
 * 2. subject_home  e讲堂搜索(校园版)
 * 3. fm Fm搜索
 */
export function openSearch() {
  openRoute({
    domain: "search",
    action: "open_search",
    params: {
      search_from: "subject_home",
    },
  });
}

/**
 * 获取渠道号
 */
export async function getChannel() {
  if (getIsInMSTApp()) {
    const res = await getNative({
      domain: "systemInfo",
      action: "getChannel",
    });
    return res?.data?.channel || "";
  }
  return "";
}

/**
 * 通知 App 刷新佩戴的勋章
 */
export function refreshUserMedal(params: any) {
  if (!getIsInMSTApp()) {
    return;
  }
  if (getAppAgentName() === "ios") {
    // ios现阶段的webview需要注册其他handleName方法来实现通知，常规路由无法通知成功
    mstJsBridge.dynamicHandlerName.push("refreshUserMedal");
    mstJsBridge.call("refreshUserMedal", params);
  } else {
    const version = getAppVersion();
    const isNewVersion = version && versionCompare(version, "10.3.0");
    if (isNewVersion) {
      // 安卓端使用原有路由形式通知即可
      getNative({
        domain: "systemInfo",
        action: "refreshUserMetal",
        params,
      });
    }
  }
}
