import MstQtAnalytics from "mst-analytics";
import mstJsBridge from "mst-js-bridge";
import Cookies from "js-cookie";
import queryString from "query-string";
import { IS_SIT, web } from "./hosts";

/** 获取query参数 */
export function getUrlParam(key: string, originUrl?: string) {
  const reg = new RegExp("(^|&)".concat(key).concat("=([^&]*)(&|$)"), "i");
  originUrl = originUrl ?? window.location.href;
  const index = originUrl.indexOf("?");
  let tmpUrl = originUrl.slice(index + 1);
  if (tmpUrl.indexOf("?") !== -1) {
    tmpUrl = tmpUrl.slice(tmpUrl.indexOf("?") + 1);
  }
  const result = tmpUrl.match(reg);
  if (result != null) {
    return decodeURIComponent(result[2]);
  }
  return "";
}

export function findLastIndex(arr, predicate) {
  if (!Array.isArray(arr)) return -1;
  for (let i = arr.length - 1; i >= 0; i--) {
    if (predicate(arr[i], i, arr)) return i;
  }
  return -1;
}

export function findLast(arr, predicate) {
  if (!Array.isArray(arr)) return undefined;
  for (let i = arr.length - 1; i >= 0; i--) {
    if (predicate(arr[i], i, arr)) return { ...arr[i] };
  }
  return undefined;
}

export function makeAnswerUrl(config) {
  return `${location.protocol + web}/tiku_h5/index/#/answer/paper?${objectToUrlParams(config)}`;
}

export function makeAnswerReportUrl(config) {
  return `${location.protocol + web}/tiku_h5/index/#/report?${objectToUrlParams(config)}`;
}

// 本地存储时添加当前时间戳，便于后期控制失效时长
export const setLocalStorage = (key: string, value: unknown) => {
  try {
    if (!key || typeof value === "undefined") return; // 没有key或没有值都不进行后续操作
    const data = {
      value,
      createTime: new Date().getTime(), // 存储当前时间
    };
    localStorage.setItem(key, JSON.stringify(data));
  } catch (error) {
    console.error(error);
  }
};

/**
 * 通过Native来新开webview
 * @param {*} url 链接
 * @param {*} title 页面标题
 * @param {*} style 页头样式
 * 0 : 导航栏是蓝底、白字的样式、标题居中
 * 1 : 导航栏是白底、黑字、标题左对齐
 * 2 : 导航栏是白底、黑字、标题居中对齐
 * 3 : 导航栏是白底、黑字、标题居中对齐 返回按钮是关闭
 */
export function openWebView(url, title = "", style = 0) {
  const params: { title?: string; style?: number; url: string } = { url };
  if (title) {
    params.title = title;
  }
  if (style) {
    params.style = style;
  }
  openRoute({
    domain: "web",
    action: "open_webview",
    params,
  });
}

/** 页面停留时长 - 带参数 */
export function separatelyReport(pageCode: string, data: Record<string, any>) {
  try {
    MstQtAnalytics.separatelyReport(pageCode, data || {});
  } catch (err) {
    console.error("separatelyReport 失败");
  }
}

export const getUid = () => {
  const { search, hash } = location;
  const { token } = queryString.parse(
    search || hash?.substring(hash?.indexOf?.("?") + 1),
  );
  return (
    token?.split("-")[0] ||
    localStorage.getItem("_user_id") ||
    Cookies.get("token")?.split("-")[0] ||
    0
  );
};

export const getToken = async () => {
  const { search, hash } = location;
  const { token } = queryString.parse(
    search || hash?.substring(hash?.indexOf?.("?") + 1),
  );
  if (token) {
    return token;
  }
  const isMstInApp = mstJsBridge?.isInMstApp();
  if (isMstInApp) {
    const res = await mstJsBridge?.getToken();
    if (res?.data?.token) {
      return res.data.token;
    }
  }
  return Cookies.get("token");
};

// 获取本地存储
// 后期对于缓存做有效期管理时可以统一控制，因此二封
export const getLocalStorage = (key: string) => {
  if (key) {
    const cacheDataRes = localStorage.getItem(key);
    return cacheDataRes;
  }
  return null;
};

// 获取本地存储 ToValue
export const getLocalStorageToValue = (key: string) => {
  if (key) {
    try {
      const cacheDataRes = JSON.parse(localStorage.getItem(key)) || {};
      return cacheDataRes.value;
    } catch (error) {
      console.error("getLocalStorage error", error);
      return undefined;
    }
  }
  return undefined;
};

/**
 * 数值展示
 * 1万以内显示完整的数字，超过1万显示为a.b万，b为0时不显示。如1万，2.7万
 */

export const handleNumberToTenThousand = (n: number) => {
  if (!Number.isInteger(n)) {
    return "-";
  }
  if (n < 10000) {
    return n;
  }
  const toTenThousand = n / 10000;
  return n % 10000
    ? `${Math.round(toTenThousand * 10) / 10}万`
    : `${toTenThousand}万`;
};

/** 转换float类型的数字，整数就不显示 .0 了 */
export const formatFloat = (numerator: any, denominator: any) => {
  try {
    if (!numerator || !denominator) {
      return 0;
    }
    const numeratorNum = Number(numerator);
    const denominatorNum = Number(denominator);
    if (Number.isNaN(numeratorNum) || Number.isNaN(denominatorNum)) {
      return 0;
    }
    return parseInt(`${(numeratorNum / denominatorNum) * 1000}`, 10) / 10;
  } catch (error) {
    return 0;
  }
};

/** 去除query string上指定key的值 */
export function removeQueryKey(url: string, keys: string[]) {
  const newSearch = keys.reduce((newUrl, key) => {
    const reg = new RegExp(`&${key}=[^&\/]*`, "g");
    const replaceReg = new RegExp(`\\?${key}=[^&\/]*&?`, "g");
    return newUrl.replace(reg, "").replace(replaceReg, "?");
  }, url);
  return newSearch;
}

/** 对象转为queryString */
export const objectToUrlParams = (data, isPrefix = false) => {
  const prefix = isPrefix ? "?" : "";
  const result = [];
  // eslint-disable-next-line guard-for-in
  for (const key in data) {
    const value = data[key];
    // 去掉为空的参数
    if (["", undefined, null].includes(value)) {
      // eslint-disable-next-line no-continue
      continue;
    }
    if (value.constructor === Array) {
      value.forEach((_value) => {
        result.push(
          `${encodeURIComponent(key)}[]=${encodeURIComponent(_value)}`,
        );
      });
    } else {
      result.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
    }
  }
  return result.length ? prefix + result.join("&") : "";
};

/** 拼接分享出去的落地页地址 */
export enum EJumpType {
  outside,
  inside,
}
interface ICreateURLByType {
  type?: EJumpType;
  path: string;
  // 原search，要从useLocation().search里取
  originSearch: string;
  // 要移除的key
  removeQueryKeys?: string[];
  // 要增加的key value
  addQueryObject?: Record<string, any>;
}
export function createURLByType(props: ICreateURLByType) {
  const { path, addQueryObject = {}, originSearch, type } = props;
  let newRemoveQueryKeys = props.removeQueryKeys || [];
  if (type === EJumpType.outside) {
    newRemoveQueryKeys = [...newRemoveQueryKeys, "from", "referUrl", "token"];
  }
  let search = removeQueryKey(originSearch, newRemoveQueryKeys);
  // 是否要增加连接符 &
  function shouldAddJoiner() {
    return Object.keys(addQueryObject).some((key) => {
      return !["", undefined, null].includes(addQueryObject[key]);
    })
      ? "&"
      : "";
  }
  search = `${search}${
    !search || search === "?" ? "" : shouldAddJoiner()
  }${objectToUrlParams(addQueryObject, !search)}`;
  if (type === EJumpType.outside) {
    return `${window.location.origin}/ewtcustomerh5${path}${search}`;
  }
  return `${path}${search}`;
}

/**
 * 控件点击
 * @param {string} eventCode 事件编码
 * @param {object} more 更多参数
 */
export function clickPv(eventCode, more = {}) {
  try {
    MstQtAnalytics.clickPv({
      eventCode,
      ...more,
    });
  } catch (e) {
    console.log("click 埋点失败", e);
  }
}

/**
 * 控件曝光
 * @param {string} eventCode 事件编码
 * @param {object} more 更多参数
 */
export function expPv(eventCode, more = {}) {
  try {
    MstQtAnalytics.record({
      eventCode,
      eventType: "EXP",
      ...more,
    });
  } catch (e) {
    console.log("exp pv 埋点失败", e);
  }
}

// 打开webview
export function openUrlInWebView(url = "", title = "") {
  openRoute({
    domain: "web",
    action: "open_webview",
    params: {
      title,
      url,
    },
  });
}

// 打开课程包播放页面
export function openVideoPackage(coursePackageId: string | number) {
  openRoute({
    domain: "course",
    action: "open_detail",
    params: {
      id: coursePackageId,
    },
  });
}

// 调起app
export const openRoute = (route: any) => {
  try {
    mstJsBridge.openNative(route);
  } catch (e) {
    console.log(e);
  }
};

// 获取app信息
export const getNativeData = (route: any) => {
  try {
    return mstJsBridge.getNativeData(route);
  } catch (e) {
    console.log(e);
  }
  return {};
};

/** 过滤掉 为 false 的 的样式类 */
export const cls = (names: Array<string | false | undefined>) =>
  names.filter((v) => v).join(" ");

/**
 * 防抖
 * func: 需要执行的函数
 * delay: 毫秒数，防抖间隔，默认500毫秒执行一次
 */
export function debounce(func, delay = 500) {
  let timer = null;
  return (...rest) => {
    clearTimeout(timer);
    timer = setTimeout(() => {
      func.apply(this, rest);
    }, delay);
  };
}

/** 函数节流 */

export const throttle = (func, wait = 200) => {
  let timeout;
  return function () {
    let context = this;
    let args = arguments;
    // 若没有定时器，说明上一次设定的定时器已到时销毁
    if (!timeout) {
      timeout = setTimeout(function () {
        func.apply(context, args);
        timeout = null;
      }, wait);
    }
  };
};

/**
 * 比较版本号的大小，如果curV 大于 reqV，则返回true，否则返回false
 * @param {String} curV 当前版本.
 * @param {String} reqV 比较版本.
 * @return {Boolean} 当前版本小于比较版本返回 true
 * 使用
 * VersionCompare("9.9.9","10.0.3"); // false.
 * VersionCompare("10.0.3", "10.0.3"); // true.
 * VersionCompare("10.0.4", "10.0.3"); // true.
 */
export const versionCompare = (curV: string, reqV: string) => {
  if (curV === reqV) {
    return true; // 相等情况下也为true
  }
  const arr1 = curV.toString().split("."); // 当前版本号分割后数组
  const arr2 = reqV.toString().split("."); // 对比版本号分割后数组
  var minL = Math.min(arr1.length, arr2.length); // 最小值
  let pos = 0; // 当前比较位
  let diff = 0; // 当前为位比较是否相等
  let flag = false;
  // 逐个比较如果当前位相等则继续比较下一位
  while (pos < minL) {
    diff = parseInt(arr1[pos]) - parseInt(arr2[pos]);
    if (diff == 0) {
      pos++;
      continue;
    } else if (diff > 0) {
      flag = true;
      break;
    } else {
      flag = false;
      break;
    }
  }
  return flag;
};
/** 转换日期格式 */
export function convertDateFormat(dateString: string) {
  if (!dateString) {
    return "";
  }
  const dateArr = dateString.split("-");
  return `${dateArr[0]}年${+dateArr[1]}月${+dateArr[2]}日`;
}

/** 将秒数转换为xx分xx秒 */
export function covertSecondToProgress(seconds: number) {
  if (seconds) {
    const minutes = Math.floor(seconds / 60); // 计算分钟
    const remainingSeconds = seconds % 60; // 计算剩余的秒数
    return minutes + "分" + remainingSeconds + "秒";
  }
  return "";
}

// 获取用户id
export function getUserInfo() {
  try {
    const token = getUrlParam("token");
    const userId = token.split("-")[0];
    if (!userId) {
      return "";
    }
    return userId;
  } catch (error) {
    return "";
  }
}

/**
 * 根据code，判断错误是否在忽略列表中
 * @param {Error} error 错误
 * @param {array} codeList 错误码列表，支持数字和字符串
 * @returns {boolean}
 */
export function isIgnoreError(error: any, codeList: (string | number)[] = []) {
  try {
    if (
      error &&
      error.code &&
      codeList &&
      codeList.length &&
      codeList.some((code) => `${error.code}` === `${code}`)
    ) {
      return true;
    }
  } catch (e) {
    // do nothing
  }
  return false;
}

/** 延时方法
 * @param {*} time 毫秒
 */
export function sleep(time: number) {
  return new Promise((resolve) => setTimeout(resolve, time));
}

export async function getChannel() {
  try {
    const { data } = await mstJsBridge.getNativeData({
      domain: "systemInfo",
      action: "getChannel",
      params: {},
    });
    if (data?.channel) {
      return data.channel;
    }
  } catch (error) {
    console.error("获取渠道号失败", error);
  }
  return "";
}

/**
 * 转换桌面时间
 * 小于120分钟显示分钟，大于120分钟显示x.x小时
 * @param time 时间
 * @returns 时间
 */
export const parseDeskPlanTime = (time: number) => {
  if (!time) {
    return "";
  }
  if (time <= 120) {
    return `${time}分钟`;
  }
  return `${(time / 60).toFixed(1)}小时`;
};

/** 获取安全区域底部高度 */
export function getSafeAreaBottom() {
  try {
    const div = document.createElement("div");
    div.style.position = "fixed";
    div.style.left = "0";
    div.style.right = "0";
    div.style.bottom = "0";
    div.style.height = "env(safe-area-inset-bottom)";
    document.body.appendChild(div);

    const height = parseInt(window.getComputedStyle(div).height, 10) || 0;
    document.body.removeChild(div);

    return height;
  } catch (error) {
    console.log("获取安全区域底部高度失败:", error);
  }
  return 0;
}
