/**
 * 任务队列
 * @param name 队列名称
 * @param action 任务执行方法
 * @param batchSize 同时执行的任务数量
 */
export default class TaskQueue<T> {
  // 队列名称
  private name: string = "任务队列";
  // 等待加载的任务 ID 列表
  private waitingIds: string[] = [];
  // 正在运行的任务 ID 列表
  private runningIds: string[] = [];
  // 同时执行的 ID 数量
  private batchSize: number = 1;
  // 任务执行结果缓存
  private dataCache: Record<string, ITaskCache<T>> = {};
  // 任务停止时使用
  private _doClear: number = 0;
  // 实际执行方法
  private action: (ids: string[]) => Promise<Record<string, any>>;
  // 数据更新通知
  private onUpdate: (ids: string[], emptyIds: string[]) => void;
  constructor(
    name: string,
    action: (ids: string[]) => Promise<any>,
    onUpdate: (ids: string[], emptyIds: string[]) => void,
    batchSize: number = 1,
  ) {
    this.name = name;
    this.action = action;
    this.onUpdate = onUpdate;
    this.batchSize = batchSize;
  }

  // 塞入任务 ID
  push(ids: string[]) {
    this.waitingIds = [...this.waitingIds, ...ids];
  }

  // 执行任务，并返回首次执行结果
  async start() {
    if (this.runningIds.length > 0) {
      return;
    }
    const now = this._doClear;
    const tempIds = new Set<string>();
    while (this.waitingIds.length > 0 && tempIds.size < this.batchSize) {
      const id = this.waitingIds.shift();
      if (id && !this.dataCache[id] && !tempIds.has(id)) {
        tempIds.add(id);
      }
    }
    if (tempIds.size === 0) {
      return;
    }
    const ids = Array.from(tempIds);
    this.runningIds = ids;
    let emptyIds: string[] = [];
    try {
      const data = await this.action(ids);
      if (now !== this._doClear) {
        return;
      }
      ids.forEach((id) => {
        if (!data[id]) {
          emptyIds.push(id);
        }
        this.dataCache[id] = {
          isError: false,
          data: data[id],
        };
      });
    } catch (e) {
      console.log(`${this.name} 执行失败`, e);
      if (now !== this._doClear) {
        return;
      }
      ids.forEach((id) => {
        this.dataCache[id] = {
          isError: true,
        };
      });
    }
    try {
      this.onUpdate(ids, emptyIds);
    } catch (e) {
      console.log(`${this.name} 通知执行失败`, e);
    }
    this.runningIds = [];
    // 递归执行
    this.start();
  }

  // 停止任务并清空队列
  clearQuery() {
    this._doClear++;
    this.waitingIds = [];
    this.runningIds = [];
  }

  // 重新执行某任务
  retry(id: string) {
    // 清理之前的状态
    delete this.dataCache[id];
    // 插入首位
    this.waitingIds.splice(0, 0, id);
    // 开始执行
    this.start();
  }

  // 获取某任务执行状态
  getStatus(id: string): ITaskCache<T> {
    return (
      this.dataCache[id] || {
        isLoading: true,
      }
    );
  }

  // 重置状态
  reset() {
    this._doClear++;
    this.waitingIds = [];
    this.runningIds = [];
    this.dataCache = {};
  }
}

export interface ITaskCache<T> {
  isError?: boolean;
  isLoading?: boolean;
  data?: T;
}
