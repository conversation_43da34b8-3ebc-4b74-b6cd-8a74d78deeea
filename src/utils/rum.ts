const envMap = {
  pre: "pre",
  prod: "prod",
  sit: "daily",
  dev: "daily",
};

function initRum() {
  try {
    const ArmsRum = require("@arms/rum-browser").default;

    ArmsRum.init({
      pid: "aocb5mxsv0@8c63f8365350fb5",
      endpoint: "https://aocb5mxsv0-default-cn.rum.aliyuncs.com",
      // 环境情况
      env: envMap[process.env.DEPLOYMENT_ENV] || "local",
      // 灰度信息利用版本上传
      version: window.__MST_LABEL__ || "baseline",
      // history 路由
      spaMode: "history",
      sessionConfig: {
        // Session 采样率：[0, 1]，默认 5%
        sampleRate: Math.max(
          0,
          Math.min(1, 1 / (window.ARMS_GLOBAL_CONFIG_SAMPLE || 20)),
        ),
      },
      filters: {
        resource: [
          // 忽略 qt 上报失败的情况
          "https://quickaplus-he-api-cn-shanghai.aliyuncs.com/",
        ],
        exception: [
          /^ResizeObserver loop/,
          /^Loading( CSS)? chunk [^ ]+ failed/,
          /^Network Error/,
          /^timeout of/,
          /^Script error\.?$/,
          /Request failed with status 0/,
        ],
      },
      properties: {
        buildTime: __BUILD_TIME__ || "0",
      },
    });
  } catch (e) {
    console.error("rum 初始化失败", e);
  }
}

export default initRum;
