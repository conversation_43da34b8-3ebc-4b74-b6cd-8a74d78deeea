/**
 * 文本高度计算器
 * 用于计算指定文本在给定样式下的渲染高度
 *
 * @example
 * ```ts
 * // 创建计算器实例
 * const calculator = new TextHeightCalculator({
 *   fontSize: '14px',
 *   lineHeight: '1.5',
 *   width: '300px'
 * });
 *
 * // 计算文本高度
 * const height = calculator.calculateHeight('需要计算高度的文本内容');
 * console.log(`文本高度: ${height}px`);
 *
 * // 清除缓存
 * calculator.clearCache();
 *
 * // 使用完后记得销毁实例
 * calculator.destroy();
 * ```
 */
export default class TextHeightCalculator {
  private cache: { [key: string]: number };
  private minLength: number;
  private defaultHeight: number;
  private element: HTMLDivElement;

  constructor(
    style: Partial<CSSStyleDeclaration>,
    minLength: number = 0,
    defaultHeight: number = 20,
  ) {
    this.cache = {};
    this.minLength = minLength;
    this.defaultHeight = defaultHeight;

    // 初始化时创建DOM元素
    this.element = document.createElement("div");

    // 应用样式
    Object.assign(this.element.style, style, {
      position: "absolute",
      visibility: "hidden",
      wordWrap: "break-word",
      left: 0,
      top: 0,
      pointerEvents: "none",
    });

    // 将元素添加到DOM中
    document.body.appendChild(this.element);
  }

  /**
   * 计算文本高度
   * @param text 要计算的文本
   * @returns 文本高度(px)
   */
  calculateHeight(text: string): number {
    // 当文本长度小于最小长度时，直接返回默认高度
    if (text.length < this.minLength) {
      return this.defaultHeight;
    }

    // 检查缓存
    const cachedHeight = this.cache[text];
    if (cachedHeight !== undefined) {
      return cachedHeight;
    }

    // 设置文本内容
    this.element.textContent = text;

    // 获取实际高度
    const height = this.element.offsetHeight;

    // 存入缓存
    this.cache[text] = height;

    return height;
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache = {};
  }

  /**
   * 销毁实例时清理DOM
   */
  destroy(): void {
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
  }
}
