export enum ErrorCodeEnum {
  /**
   * 当前进行中的计划过多，请先完成一部分后再添加新课程
   */
  TOO_MANY_LESSON = 7772001,
  /**
   * 课程已经在计划中了，请添加其他课程
   */
  LESSON_IN_PLAN = 7772002,

  /**
   * 模板计划已经在计划中了，请添加其他模板
   */
  TEMPLATE_IN_PLAN = 7774007,

  /**
   * 用户没有e游记对应的nodeId（城市节点id）
   */
  NO_NODE_ID = 6080309,

  /**
   * 非会员
   */
  NOT_VIP = 9091401,
  /**
   * 非至尊会员
   */
  NOT_SVIP = 9091402,
  /**
   * 会员等级不够
   */
  VIP_LEVEL = 9091403,

  /**
   * 未登录
   */
  NOT_LOGIN = 2001106,
}

/**
 * 无权限 code 列表
 */
export const NO_PERMISSION = [
  ErrorCodeEnum.NOT_VIP,
  ErrorCodeEnum.NOT_SVIP,
  ErrorCodeEnum.VIP_LEVEL,
];

export enum ERoutePath {
  DaySignRootPath = "/DaySign",
  IntentFormH5Path = "/IntentFormH5",
  IntentionH5 = "/IntentionH5",
  IntentionPC = "/IntentionPC",
  RegisterFormH5Path = "/RegisterFormH5",
  IntentFormPcPath = "/IntentFormPc",
  GuideTaskPath = "/GuideTask",
  UnifyAuthenticationPath = "/unifyAuthentication",
  UnifyAuthenticationPcPath = "/unifyAuthentication/webNoAuth",
  UnifyAuthenticationH5Path = "/unifyAuthentication/nativeNoAuth",

  SupervisePath = "/parents/supervise",
  Apple = "/parents/apple",
  Huawei = "/parents/huawei",
  Xiaomi = "/parents/xiaomi",
  Oppo = "/parents/oppo",
  Vivo = "/parents/vivo",
  Meizu = "/parents/meizu",
  Other = "/parents/other",
  // 家长监督iPad
  SuperviseIpadPath = "/parents/superviseIpad",
  AppleIpad = "/parents/appleIpad",
  HuaweiIpad = "/parents/huaweiIpad",
  XiaomiIpad = "/parents/xiaomiIpad",
  OtherIpad = "/parents/otherIpad",
  // 家长监督iPad
  SuperviseHDPath = "/parentsHD/supervise",
  AppleHD = "/parentsHD/apple",
  HuaweiHD = "/parentsHD/huawei",
  XiaomiHD = "/parentsHD/xiaomi",
  OtherHD = "/parentsHD/other",

  // 排行榜
  RankingPath = "/ranking",

  //学科训练营首页 - H5
  SubjectExerciseHomeH5 = "/set-sail-activity-h5/subject-exercise-home",
  //学科训练营检测页 - H5
  SubjectExerciseTestH5 = "/set-sail-activity-h5/subject-exercise-test",
  //学科训练营首页 - PC
  SubjectExerciseHomePC = "/set-sail-activity-pc/subject-exercise-home",

  // 假期加油站 - H5
  SelectTextbookVersionH5 = "/single-point-page/#/holiday-selection-h5/select-textbook-version",
  VacationChargingStationH5 = "/holiday-selection-h5/home",
  // 寒假 - 自主学习 - 高一学科提升
  SelfLearningForSubject = "/self-learning-for-subject",
  // 高一上、高二上周末智学
  WeekStudy = "/week-study",

  // 用户激励 - 奖品记录
  PrizeRecord = "/user-incentive/prize-record",
}

/**
 * 学科枚举
 */
export enum SubjectEnum {
  // 语文
  CHINESE = 1,
  // 数学
  MATH = 2,
  // 英语
  ENGLISH = 3,
  // 物理
  PHYSICS = 4,
  // 化学
  CHEMISTRY = 5,
  // 生物
  BIOLOGY = 6,
  // 政治
  POLITICS = 7,
  // 历史
  HISTORY = 8,
  // 地理
  GEOGRAPHY = 9,
  // 信息技术
  INFORMATION = 10,
  // 通用技术
  GENERAL = 11,
}
