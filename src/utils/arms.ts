function initArms() {
  try {
    const BrowserLogger = require("@arms/js-sdk");

    const regex = new RegExp(
      "^(?!(/|" +
        window.location.host +
        "|gateway\\d*\\.ewt360\\.com|\\S*\\.mistong\\.com))",
    );
    const sample = window.ARMS_GLOBAL_CONFIG_SAMPLE || 20;
    const version = window.__MST_LABEL__ || "baseline";
    const environment = ["prod", "pre"].includes(process.env.DEPLOYMENT_ENV)
      ? process.env.DEPLOYMENT_ENV
      : "daily";
    BrowserLogger.singleton({
      pid: "aocb5mxsv0@ffada3e1421ae91",
      appType: "web",
      imgUrl: "https://arms-retcode.aliyuncs.com/r.png?",
      sendResource: true,
      enableLinkTrace: true,
      behavior: true,
      useFmp: true,
      enableSPA: true,
      // 采样率读取全局配置 默认为 5%（按 1/sample 比例采样）
      sample: sample,
      pvSample: sample,
      ignore: {
        ignoreApis: [regex],
        ignoreErrors: [
          /^ResizeObserver loop/,
          /^Loading( CSS)? chunk [^ ]+ failed/,
          /^Network Error/,
          /^timeout of/,
          /^Script error\.?$/,
          /Request failed with status 0/,
        ],
      },
      // 先不做过滤
      // beforeReport: function(data) {
      //   if (data && data.t === 'error') {
      //   }
      //   return data;
      // },
      release: version,
      environment: environment,
      c1: __BUILD_TIME__ || "0",
    });
  } catch (e) {
    console.error("arms初始化失败", e);
  }
}

export default initArms;
