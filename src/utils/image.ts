/**
 * 检查浏览器是否支持 webp 格式图片
 * @returns Promise<boolean> webp支持状态
 */
export function checkWebpSupport(): Promise<boolean> {
  return new Promise((resolve) => {
    const webp = new Image();

    // 用于测试的最小的webp图片base64编码
    webp.src =
      "data:image/webp;base64,UklGRiQAAABXRUJQVlA4IBgAAAAwAQCdASoBAAEAAwA0JaQAA3AA/vuUAAA=";

    // 图片加载完成时检查是否正常显示
    webp.onload = function () {
      resolve(webp.width > 0 && webp.height > 0);
    };

    // 图片加载失败时表示不支持webp
    webp.onerror = function () {
      resolve(false);
    };
  });
}

const supportInfo = {
  webp: false,
};

async function initWebpSupport() {
  try {
    supportInfo.webp = await checkWebpSupport();
  } catch (error) {
    console.log("检测浏览器是否支持 webp 失败", error);
  }
}

// 一开始就检测是否支持 webp
initWebpSupport();

// 支持缩略图的域名（阿里 cdn）
const SUPPORT_DOMAINS = [
  "cdn.ewt360.com",
  "cdn.test.ewt360.com",
  "cdn.dev.ewt360.com",
  "si.ewt360.com",
  "test-si.ewt360.com",
];
/**
 * 利用阿里云 oss 能力，给阿里云下的图片资源做缩略图使用
 * 支持 cdn.ewt360.com 和 si.ewt360.com 等
 * @param {string} url 源地址
 * @param {number} width 宽
 * @param {number} height 高
 * @param {string} format 默认 webp，若不支持 webp，默认 png
 * @returns {string} 缩略图地址
 */
export function makeThumbnailImage(
  url: string,
  width: number,
  height: number,
  format: string = "webp",
): string {
  try {
    if (!url) {
      return "";
    }
    const domain = url.split("//")[1].split("/")[0];
    let newFormat = format;
    if (format === "webp" && !supportInfo.webp) {
      newFormat = "png";
    }
    if (url.indexOf("?") === -1 && SUPPORT_DOMAINS.indexOf(domain) > -1) {
      return `${url}?x-oss-process=image/resize,w_${width},h_${height},m_lfit/format,${newFormat}`;
    }
  } catch (e) {
    // do nothing
  }
  return url;
}
