import { create, CustomAxiosRequestConfig } from "@ewt/request";
import Cookies from "js-cookie";
import { Toast } from "antd-mobile";
import ECustomerApi from "mst-customer-api";
import mstJsBridge from "mst-js-bridge";
import { ErrorCodeEnum, NO_PERMISSION } from "./constant";

interface ResponseData<T = any> {
  code?: string | number;
  data?: T;
  msg?: string;
}
export type RequestResponse<T = any> = Promise<ResponseData<T>>;

const fetcher = create({
  withCredentials: false,
  timeout: 30000,
  headers: {
    "Access-Control-Allow-Origin": "*",
    "content-type": "application/json",
  },
});

fetcher.interceptors.request.use(
  (config: CustomAxiosRequestConfig) => {
    const { params = {}, data } = config;
    config.params = {
      _: Date.now(),
      // 添加无需忽略错误的参数
      ignoreError: data?.ignoreError || void 0,
      ...params,
    };
    // 告知拦截层是否静默
    config.silentMode = !!config.params.ignoreError;
    return config;
  },
  (error) => Promise.reject(error),
);

function checkStatus(response) {
  const code = Number(response.data.code);
  const { msg } = response.data;
  response.data.code = code;
  if (code !== 200) {
    console.error(`
      XHR request error, the request url is ${response.config.url},
      code is ${response.data.code},
      msg is ${msg}`);
    if (ErrorCodeEnum.NOT_LOGIN === code) {
      localStorage.removeItem("token");
      Cookies.remove("token");
      Cookies.remove("ewt_user");
      const inApp = mstJsBridge.isInMstApp();
      if (inApp) {
        Toast.show({ content: msg });
      }
    } else if (NO_PERMISSION.indexOf(code) !== -1) {
      try {
        ECustomerApi.toNoPermissionPage(code, msg);
      } catch (e) {
        console.log(e);
      }
    } else {
      // 部分情况想要隐藏全局错误
      !response?.config?.params?.ignoreError &&
        Toast.show({ content: msg || "网络异常, 请稍后再试!" });
    }
    return Promise.reject({ ...response.data });
  }
  return response.data;
}

// 拦截器,response之后
fetcher.interceptors.response.use(
  // 这里的 checkStatus 返回其实不符合 axios 预期，不过为了方便，没有做处理
  (response) => checkStatus(response),
  (e) => {
    // 非限流，且 不隐藏全局抛错，则统一抛出错误
    if (e && !e.isIntercepted && !e.config?.params?.ignoreError) {
      Toast.show({ content: "网络异常, 请稍后再试!" });
    }
    return Promise.reject(e);
  },
);

export default fetcher;
