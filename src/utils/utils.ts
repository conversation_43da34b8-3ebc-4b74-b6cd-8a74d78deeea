import Cookies from "js-cookie";
import dayjs from "dayjs";
import queryString from "query-string";
import { web } from "./hosts";

/**
 * 防抖
 * func: 需要执行的函数
 * delay: 毫秒数，防抖间隔，默认600毫秒执行一次
 */
export function debounce(func = () => {}, delay = 600) {
  let timer = null;
  return (...rest) => {
    clearTimeout(timer);
    timer = setTimeout(() => {
      func.apply(this, rest);
    }, delay);
  };
}

// 将时间戳转换成日期格式,默认（YYYY-MM-DD HH:mm:ss）
export function formDate(timestamp, format) {
  return dayjs(Number(timestamp)).format(format || "YYYY-MM-DD HH:mm:ss");
}

export function getToken(tokenKey = "token") {
  const { search } = location;
  const { token } = queryString.parse(search);
  return token;
}

export function toLogin() {
  Cookies.remove("token");
  window.location.href = `${web}/register/#/login?fromurl=${encodeURIComponent(window.location.href)}`;
}

// 对错误返回的统一处理，不想业务代码内一直判断
export const errorResponse = (error) => {
  try {
    const codeNum = Number(error?.code) || 0;
    if ([2001106, 117001].includes(codeNum)) {
      toLogin();
    }
  } catch (error) {
    console.log("接口返回处理出错，信息：", error);
  }
};

/**
 * 同步接口，判断是否在App中
 * 参照mst-bridge代码处理
 * @returns {boolean}
 */
export function getIsInMSTApp() {
  const {
    navigator: { userAgent },
  } = window;
  return userAgent.indexOf("EWT/") > -1;
}
