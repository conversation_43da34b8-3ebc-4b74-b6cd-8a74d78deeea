import React, { useState, useEffect, useRef } from "react";
import Header from "./components/header";
import styles from "./index.module.scss";
import Summary from "./components/summary";
import {
  IThemeAward,
  getThemeAward,
  getMedalList,
  IMedalItem,
  IThemeAwardItem,
} from "./apis";
import {
  closeWebview,
  getSafeAreaTop,
  openWebView,
  openRoute,
} from "@/utils/bridge-utils";
import Detail from "./components/detail";
import Medal from "./components/medal";
import { useSearchParams } from "react-router-dom";
import SafeLogger from "@/utils/safe-logger";
import { Toast } from "antd-mobile";
import DelayPageLoading from "@/components/delay-page-loading";
import ErrorInfo from "@/components/error-info";
import { useVisibilitychange } from "@/hooks";
import EmptyInfo from "@/components/empty-info";
import CustomizeMedalModal from "@/components/medal-detail-modal";
import { getMedalSwitch } from "@/service/medal";
import MSTAnalytics from "mst-analytics";
import { clickPv } from "@/utils/tool";

export const SECRET_TOPIC_ID = "-1";

interface ICampInfo {
  phaseId: string;
  campId: string;
}

const Achievement = () => {
  const [searchParams] = useSearchParams();
  const loggerRef = useRef<SafeLogger>();
  const [loading, setLoading] = useState(false);
  const campInfoRef = useRef<ICampInfo>();
  const [safeTop, setSafeTop] = useState(0);
  const [info, setInfo] = useState<IThemeAward>();
  const [medalList, setMedalList] = useState<IMedalItem[]>([]);
  const [isError, setIsError] = useState(false);
  const isRefreshingRef = useRef(true);
  const [showMedal, setShowMedal] = useState("");
  const [isInitEnd, setIsInitEnd] = useState(false);
  const [showMoreMedal, setShowMoreMedal] = useState(false);

  const initSafeTop = async () => {
    const safeTop = await getSafeAreaTop();
    setSafeTop(safeTop);
  };

  const initInfo = async () => {
    setLoading(true);
    const campInfo = campInfoRef.current;
    try {
      const [infoRes, medalListRes, medalSwitchRes] = await Promise.all([
        getThemeAward(campInfo),
        getMedalList({ ...campInfo, ignoreError: true }).catch((error) => {
          loggerRef.current?.warn("medal-list-error", {
            error,
            params: { ...campInfo },
          });
        }),
        getMedalSwitch({ ignoreError: true }).catch((error) => {
          loggerRef.current?.warn("medal-switch-error", {
            error,
            params: { ...campInfo },
          });
        }),
      ]);
      setInfo(infoRes.data);
      setMedalList((medalListRes && medalListRes.data) || []);
      setIsError(false);
      setIsInitEnd(true);
      setShowMoreMedal(!!(medalSwitchRes && medalSwitchRes.data));
      isRefreshingRef.current = false;
    } catch (error) {
      loggerRef.current?.error("page-blocking", {
        reason: "init-info-error",
        error,
        params: { ...campInfo },
      });
      setIsError(true);
    }
    setLoading(false);
  };

  const refreshInfo = async () => {
    const campInfo = campInfoRef.current;
    if (isRefreshingRef.current || !campInfo) {
      return;
    }
    isRefreshingRef.current = true;
    const params = { ...campInfo, ignoreError: true };
    try {
      const [infoRes, medalListRes] = await Promise.all([
        getThemeAward(params),
        getMedalList(params),
      ]);
      setInfo(infoRes.data);
      setMedalList((medalListRes && medalListRes.data) || []);
    } catch (error) {
      loggerRef.current?.warn("refresh-info-error", {
        error,
        params,
      });
    }
    isRefreshingRef.current = false;
  };

  useVisibilitychange({
    handleVisibilitychange: (visible) => {
      if (visible) {
        refreshInfo();
      }
    },
  });

  const onShowMedal = (medal: IMedalItem) => {
    setShowMedal(medal.medalId);
  };

  const onToCollect = (item: IThemeAwardItem) => {
    if (item.themeId === SECRET_TOPIC_ID || !item.themeRoute) {
      return;
    }
    try {
      // 兼容铭师堂协议
      if (item.themeRoute.startsWith("mistong://")) {
        openRoute(JSON.parse(item.themeRoute.slice("mistong://".length)));
      } else {
        openWebView(item.themeRoute);
      }
    } catch (error) {
      console.log(error);
    }
    clickPv("ewt_h5_base_operation_camp_achievement_collect_click", {
      camp_id: campInfoRef.current?.campId,
    });
  };

  // 链接不合法，强制退出页面
  const invalidEntry = () => {
    Toast.show({
      content: "页面路径不合法，3 秒后自动退出",
      duration: 0,
      maskClickable: false,
    });
    setTimeout(() => {
      closeWebview();
    }, 3000);
    loggerRef.current?.error("invalid-visit");
  };

  useEffect(() => {
    loggerRef.current = new SafeLogger("camp-achievement");
    const phaseId = searchParams.get("phaseId");
    const campId = searchParams.get("campId");
    initSafeTop();
    if (phaseId && campId) {
      campInfoRef.current = { phaseId, campId };
      initInfo();
      MSTAnalytics.separatelyReport(
        "ewt_h5_base_operation_camp_achievement_view",
        { camp_id: campId },
      );
    } else {
      invalidEntry();
    }
  }, []);

  const isEmpty = !info?.themeAwardList?.length;

  let content = null;
  if (isError) {
    content = <ErrorInfo type="light" onRetry={() => initInfo()} />;
  } else if (isInitEnd) {
    if (isEmpty) {
      content = (
        <EmptyInfo
          wordStyle={{ color: "#666666" }}
          text="同学，任务正在准备中，请等待1-2分钟后再来哦"
        />
      );
    } else if (info) {
      content = (
        <>
          <Summary
            info={info}
            secretTopicId={SECRET_TOPIC_ID}
            onClickItem={onToCollect}
          />
          {!!medalList?.length && (
            <Medal
              showMoreMedal={showMoreMedal}
              medalList={medalList}
              onShowMedal={onShowMedal}
            />
          )}
          <Detail
            info={info}
            secretTopicId={SECRET_TOPIC_ID}
            onClickItem={onToCollect}
          />
        </>
      );
    }
  }

  return (
    <div className={styles.container}>
      <Header safeTop={safeTop} ruleUrl={info?.ruleUrl} />
      <div className={styles.content}>
        <div className={styles.list}>{content}</div>
      </div>
      <CustomizeMedalModal
        visible={!!showMedal}
        onCancel={() => setShowMedal("")}
        medalCode={showMedal}
      />
      <DelayPageLoading loading={loading} />
    </div>
  );
};

export default Achievement;

export const Component = Achievement;
