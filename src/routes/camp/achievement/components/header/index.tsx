import { IconSvg } from "@/components";
import React, { useEffect } from "react";
import styles from "./index.module.scss";
import {
  changeStatusBarStyle,
  closeWebview,
  openWebView,
} from "@/utils/bridge-utils";

const Header = ({
  safeTop,
  ruleUrl,
}: {
  safeTop: number;
  ruleUrl?: string;
}) => {
  const onBack = () => {
    closeWebview();
  };

  const onRule = () => {
    if (ruleUrl) {
      openWebView(ruleUrl);
    }
  };

  useEffect(() => {
    changeStatusBarStyle(2);
  }, []);

  return (
    <div className={styles.container} style={{ paddingTop: safeTop || 0 }}>
      <div className={styles.star}></div>
      <div className={styles.title}>
        <div className={styles.back} onClick={onBack}>
          <IconSvg name="icon-a-jinru2x" />
        </div>
        <div>我的成就</div>
      </div>
      {ruleUrl && (
        <div className={styles.more} onClick={onRule}>
          获奖规则
        </div>
      )}
    </div>
  );
};

export default Header;
