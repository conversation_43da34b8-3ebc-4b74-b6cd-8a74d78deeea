.container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 46px;
  box-sizing: content-box;
  position: relative;
  flex: 0 0 auto;
  z-index: 1;

  .title {
    font-weight: bold;
    font-size: 18px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 42px;
    height: 42px;
    display: flex;
    align-items: center;
    margin-top: 4px;

    .back {
      font-size: 14px;
      width: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      transform: scaleX(-1);
    }
  }

  .more {
    font-weight: bold;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 42px;
    height: 42px;
    padding-right: 12px;
    margin-top: 4px;
  }

  .star {
    position: absolute;
    z-index: -1;
    right: 38px;
    bottom: -32px;
    width: 80px;
    height: 76px;
    background: url("@/assets/camp/achievement/header-star.png") no-repeat center/contain;
  }
}
