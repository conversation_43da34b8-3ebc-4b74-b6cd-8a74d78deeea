import React from "react";
import { IMedalItem } from "../../apis";
import { IconSvg } from "@/components";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import styles from "./index.module.scss";
import { cls } from "@/utils/tool";
import { openWebView } from "@/utils/bridge-utils";
import { web } from "@/utils/hosts";
import LoadingImage from "@/components/loading-image";
import MedalDefaultPng from "@/assets/medal/medal-default.png";

export const Medal = ({
  medalList,
  showMoreMedal,
  onShowMedal,
}: {
  medalList: IMedalItem[];
  showMoreMedal: boolean;
  onShowMedal: (medal: IMedalItem) => void;
}) => {
  const onMore = () => {
    openWebView(
      `https://${web}/user-incentive-system/#/user-growing-up/my-medal-list?showTopBar=false&allows_gestures_navigation=false`,
    );
  };

  return (
    <div className={styles.container}>
      <div className={styles.title_container}>
        <div className={styles.title}>我获得的勋章</div>
        {showMoreMedal && (
          <div className={styles.more} onClick={onMore}>
            更多勋章
            <IconSvg name="icon-a-jinru2x" className={styles.more_icon} />
          </div>
        )}
      </div>
      <div className={styles.desc}>
        收集一定星星数量后可获得勋章，点击勋章查看具体获得方式
      </div>
      <div className={styles.medal_list}>
        <Swiper slidesPerView="auto" threshold={10}>
          {medalList.map((item, index) => (
            <SwiperSlide key={item.medalId}>
              <div
                className={styles.medal_item}
                key={item.medalId}
                onClick={() => onShowMedal(item)}
              >
                <div className={styles.medal_item_img_bg}>
                  <LoadingImage
                    fallback={MedalDefaultPng}
                    src={item.medalImgUrl}
                    className={cls([
                      styles.medal_item_img,
                      !item.hasGet && styles.no_get,
                    ])}
                  />
                </div>

                <div className={styles.medal_item_name}>{item.medalName}</div>
              </div>
            </SwiperSlide>
          ))}

          <SwiperSlide key="empty"></SwiperSlide>
        </Swiper>
      </div>
    </div>
  );
};

export default Medal;
