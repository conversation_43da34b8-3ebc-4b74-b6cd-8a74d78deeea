@import "~@/styles/lib.scss";

.container {
  margin-bottom: 24px;

  .title_container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 16px;

    .title {
      font-weight: bold;
      font-size: 18px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 25px;
    }

    .more {
      font-size: 14px;
      color: #666666;
      line-height: 20px;
      display: flex;
      align-items: center;

      .more_icon {
        font-size: 8px;
        margin-left: 4px;
        color: #bfbfbf;
      }
    }
  }

  .desc {
    font-size: 12px;
    color: #666666;
    line-height: 17px;
    margin-top: 4px;
    width: 343px;
    word-wrap: break-word;
  }

  .medal_list {
    width: 343px;
    height: 172px;
    background: #ffffff;
    border: 1px solid #021e66;
    box-shadow: 2px 2px 0 0 rgba(2, 30, 102, 0.15);
    border-radius: 12px;
    margin-top: 16px;
    padding-top: 16px;
    overflow: hidden;

    :global {
      .swiper {
        .swiper-slide {
          width: auto !important;
          padding-left: 16px;
        }
      }
    }

    .medal_item {
      width: 100px;
      display: flex;
      flex-direction: column;
      align-items: center;

      .medal_item_img_bg {
        width: 100px;
        height: 100px;
        background: rgba(167, 176, 255, 0.1);
        border-radius: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .medal_item_img {
        width: 82px;
        height: 82px;
        background-color: transparent;

        &.no_get {
          filter: grayscale(100%);
        }
      }

      .medal_item_name {
        width: 84px;
        font-size: 12px;
        color: #333333;
        line-height: 16px;
        margin-top: 8px;
        text-align: center;

        @include multi_lines_ellipsis(2);
      }
    }
  }
}
