import React from "react";
import { IThemeAward, IThemeAwardItem } from "../../apis";
import styles from "./index.module.scss";
import { cls } from "@/utils/tool";
import { IconSvg } from "@/components";

export const Detail = ({
  info,
  secretTopicId,
  onClickItem,
}: {
  info: IThemeAward;
  secretTopicId: string;
  onClickItem: (item: IThemeAwardItem) => void;
}) => {
  return (
    <div className={styles.container}>
      <div className={styles.title}>任务明细</div>
      {!!info.fallbackTips && (
        <div className={styles.desc}>{info.fallbackTips}</div>
      )}
      {(info.themeAwardList || []).map((item, index) => (
        <div
          key={item.themeId}
          className={cls([styles.item, `topic-${index % 7}`])}
        >
          <div className={styles.item_title}>{item.themeName}</div>
          <div className={styles.item_progress}>
            <div className={styles.bar}>
              <div
                className={styles.top}
                style={{
                  width: `${(item.getStarNum / item.totalStarNum) * 100}%`,
                }}
              ></div>
            </div>
            <div className={styles.star}></div>
            <div className={styles.get_num}>x{item.getStarNum}</div>
            <div className={styles.total_num}>/{item.totalStarNum}</div>
          </div>
          {(item.taskList || []).map((task, index) => (
            <div
              className={cls([
                styles.task_item,
                task.taskStatus && styles.done,
              ])}
              key={index}
            >
              <div className={styles.task_point}></div>
              <div className={styles.task_title}>{task.taskName}</div>
              <div className={styles.task_done}>
                <IconSvg name="icon-a-wancheng2x" />
              </div>
            </div>
          ))}
          {item.themeId !== secretTopicId && !!item.themeRoute && (
            <div className={styles.item_btn} onClick={() => onClickItem(item)}>
              {item.totalStarNum > item.getStarNum ? "去收集" : "已集完"}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default Detail;
