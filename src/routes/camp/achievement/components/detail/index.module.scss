.container {
  .title {
    font-weight: bold;
    font-size: 18px;
    color: rgba(0, 0, 0, 0.85);
  }

  .desc {
    margin-top: 4px;
    font-size: 12px;
    color: #666666;
    line-height: 17px;
    width: 343px;
    word-wrap: break-word;
  }

  .item {
    width: 343px;
    background: #ffffff;
    border: 1px solid #021e66;
    border-radius: 12px;
    padding: 16px 15px;
    position: relative;
    overflow: hidden;
    margin-top: 16px;

    &::before {
      content: " ";
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      height: 17px;
      background: linear-gradient(
        0deg,
        #ffffff 0%,
        var(--topic-gradient-color) 100%
      );
    }

    .item_title {
      font-weight: bold;
      font-size: 16px;
      color: #333333;
    }

    .item_progress {
      display: flex;
      align-items: center;
      padding: 4px 0;
      height: 25px;

      .bar {
        width: 100px;
        height: 6px;
        background: #e6e6e6;
        border-radius: 3px;
        overflow: hidden;

        .top {
          width: 0%;
          height: 100%;
          background: linear-gradient(270deg, #ff9200 0%, #ffce57 100%);
          border-radius: 3px;
        }
      }

      .star {
        width: 12px;
        height: 12px;
        margin-left: 8px;
        background: url("@/assets/common/star-12.png") no-repeat center/contain;
      }

      .get_num {
        font-weight: bold;
        font-size: 12px;
        color: #333333;
        line-height: 17px;
        margin-left: 4px;
      }

      .total_num {
        font-size: 12px;
        color: #666666;
        line-height: 17px;
      }
    }

    .task_item {
      width: 313px;
      background: #f8f8f8;
      border-radius: 8px;
      display: flex;
      align-items: center;
      padding: 8px 0 7px 25px;
      margin-top: 8px;
      position: relative;

      .task_point {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: var(--topic-point-color);
        border: 1px solid #333333;
        position: absolute;
        left: 9px;
        top: 14px;
      }

      .task_title {
        width: 238px;
        word-wrap: break-word;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
      }

      .task_done {
        font-size: 20px;
        color: #51c41b;
        margin-left: 18px;
        display: none;
        line-height: 20px;
      }

      &.done {
        .task_point {
          background: #999999;
          border-color: #999999;
        }

        .task_title {
          color: #999999;
          text-decoration: line-through;
        }

        .task_done {
          display: block;
        }
      }
    }

    .item_btn {
      width: 70px;
      height: 28px;
      background: #2e86ff;
      border: 1px solid #021e66;
      border-radius: 14px;
      font-weight: bold;
      font-size: 14px;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      right: 16px;
      top: 24px;
    }
  }
}
