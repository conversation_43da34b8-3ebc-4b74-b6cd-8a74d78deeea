.container {
  margin-bottom: 27px;

  .title_container {
    display: flex;
    align-items: center;
    height: 25px;
    line-height: 25px;

    .title {
      font-weight: bold;
      font-size: 18px;
      color: rgba(0, 0, 0, 0.85);
    }

    .star {
      width: 16px;
      height: 16px;
      background: url("@/assets/common/star-16.png") no-repeat center/contain;
    }

    .get_num {
      font-weight: bold;
      font-size: 18px;
      color: #333333;
      margin-left: 4px;
    }

    .total_num {
      font-size: 14px;
      color: #666666;
      line-height: 20px;
      margin-bottom: -1px;
    }
  }

  .desc {
    margin-top: 4px;
    font-size: 12px;
    color: #666666;
    line-height: 17px;
    width: 343px;
    word-wrap: break-word;
  }

  .award_list {
    display: flex;
    flex-wrap: wrap;
    margin-left: -16px;

    .item {
      width: 163px;
      height: 131px;
      box-shadow: 2px 2px 0 0 rgba(2, 30, 102, 0.15);
      border: 1px solid #021e66;
      border-radius: 12px;
      margin-left: 16px;
      margin-top: 16px;
      background: linear-gradient(
        180deg,
        var(--topic-gradient-color) 0%,
        #fff 44.3%,
        #fff 100%
      );
      position: relative;

      .item_star {
        width: 40px;
        height: 40px;
        position: absolute;
        top: 16px;
        left: 27px;
        background: url("@/assets/common/star.png") no-repeat center/contain;
      }

      .item_count {
        position: absolute;
        left: 75px;
        top: 22px;
        height: 29px;
        line-height: 29px;
        display: flex;
        align-items: center;

        .count_get {
          font-weight: bold;
          font-size: 21px;
          color: #333333;
        }

        .count_total {
          font-size: 14px;
          color: #666666;
          line-height: 20px;
          margin-bottom: -3px;
        }
      }

      .item_info {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        top: 47px;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-top: 17px;
        background: url("@/assets/camp/achievement/line.png") no-repeat 0% 0% /
          contain;

        .item_title {
          font-weight: bold;
          font-size: 16px;
          color: #333333;
          max-width: 155px;
          height: 22px;
          line-height: 22px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .item_btn {
          width: 70px;
          height: 28px;
          background: #2e86ff;
          border: 1px solid #021e66;
          border-radius: 14px;
          font-weight: bold;
          font-size: 14px;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 7px;
        }

        .item_desc {
          font-size: 14px;
          color: #666666;
          margin-top: 8px;
          line-height: 20px;
        }
      }
    }
  }
}
