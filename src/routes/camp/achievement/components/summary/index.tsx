import React from "react";
import styles from "./index.module.scss";
import { IThemeAward, IThemeAwardItem } from "../../apis";
import { cls } from "@/utils/tool";

const Summary = ({
  info,
  secretTopicId,
  onClickItem,
}: {
  info: IThemeAward;
  secretTopicId: string;
  onClickItem: (item: IThemeAwardItem) => void;
}) => {
  return (
    <div className={styles.container}>
      <div className={styles.title_container}>
        <div className={styles.title}>我已获得：</div>
        <div className={styles.star}></div>
        <div className={styles.get_num}>x{info.getStarNum}</div>
        <div className={styles.total_num}>/{info.totalStarNum}</div>
      </div>
      <div className={styles.desc}>
        星星可通过完成任务进行收集，收集一定星星后可获得勋章
      </div>
      <div className={styles.award_list}>
        {(info.themeAwardList || []).map((item, index) => (
          <div
            className={cls([styles.item, `topic-${index % 7}`])}
            key={item.themeId}
          >
            <div className={styles.item_star}></div>
            <div className={styles.item_count}>
              <div className={styles.count_get}>x{item.getStarNum}</div>
              <div className={styles.count_total}>/{item.totalStarNum}</div>
            </div>
            <div className={styles.item_info}>
              <div className={styles.item_title}>{item.themeName}</div>
              {item.themeId === secretTopicId ? (
                <div className={styles.item_desc}>等你来发现~</div>
              ) : (
                !!item.themeRoute && (
                  <div
                    className={styles.item_btn}
                    onClick={() => onClickItem(item)}
                  >
                    {item.totalStarNum > item.getStarNum ? "去收集" : "已集完"}
                  </div>
                )
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Summary;
