.container {
  height: 100vh;
  background: linear-gradient(0deg, #ffb01d 0%, #ffca00 100%);
  display: flex;
  flex-direction: column;

  --topic-gradient-color: #bfffa5;
  --topic-point-color: #c0ffa7;

  :global {
    .topic-1 {
      --topic-gradient-color: #ffd3a5;
      --topic-point-color: #ffd5a7;
    }

    .topic-2 {
      --topic-gradient-color: #ffc1c9;
      --topic-point-color: #ffc1ca;
    }

    .topic-3 {
      --topic-gradient-color: #a5c6ff;
      --topic-point-color: #a7c7ff;
    }

    .topic-4 {
      --topic-gradient-color: #fff8a5;
      --topic-point-color: #fff9a8;
    }

    .topic-5 {
      --topic-gradient-color: #b3a5ff;
      --topic-point-color: #b4a7ff;
    }

    .topic-6 {
      --topic-gradient-color: #a5f2ff;
      --topic-point-color: #a7f3ff;
    }
  }

  .content {
    flex: 1;
    overflow-x: hidden;
    overflow-y: auto;
    position: relative;
    padding-top: 4px;
    z-index: 1;
    display: flex;
    flex-direction: column;

    .list {
      flex: 1;
      background: #fff7df;
      border-radius: 12px 12px 0 0;
      padding: 16px 0 16px 16px;
      padding-bottom: calc(constant(safe-area-inset-bottom) + 16px);
      padding-bottom: calc(env(safe-area-inset-bottom) + 16px);
    }
  }
}
