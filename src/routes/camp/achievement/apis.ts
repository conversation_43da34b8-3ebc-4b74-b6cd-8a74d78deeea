import request, { RequestResponse } from "@/utils/request";

export interface IThemeAward {
  /** 获取星星数 */
  getStarNum: number;
  /** 总星星数 */
  totalStarNum: number;
  /** 获奖规则url */
  ruleUrl: string;
  /** 降级提示文案 */
  fallbackTips: string;
  /** 主题列表 */
  themeAwardList: IThemeAwardItem[];
}

export interface IThemeAwardItem {
  /** 主题id  隐藏主题='-1' */
  themeId: string;
  /** 主题名  隐藏主题 ='神秘隐藏任务' */
  themeName: string;
  /** 主题路由 */
  themeRoute?: string;
  /** 获取星星数 */
  getStarNum: number;
  /** 总星星数 */
  totalStarNum: number;
  /** 任务列表 */
  taskList: ITaskItem[];
}

export interface ITaskItem {
  /** 任务名 */
  taskName: string;
  /** 任务是否完成  true 完成 false 未完成 */
  taskStatus: boolean;
}

// 获取成就档案-主题奖励
export const getThemeAward = (params: {
  phaseId: string;
  campId: string;
  ignoreError?: boolean;
}) =>
  request.get("/api/activitycenter/camp/app/archives/getThemeAward", {
    params,
  }) as RequestResponse<IThemeAward>;

export interface IMedalItem {
  /** 勋章id */
  medalId: string;
  /** 勋章名称 */
  medalName: string;
  /** 勋章图 */
  medalImgUrl: string;
  /** 是否已领取 */
  hasGet: boolean;
}

// 获取成就档案-勋章
export const getMedalList = (params: {
  phaseId: string;
  campId: string;
  ignoreError?: boolean;
}) =>
  request.get("/api/activitycenter/camp/app/archives/getMedalList", {
    params,
  }) as RequestResponse<IMedalItem[]>;
