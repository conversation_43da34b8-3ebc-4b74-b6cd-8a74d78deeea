.container {
  height: 100vh;
  background: #2E86FF;
  display: flex;
  flex-direction: column;

  .content {
    flex: 1;
    overflow: hidden;
    position: relative;
    z-index: 1;
    background: url("@/assets/camp/rank/rank-bg.jpg") no-repeat 0% 0%/cover, #DDEBFF;
    border-radius: 12px 12px 0 0;
    padding-top: 12px;

    display: flex;
    flex-direction: column;
    align-items: center;

    @keyframes fadeIn {
      from {
        opacity: 0;
      }
      to {
        opacity: 1;
      }
    }

    .rank_content {
      flex: 1;
      overflow: hidden;
      opacity: 0;
      animation: fadeIn 0.5s ease-in-out;
      animation-fill-mode: forwards;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }

  .loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 100px;
  }
}

.rank_info {
  padding: 0 20px 10px;
  font-size: 14px;
  color: #333333;
  line-height: 22px;
  max-height: 60vh;
  overflow-x: hidden;
  overflow-y: auto;
  tab-size: 2;
}
