import { getSafeAreaTop } from "@/utils/bridge-utils";
import SafeLogger from "@/utils/safe-logger";
import React, { useEffect, useReducer, useRef, useState } from "react";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import styles from "./index.module.scss";
import Header from "./components/header";
import {
  rankTypeEnum,
  rankPeriodEnum,
  NO_SCHOOL_CODE,
  enableStatus,
} from "./constant";
import EmptyInfo from "@/components/empty-info";
import Period from "./components/period";
import Range from "./components/range";
import {
  IRankInfo,
  getRankByType,
  getRankConfig,
  IPhaseInfo,
  IShowRank,
  getAllPhaseList,
} from "./apis";
import DelayPageLoading from "@/components/delay-page-loading";
import List from "./components/list";
import ErrorInfo from "@/components/error-info";
import BasePopup from "@/components/base-popup";
import Loading from "@/components/loading";
import { createURLByType, expPv } from "@/utils/tool";
import { useAPILevel } from "@/hooks";
import { RequestResponse } from "@/utils/request";
import { Toast } from "antd-mobile";
import Phase from "./components/phase";

interface IRankInfoMap {
  [key: string | number]: {
    isLoading?: boolean;
    isError?: boolean;
    data?: IRankInfo;
  };
}

interface IPhaseRequestInfo {
  isLoading?: boolean;
  isError?: boolean;
  promise?: RequestResponse<IPhaseInfo[]>;
  data?: IPhaseInfo[];
}

interface IQueryParams {
  tab?: number;
  period?: number;
  phaseId?: string;
}

const Rank = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const location = useLocation();
  const loggerRef = useRef<SafeLogger>();
  const queryParamsRef = useRef<IQueryParams>({});
  const selectParamsRef = useRef({
    tab: rankTypeEnum.STAR,
    period: rankPeriodEnum.TOTAL,
    // 当前进行中的阶段 id
    currentPhaseId: "",
    // 总榜显示的阶段 id
    showPhaseId: "",
  });
  const [loading, setLoading] = useState(false);
  const { upLevel, getLevelKey } = useAPILevel(3);
  const [safeTop, setSafeTop] = useState(0);
  const [showRankInfo, setShowRankInfo] = useState(false);
  const [_, forceUpdate] = useReducer((x) => x + 1, 0);
  const rankInfoRef = useRef<IRankInfoMap>({});
  const phaseListRef = useRef<IPhaseRequestInfo>({});
  const [showRanks, setShowRanks] = useState<IShowRank[]>([]);
  const [initEmptyWord, setInitEmptyWord] = useState("");
  const [isInitError, setIsInitError] = useState(false);
  const [rangeWord, setRangeWord] = useState("");
  const [rankInfoWord, setRankInfoWord] = useState("");

  const initSafeTop = async () => {
    const safeTop = await getSafeAreaTop();
    setSafeTop(safeTop);
  };

  // 1. 获取排行榜配置
  const initRankConfig = async () => {
    setLoading(true);
    upLevel(1);
    try {
      const res = await getRankConfig();
      let word = "";
      let ranks: IShowRank[] = [];
      if (res.data) {
        const { schoolName, schoolYear, rankTypeList } = res.data;
        word = schoolName || "";
        if (schoolYear) {
          word += ` ${schoolYear}级`;
        }
        ranks = (rankTypeList || []).filter(
          (item) => item.rankType > 0 && item.rankType < 4,
        );
      }
      if (ranks.length > 0) {
        // 优先使用链接上的 tab
        const activeRank =
          ranks.find((rank) => rank.rankType === queryParamsRef.current.tab) ||
          ranks[0];
        selectParamsRef.current.tab = activeRank.rankType;
        // 优先使用链接上的周期
        selectParamsRef.current.period =
          (activeRank.cycleList || []).find(
            (item) => item.rankCycle === queryParamsRef.current.period,
          )?.rankCycle || rankPeriodEnum.TOTAL;
        selectParamsRef.current.currentPhaseId = res.data.phaseId;
        // 链接上的参数仅生效一次
        delete queryParamsRef.current.tab;
        delete queryParamsRef.current.period;
        setShowRanks(ranks);
        setRangeWord(word);
        setRankInfoWord(res.data.rankRule || "");
        getPhaseList();
        expoTab(ranks[0].rankName);
      } else {
        setInitEmptyWord("暂无数据");
        loggerRef.current?.error("page-blocking", {
          reason: "get-rank-config-empty",
        });
      }
      setIsInitError(false);
    } catch (error) {
      loggerRef.current?.error("page-blocking", {
        reason: "get-rank-config-error",
        error,
      });
      // 用户未加入学校
      if (`${error?.code}` === NO_SCHOOL_CODE) {
        setInitEmptyWord("你尚未加入学校或年级，无法查看本年级排行榜");
      } else {
        setIsInitError(true);
      }
    }
    setLoading(false);
  };

  // 2. 获取阶段列表
  const getPhaseList = async () => {
    const levelKey = upLevel(2);
    const { isShowPhase } = checkShowInfo(
      selectParamsRef.current.tab,
      selectParamsRef.current.period,
    );
    // 不需要显示阶段时，直接假装阶段已经获取完成
    if (!isShowPhase) {
      phaseListRef.current = {
        ...phaseListRef.current,
        isLoading: false,
        isError: false,
      };
      forceUpdate();
      getRankInfo();
      return;
    }
    try {
      if (!phaseListRef.current.data) {
        phaseListRef.current = {
          promise: phaseListRef.current.promise || getAllPhaseList(),
          isLoading: true,
          isError: false,
        };
        forceUpdate();
        const res = await phaseListRef.current.promise;
        if (levelKey !== getLevelKey(2)) {
          return;
        }
        phaseListRef.current = {
          isLoading: false,
          isError: false,
          data: res.data || [],
        };
      }
      if (phaseListRef.current.data.length > 0) {
        let lastPhaseId = "";
        let queryPhaseId = "";
        phaseListRef.current.data.forEach((item) => {
          if (enableStatus.includes(item.status)) {
            // 检查链接上的阶段 id
            if (item.id === queryParamsRef.current.phaseId) {
              queryPhaseId = item.id;
            }
            // 找到最后一个非开始的阶段 ID
            lastPhaseId = item.id;
          }
        });
        // 优先使用链接上的阶段 id，否则使用最后一个非开始的阶段 id
        const showPhaseId = queryPhaseId || lastPhaseId;
        // 链接上的参数仅生效一次
        delete queryParamsRef.current.phaseId;
        selectParamsRef.current.showPhaseId = showPhaseId;
        getRankInfo();
      } else {
        // 无阶段数据，显示空状态
        loggerRef.current?.error("get-phase-list-empty");
      }
      forceUpdate();
    } catch (error) {
      loggerRef.current?.error("get-phase-list-error", {
        error,
      });
      if (levelKey !== getLevelKey(2)) {
        return;
      }
      phaseListRef.current = {
        isLoading: false,
        isError: true,
      };
      forceUpdate();
    }
  };

  // 3. 获取排行榜数据
  const getRankInfo = async () => {
    // 因为这个请求处在串联请求的尾部，且对请求状态已经做了缓存处理，所以其实不需要再做 level 的判断
    const { key, params } = getCurrentParams();
    if (rankInfoRef.current[key]) {
      return;
    }
    rankInfoRef.current[key] = {
      isLoading: true,
    };
    forceUpdate();
    try {
      const res = await getRankByType(params);
      rankInfoRef.current[key] = {
        data: res.data,
      };
      forceUpdate();
    } catch (error) {
      loggerRef.current?.error("get-rank-info-error", {
        error,
      });
      rankInfoRef.current[key] = {
        isError: true,
      };
      forceUpdate();
    }
  };

  // 榜单切换
  const onTabChange = (tab: rankTypeEnum, rank: IShowRank) => {
    selectParamsRef.current.tab = tab;
    // 自动选中第一个周期
    selectParamsRef.current.period =
      rank.cycleList?.[0]?.rankCycle || rankPeriodEnum.TOTAL;
    selectParamsRef.current.showPhaseId = "";
    forceUpdate();
    getPhaseList();
    syncToQuery();
    expoTab(rank.rankName);
  };

  // 周期切换
  const onPeriodChange = (period: rankPeriodEnum) => {
    selectParamsRef.current.period = period;
    selectParamsRef.current.showPhaseId = "";
    forceUpdate();
    getPhaseList();
    syncToQuery();
  };

  // 阶段切换
  const onPhaseChange = (phaseId: string, phase: IPhaseInfo) => {
    if (!enableStatus.includes(phase.status)) {
      Toast.show("该阶段暂未开始");
      return;
    }
    selectParamsRef.current.showPhaseId = phaseId;
    forceUpdate();
    getRankInfo();
    syncToQuery();
  };

  const onInfoClick = () => {
    setShowRankInfo(true);
  };

  const expoTab = (rank: string) => {
    expPv("ewt_h5_base_operation_camp_rank_tab_expo", {
      rank,
    });
  };

  // 同步设置到链接
  const syncToQuery = () => {
    const { tab, period, showPhaseId } = selectParamsRef.current;
    navigate(
      createURLByType({
        path: location.pathname,
        originSearch: location.search,
        removeQueryKeys: ["tab", "period", "phaseId"],
        addQueryObject: {
          tab,
          period,
          phaseId: showPhaseId || undefined,
        },
      }),
      { replace: true },
    );
  };

  // 根据 榜单 与 周期，判断是否显示周期与阶段
  const checkShowInfo = (tab: rankTypeEnum, period: rankPeriodEnum) => {
    const isShowPeriod = tab !== rankTypeEnum.STAR;
    const isShowPhase = isShowPeriod && period === rankPeriodEnum.TOTAL;
    return {
      isShowPeriod,
      isShowPhase,
    };
  };

  // 根据当前选择，获取当前一些基础信息
  const getCurrentParams = () => {
    const { tab, period, currentPhaseId, showPhaseId } =
      selectParamsRef.current;
    const { isShowPeriod, isShowPhase } = checkShowInfo(tab, period);
    const params = {
      type: tab,
      cycle: period,
      phaseId: isShowPhase ? showPhaseId : currentPhaseId,
    };
    const key = `${params.type}-${params.cycle}-${params.phaseId}`;
    return {
      isShowPeriod,
      isShowPhase,
      key,
      params,
      tab,
      period,
      currentPhaseId,
      showPhaseId,
    };
  };

  useEffect(() => {
    loggerRef.current = new SafeLogger("camp-rank");
    queryParamsRef.current = {
      tab: +searchParams.get("tab") || undefined,
      period: +searchParams.get("period") || undefined,
      phaseId: searchParams.get("phaseId") || undefined,
    };
    initSafeTop();
    initRankConfig();
  }, []);

  const {
    key: renderKey,
    isShowPeriod,
    isShowPhase,
    tab: activeTab,
    period: rankPeriod,
    showPhaseId: activePhaseId,
  } = getCurrentParams();
  const activeRank = showRanks.find((rank) => rank.rankType === activeTab);
  const periodList = activeRank?.cycleList || [];

  const info = rankInfoRef.current[renderKey] || {};
  const rankInfo = info.data;

  let showContent = null;
  if (!!initEmptyWord) {
    // 无有效排行榜数据
    showContent = (
      <EmptyInfo text={initEmptyWord} wordStyle={{ color: "#666666" }} />
    );
  } else if (isInitError) {
    // 排行榜配置获取失败
    showContent = (
      <ErrorInfo
        type="light"
        onRetry={() => {
          initRankConfig();
        }}
      />
    );
  } else if (info.isLoading || phaseListRef.current.isLoading) {
    // 排行榜数据或者阶段数据加载中
    showContent = <Loading />;
  } else if (info.isError || phaseListRef.current.isError) {
    // 排行榜数据或者阶段数据加载失败
    showContent = (
      <ErrorInfo
        type="light"
        onRetry={() => {
          if (phaseListRef.current.isError) {
            delete phaseListRef.current.promise;
            getPhaseList();
          } else {
            delete rankInfoRef.current[renderKey];
            getRankInfo();
          }
        }}
      />
    );
  } else if (isShowPhase && !phaseListRef.current.data?.length) {
    // 需要显示 阶段，但是无阶段数据
    showContent = (
      <EmptyInfo text="暂无数据" wordStyle={{ color: "#666666" }} />
    );
  } else if (!!rankInfo) {
    // 有有效排行榜数据
    showContent = (
      <div className={styles.rank_content} key={renderKey}>
        <Range
          rankInfo={rankInfo}
          rangeWord={rangeWord}
          timeFormat={
            isShowPeriod && rankPeriod === rankPeriodEnum.WEEK && "M.D"
          }
        />
        <List type={activeTab} data={rankInfo.rankList || []} />
      </div>
    );
  }
  return (
    <div className={styles.container}>
      <Header
        safeTop={safeTop}
        activeTab={activeTab}
        showRanks={showRanks}
        rankInfoWord={rankInfoWord}
        onTabChange={onTabChange}
        onInfoClick={onInfoClick}
      />
      <div className={styles.content}>
        {isShowPeriod && !!periodList.length && (
          <Period
            activePeriod={rankPeriod}
            periodList={periodList}
            onChange={onPeriodChange}
          />
        )}
        {isShowPhase && !!phaseListRef.current.data && (
          <Phase
            phaseList={phaseListRef.current.data}
            activePhaseId={activePhaseId}
            onChange={onPhaseChange}
          />
        )}
        {showContent}
      </div>
      <BasePopup
        title="榜单说明"
        confirmText="我知道了"
        open={showRankInfo}
        closeOnMaskClick
        onClose={() => {
          setShowRankInfo(false);
        }}
        showConfirm
        onConfirm={() => {
          setShowRankInfo(false);
        }}
      >
        <pre className={styles.rank_info}>{rankInfoWord}</pre>
      </BasePopup>
      <DelayPageLoading loading={loading} />
    </div>
  );
};

export default Rank;

export const Component = Rank;
