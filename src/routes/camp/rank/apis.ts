import request, { RequestResponse } from "@/utils/request";
import { phaseStatusEnum, rankPeriodEnum, rankTypeEnum } from "./constant";

export interface IRankConfig {
  /** 学校名 */
  schoolName: string;
  /** 入学年份 */
  schoolYear: string;
  /** 排行榜规则说明 */
  rankRule: string;
  /** 排行榜类型集合 */
  rankTypeList: IShowRank[];
  /** 当前可见阶段id */
  phaseId: string;
}

export interface IShowRank {
  /** 排行榜类型：1 星星榜 2 看课榜 3 答题榜 */
  rankType: rankTypeEnum;
  /** 排行榜名称 */
  rankName: string;
  /** 周期列表 */
  cycleList: ICycle[];
}

export interface ICycle {
  /** 周期类型：1 周榜，2 总榜 */
  rankCycle: rankPeriodEnum;
  /** 名称:周榜、总榜 */
  rankName: string;
}

// 获取排行榜配置
export const getRankConfig = () =>
  request.get(
    "/api/activitycenter/camp/app/rank/getRankConfig",
  ) as RequestResponse<IRankConfig>;

export interface IRankInfo {
  /** 统计开始时间。毫秒级时间戳 */
  startTime: number;
  /** 统计结束时间。毫秒级时间戳 */
  endTime: number;
  /** 更新时间 格式MMDD HHMI */
  updateTime: string;
  /** 排行榜 */
  rankList: IRankItem[];
}

export interface IRankItem {
  /** 用户id */
  userId: string;
  /** 是否本人 */
  isMe: boolean;
  /** 头像 */
  headUrl: string;
  /** 昵称 */
  nickName: string;
  /** 排名 */
  rankNo: number;
  /** 排名值 */
  rankValue: number;
}

// 获取排行榜数据
export const getRankByType = (params: {
  phaseId: string;
  type: rankTypeEnum;
  cycle?: rankPeriodEnum;
}) =>
  request.get("/api/activitycenter/camp/app/rank/getRankByType", {
    params,
  }) as RequestResponse<IRankInfo>;

export interface IPhaseInfo {
  /** 阶段ID */
  id: string;
  /** 阶段名称 */
  name: string;
  /** 状态: null 未上线，1 预热， 2 进行中， 3 已结束 */
  status?: phaseStatusEnum;
  /** 状态名字 */
  statusName: string;
}

// 获取全部阶段基本信息列表
export const getAllPhaseList = () =>
  request.get(
    "/api/activitycenter/camp/app/getAllPhaseList",
  ) as RequestResponse<IPhaseInfo[]>;
