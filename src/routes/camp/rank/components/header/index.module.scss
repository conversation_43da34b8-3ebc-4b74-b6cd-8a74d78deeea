.container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 51px;
  box-sizing: content-box;
  position: relative;
  flex: 0 0 auto;
  z-index: 1;

  .logo {
    position: absolute;
    z-index: -1;
    right: 34px;
    bottom: -10px;
    width: 110px;
    height: 43px;
    background: url("@/assets/camp/rank/rank-logo.png") no-repeat center/contain;
  }

  .back {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #fff;
    transform: scaleX(-1);
  }

  .info {
    width: 40px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #fff;
    padding-right: 16px;
    padding-left: 8px;
  }

  .tabs {
    position: absolute;
    left: 51px;
    right: 51px;
    bottom: 12px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &.tabs-1 {
      justify-content: center;
    }

    &.tabs-2 {
      justify-content: space-around;
    }

    .tab {
      width: 80px;
      height: 28px;
      line-height: 28px;
      text-align: center;
      font-size: 18px;
      color: #ffffff;
      position: relative;

      &::after {
        content: " ";
        position: absolute;
        width: 70px;
        height: 12px;
        left: 11px;
        bottom: -4px;
        background: url("@/assets/camp/rank/star-rank-active.png") no-repeat
          center/contain;
        display: none;
      }

      &.active {
        font-weight: bold;
        font-size: 20px;
        pointer-events: none;

        &::after {
          display: block;
        }
      }
    }

    .tab-2::after {
      background-image: url("@/assets/camp/rank/video-rank-active.png");
    }

    .tab-3::after {
      background-image: url("@/assets/camp/rank/paper-rank-active.png");
    }
  }
}
