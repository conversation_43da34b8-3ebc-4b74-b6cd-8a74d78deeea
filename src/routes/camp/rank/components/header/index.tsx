import React from "react";
import styles from "./index.module.scss";
import { IconSvg } from "@/components";
import { rankTypeEnum } from "../../constant";
import { cls } from "@/utils/tool";
import { IShowRank } from "../../apis";
import { closeWebview } from "@/utils/bridge-utils";

const Header = ({
  safeTop,
  activeTab,
  showRanks,
  rankInfoWord,
  onTabChange,
  onInfoClick,
}: {
  safeTop: number;
  activeTab: rankTypeEnum;
  showRanks: IShowRank[];
  rankInfoWord?: string;
  onTabChange: (tab: rankTypeEnum, rank: IShowRank) => void;
  onInfoClick: () => void;
}) => {
  const onBack = () => {
    closeWebview();
  };

  return (
    <div className={styles.container} style={{ paddingTop: safeTop || 0 }}>
      <div className={styles.back} onClick={onBack}>
        <IconSvg name="icon-a-jinru2x" />
      </div>
      <div className={styles.logo} />
      <div className={cls([styles.tabs, styles[`tabs-${showRanks.length}`]])}>
        {showRanks.map((tab) => (
          <div
            className={cls([
              styles.tab,
              styles[`tab-${tab.rankType}`],
              activeTab === tab.rankType && styles.active,
            ])}
            key={tab.rankType}
            onClick={() => onTabChange(tab.rankType, tab)}
          >
            {tab.rankName}
          </div>
        ))}
      </div>
      {showRanks.length > 0 && !!rankInfoWord && (
        <div className={styles.info} onClick={onInfoClick}>
          <IconSvg name="icon-a-tanhao3x" />
        </div>
      )}
    </div>
  );
};

export default Header;
