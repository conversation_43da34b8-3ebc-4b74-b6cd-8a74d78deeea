import React from "react";
import styles from "./index.module.scss";
import { rankPeriodEnum } from "../../constant";
import { cls } from "@/utils/tool";
import { ICycle } from "../../apis";

const Period = ({
  activePeriod,
  periodList,
  onChange,
}: {
  activePeriod: rankPeriodEnum;
  periodList: ICycle[];
  onChange: (period: rankPeriodEnum, item: ICycle) => void;
}) => {
  // TODO 当前的样式只支持 2 个周期，因为当前也只有 2 个周期，所以暂时不处理多周期的情况
  return (
    <div className={styles.container}>
      <div className={styles.tabs}>
        {periodList.slice(0, 2).map((item) => (
          <div
            key={item.rankCycle}
            className={cls([
              styles.tab,
              activePeriod === item.rankCycle && styles.active,
            ])}
            onClick={() => onChange(item.rankCycle, item)}
          >
            {item.rankName}
          </div>
        ))}
      </div>
    </div>
  );
};

export default Period;
