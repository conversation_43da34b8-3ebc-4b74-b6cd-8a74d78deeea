.container {
  flex: 0 0 auto;
  height: 27px;
  margin-bottom: 12px;
  display: flex;
  justify-content: center;

  .tabs {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 164px;

    .tab {
      height: 27px;
      text-align: center;
      padding-bottom: 5px;
      line-height: 22px;
      font-size: 16px;
      color: #434F59;
      position: relative;
      width: 72px;

      &.active {
        color: #2D86FE;
        font-weight: bold;
        pointer-events: none;

        &::after {
          content: " ";
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 10px;
          height: 3px;
          background: #2D86FE;
          border-radius: 1.5px;
        }
      }
    }
  }
}
