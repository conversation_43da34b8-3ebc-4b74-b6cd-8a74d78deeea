import React from "react";
import styles from "./index.module.scss";
import { IRankInfo } from "../../apis";
import dayjs from "dayjs";

const Range = ({
  rankInfo,
  rangeWord,
  timeFormat,
}: {
  rankInfo: IRankInfo;
  rangeWord: string;
  timeFormat?: string;
}) => {
  if (!rankInfo) return null;
  const { startTime, endTime, updateTime } = rankInfo;
  const text1 = !!rangeWord ? `评比范围：${rangeWord}` : "";
  const text2Arr = [];
  const format = timeFormat || "YYYY.M.D";
  if (startTime && endTime) {
    text2Arr.push(
      `评比周期：${dayjs(+startTime).format(format)}-${dayjs(+endTime).format(format)}`,
    );
  }
  if (updateTime) {
    text2Arr.push(updateTime);
  }
  const text2 = text2Arr.join(" ");
  return (
    <div className={styles.container}>
      {!!text1 && <div className={styles.text}>{text1}</div>}
      {!!text2 && <div className={styles.text}>{text2}</div>}
    </div>
  );
};

export default Range;
