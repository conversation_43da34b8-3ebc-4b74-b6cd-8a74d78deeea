import React from "react";
import styles from "./index.module.scss";
import { IPhaseInfo } from "../../apis";
import { CapsuleTabs } from "antd-mobile";
import { enableStatus } from "../../constant";
import classNames from "classnames";

const Phase = ({
  phaseList,
  activePhaseId,
  onChange,
}: {
  phaseList: IPhaseInfo[];
  activePhaseId: string;
  onChange: (phaseId: string, phase: IPhaseInfo) => void;
}) => {
  return (
    <div className={styles.container}>
      <CapsuleTabs
        activeKey={activePhaseId}
        onChange={(key) => {
          const phase = phaseList.find((item) => item.id === key);
          if (phase) {
            onChange(key, phase);
          }
        }}
      >
        {phaseList.map((item, index) => (
          <CapsuleTabs.Tab
            key={item.id}
            title={
              <div
                className={classNames(styles.item, {
                  [styles.selected]: activePhaseId === item.id,
                  [styles.disabled]: !enableStatus.includes(item.status),
                  [styles.last]: index === phaseList.length - 1,
                })}
              >
                <div className={styles.word}>{item.name}</div>
                <div className={styles.arrow}></div>
              </div>
            }
          />
        ))}
      </CapsuleTabs>
    </div>
  );
};

export default Phase;
