.container {
  width: 375px;
  padding-top: 4px;
  margin-bottom: 16px;

  :global {
    .adm-capsule-tabs-header {
      padding: 0;
      border: 0;

      .adm-scroll-mask {
        display: none;
      }

      .adm-capsule-tabs-tab-wrapper {
        padding: 0;
        padding-left: 12px;

        .adm-capsule-tabs-tab {
          padding: 0;
          background: transparent;
        }
      }

      div.adm-capsule-tabs-tab-wrapper:first-child {
        padding-left: 16px;
      }

      div.adm-capsule-tabs-tab-wrapper:last-child {
        padding-right: 16px;
      }
    }
  }

  .item {
    position: relative;
    padding-right: 15px;

    .word {
      padding: 0 1px 0 8px;
      display: flex;
      align-items: center;
      font-weight: bold;
      font-size: 14px;
      color: #333333;
      background-color: #fff;
      height: 32px;
      border-radius: 4px 0 0 4px;
    }

    .arrow {
      position: absolute;
      right: 0;
      top: 0;
      bottom: 0;
      width: 16px;
      background: url("~@/assets/camp/rank/phase-arrow.png") no-repeat center
        right/cover;
      z-index: -1;
    }

    &::before {
      content: "";
      position: absolute;
      right: -14px;
      top: 50%;
      width: 15px;
      height: 1px;
      transform: translateY(-50%);
      background-color: #add0ff;
      z-index: -1;
    }

    &.selected {
      .word {
        color: #fff;
        background: linear-gradient(270deg, #0387ff 0%, #02aaff 100%);
      }

      .arrow {
        background-image: url("~@/assets/camp/rank/phase-arrow-selected.png");
      }
    }

    &.disabled {
      .word {
        color: #333;
        background-color: #d8d8d8;
      }

      .arrow {
        background-image: url("~@/assets/camp/rank/phase-arrow-disable.png");
      }

      &::before {
        background-color: #d8d8d8;
      }
    }

    &.last {
      &::before {
        display: none;
      }
    }
  }
}
