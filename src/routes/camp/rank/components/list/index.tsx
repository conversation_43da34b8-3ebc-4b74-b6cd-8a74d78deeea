import React from "react";
import styles from "./index.module.scss";
import { IconSvg } from "@/components";
import { cls } from "@/utils/tool";
import LoadingImage from "@/components/loading-image";
import StudentPng from "@/assets/common/student-default.png";
import { IRankItem } from "../../apis";
import { rankTypeEnum } from "../../constant";
import EmptyInfo from "@/components/empty-info";

const Avatar = ({
  type,
  rank,
  isEmpty,
  url,
}: {
  type: number;
  rank: number;
  isEmpty: boolean;
  url: string;
}) => {
  const isTop3 = type < 4;
  return (
    <div className={cls([styles.avatar, isTop3 && styles[`top-${type}`]])}>
      {isEmpty ? (
        <div className={styles.empty}>
          <IconSvg name="icon-a-shafa2x" />
        </div>
      ) : (
        <LoadingImage
          src={url}
          fallback={StudentPng}
          className={styles.avatar_img}
        />
      )}
      {isTop3 && (
        <div className={styles.top_more}>
          <div className={styles.crown}></div>
          <div className={styles.band}>NO.{rank}</div>
        </div>
      )}
    </div>
  );
};

const Item = ({
  index,
  item,
  renderCount,
  className,
}: {
  index: number;
  item?: IRankItem;
  renderCount: (count: number) => React.ReactNode;
  className?: string;
}) => {
  const isTop3 = index < 3;
  let isEmpty = true;
  let showName = "虚位以待";
  let rank = index + 1;
  if (item) {
    isEmpty = false;
    showName = item.nickName?.slice(0, 4) || "";
    rank = item.rankNo;
  }
  return (
    <div className={cls([styles.item, isTop3 && styles.item_top, className])}>
      {!isTop3 && <div className={styles.rank_num}>{rank}</div>}
      <Avatar
        type={index + 1}
        rank={rank}
        isEmpty={isEmpty}
        url={item?.headUrl}
      />
      <div className={styles.name}>
        <div className={styles.name_text}>{showName}</div>
        {!!item?.isMe && (
          <div className={styles.me}>
            <span>我</span>
          </div>
        )}
      </div>
      <div className={styles.rank_value}>
        {!isEmpty && renderCount(item.rankValue)}
      </div>
    </div>
  );
};

// Top 3
const top3Items = Array.from({ length: 3 });
// 需要补全 20 个排行
const fillItems = Array.from({ length: 17 });

// 格式话数字，超过 99999 显示 99999+，如果带小数点，则保留两位小数，但是去掉小数点后面的 0
const formatCount = (count: number) => {
  if (count > 99999) {
    return "99999+";
  }
  return Math.round(count * 100) / 100;
};

const List = ({ type, data }: { type: rankTypeEnum; data: IRankItem[] }) => {
  const isEmpty = data.length === 0;

  const renderCount = (isTop3?: boolean) => {
    return function renderTypeCount(count: number) {
      if (type === rankTypeEnum.STAR) {
        return (
          <div className={cls([styles.star_value, isTop3 && styles.top_star])}>
            <div className={styles.star}></div>
            <div className={styles.value}>x{count}</div>
          </div>
        );
      }
      return (
        <div className={styles.value_container}>
          <div className={styles.value}>{formatCount(count)}</div>
          <div className={styles.unit}>
            {type === rankTypeEnum.VIDEO ? "分钟" : "题"}
          </div>
        </div>
      );
    };
  };

  return (
    <div className={styles.container}>
      <div className={styles.top3}>
        {top3Items.map((_, index) => (
          <Item
            key={index}
            index={index}
            item={data[index]}
            renderCount={renderCount(true)}
            className={styles[`item_top_${index + 1}`]}
          />
        ))}
      </div>
      <div className={styles.list}>
        {isEmpty ? (
          <EmptyInfo wordStyle={{ color: "#666666" }} />
        ) : (
          fillItems.map((_, index) => (
            <Item
              key={index}
              index={3 + index}
              item={data[3 + index]}
              renderCount={renderCount()}
            />
          ))
        )}
      </div>
      {!isEmpty && <div className={styles.bottom_text}>已经到底了</div>}
    </div>
  );
};

export default List;
