.avatar {
  position: relative;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  --empty-bg: #e4efff;
  --empty-icon: #2e86ff;

  .empty {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--empty-bg);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: var(--empty-icon);
  }

  .avatar_img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: var(--empty-bg);
  }

  .top_more {
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 2px solid var(--empty-icon);

    .crown {
      position: absolute;
      top: -14px;
      left: -14px;
      width: 32px;
      height: 32px;
    }

    .band {
      position: absolute;
      width: 51px;
      height: 15px;
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%);
      text-align: center;
      line-height: 15px;
      font-size: 12px;
      color: #fff;
      font-weight: bold;
    }
  }

  &.top-1 {
    --empty-bg: #fff5a4;
    --empty-icon: #ffc632;
    width: 68px;
    height: 68px;
    background-color: var(--empty-bg);

    .empty {
      font-size: 30px;
    }

    .top_more {
      .crown {
        background: url("@/assets/camp/rank/crown-1.png") no-repeat
          center/contain;
      }

      .band {
        background: url("@/assets/camp/rank/band-1.png") no-repeat
          center/contain;
      }
    }
  }

  &.top-2 {
    --empty-bg: #c3fcf9;
    --empty-icon: #41bbef;
    background-color: var(--empty-bg);

    .top_more {
      .crown {
        background: url("@/assets/camp/rank/crown-2.png") no-repeat
          center/contain;
      }

      .band {
        background: url("@/assets/camp/rank/band-2.png") no-repeat
          center/contain;
      }
    }
  }

  &.top-3 {
    --empty-bg: #fee9ce;
    --empty-icon: #fd9249;
    background-color: var(--empty-bg);

    .top_more {
      .crown {
        background: url("@/assets/camp/rank/crown-3.png") no-repeat
          center/contain;
      }

      .band {
        background: url("@/assets/camp/rank/band-3.png") no-repeat
          center/contain;
      }
    }
  }
}

.item {
  display: flex;
  align-items: center;
  width: 310px;
  height: 74px;
  border-bottom: 1px solid #eef5ff;
  margin: 0 auto;

  .rank_num {
    width: 22px;
    height: 22px;
    background: #eef5ff;
    border-radius: 4px;
    text-align: center;
    line-height: 22px;
    font-weight: bold;
    font-size: 15px;
    color: #2e86ff;
    margin-right: 16px;
  }

  .name {
    color: #333333;
    height: 20px;
    display: flex;
    align-items: center;
    margin-left: 12px;
    flex: 1;

    .name_text {
      font-size: 14px;
      line-height: 20px;
    }

    .me {
      width: 16px;
      height: 16px;
      background: #ffebeb;
      border: 1px solid #ff9b9b;
      border-radius: 2px;
      margin-left: 4px;
      font-weight: bold;
      color: #ff5353;
      display: flex;
      align-items: center;
      justify-content: center;

      span {
        line-height: 1.4;
        font-size: 24px;
        transform: scale(0.5);
        transform-origin: center;
      }
    }
  }
}

.item_top {
  flex-direction: column;
  width: 110px;
  height: 142px;
  border-bottom: none;
  position: absolute;

  .name {
    margin-left: 0;
    margin-top: 9px;
    flex: 0;
  }

  .rank_value {
    margin-top: 4px;
  }
}

.item_top_1 {
  left: 116.5px;
  top: -12px;
}

.item_top_2 {
  top: 23px;
}

.item_top_3 {
  right: 0;
  top: 23px;
}

.container {
  flex: 1;
  padding-top: 24px;
  width: 375px;
  overflow-x: hidden;
  overflow-y: auto;
  padding-bottom: 20px;
  padding-bottom: calc(20px + constant(safe-area-inset-bottom));
  padding-bottom: calc(20px + env(safe-area-inset-bottom));

  .top3 {
    width: 343px;
    height: 142px;
    background: url("@/assets/camp/rank/top3-bg.png") no-repeat center/contain;
    position: relative;
    margin: 0 auto;
  }

  .list {
    width: 343px;
    border-radius: 0 0 12px 12px;
    background-color: #fff;
    min-height: 442px;
    margin: 0 auto;
  }

  .value {
    font-weight: bold;
    font-size: 18px;
    color: #333333;
    line-height: 25px;
  }

  .unit {
    font-size: 12px;
    color: #333333;
    line-height: 17px;
  }

  .star_value {
    display: flex;
    align-items: center;
    width: 52px;

    .star {
      width: 16px;
      height: 16px;
      margin-right: 4px;
      background: url("@/assets/common/star-16.png") no-repeat center/contain;
      flex: 0 0 auto;
    }

    &.top_star {
      width: auto;
    }
  }

  .value_container {
    display: flex;
    align-items: center;
  }

  .bottom_text {
    font-size: 12px;
    color: #666666;
    line-height: 17px;
    text-align: center;
    margin-top: 12px;
  }
}
