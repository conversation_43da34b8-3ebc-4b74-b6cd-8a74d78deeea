import * as React from "react";
import styles from "./style.module.scss";
import {
  EAddedLessonStatus,
  IDetailLessonInfo,
} from "@/service/self-learning/drawer";
import { clickPv, cls, openRoute } from "@/utils/tool";
import { EPopType } from "../plan-detail-content";
import Logger from "@/utils/safe-logger";

import FinishedFireImg from "@/assets/common/icon-finish-icon.png";
import UnFinishedFireImg from "@/assets/common/icon-un-finish-fire.png";
import WatchedFireImg from "@/assets/common/watched-detail.png";
import PlayImg from "@/assets/common/play.png";

let logger = null;
try {
  logger = new Logger("self-learning-plan-detail");
} catch (error) {
  console.warn("sls初始化失败", error);
}

export interface IPlanDetailLessonListProps {
  /** 讲列表 */
  list: IDetailLessonInfo[];
  /** 课包id */
  courseId: string;
  /** 课包名称 */
  courseName: string;
  /** 二次确认框的类型回调 */
  setPopType: (type: EPopType) => void;
  // 公共pop的回调
  setCommonPopVisible: (visible: boolean) => void;
  // 课包是否在计划中
  packageInPlan: boolean;
  /** 点击完成课讲按钮回调 */
  handleCompleteClick?: (lesson: IDetailLessonInfo) => void;
}

export const PlanDetailLessonList: React.FC<IPlanDetailLessonListProps> = (
  props,
) => {
  const {
    list,
    courseId,
    courseName,
    setPopType,
    setCommonPopVisible,
    packageInPlan,
    handleCompleteClick,
  } = props;

  /** 去看课 */
  const toWatchedLesson = async (lesson: IDetailLessonInfo) => {
    clickPv("ewt_h5_study_course_self_learning_plan_detail_page_lesson_click", {
      courseId,
      courseName,
      lessonId: lesson.lessonId,
      lessonName: lesson.title,
    });
    /** 如果没有课包id或没有找到课讲id不进行后续处理 */
    if (!courseId || !lesson?.lessonId) {
      return false;
    }
    // 课程已失效时直接提示
    if (lesson?.status === EAddedLessonStatus.lose) {
      setPopType(EPopType.loseEfficacy);
      setCommonPopVisible(true);
      return false;
    }
    try {
      // 直接去看课
      openRoute({
        domain: "course",
        action: "open_detail",
        params: {
          id: `${courseId}`,
          lessionID: `${lesson.lessonId}`,
        },
      });
    } catch (error) {
      logger?.error("update-lesson-status-error", {
        courseId,
        error,
        ...lesson,
      });
    }
  };

  return !!list?.length ? (
    <div className={styles["list-container"]}>
      {list.map((lesson: IDetailLessonInfo, index: number) => (
        <div
          key={`${lesson.lessonId}_${index}`}
          className={cls([
            styles["list-box"],
            lesson?.status === EAddedLessonStatus.lose &&
              styles["lose-efficacy"],
          ])}
        >
          <div
            className={styles["lesson-item"]}
            onClick={() => toWatchedLesson(lesson)}
          >
            <div className={styles["course-info-and-status"]}>
              {!!packageInPlan && (
                <React.Fragment>
                  {/* 完成与否的图片标识+主动完成的按钮 */}
                  {lesson?.status === EAddedLessonStatus.lose ? (
                    <span className={styles["lose-span"]}>已失效</span>
                  ) : (
                    <img
                      className={styles["fire-icon"]}
                      src={
                        lesson?.status === EAddedLessonStatus.completed
                          ? FinishedFireImg
                          : lesson?.status === EAddedLessonStatus.watched
                            ? WatchedFireImg
                            : UnFinishedFireImg
                      }
                      alt=""
                      onClick={(e: any) => {
                        // 阻止向上冒
                        e.stopPropagation();
                        handleCompleteClick?.(lesson);
                      }}
                    />
                  )}
                </React.Fragment>
              )}
              {/* 课讲名称、时长 */}
              <div className={styles["course-base-info"]}>
                <p className={styles["lesson-name"]}>{lesson.title}</p>
                <span>时长：{lesson.durationDisplay}</span>
              </div>
            </div>
            <div className={styles["right-box"]}>
              <div className={styles["lesson-see-status"]}>
                {/* 看过的标识 */}
                {lesson.status === EAddedLessonStatus.watched && (
                  <span className={styles["watched"]}>看过</span>
                )}
              </div>
              {/* 播放按钮 */}
              <img src={PlayImg} alt="" className={styles["play-button"]} />
            </div>
          </div>
          {index !== list.length - 1 && <hr className={styles["split-line"]} />}
        </div>
      ))}
    </div>
  ) : null;
};
