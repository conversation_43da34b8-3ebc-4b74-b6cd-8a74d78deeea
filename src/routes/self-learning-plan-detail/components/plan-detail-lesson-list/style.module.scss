
.list-container {
  background-color: #fff;
  border-radius: 8px;
  margin: 4px 12.5px;

  .list-box {
    padding: 0 10px;

    &.lose-efficacy {
      filter: grayscale(100%);
      background: none;
      background-color: #EFF1F7;
    }
  }

  .lesson-item {
    display: flex;
    justify-content: space-between;
    padding: 6px 5px;
    align-items: center;

    .course-info-and-status {
      display: flex;
      align-items: center;

      .fire-icon {
        width: 20px;
        height: 24px;
        margin-right: 16px;
      }

      .lose-span {
        display: block;
        width: 37px;
        height: 24px;
        color: #999;
        font-size: 12px;
        margin-left: -9px;
        margin-right: 9px;
      }
    }

    .lesson-name {
      font-size: 14px;
      color: #333;
      max-width: 207px;
      margin-bottom: 2px;

      & + span {
        font-size: 12px;
        color: #999;
        margin-bottom: 4px;
      }
    }

    .play-button {
      width: 24px;
      height: 24px;
      display: block;
    }

    .right-box {
      display: flex;
      align-items: center;

      .lesson-see-status {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #999;
        margin-right: 16px;

        & > img {
          width: 10px;
          height: 12px;
          margin-right: 5px;

          & + span {
            color: #FA8B16!important;
          }
        }

        .watched {
          color: #333;
        }
      }
    }
  }
  .split-line {
    width: 100%;
    border: 0;
    height: 1px;
    border-top: 0.5px solid #DDD;
  }
}
