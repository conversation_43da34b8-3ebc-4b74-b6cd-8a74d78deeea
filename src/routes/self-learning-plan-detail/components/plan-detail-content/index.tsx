import React, { useEffect, useReducer, useRef, useState } from "react";
import {
  IDetailPackageInfo,
  getAddedLessonList,
  getAddedPackageInfo,
  getNoAddLessonList,
  getNoAddPackageInfo,
  getPackageIsAddedPlan,
  postRemovePackageByPlan,
  postAddPackageToPlan,
  getCheckPackageStatus,
  postUpdateCourseStatus,
  postRemoveLoseLesson,
  IDetailLessonInfo,
  EAddedLessonStatus,
} from "@/service/self-learning/drawer";

import styles from "./style.module.scss";
import { clickPv, cls, isIgnoreError } from "@/utils/tool";
import { InfiniteScroll, Toast } from "antd-mobile";

import EmptyStatus, { NetworkErrorStatus } from "@/components/empty-status";
import {
  Button,
  CourseNotCompletePopup,
  Spin,
  TipPopup,
  InfiniteScrollBottom,
  SignCompleteLessonPopup,
} from "@/components";

import Logger from "@/utils/safe-logger";
import { useVisibilitychange } from "@/hooks";
import { PlanDetailForCourse } from "../plan-detail-for-course";
import { PlanDetailLessonList } from "../plan-detail-lesson-list";

import { ESourceFromForText } from "@/typing.d";
import { ErrorCodeEnum } from "@/utils/constant";

const logger = new Logger("self-learning-plan-detail");

/** 方案详情内容组件，为了支持抽屉和页面两种形式 */
export interface ISelfLearningPlanDetailProps {
  courseId: string;
  /** 设置页面标题 */
  setPageTitle: (title: string) => void;
}

export enum EPopType {
  courseUpdate = "courseUpdate",
  loseEfficacy = "loseEfficacy",
  removePlan = "removePlan",
}

/** 推荐方案的内容组件 */
const SelfLearningPlanDetail: React.FC<ISelfLearningPlanDetailProps> = (
  props,
) => {
  // 课程包id，必须的参数
  const { courseId, setPageTitle } = props;
  /** 课包的信息 */
  const [packageInfo, setPackageInfo] = useState<IDetailPackageInfo>();
  /** 课包是否在计划内，默认为不在计划内 */
  const [packageIsInPlan, setPackageIsInPlan] = useState(false);
  const pageParams = useRef({
    haveNextPage: true,
    pageIndex: 1,
    pageSize: 10,
  });
  const packageStatus = useRef({
    isInPlan: false,
  });
  /** 课包绑定的课讲列表 */
  const lessonRef = useRef({
    list: [],
    clickLesson: {} as IDetailLessonInfo,
  });
  const [loading, setLoading] = useState(true);
  const [_, forceUpdate] = useReducer((x: number) => x + 1, 0); // 刷新数据信息，使ref内的list信息变更
  const [courseNetworkError, setCourseNetworkError] = useState(false); // 网络错误
  const [lessonNetworkError, setLessonNetworkError] = useState(false); // 网络错误
  const [popType, setPopType] = useState(""); // pop类型
  const [commonPopVisible, setCommonPopVisible] = useState(false); // pop弹窗
  const [markCompleteVisible, setMarkCompleteVisible] = useState(false); // 主动标记完成课程讲

  const [noFinishPopupVis, setNoFinishPopupVis] = useState(false); // 未完成的提示底抽visible
  // 没有看课popup提示框上报qt时所需课程信息
  const [noWatchedCourse, setNoWatchedCourse] =
    React.useState<IDetailLessonInfo>(null);

  // 查询课程讲列表
  const queryLessonList = async (isAddedPlan?: boolean) => {
    try {
      // 没传递参数就用默认的，否则不管true或false都用参数的值
      const flag = isAddedPlan ?? packageIsInPlan;
      const targetApi = flag ? getAddedLessonList : getNoAddLessonList;
      const { data } = await targetApi({
        courseId,
        ignoreError: true,
        ...pageParams.current,
      });
      // 有数据才刷新
      if (data?.data?.length) {
        lessonRef.current.list = [...lessonRef.current.list, ...data.data];
      }
      pageParams.current.haveNextPage = !!data?.haveNextPage;
      setLessonNetworkError(!data?.data?.length);
      forceUpdate(); // 更新
    } catch (error) {
      // 异常时直接重置为为最后一页，不再请求了，等待下次可执行
      pageParams.current.haveNextPage = false;
      setLessonNetworkError(true);
      logger.error("plan-detail-lesson-list-error", {
        error,
        ...pageParams.current,
        isAddedPlan,
      });
    } finally {
      setLoading(false);
    }
  };

  /** 获取计划的课包详情信息 */
  const queryPackageInfo = async (params?: {
    isAddedPlan?: boolean;
    isNoReFreshLesson?: boolean;
  }) => {
    try {
      // 没传递参数就用默认的，否则不管true或false都用参数的值
      const flag = params?.isAddedPlan ?? packageIsInPlan;
      // 已加入和未加入计划调用不同的包信息接口
      const targetApi = flag ? getAddedPackageInfo : getNoAddPackageInfo;
      const { data } = await targetApi({
        courseId,
        ignoreError: true,
      });
      if (data?.title) {
        setPackageInfo({ ...data });
        setCourseNetworkError(false);
        // 如果是完成单个讲，就不刷新列表，刷新包信息就够了
        if (!params.isNoReFreshLesson) {
          /** 清空讲列表，已最新的为准 */
          lessonRef.current.list = [];
          queryLessonList(flag);
        }
      } else {
        // 没有包名称代表数据也不对，课包资源是必有title的
        setCourseNetworkError(true);
        setLoading(false);
      }
    } catch (error) {
      setLoading(false);
      setCourseNetworkError(true);
      logger.error("plan-detail-page-error", {
        error,
        isAddedPlan: params?.isAddedPlan,
        reason: "plan detail page request package info error",
      });
    }
  };

  const initData = async (isNoReFreshLesson?: boolean) => {
    try {
      // 刷新时页数恢复1
      pageParams.current.pageIndex = 1;
      const { data: isAddedPlan } = await getPackageIsAddedPlan({
        courseId,
        ignoreError: true,
      });
      setPageTitle(isAddedPlan ? "计划详情" : "方案详情");
      setPackageIsInPlan(isAddedPlan);
      packageStatus.current.isInPlan = isAddedPlan;
      setCourseNetworkError(false);
      queryPackageInfo({ isAddedPlan, isNoReFreshLesson });
    } catch (error) {
      setLoading(false);
      setCourseNetworkError(true);
      logger.error("plan-detail-page-error", {
        reason: "plan detail page check package api error",
        courseId,
        error,
      });
    }
  };

  const handleLoadMore = async () => {
    if (pageParams.current.haveNextPage && !loading) {
      pageParams.current.pageIndex += 1;
      await queryLessonList();
    }
  };

  const handleButtonClick = async () => {
    try {
      clickPv(
        "ewt_h5_study_course_self_learning_plan_detail_page_action_button_click",
        {
          courseId,
          courseName: packageInfo.title,
          action: packageIsInPlan ? "移出计划" : "加入计划",
        },
      );
      setLoading(true);
      const targetApi = packageIsInPlan
        ? postRemovePackageByPlan
        : postAddPackageToPlan;
      const { data: result } = await targetApi({
        courseId,
      });

      if (result) {
        Toast.show(packageIsInPlan ? "计划移出成功" : "加入计划成功");
        /** 操作完成后开始重置状态和刷新包信息、讲信息 */
        setPackageIsInPlan(!packageIsInPlan);
        packageStatus.current.isInPlan = !packageIsInPlan;
        pageParams.current = {
          ...pageParams.current,
          pageIndex: 1,
        };
        initData();
        setCommonPopVisible(false);
        setPopType("");
      }
    } catch (error) {
      if (
        !isIgnoreError(error, [
          ErrorCodeEnum.TOO_MANY_LESSON,
          ErrorCodeEnum.LESSON_IN_PLAN,
        ])
      ) {
        logger.error("plan-detail-operate-package-error", {
          courseId,
          packageIsInPlan,
          error,
        });
      }
    } finally {
      setLoading(false);
    }
  };

  // 检测课程包的状态，是否有更新
  const checkPackageStatus = async () => {
    try {
      const { data } = await getCheckPackageStatus({
        courseId,
        ignoreError: true,
      });
      if (data?.updateStatus) {
        // 延迟弹出
        window.setTimeout(() => {
          setPopType(EPopType.courseUpdate);
          setCommonPopVisible(true);
        }, 300);
      }
    } catch (error) {
      console.warn("检测课程包更新状态失败", error);
    }
  };

  // 更新课包状态
  const updateCourseStatus = async () => {
    try {
      const { data } = await postUpdateCourseStatus({
        courseId,
      });
      if (data) {
        pageParams.current.pageIndex = 1;
        // 更新成功后要刷新包、讲列表
        initData();
      }
    } catch (error) {
      console.warn("课包同步失败", error);
    } finally {
      // 失败也关闭这个框，等待下次弹出来提示
      setCommonPopVisible(false);
      setPopType("");
    }
  };

  // 删除课包内已失效的课程讲
  const toRemoveLoseLesson = async () => {
    try {
      const { data } = await postRemoveLoseLesson({
        courseId,
      });
      if (data) {
        // 更新成功后要刷新包、讲列表
        initData();
      } else {
        logger.error("remove-lose-efficacy-lesson-error", {
          reason: "remove lose efficacy lesson error",
          courseId,
          responseData: data,
        });
      }
    } catch (error) {
      console.warn("移出失效讲失败了", error);
    } finally {
      setCommonPopVisible(false);
      setPopType("");
    }
  };

  // 完成课程讲
  const onCompleteClick = async () => {
    try {
      setMarkCompleteVisible(false);
      // 使用完成后重置掉数据
      lessonRef.current.clickLesson = {} as IDetailLessonInfo;
      // 状态变更了重置页数到1
      pageParams.current.pageIndex = 1;
      // 最后更新视图
      forceUpdate();
      // 刷新包但不刷新讲列表
      queryPackageInfo({ isAddedPlan: true, isNoReFreshLesson: false });
    } catch (error) {
      logger.error("finish-lesson-error", {
        reason: "plan detail page mark finish lesson error",
        error,
        ...lessonRef.current.clickLesson,
      });
      setMarkCompleteVisible(false);
      lessonRef.current.clickLesson = {} as IDetailLessonInfo;
    }
  };

  useEffect(() => {
    initData();
    checkPackageStatus();
  }, []);

  const checkCurrentPackageStatus = async () => {
    try {
      const { data: isAddedPlan } = await getPackageIsAddedPlan({
        courseId,
        ignoreError: true,
      });
      if (Boolean(isAddedPlan) !== Boolean(packageStatus.current.isInPlan)) {
        setPageTitle(isAddedPlan ? "计划详情" : "方案详情");
        setPackageIsInPlan(isAddedPlan);
        packageStatus.current.isInPlan = isAddedPlan;
      }
      // 1.2变更: 该版本开始在app内连续看课也可以完成课讲了，因此回来后都要刷新一次列表
      // 刷新的同时将容器滚动到开始位置
      const dom = document.querySelector(".plan-detail-content-list-box");
      if (dom) {
        dom.scrollTop = 0;
      }
      // 状态变更了重置页数到1
      pageParams.current.pageIndex = 1;
      queryPackageInfo({ isAddedPlan, isNoReFreshLesson: false });
    } catch (error) {
      console.warn("静默更新时出错", error);
    }
  };

  // 页面change时重新查询下信息
  const handleRefreshConfig = (isShow: boolean) => {
    if (isShow) {
      // 从app看课回来不刷新课讲列表
      checkCurrentPackageStatus();
    }
  };

  // 页面的可见/不可见监控，变更后查询最新的状态
  useVisibilitychange({
    handleVisibilitychange: handleRefreshConfig,
  });

  return (
    <>
      <div
        className={cls([
          styles["self-learning-plan-detail-container"],
          "plan-detail-content-list-box",
        ])}
      >
        {/* 重新加载的错误提示 */}
        {courseNetworkError && !loading && (
          <div style={{ height: "100%" }}>
            <NetworkErrorStatus
              buttonOption={{
                handleClick: () => initData(),
              }}
            />
          </div>
        )}

        {/* 课包信息信息组件（排除课讲列表和移出按钮） */}
        <PlanDetailForCourse
          packageInfo={packageInfo}
          isInPlan={packageIsInPlan}
        />

        {/* 课讲列表 */}
        <div
          className={styles["lesson-list-box"]}
          style={!lessonRef.current.list?.length ? { paddingBottom: 0 } : {}}
        >
          <PlanDetailLessonList
            courseId={courseId}
            courseName={packageInfo?.title}
            list={lessonRef.current.list}
            setPopType={(type: EPopType) => setPopType(type)}
            setCommonPopVisible={(visible: boolean) =>
              setCommonPopVisible(visible)
            }
            packageInPlan={packageIsInPlan}
            handleCompleteClick={(lesson: IDetailLessonInfo) => {
              if (lesson?.status === EAddedLessonStatus.noSee) {
                setNoWatchedCourse(lesson);
                setNoFinishPopupVis(true);
                return;
              }
              if (EAddedLessonStatus.lose === lesson?.status) {
                setPopType(EPopType.loseEfficacy);
                setCommonPopVisible(true);
                return;
              }
              if (EAddedLessonStatus.completed === lesson?.status) {
                return; // 已失效、已完成的点击不处理
              }
              if (EAddedLessonStatus.watched === lesson?.status) {
                lessonRef.current.clickLesson = { ...lesson };
                setMarkCompleteVisible(true);
              }
            }}
          />

          {!lessonRef?.current.list?.length &&
            !loading &&
            !courseNetworkError &&
            !lessonNetworkError && (
              <div className={styles["list-empty-box"]}>
                <EmptyStatus />
              </div>
            )}

          {!lessonRef?.current.list?.length &&
            lessonNetworkError &&
            !loading && (
              <div style={{ height: "45vh" }}>
                <NetworkErrorStatus
                  buttonOption={{
                    handleClick: () => queryLessonList(),
                  }}
                />
              </div>
            )}

          {!!lessonRef?.current.list?.length && (
            <InfiniteScroll
              loadMore={handleLoadMore}
              hasMore={pageParams.current.haveNextPage}
            >
              <InfiniteScrollBottom hasMore={pageParams.current.haveNextPage} />
            </InfiniteScroll>
          )}
        </div>

        {/* 加入/移出计划按钮，前提是有包信息且加载完毕 */}
        {packageInfo?.title && !loading && (
          <Button
            text={!!packageIsInPlan ? "移出计划" : "加入计划"}
            className={cls([
              styles["fixed-button"],
              packageIsInPlan && styles["added-plan-button"],
              packageIsInPlan &&
                packageInfo?.lessonCount === packageInfo?.completeCount &&
                styles["hide-button"],
            ])}
            onClick={() => {
              if (packageIsInPlan) {
                setPopType(EPopType.removePlan);
                setCommonPopVisible(true);
              } else {
                handleButtonClick();
              }
            }}
          />
        )}

        <CourseNotCompletePopup
          visible={noFinishPopupVis}
          onCancel={() => setNoFinishPopupVis(false)}
          onOk={() => {
            setNoFinishPopupVis(false);
            clickPv(
              "ewt_h5_study_course_self_learning_no_watched_popup_ok_button_click",
              {
                sourceFrom: "计划详情页",
                courseId,
                lessonId: noWatchedCourse.lessonId,
              },
            );
          }}
        />

        {/* 课程更新、课讲失效、移出计划二次确认框 */}
        <TipPopup
          onClose={() => {
            setCommonPopVisible(false);
            setPopType("");
          }}
          hiddenCancel={popType === EPopType.courseUpdate}
          visible={
            commonPopVisible && !courseNetworkError && !lessonNetworkError
          }
          closeOnMaskClick={!(popType === EPopType.courseUpdate)}
          okText={
            [EPopType.courseUpdate, EPopType.loseEfficacy].includes(
              popType as EPopType,
            )
              ? "我知道了"
              : "确定"
          }
          title={
            popType === EPopType.courseUpdate
              ? "课程更新"
              : popType === EPopType.loseEfficacy
                ? "课程失效"
                : "是否移出计划"
          }
          onOk={() => {
            if (popType === EPopType.courseUpdate) {
              updateCourseStatus();
            }
            if (popType === EPopType.loseEfficacy) {
              toRemoveLoseLesson();
            }
            if (popType === EPopType.removePlan) {
              handleButtonClick();
            }
          }}
        >
          {popType === EPopType.courseUpdate && (
            <p className={styles["course-update-tip-text"]}>
              检测到该课程包中更新了部分内容，已为你自动更新到最新状态。
            </p>
          )}
          {popType === EPopType.loseEfficacy && (
            <p className={styles["course-lose-tip-text"]}>
              部分课程已失效，将从计划中移除。
            </p>
          )}
        </TipPopup>

        {/* 手动完成课程 */}
        <SignCompleteLessonPopup
          sourceFrom={ESourceFromForText.planDetail}
          courseId={courseId}
          lessonId={lessonRef.current.clickLesson.lessonId}
          visible={markCompleteVisible}
          onCancel={() => {
            // 关闭时清空信息
            lessonRef.current.clickLesson = {} as IDetailLessonInfo;
            setMarkCompleteVisible(false);
          }}
          onConfirm={onCompleteClick}
        />
        {/* 全局加载中 */}
        {loading && <Spin />}
      </div>
    </>
  );
};

export default React.memo(SelfLearningPlanDetail);
