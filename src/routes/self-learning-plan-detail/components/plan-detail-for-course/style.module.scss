.package-info-box {
  background-image: linear-gradient(180deg, #2D86FE 0%, #EEF1F6 98%);
  border: 1px solid #EFF1F7;

  .course-package-item {
    position: relative;
    z-index: 1;
    margin-bottom: 10px;
    border-radius: 8px;
    background: rgba(255, 255, 255, .85);
    border: 1px solid #FFFFFF;
    margin: 52px 12px 10px;

    .course-package-info-box {

      .base-info-container{
        padding: 8px 12px 10px;
        border: 1px solid transparent;
        position: relative;

        .covert-image {
          position: absolute;
          left: 12px;
          top: -32px;
          width: 110px;
          height: 63px;
          box-shadow: 0 0 8px 0 #338afe40;
          border: 0.92px solid #FFFFFF;
          border-radius: 4px;
        }

        .base-info-box {
          padding-left: 110px;
          display: flex;
          align-items: baseline;
          justify-content: space-between;

          .small-image-box {
            display: flex;
            align-items: baseline;
            color: #1A3252;

            span {
              margin-left: 10px;
            }

            .teacher-name {
              max-width: 80px;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }

            & + span {
              color: rgba(26, 50, 82, .45);
            }
          }
        }

        .course-page-title {
          font-size: 15px;
          font-weight: bold;
          color: #1A3252;
          margin-top: 12px;
          margin-bottom: 4px;

          & + div {
            color: rgba(26, 50, 82, .65);
          }
        }
      }
    }

    .add-to-plan-button {
      position: absolute;
      right: 8px;
      top: 8px;
      padding: 6px 8px;
      border: 1px solid #2D86FE;

      & > div {
        color: #2D86FE;
        font-size: 12px;

        i {
          font-size: 12px;
          margin-right: 4px;
        }
      }

      &.added {
        background-color: #52C41A;
        border: 1px solid #fff;
        position: absolute;
        top: -35px;
        right: 0;

        :global {
          .svg-icon {
            margin-right: 2px;
          }
        }

        & > div {
          color: #fff;
          padding: 0;
          line-height: 12px;
        }
      }
    }

    // 进度信息
    .plan-progress {
      padding: 0 12px 16px;

      .plan-num-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;

        p {
          font-size: 14px;

          .orange-text {
            font-weight: bold;
            color: #FA8B16;
          }
        }
      }
    }
  }
}

// 早于90天的提示
.earlier-finish-text {
  margin: 0 auto 14px;
  padding: 0 24px;
  color: #999999;
  font-size: 12px;
}
