import * as React from "react";
import styles from "./style.module.scss";
import { IDetailPackageInfo } from "@/service/self-learning/drawer";
import { Button, EButtonType, IconSvg } from "@/components";
import { cls, formatFloat, handleNumberToTenThousand } from "@/utils/tool";
import { Ellipsis, ProgressBar } from "antd-mobile";

export interface IPlanDetailForCourseProps {
  packageInfo: IDetailPackageInfo;
  isInPlan: boolean;
}

export const PlanDetailForCourse: React.FC<IPlanDetailForCourseProps> = (
  props,
) => {
  const { packageInfo, isInPlan } = props;
  const progressNum = isInPlan
    ? formatFloat(packageInfo?.completeCount, packageInfo?.lessonCount)
    : 0;

  return !!packageInfo?.title ? (
    <>
      <div className={styles["package-info-box"]}>
        <div className={styles["course-package-item"]}>
          {/* 右上角的已加计划的状态按钮 */}
          {!!isInPlan && (
            <Button
              type={EButtonType.grey}
              text="已加计划"
              className={cls([styles["add-to-plan-button"], styles["added"]])}
              icon={<IconSvg name="icon-yijiajihua" />}
            />
          )}
          {/* 中间课包的信息 */}
          <div className={styles["course-package-info-box"]}>
            <div className={styles["base-info-container"]}>
              {/* 课包封面图 */}
              <img
                src={packageInfo.coverImgUrl}
                alt=""
                className={styles["covert-image"]}
                onError={(e: any) =>
                  (e.target.style.background =
                    "linear-gradient(90deg, #7EB5FF, #FFDAB7)")
                }
              />
              {/* 共XX讲数 老师名称 多少人已选信息 */}
              <div className={styles["base-info-box"]}>
                <div className={styles["small-image-box"]}>
                  <span>共{packageInfo.lessonCount}讲</span>
                  <span className={styles["teacher-name"]}>
                    {packageInfo.authorList?.join(",")}
                  </span>
                </div>
                {/* 多少人已选会转换为xx万 */}
                <span>
                  {handleNumberToTenThousand(packageInfo.chooseCount)}人已选
                </span>
              </div>
              {/* 课包标题 */}
              <Ellipsis
                className={styles["course-page-title"]}
                content={packageInfo?.title}
                rows={2}
                direction="end"
              />
              {/* 课包的描述/推荐理由 */}
              {packageInfo.description && (
                <Ellipsis
                  content={packageInfo?.description}
                  rows={2}
                  direction="end"
                />
              )}
            </div>
          </div>

          {/* 加入计划后的进度信息 */}
          {!!isInPlan && (
            <div className={styles["plan-progress"]}>
              <div className={styles["plan-num-info"]}>
                <p>
                  <span>已完成：</span>
                  <span>
                    <span className={styles["orange-text"]}>
                      {packageInfo?.completeCount || 0}
                    </span>
                    /{packageInfo?.lessonCount || 0}
                  </span>
                </p>
                <p>
                  <span>总完成进度：</span>
                  {packageInfo?.lessonCount ? (
                    <span className={styles["orange-text"]}>
                      {`${progressNum}%`}
                    </span>
                  ) : (
                    0
                  )}
                </p>
              </div>
              <ProgressBar
                percent={Number(progressNum)}
                style={{
                  "--fill-color":
                    "linear-gradient(90deg, #FFC45A 0%, #F96D05 100%)",
                  "--track-color": "#EEF1F6",
                  "--track-width": "3.2vw",
                }}
              />
            </div>
          )}
        </div>
      </div>
      <div className={styles["earlier-finish-text"]}>
        {packageInfo?.earlierFinish
          ? "当前计划已完成超过90天，以下课程观看后将不计入当日学习打卡时长"
          : "加入计划后最近90天内观看1分钟以上的课程才能标记完成，刚看完请耐心等待1-2分钟~"}
      </div>
    </>
  ) : null;
};
