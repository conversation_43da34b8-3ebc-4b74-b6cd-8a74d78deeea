import * as React from "react";
import { Layout, ScrollToTop } from "@/components";
import type { ILayout } from "@/components";
import BackBlackImg from "@/assets/common/back_black.png";
import SelfLearningPlanDetail from "./components/plan-detail-content";
import { useLocation, useNavigate } from "react-router-dom";
import { EJumpType, createURLByType, getUrlParam } from "@/utils/tool";
import Style from "./style.module.scss";
import MstQtAnalytics from "mst-analytics";
import mstJsBridge from "mst-js-bridge";

export const Component: React.FC = () => {
  const location = useLocation();
  const courseId = getUrlParam("courseId", location.search);
  const navigate = useNavigate();

  const [pageTitle, setPageTitle] = React.useState("");

  React.useEffect(() => {
    MstQtAnalytics.separatelyReport(
      "ewt_h5_study_course_self_learning_plan_detail_page_view",
      {
        courseId,
      },
    );
  }, []);

  return (
    <Layout
      showHeader
      // 给安全区染色
      topSafeAreaProps={
        { className: Style["top-safe-area"] } as ILayout["topSafeAreaProps"]
      }
      setChildrenContainerHeight={({ top, bottom }) => {
        return `calc(100vh - ${top || 0}px - ${bottom || 0}px)`;
      }}
      className={Style["plan-detail-page-class"]}
      headerProps={{
        children: pageTitle,
        className: Style["header"],
        backIconUrl: BackBlackImg,
        onBack: () => {
          // 如果是首页跳转过来的，就 replace 跳转回去，否则就关闭 webview
          const isFromHome = location.search.indexOf("from=home") !== -1;
          if (isFromHome) {
            navigate(
              createURLByType({
                type: EJumpType.inside,
                originSearch: location.search,
                path: "/self-learning/home",
                removeQueryKeys: ["from", "courseId"],
              }),
              { replace: true },
            );
          } else {
            mstJsBridge.closeWebview();
          }
        },
      }}
    >
      <div
        style={{
          overflow: "auto",
          maxHeight: `calc(100vh - 12.8vw)`,
          height: `calc(100vh - 12.8vw)`,
        }}
      >
        <SelfLearningPlanDetail
          courseId={courseId}
          setPageTitle={setPageTitle}
        />
      </div>
      <ScrollToTop scrollElementClass="plan-detail-content-list-box" />
    </Layout>
  );
};
