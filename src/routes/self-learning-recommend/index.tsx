import React, { useState } from "react";
import { Layout, Popup, ScrollToTop } from "@/components";
import type { ILayout } from "@/components";
import RecommendContent from "@/components/recommend-content";
import BackBlackImg from "@/assets/common/back_black.png";

import Style from "./style.module.scss";
import { useLocation, useNavigate } from "react-router-dom";
import { EJumpType, clickPv, createURLByType } from "@/utils/tool";
import { ERecommendContentSourceFrom } from "@/typing.d";
import { StudyRecommendPopup } from "@/components";
import mstJsBridge from "mst-js-bridge";

export const Component: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isShowMore, setIsShowMore] = useState(false);
  const [addCourseVisible, setAddCourseVisible] = useState(false);

  return (
    <Layout
      showHeader
      // 给安全区染色
      topSafeAreaProps={
        { className: Style["top-safe-area"] } as ILayout["topSafeAreaProps"]
      }
      setChildrenContainerHeight={({ top, bottom }) => {
        return `calc(100vh - ${top || 0}px - ${bottom || 0}px)`;
      }}
      headerProps={{
        children: "推荐方案",
        className: Style["header"],
        backIconUrl: BackBlackImg,
        onBack: () => {
          // 如果是首页跳转过来的，就 replace 跳转回去，否则就关闭 webview
          const isFromHome = location.search.indexOf("from=home") !== -1;
          if (isFromHome) {
            navigate(
              createURLByType({
                type: EJumpType.inside,
                originSearch: location.search,
                path: "/self-learning/home",
                removeQueryKeys: ["from"],
              }),
              { replace: true },
            );
          } else {
            mstJsBridge.closeWebview();
          }
        },
        right: isShowMore && (
          <span
            className={Style["add-more-course"]}
            onClick={() => {
              clickPv(
                "ewt_h5_study_course_self_learning_recommend_page_add_more_course_button_click",
              );
              setAddCourseVisible(true);
            }}
          >
            添加更多课程
          </span>
        ),
      }}
    >
      <div
        style={{
          overflow: "auto",
          maxHeight: `calc(100% - 12.8vw)`,
          height: `calc(100vh - 12.8vw)`,
          backgroundColor: "#FFF",
        }}
        className="self-learning-recommend-page"
        id="self-learning-recommend-page"
      >
        <RecommendContent
          sourceFrom={ERecommendContentSourceFrom.recommend}
          subjectListLoaded={(subjectList: any[]) =>
            setIsShowMore(!!subjectList?.length)
          }
        />
        <ScrollToTop scrollElementClass="self-learning-recommend-page" />
      </div>
      {/* 新增：引导添加更多内容底抽 */}
      <Popup
        title="如何通过E讲堂添加"
        visible={addCourseVisible}
        onClose={() => setAddCourseVisible(false)}
      >
        <StudyRecommendPopup
          noPadding={true}
          onOk={() => {
            clickPv(
              "ewt_h5_study_course_self_learning_recommend_page_to_study_home_button_click",
            );
          }}
        />
      </Popup>
    </Layout>
  );
};
