import * as React from "react";
import "raf/polyfill";
// 不需要动态加载的必须组件
import { RouteObject, Navigate, createBrowserRouter } from "react-router-dom";
import { Error } from "./error";

import Auth from "./self-learning-auth";
import { NoAuth } from "./self-learning-auth/no-auth";

const routes: RouteObject[] = [
  {
    errorElement: <Error />,
    children: [
      {
        path: "/",
        // 兼容改造前的跳转逻辑
        Component: () => <Navigate to="/svip" replace={true} />,
      },
      {
        path: "/svip",
        lazy: () => import(/* webpackChunkName: "svip" */ "@/routes/Svip"),
      },
      {
        path: "/mbti",
        lazy: () => import(/* webpackChunkName: "mbti" */ "@/routes/mbti"),
      },
      {
        path: "/high-school-journey",
        lazy: () =>
          import(
            /* webpackChunkName: "highSchoolJourney" */ "@/routes/high-school-journey"
          ),
      },
      {
        path: "/self-learning",
        Component: Auth,
        children: [
          {
            path: "home",
            lazy: () =>
              import(
                /* webpackChunkName: "selfLearningHome" */ "@/routes/self-learning-home"
              ),
          },
          {
            /** 90天已完成的课程 */
            path: "completed-course",
            lazy: () =>
              import(
                /* webpackChunkName: "selfLearningCompletedCourse" */ "@/routes/self-learning-completed-course"
              ),
          },
          {
            path: "plan",
            lazy: () =>
              import(
                /* webpackChunkName: "selfLearningPlan" */ "@/routes/self-learning-plan"
              ),
          },
          {
            path: "recommend",
            lazy: () =>
              import(
                /* webpackChunkName: "selfLearningRecommend" */ "@/routes/self-learning-recommend"
              ),
          },
          {
            path: "plan-detail",
            lazy: () =>
              import(
                /* webpackChunkName: "selfLearningPlanDetail" */ "@/routes/self-learning-plan-detail"
              ),
          },
          {
            path: "completed-list",
            lazy: () =>
              import(
                /* webpackChunkName: "selfLearningCompletedList" */ "@/routes/self-learning-completed-list"
              ),
          },
        ],
      },
      {
        /** 周末智学 */
        path: "week-study",
        lazy: () =>
          import(
            /* webpackChunkName: "weekStudy" */ "@/routes/week-study"
          ),
      },
      {
        path: "/desk",
        children: [
          {
            /** 设定创建目标 */
            path: "create-target",
            lazy: () =>
              import(
                /* webpackChunkName: "createTarget" */ "@/routes/desk/create-target"
              ),
          },
          {
            /** 目标信息 */
            path: "target-info",
            lazy: () =>
              import(
                /* webpackChunkName: "targetInfo" */ "@/routes/desk/target-info"
              ),
          },
          {
            path: "chat",
            lazy: () =>
              import(/* webpackChunkName: "chat" */ "@/routes/desk/chat"),
          },
          {
            /** 创建计划 */
            path: "create-plan",
            lazy: () =>
              import(
                /* webpackChunkName: "createPlan" */ "@/routes/desk/create-plan"
              ),
          },
          {
            /** 计划内容 */
            path: "plan-resource",
            lazy: () =>
              import(
                /* webpackChunkName: "deskPlanResource" */ "@/routes/desk/plan-resource"
              ),
          },
          {
            /** 计划广场详情 */
            path: "plan-detail",
            lazy: () =>
              import(
                /* webpackChunkName: "planDetail" */ "@/routes/desk/plan-detail"
              ),
          },
          {
            /** 计划列表 */
            path: "plan-list",
            lazy: () =>
              import(
                /* webpackChunkName: "planList" */ "@/routes/desk/plan-list"
              ),
          },
          {
            /** 计划广场 */
            path: "plan-square",
            lazy: () =>
              import(
                /* webpackChunkName: "planSquare" */ "@/routes/desk/plan-square"
              ),
          },
          {
            /** 打卡   */
            path: "check-in",
            lazy: () =>
              import(
                /* webpackChunkName: "checkIn" */ "@/routes/desk/check-in"
              ),
          },
          {
            /** 未完成的自习计划课程   */
            path: "unfinished-course",
            lazy: () =>
              import(
                /* webpackChunkName: "unfinishedCourse" */ "@/routes/desk/unfinished-course"
              ),
          },
        ],
      },
      {
        path: "/self-learning/no-auth",
        Component: NoAuth,
      },
      {
        path: "/subject-channel/:channelType",
        lazy: () =>
          import(
            /* webpackChunkName: "subject-channel" */ "@/routes/subject-channel"
          ),
      },
      {
        path: "/camp",
        children: [
          {
            path: "achievement",
            lazy: () =>
              import(
                /* webpackChunkName: "camp-achievement" */ "@/routes/camp/achievement"
              ),
          },
          {
            path: "rank",
            lazy: () =>
              import(/* webpackChunkName: "camp-rank" */ "@/routes/camp/rank"),
          },
        ],
      },
    ],
  },
];
export default createBrowserRouter(routes, {
  basename: __APP_DIR__,
});
