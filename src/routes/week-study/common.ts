import dayjs, { OpUnitType } from "dayjs";
import { Dialog } from "antd-mobile";
import { getLocalStorage, getLocalStorageToValue, setLocalStorage } from "~/utils/tool";
import {
  ISubjectBaseEditionList,
  IUserSubjectBaseEdition,
} from "@/service/common";

import { IPlanListItem, TSubjectItem } from "./apis";

export function getPreviousWeekDate(weeks: IPlanListItem[]) {
  if (!Array.isArray(weeks) || weeks.length === 0) return "";
  const currenWeek = weeks.find((week) => week.currentWeek);
  if (!currenWeek || !currenWeek.startTimestamp) {
    return "";
  }
  console.log(currenWeek);
  return dayjs(+currenWeek.startTimestamp)
    .subtract(1, "week")
    .startOf("isoWeek" as OpUnitType)
    .format("YYYY-MM-DD");
}

// 二封本地存储，保存用户+年级维度的数据
export const unifySetLocalStorage = (params) => {
  const {
    grade: targetGrade,
    versionData,
    LOCAL_SUBJECT_VERSION_CACHE_KEY,
  } = params;
  const localDataStr = getLocalStorage(LOCAL_SUBJECT_VERSION_CACHE_KEY) || null;
  const localData = localDataStr ? JSON.parse(localDataStr).value : null;

  setLocalStorage(LOCAL_SUBJECT_VERSION_CACHE_KEY, {
    currentGrade: targetGrade,
    versionMap: {
      ...(localData?.versionMap || {}),
      [targetGrade]: versionData,
    },
  });
};

export const AICommend = "AI推题";
export const friendlyReminder = "温馨提醒";

// 页面访问来源
export enum EFromSource {
  push = 1,
  other = 2,
}

export const flagDict = {
  0: "复习",
  1: "预习",
  // 自主学习枚举 begin
  2: "兴趣素养",
  3: "基础夯实",
  4: "专项提升",
  5: "知识积累",
  // 自主学习枚举 end
  99: "其他",
};

/**
 * 假期活动场景
 * 1 寒假精选
 * 2 自主学习计划 - 学科提升方案 - 高一
 */
export enum EHolidayBizeCode {
  holidaySelection = 1,
  selfLearningForSubject = 2,
}

// 资源类型
export enum EResourceType {
  package = 1,
  paper = 2,
  fm = 3,
}

/**
 * 智学计划
 */
export const contentTypetoMap = {
  [EResourceType.package]: "视频课",
  [EResourceType.paper]: "试卷",
  [EResourceType.fm]: "FM",
};

// 课程讲
export interface IResourceForLesson {
  /** 课程讲id */
  lessonId?: number;
  /** 课程讲名称 */
  lessonName?: string;
  /** 导学案的id */
  guideId?: number;
  /** 导学案的id */
  paperId?: number;
  /** 时长 */
  duration?: number;
  /** 类型：0预习1复习 */
  typeFlag?: number;
}

// 试卷
export interface IResourceForPaper {
  /** 试卷id */
  paperId?: string;
  /** 试卷名称 */
  paperName?: string;
  /** 试卷时长 */
  paperDuration?: number;
  /** 试卷题目数 */
  paperQuestionNum?: number;
  /** 试卷作答码 */
  paperBizCode?: number;
  /** 试卷的作答平台 */
  paperPlatform?: number;
  /** 是否完成 true完成，false未完成 */
  hasFinish?: boolean;
  /** 报告id */
  reportId?: string;
  /** 正确率 */
  rightRate?: string;
  /** 是否已批改， true 已批改；false未批改 */
  correctResult?: boolean;
}

export interface ILessonList {
  /** 课程讲id */
  lessonId?: number;
  /** 课程讲名称 */
  lessonName?: string;
  /** 导学案的id */
  paperId?: number;
  /** 时长 */
  durationSecond: number;
  /** 类型：0预习1复习 */
  typeFlag?: number;
  /** 是否完成 true完成，false未完成 */
  hasFinish?: boolean;
  /** 报告id */
  reportId?: string;
  /** 正确率 */
  rightRate?: string;
  /** 是否已批改， true 已批改；false未批改 */
  correctResult?: boolean;
  /** 用户看课的进度 */
  progressTime?: number;
  /** 是否上一次看过的讲 */
  lastVisit?: boolean;
}

export interface IPackageInfo {
  /** 课程包id */
  id?: number;
  /** 课程包名称 */
  name?: string;
  /** 包图片路径 */
  picture?: string;
  /** 课程讲的数目 */
  lessonNum?: number;
  /** 课程讲列表 ,LessonInfoVO */
  lessonList?: ILessonList[];
}

export interface IFmInfo {
  /** fmid */
  id: string;
  /** 标题 */
  name: string;
  /** 播放时长,单位秒 */
  durationSecond: number;
  /** 图片地址 */
  picture: string;
}

export interface IPaperInfo {
  /** 试卷id */
  id?: string;
  /** 试卷名称 */
  name?: string;
  /** 试卷时长，单位分钟 */
  durationMinute?: number;
  /** 试卷题目数 */
  questionNum?: number;
  /** 试卷作答码 */
  bizCode?: number;
  /** 试卷的作答平台 */
  platform?: number;
  /** 是否完成 true完成，false未完成 */
  hasFinish?: boolean;
  /** 报告id */
  reportId?: string;
  /** 正确率 */
  rightRate?: string;
  /** 是否已批改， true 已批改；false未批改 */
  correctResult?: boolean;
  /** 类型：0预习1复习 */
  typeFlag?: number;
}

export interface IContentList {
  /** 试卷和报告信息 */
  paperInfo?: IPaperInfo;
  /** 注释 */
  fmInfo?: IFmInfo;
  /** 模块id ,PackageInfoVO */
  packageInfo?: IPackageInfo;
  /** 1 课程包；  2试卷；3 fm */
  contentType: number;
}

// 资源
export interface IResource {
  /** 书本 */
  bookId: number;
  /** 书本名称 */
  bookName: string;
  /** 模块id ,PackageInfoVO */
  contentList?: IContentList[];
}

// 学科类型
export interface ISubjectItem {
  subjectId: number | string;
  subjectName: string;
}

// 层次类型
export interface ILevelItem {
  level: number;
  levelName: string;
}

export const mindCode = 100; // 素养-心理的科目枚举，该枚举下有特殊样式

// 跨周检测所用 code
const WEEK_NOT_VALID = [
  // 操作历史计划的
  7773009,
  // 操作历史周期的总结
  7771301,
];

/**
 * 通过后端接口返回的错误码判断是否是下一周，不是严格准确
 * @param errorCode
 * @returns {boolean} 是否符合下一周
 */
export function checkNextWeek(errorCode?: number | string) {
  const isNextWeek = WEEK_NOT_VALID.includes(Number(errorCode));
  if (isNextWeek) {
    Dialog.alert({
      content: "新一周学习任务已上线，点击刷新获取最新计划",
      confirmText: "刷新",
      onConfirm: () => {
        // 删除链接上的 周期 信息，改的是 hash ，不会触发重新加载
        window.location.replace(
          window.location.href
            .replace(/planIdForRoom=[^&]+(&|$)/, "")
            .replace(/planIdForSquare=[^&]+(&|$)/, ""),
        );
        window.location.reload();
      },
    });
  }
  return isNextWeek;
}

// 进入周末智学的入口场景
export enum UpgradeSceneEnum {
  // 个人中心
  SELF = "self",
  // 首页金刚位 - 8+2
  EIGHT_TWO = "eight_two",
}

const SELF_WEEK_STUDY = "self_week_study_upgrade";

export function checkNeedShowUpgrade(scene: UpgradeSceneEnum) {
  try {
    const info = (scene || "").toLocaleLowerCase();
    // TODO 具体规则待定
    if (info === UpgradeSceneEnum.EIGHT_TWO) {
      return true;
    }
    if (info === UpgradeSceneEnum.SELF) {
      return !localStorage.getItem(SELF_WEEK_STUDY);
    }
  } catch (e) {
    // do nothing
  }
  return false;
}

export function cacheShowUpgrade(scene: UpgradeSceneEnum) {
  try {
    const info = (scene || "").toLocaleLowerCase();
    if (info === UpgradeSceneEnum.SELF) {
      localStorage.setItem(SELF_WEEK_STUDY, "1");
    }
  } catch (e) {
    // do nothing
  }
}

// 过滤信息技术、通用技术
export function filterSubjects(list: { subjectId: number }[]) {
  return list.filter((v) => ![10, 11].includes(v.subjectId));
}

// 请求状态
export enum EApiStatus {
  loading,
  success,
  failed,
  empty,
}

// 缓存更新 - 包含默认学科以及下面的书本等信息
interface IUpdateCache {
  cacheKey: string;
  updateDefaultSubjectId: boolean;
  subjectId?: string;
  key?: string;
  value?: string;
}
export function updateCache(props: IUpdateCache) {
  const { cacheKey, updateDefaultSubjectId, key, value, subjectId } = props;
  const cacheValue = getLocalStorageToValue(cacheKey);
  if (updateDefaultSubjectId) {
    setLocalStorage(cacheKey, {
      ...(cacheValue || {}),
      subjectId: value,
    });
  } else {
    setLocalStorage(cacheKey, {
      ...(cacheValue || {}),
      [subjectId]: {
        ...(cacheValue?.[subjectId] || {}),
        [key]: value,
      },
    });
  }
}
