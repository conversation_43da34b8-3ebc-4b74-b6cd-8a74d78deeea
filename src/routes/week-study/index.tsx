import React, { useState, useEffect } from "react";

import { useQuery } from "~/hooks";
import { getSafeAreaTop } from "@/utils/bridge-utils";

import WeekStudyContent from "./home";
import { EHolidayBizeCode } from "./common";

import "@/assets/font/iconfont/iconfont.css";

const WeekStudy: React.FC = (props: any) => {
  // 使用 useQuery 获取地址栏参数
  const query = useQuery();

  // 学科id
  const subjectId = Number(query.subjectId);
  // 层次id
  const level = Number(query.level) || 1;
  // 书本/期数
  const phase = query.phase || null;
  const [safeTop, setSafeTop] = useState<number>();
  const [safeTopColor, setSafeTopColor] = useState<string>("2e86ff");
  const initial = async () => {
    const safeTop = await getSafeAreaTop();
    setSafeTop(safeTop);
  };

  useEffect(() => {
    initial();
  }, []);

  return (
    <div>
      <div style={{ paddingTop: safeTop, backgroundColor: safeTopColor }}></div>
      <WeekStudyContent
        {...props}
        safeTop={safeTop}
        subjectId={subjectId}
        level={level}
        phase={phase}
        setSafeTopColor={setSafeTopColor}
        bizType={EHolidayBizeCode.selfLearningForSubject}
      />
    </div>
  );
};

export const Component = WeekStudy;
