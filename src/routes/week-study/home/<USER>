import React, { useEffect, useRef, useState } from "react";
import { SideBar, Toast } from "antd-mobile";
import Logger from "@/utils/safe-logger";
import { closeWebview } from "@/utils/bridge-utils";
import CurrentPlanFloat from "../components/current-plan-float";
import TopNavBar from "~/components/FullScreenComponents/top-nav-bar";
import styles from "./styles/index.module.scss";
import PageLoading from "~/components/FullScreenComponents/page-loading";
import HeaderBanner from "../components/header_banner";
import ProvinceModal from "../components/province-modal";
import BooksSwiper from "../components/books-swiper";
import Empty from "../components/empty";
import StageSelect from "../components/stage-select";
import { Error } from "../components/error";
import HeaderRight from "../components/header-right";
import {
  cls,
  setLocalStorage,
  getUid,
  openRoute,
  openWebView,
  getLocalStorageToValue,
  getChannel,
  separatelyReport,
  clickPv,
} from "~/utils/tool";
import { useQuery, useSyncState } from "~/hooks";
import { web } from "~/utils/hosts";
import EditionPopup, {
  IUserSubjectEditionMap,
} from "@/components/edition-popup";
import useBaseEdition from "@/components/edition-popup/base-edition-hook";
import { filterSubjects, mindCode, EApiStatus, updateCache } from "../common";
import { ResourceCard } from "../components/resource-card";
import SubjectSwiper from "../components/swiper-for-subject";
import {
  useChangeTab,
  useAddAndRemote,
  usePlanDetail,
  usePlanList,
  useRouter,
  useChangePlan,
} from "../hooks";
import { ETAB, squareText } from "../components/tabs";
import {
  IPlanListItem,
  TStageItem,
  TSubjectChildNode,
  TSubjectItem,
  getStageList,
  getSubjects,
  getBookList,
  getChapterList,
  getNewResourceList,
  TNode,
  ESubjectChildNodeType,
  TChapterNode,
  TNewResourceItem,
  getUserProvinceInfo,
  stageText,
} from "../apis";
import StudyClass from "../components/study-class";
import TipsModal, { useTips } from "../components/tips-modal";
import SubjectChildNode from "../components/subject-child-node.tsx";

export const SAVEKEY = "_newWeekStudy";
const logger = new Logger("week-study");

interface IWeekStudyContent {
  safeTop: number;
  setSafeTopColor: (color: string) => void;
}

const WeekStudyContent: React.FC<IWeekStudyContent> = (props) => {
  const { safeTop, setSafeTopColor } = props;
  /**
   * 第一步获取阶段
   */
  const [stages, setStages] = useState<TStageItem[]>([]);
  // 目前选择的阶段
  const [currentStage, setCurrentStage] = useSyncState<number>();
  async function getStages() {
    try {
      setLoading(true);
      const { data } = await getStageList();
      if (data?.length) {
        let defaultStage = (data || []).find((item) => {
          return item.whetherDefault;
        });
        // 异常情况 - 没有默认的阶段就取第一个
        if (!defaultStage) {
          defaultStage = data[0];
        }
        setCurrentStage(defaultStage.stageCode);
        setStages(data);
        return true;
      } else {
        setStages([]);
        setShowErrorPage(true);
        return false;
      }
    } catch (e) {
      setLoading(false);
      setShowErrorPage(true);
      logger.error("getStagesFailed", { e });
      return false;
    }
  }
  // 切换阶段 - 从获取学科开始往下走
  function changeStage(code: number) {
    if (currentStage.current === code) {
      return;
    }
    setCurrentStage(code);
    fetchSubjectList();
  }
  //==================================
  // 教材版本相关
  // 弹窗类型
  const [showEditionPop, setShowEditionPop] = useState<boolean>();
  const {
    loading: editionLoading,
    isError: editionError,
    initEditions,
    allSubjectEditionRef,
    userSubjectEditionRef,
    onSaveEdition,
  } = useBaseEdition({
    logger,
    transformAllSubjectEdition: filterSubjects,
  });
  // 用户是否已经有了省份信息
  const hasSetProvinceInfo = useRef<boolean>(false);
  const onEditionConfirm = async (userSelected: IUserSubjectEditionMap) => {
    const isDown = await onSaveEdition(userSelected);
    if (!isDown) {
      return;
    }
    setShowEditionPop(false);
    // 设置完教材版本后，获取省份信息，如果已经设置了省份信息，就到下一步获取学科列表
    if (!hasSetProvinceInfo.current) {
      getProvinceInfo();
    } else {
      // 如果已经设置过省份信息了就获取科目列表
      fetchSubjectList();
    }
  };
  // 获取教材版本失败的重试回调
  const onInitErrorRetry = () => {
    checkAllSubjectEdition();
  };
  // 关闭教材版本
  const onEditionClose = () => {
    try {
      // 当前是否所有学科都设置了教材版本
      const isAllSubjectHasEdition =
        allSubjectEditionRef.current.length ===
        Object.keys(userSubjectEditionRef.current).length;
      // 判断是不是 所有学科 都选择了 教材版本
      // 如果已经都选了，则直接关闭，或者返回上一个弹窗
      // 反之，如果当前有选中书本，则直接关闭，如果没有选中书本，则直接退出页面
      if (isAllSubjectHasEdition) {
        setShowEditionPop(false);
      } else {
        logger.info("onEditionClose");
        closeWebview();
      }
    } catch (error) {
      console.error("error");
    }
  };
  // 校验教材版本是否全部已经选择过
  async function checkAllSubjectEdition() {
    squareInitRef.current = true;
    // 获取所有的教材版本、用户选择的教材版本
    const isReady = await initEditions();
    // 接口异常则弹出教材版本弹窗，设置教材版本
    if (!isReady) {
      setLoading(false);
      setShowEditionPop(true);
      return;
    }
    // 当前是否所有学科都设置了教材版本
    const isAllSubjectHasEdition =
      allSubjectEditionRef.current.length ===
      Object.keys(userSubjectEditionRef.current).length;
    if (!isAllSubjectHasEdition) {
      // 如果存在有学科未选择 教材版本，则必然 弹出 教材版本 弹窗
      setShowEditionPop(true);
      setLoading(false);
    } else {
      // 当前所有学科已设置教材版本
      setShowEditionPop(false);
      // 第四步校验用户高考身份信息
      getProvinceInfo();
    }
  }
  //==============================================================
  // 第四步获取省份信息
  const [showProvinceModal, setShowProvinceModal] = useState(false);
  async function getProvinceInfo() {
    try {
      setLoading(true);
      const { data } = await getUserProvinceInfo();
      if (!data.memberProvinceCode && !data.schoolProvinceCode) {
        setShowProvinceModal(true);
        setLoading(false);
      } else {
        // 标识已经设置过省份了
        hasSetProvinceInfo.current = true;
        // 第五步获取学科
        fetchSubjectList(true);
      }
    } catch (e) {
      setLoading(false);
      setShowErrorPage(true);
      logger.error("getProvinceInfoFailed", { e });
    }
  }

  // 使用 useQuery 获取地址栏参数
  const query = useQuery();

  const [loading, setLoading] = useState(false); // 加载中状态
  const userId = getUid();
  // 本地存储的key，高一自习场景和寒假精选场景区分开
  const LOCAL_SUBJECT_VERSION_CACHE_KEY = `${userId}${SAVEKEY}`;
  // 本地存储字符串
  // 学科列表
  const [subjectList, setSubjectList] = useSyncState<TSubjectItem[]>([]);
  // 当前选中的学科对象
  const [currentSubjectItem, setCurrentSubjectItem] =
    useSyncState<TSubjectItem>();
  // 当前选中的学科下的直接子节点，是教材同步或专项提升
  const [currentSubjectChildNode, setCurrentSubjectChildNode] =
    useSyncState<TSubjectChildNode>();
  // 当前选中的书本
  const [currentBook, setCurrentBook] = useSyncState<TNode>();
  // 书本列表
  const [bookList, setBookList] = useSyncState<TNode[]>();
  // 当前选中的学科id，如果本地存储过对应的年级学科信息就取出来第一个，否则就默认到1-语文
  const [currentSubjectId, setCurrentSubjectId] = useSyncState<number>(1);
  const [sourceList, setSourceList] = useSyncState<TNewResourceItem[]>([]);
  const [showBackTop, setShowBackTop] = useState(false);
  const containerRef = useRef<any>();
  // 重新设置缓存
  const cacheKey = `${userId}_new_week_study`;
  // 广场初始化标记
  const squareInitRef = React.useRef(false);
  // 第五步 - 获取科目
  const hasUseSubjectQuery = React.useRef<boolean>(false);
  const fetchSubjectList = async (useUrlParam: boolean = false) => {
    setLoading(true);
    let channel = "";
    // 增加渠道号头信息，当失败时传递空值即可
    try {
      setShowErrorPage(false);
      setLoading(true);
      channel = (await getChannel()) as string;
    } catch (error) {
      console.error("获取渠道号失败", error);
    }

    try {
      const { data } = await getSubjects({
        stageCode: currentStage.current,
        channel,
      });
      if (data && data.length) {
        // 过滤不需要展示的技术两门学科
        const list = data;
        // 检测指定的学科是否存在
        const isHave =
          useUrlParam && query.subjectId && !hasUseSubjectQuery.current
            ? list.some((item) => item.subjectId === +query.subjectId)
            : false;
        let newSubjectId = list[0].subjectId; // 默认取得第一个学科
        const cacheValue = getLocalStorageToValue(cacheKey);
        // 如果是第一次渲染，并且地址栏携带了学科参数，且列表内存在，则优先级最高
        if (isHave) {
          hasUseSubjectQuery.current = true;
          newSubjectId = +query.subjectId;
          // 如果地址栏没指定学科，且本地缓存里有上一次选择的学科，则定位到该学科
        } else if (
          cacheValue &&
          cacheValue.subjectId &&
          list.some((item) => item.subjectId === cacheValue.subjectId)
        ) {
          // 如果缓存里有学科并且合法
          newSubjectId = cacheValue.subjectId;
        }

        // 更新缓存 - 默认选中的学科
        setLocalStorage(cacheKey, {
          ...(cacheValue || {}),
          subjectId: newSubjectId,
        });

        const subjectItem = list.find(
          (item) => item.subjectId === newSubjectId,
        ) as unknown as TSubjectItem;
        setCurrentSubjectItem(subjectItem);
        setSubjectList(list);
        // 当前选中的学科id
        setCurrentSubjectId(newSubjectId);
        // 第六步获取学科下的书本或者直接获取知识模块，和学科类型挂钩
        // 语文、英语下有专项提升、教材同步
        if (subjectItem.children?.length === 2) {
          let childNode = subjectItem.children[0];
          if (cacheValue && cacheValue[newSubjectId]?.childId) {
            const find = subjectItem.children.find(
              (item) => item.nodeId == cacheValue[newSubjectId].childId,
            );
            if (find) {
              childNode = find;
            }
          }
          // 本地有缓存且合法就使用，不然默认选中第一个
          setCurrentSubjectChildNode(childNode);
          setLocalStorage(cacheKey, {
            ...(cacheValue || {}),
            [newSubjectId]: {
              ...(cacheValue?.[newSubjectId] || {}),
              childId: childNode.nodeId,
            },
          });
          getBooks(!childNode.hasBook);
        } else {
          // 科目下边没有子节点
          setCurrentSubjectChildNode(undefined);
          getBooks(!subjectItem.hasBook);
        }
      } else {
        setSubjectList([]);
        setCurrentSubjectId(undefined);
        setCurrentSubjectItem(undefined);
        setCurrentSubjectChildNode(undefined);
        setLoading(false);
        // 如果没有科目当做异常情况处理
        setShowErrorPage(true);
      }
    } catch (error) {
      if (error?.code && +error.code === 7774103) {
        Toast.clear();
        Toast.show({
          maskStyle: {
            "--adm-mask-z-index": 1011,
          },
          content: `${error.msg || ""} 请重新选择教材`,
        });
        setShowEditionPop(true);
      }
      setShowErrorPage(true);
      setLoading(false);
      logger.error("fetchSubjectListError", { error });
    }
  };
  // 第六步 - 获取书本（如需）
  const [getBookStatus, setGetBookStatus] = useState<EApiStatus>();
  // 即使不获取书本的，我也调用 getBooks 函数，为了重置相关状态
  async function getBooks(isSkip?: boolean) {
    try {
      // 重置书本相关状态
      if (isSkip) {
        setBookList([]);
        setCurrentBook(undefined);
        setGetBookStatus(undefined);
        getChapters();
        return;
      }
      setLoading(true);
      setGetBookStatus(undefined);
      const { data } = await getBookList({
        stageCode: currentStage.current,
        subjectId: currentSubjectId.current,
        // 节点id, 语文或英语，传的是教材同步的节点id， 否则其他学科传的是学科列表的节点id
        nodeId:
          currentSubjectChildNode.current &&
          currentSubjectChildNode.current.nodeType ===
            ESubjectChildNodeType.textbook
            ? currentSubjectChildNode.current.nodeId
            : currentSubjectItem.current.nodeId,
      });
      if (data?.length) {
        // 取出最新的缓存
        let book = data[0];
        const cacheValue = getLocalStorageToValue(cacheKey);
        if (cacheValue && cacheValue[currentSubjectId.current]?.bookId) {
          const find = data.find(
            (item) =>
              item.nodeId == cacheValue[currentSubjectId.current].bookId,
          );
          if (find) {
            book = find;
          }
        }
        updateCache({
          cacheKey,
          updateDefaultSubjectId: false,
          subjectId: currentSubjectId.current,
          key: "bookId",
          value: book.nodeId,
        });
        setCurrentBook(book);
        setBookList(data);
        // 第7步获取知识模块
        getChapters();
      } else {
        // 只有当节点下hasBook为true的时候，才会去获取书本，如果这时候书本为空，那么就展示空态
        setGetBookStatus(EApiStatus.empty);
        setBookList([]);
        setCurrentBook(undefined);
        setLoading(false);
      }
    } catch (error) {
      setGetBookStatus(EApiStatus.failed);
      setLoading(false);
      logger.error("getBookError", { error });
    }
  }
  function changeBook(book: TNode) {
    if (currentBook.current && book.nodeId === currentBook.current.nodeId) {
      return;
    }
    updateCache({
      cacheKey,
      updateDefaultSubjectId: false,
      subjectId: currentSubjectId.current,
      key: "bookId",
      value: book.nodeId,
    });
    setCurrentBook(book);
    // 获取知识模块
    getChapters();
  }
  //================================
  const [getChaptersStatus, setGetChaptersStatus] = useState<EApiStatus>();
  const [chapters, setChapters] = useSyncState<TChapterNode[]>();
  const [currentChapter, setCurrentChapter] = useSyncState<TChapterNode>();
  const [currentChapterLevel, setCurrentChapterLevel] = useSyncState<TNode>();
  // 第七步 - 获取知识模块、难度
  async function getChapters() {
    try {
      setLoading(true);
      setGetChaptersStatus(EApiStatus.loading);
      let { data } = await getChapterList({
        stageCode: currentStage.current,
        subjectId: currentSubjectId.current,
        // 节点id， 语文或英语 教材同步的书本id或者专项提升节点id， 其他学科是书本id； 心理的是学科的节点id
        nodeId:
          currentBook.current?.nodeId ||
          currentSubjectChildNode.current?.nodeId ||
          currentSubjectItem.current.nodeId,
      });
      // 构造唯一id
      data = (data || []).map((item) => ({
        ...item,
        // 心理资源和其他学科下的资源不是同一个体系的，为了防止节点ID重复就拼接上了学科ID
        feKey: `${item.nodeId}-${currentSubjectId.current}`,
      }));
      if (data?.length) {
        let chapter = data[0];
        let level = data[0].children?.[0];
        // 取出最新的缓存
        const cacheValue = getLocalStorageToValue(cacheKey);
        if (cacheValue && cacheValue[currentSubjectId.current]?.chapterId) {
          const find = data.find(
            (item) =>
              item.nodeId == cacheValue[currentSubjectId.current].chapterId,
          );
          if (find) {
            chapter = find;
            level = void 0;
            if (find.children?.length) {
              level = find.children[0];
              const levelItem = find.children.find(
                (item) =>
                  item.nodeId ===
                  cacheValue[currentSubjectId.current][find.nodeId],
              );
              if (levelItem) {
                level = levelItem;
              }
            }
          }
        }
        setChapters(data);
        setCurrentChapter(chapter);
        setCurrentChapterLevel(level);
        // 更新缓存
        setLocalStorage(cacheKey, {
          ...(cacheValue || {}),
          [currentSubjectId.current]: {
            ...(cacheValue?.[currentSubjectId.current] || {}),
            chapterId: chapter.nodeId,
            // 知识模块下的难度id
            [chapter.nodeId]: level?.nodeId || "",
          },
        });
        // 第8步获取课程资源
        setGetChaptersStatus(EApiStatus.success);
        getResource();
      } else {
        setGetChaptersStatus(EApiStatus.empty);
        setChapters([]);
        setCurrentChapter(undefined);
        setCurrentChapterLevel(undefined);
        setLoading(false);
      }
    } catch (error) {
      setGetChaptersStatus(EApiStatus.failed);
      setLoading(false);
      console.error(error);
      logger.error("getChaptersError", { error });
    }
  }
  // 切换章节
  function changeChapter(chapter: TChapterNode) {
    setCurrentChapter(chapter);
    updateCache({
      cacheKey,
      updateDefaultSubjectId: false,
      subjectId: currentSubjectId.current,
      key: "chapterId",
      value: chapter.nodeId,
    });
    if (chapter.children?.length) {
      let level = chapter.children[0];
      const cacheValue = getLocalStorageToValue(cacheKey);
      const cacheLevelId =
        cacheValue[currentSubjectId.current]?.[chapter.nodeId];
      if (cacheValue && cacheLevelId) {
        const find = chapter.children.find(
          (item) => item.nodeId == cacheLevelId,
        );
        if (find) {
          level = find;
        }
      }
      setCurrentChapterLevel(level);
      getResource();
    } else {
      // 没有难度
      setCurrentChapterLevel(undefined);
      getResource();
    }
  }
  // 切换难度
  function changeLevel(level: TNode) {
    if (level.nodeId === currentChapterLevel.current.nodeId) {
      return;
    }
    updateCache({
      cacheKey,
      updateDefaultSubjectId: false,
      subjectId: currentSubjectId.current,
      key: currentChapter.current.nodeId,
      value: level.nodeId,
    });
    setCurrentChapterLevel(level);
    onBackTop();
    getResource();
  }
  //=======================================
  // 天龙八部最后一步 - 获取资源
  const [getResourceStatus, setGetResourceStatus] = useState<EApiStatus>();
  const getResource = async (plan?: IPlanListItem) => {
    try {
      if (
        !currentChapterLevel.current?.nodeId &&
        !currentChapter.current?.nodeId
      ) {
        return;
      }
      setLoading(true);
      setGetResourceStatus(EApiStatus.loading);
      const nowPlan = plan || currentPlanForSquareRef.current;
      const { data } = await getNewResourceList({
        subjectId: currentSubjectId.current,
        startTimestamp: nowPlan.startTimestamp,
        endTimestamp: nowPlan.endTimestamp,
        stageCode: currentStage.current,
        // 节点id， 如果有难度是难度的节点id， 否则是知识模块的节点id
        nodeId:
          currentChapterLevel.current?.nodeId || currentChapter.current?.nodeId,
      });
      setSourceList(data || []);
      if (!data || !data.length) {
        setGetResourceStatus(EApiStatus.empty);
      } else {
        setGetResourceStatus(EApiStatus.success);
      }
    } catch (error) {
      logger.error("getResourceError", { error });
      setSourceList([]);
      setGetResourceStatus(EApiStatus.failed);
    } finally {
      setLoading(false);
    }
  };
  //========================================
  // 页面级异常页面展示
  const [showErrorPage, setShowErrorPage] = useState(false);
  /** tab 切换 */
  const { tabId, changeTab } = useChangeTab({
    hasError: showErrorPage,
    squareInitRef,
    squareInit: () => checkAllSubjectEdition(),
    getResource,
  });

  const { getQueryValueWithkey, addQuery } = useRouter();

  /** 获取计划列表 */
  const {
    planList,
    planStatusMap,
    currentPlanForRoom,
    currentPlanForSquareRef,
    currentPlanForSquare,
    preAndCurrentWeekPlan,
    getPlanList,
    setPlanList,
    updatePlanStatus,
    setCurrentPlanForRoom,
    setCurrentPlanForSquare,
  } = usePlanList({
    onError: setShowErrorPage,
    getQueryValueWithkey,
    setLoading,
  });

  /** 温馨提示 */

  const { visible, type, state, toggle, check } = useTips({
    plan: preAndCurrentWeekPlan,
    userUniKey: LOCAL_SUBJECT_VERSION_CACHE_KEY,
  });
  /** 获取计划详情 */
  const { planDetail, getPlanDetail } = usePlanDetail({
    tabId,
    currentPlanForSquare,
  });
  /** 加入移除计划 */
  const { handleAddAndRemove } = useAddAndRemote({
    setSourceList,
    setCurrentPlanForSquare,
    setPlanList,
    sourceList,
    currentPlanForSquare,
    setCurrentPlanForRoom,
    getPlanDetail,
  });

  /** 切换计划 */
  const { changePlan } = useChangePlan({
    planList,
    addQuery,
    setLoading,
    setCurrentPlanForSquare,
    setCurrentPlanForRoom,
    getTutorialVersionResourceList: getResource,
  });

  /**
   * 切换学科
   */
  const handleChangeSubject = (value) => {
    const { subjectId } = value;
    if (subjectId === currentSubjectId.current) {
      return;
    }
    setCurrentSubjectId(subjectId);
    setCurrentSubjectItem(value);
    // 更新本地缓存
    updateCache({
      cacheKey,
      updateDefaultSubjectId: true,
      value: subjectId,
    });
    const newWeekStudyCache = getLocalStorageToValue(cacheKey);
    if (value.children?.length === 2) {
      let childNode = value.children[0];
      if (newWeekStudyCache && newWeekStudyCache[subjectId]?.childId) {
        const find = value.children.find(
          (item) => item.nodeId == newWeekStudyCache[subjectId].childId,
        );
        if (find) {
          childNode = find;
        }
      }
      // 本地有缓存且合法就使用，不然默认选中第一个
      setCurrentSubjectChildNode(childNode);
      updateCache({
        cacheKey,
        updateDefaultSubjectId: false,
        key: "childId",
        value: childNode.nodeId,
        subjectId,
      });
      getBooks(!childNode.hasBook);
    } else {
      setCurrentSubjectChildNode(undefined);
      // 查书本
      getBooks(!value.hasBook);
    }
    onBackTop();
  };

  /**
   * 切换学科下的直接子节点 - 专项提升、教材同步
   */
  function handleChangeSubjectChildNode(subjectChildNode: TSubjectChildNode) {
    if (currentSubjectChildNode.current.nodeId === subjectChildNode.nodeId) {
      return;
    }
    setCurrentSubjectChildNode(subjectChildNode);
    updateCache({
      cacheKey,
      updateDefaultSubjectId: false,
      subjectId: currentSubjectId.current,
      key: "childId",
      value: subjectChildNode.nodeId,
    });
    getBooks(!subjectChildNode.hasBook);
  }

  // 跳转试卷
  const handleJumpPaper = (resource) => {
    const { id, hasFinish, reportId, bizCode, platform } = resource;

    clickPv("ewt_h5_study_course_self_learning_for_subject_paper_click", {
      content_grade: stageText[currentStage.current] || "",
      subject_name: currentSubjectItem.current?.subjectName || "",
      question_id: id || "",
      bizType: "周末智学",
    });

    if (hasFinish) {
      openWebView(
        `${location.protocol + web}/tiku_h5/index/#/report?paperId=${id}&reportId=${reportId}&bizCode=${bizCode}&platform=${platform}`,
      );
    } else {
      openWebView(
        `${location.protocol + web}/tiku_h5/index/#/answer/paper?paperId=${id}&bizCode=${bizCode}&platform=${platform}`,
      );
    }
  };

  // 看视频
  const handleVideoPlay = (values) => {
    const { lessonId, courseId } = values;
    clickPv("ewt_h5_study_winter_vacation_study_content_click", {
      content_grade: stageText[currentStage.current] || "",
      subject_name: currentSubjectItem.current?.subjectName || "",
      content_id: lessonId || "",
      gather_content_id: courseId || "",
      bizType: "周末智学",
    });
    if (courseId) {
      openRoute({
        domain: "media",
        action: "commonPlayer",
        params: {
          isOffLineMode: false,
          playVideoId: lessonId,
        },
      });
    } else {
      Toast.show("当前课程暂不支持观看，请观看其他课程");
    }
  };

  // 预览FM
  const handleFmClick = (id: string) => {
    clickPv("ewt_h5_study_course_self_learning_for_subject_fm_click", {
      content_grade: stageText[currentStage.current] || "",
      content_id: id || "",
      fmName: currentChapter.current?.nodeName || "",
      bizType: "周末智学",
    });
    openRoute({
      domain: "fm",
      action: "open_detail",
      params: {
        id: `${id}`,
      },
    });
  };

  // 返回到顶部
  const onBackTop = () => {
    if (containerRef.current.scrollTo) {
      containerRef.current.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    } else {
      containerRef.current.scrollTop = 0;
    }
  };

  const onScroll = () => {
    const top = containerRef.current?.scrollTop || 0;
    setShowBackTop(document.documentElement.clientWidth < top);
  };

  // 关闭挽留弹窗
  const onClose = async () => {
    if (tabId === ETAB.selfStudySquare) {
      changeTab(ETAB.selfStudyRoom);
      return;
    }
    const checkRes = await check.checkCurrent();
    if (checkRes) {
      toggle(true);
    } else {
      try {
        closeWebview();
      } catch (error) {
        console.error("关闭页面失败", error);
      }
    }
  };
  useEffect(() => {
    window.ewt_goBack = () => {
      onClose();
      return false;
    };
    return () => {
      window.ewt_goBack = () => true;
    };
  }, []);
  useEffect(() => {
    if (!currentStage.current) {
      return;
    }
    separatelyReport("ewt_h5_study_course_week_study_view", {
      content_grade: stageText[currentStage.current] || "",
    });
  }, [currentStage.current]);
  /**
   * 页面渲染前置逻辑 - 需要获取到周计划列表，确认当前选中的计划
   */
  const initFlag = useRef(false);
  const init = async () => {
    try {
      // 第一步获取阶段, 不成功就阻断流程
      const success = await getStages();
      if (!success) {
        return;
      }
      // 第二步获取计划列表 - 获取失败会展示异常页面
      const res = await getPlanList(tabId === ETAB.selfStudyRoom);
      // 计划列表获取成功
      if (res) {
        // 智学广场才要检测教材版本
        if (tabId === ETAB.selfStudySquare) {
          checkAllSubjectEdition();
        }
      }
    } catch (error) {
      console.log(error);
    }
  };

  /** 广场切换教材版本的问题 */
  useEffect(() => {
    if (!tabId) {
      return;
    }
    if (tabId === ETAB.selfStudyRoom) {
      setSafeTopColor("#2e86ff");
    } else {
      setSafeTopColor("#fff");
    }
    if (!initFlag.current) {
      initFlag.current = true;
      // 第一步初始化
      init();
    }
  }, [tabId]);

  useEffect(() => {
    containerRef.current?.addEventListener("scroll", onScroll);

    return () => {
      containerRef.current?.removeEventListener("scroll", onScroll);
    };
  }, []);

  // 智学广场需要隐藏背景图
  const hiddenBg = Boolean(tabId === ETAB.selfStudySquare);

  return (
    <div style={{ height: `calc(100vh - ${safeTop}px)`, overflow: "hidden" }}>
      <TopNavBar
        onlyCallClose
        noAndroidBack
        onClose={onClose}
        right={
          <HeaderRight
            planList={planList}
            tabId={tabId}
            openEdition={() => {
              setShowEditionPop(true);
            }}
          />
        }
        className={cls([styles.customTopNavBar, hiddenBg && styles.single])}
      >
        {/* 高一上或高二上 */}
        {hiddenBg && !!currentStage.current && (
          <div className={styles["nav-left"]}>
            {squareText[currentStage.current]}
            <StageSelect
              value={currentStage.current}
              stageList={stages}
              onChange={(code) => {
                changeStage(code);
              }}
            />
          </div>
        )}
      </TopNavBar>
      <div
        className={cls([styles.scrollContainer, styles.single])}
        ref={containerRef}
      >
        {!hiddenBg && (
          <HeaderBanner
            onShowQuestion={() => {
              openWebView(
                "https://web.ewt360.com/themeTemplateClient/index.html?id=1901827989363142658",
              );
            }}
          />
        )}
        {showErrorPage ? (
          <Error
            className={styles["page-error"]}
            onClick={() => {
              window.location.reload();
              setShowErrorPage(false);
            }}
          />
        ) : tabId === ETAB.selfStudyRoom ? (
          // 智学室
          <StudyClass
            updatePlanStatus={updatePlanStatus}
            planStatusMap={planStatusMap}
            planList={planList}
            currentPlan={currentPlanForRoom}
            onChangePlan={(plan) => changePlan(plan, false)}
            onClickAdd={() => changeTab(ETAB.selfStudySquare)}
          />
        ) : (
          // 智学广场
          <>
            <div className={styles["sticky-container"]}>
              {/* 学科列表 */}
              <SubjectSwiper
                initialSlide={currentSubjectId.current}
                list={subjectList.current}
                handleClick={handleChangeSubject}
              />
              {/* 学科下的节点，对于语文、英语学科来说，下边有教材同步和专项提升 */}
              {!!currentSubjectItem.current?.children?.length && (
                <SubjectChildNode
                  subject={currentSubjectItem.current}
                  value={currentSubjectChildNode.current?.nodeId}
                  onChange={(item) => {
                    // 切换教材同步或专项提升
                    handleChangeSubjectChildNode(item);
                  }}
                />
              )}
              {/* 书本列表 */}
              {!!bookList.current?.length && (
                <BooksSwiper
                  list={bookList.current}
                  initialSlide={currentBook.current?.nodeId}
                  handleClick={(item) => {
                    onBackTop();
                    changeBook(item);
                  }}
                />
              )}
            </div>
            {/* 获取书本的异常、空态 */}
            {getBookStatus === EApiStatus.empty ? (
              <Empty
                type="noData"
                description={
                  loading
                    ? "加载中"
                    : "该学科下暂无适合你的学习内容，切换其他学科学习吧~"
                }
              />
            ) : getBookStatus === EApiStatus.failed ? (
              <Error
                className={styles["page-error"]}
                tip="啊哦，遇到问题了，请重试～"
                onClick={() => {
                  getBooks();
                }}
              ></Error>
            ) : getChaptersStatus === EApiStatus.empty ? (
              <Empty
                type="noData"
                description={loading ? "加载中" : "暂无数据"}
              />
            ) : getChaptersStatus === EApiStatus.failed ? (
              <Error
                className={styles["page-error"]}
                tip="啊哦，遇到问题了，请重试～"
                onClick={() => {
                  getChapters();
                }}
              />
            ) : (
              <div className={styles.page_container}>
                <div className={styles.leftBox}>
                  <SideBar
                    activeKey={currentChapter.current?.feKey}
                    onChange={(item) => {
                      const find = chapters.current.find(
                        (node) => node.feKey === item,
                      );
                      onBackTop();
                      changeChapter(find);
                    }}
                    className={styles.custom_side_bar}
                  >
                    {(chapters.current || []).map((item) => (
                      <SideBar.Item key={item.feKey} title={item.nodeName} />
                    ))}
                  </SideBar>
                  <div className={styles.left_empty}></div>
                </div>
                <div className={styles.rightBox}>
                  {!!currentChapter.current?.children?.length && (
                    <div
                      className={cls([
                        styles.tabsBox,
                        (currentSubjectChildNode.current?.nodeId ||
                          currentBook.current?.nodeId) &&
                          styles["tow-part-top"],
                        currentSubjectChildNode.current?.nodeId &&
                          currentBook.current?.nodeId &&
                          styles["three-part-top"],
                      ])}
                    >
                      {/* 难度 */}
                      {currentChapter.current.children.map((v) => (
                        <li
                          key={v.nodeId}
                          className={
                            v.nodeId === currentChapterLevel.current?.nodeId
                              ? styles.active
                              : ""
                          }
                          onClick={() => {
                            onBackTop();
                            changeLevel(v);
                          }}
                        >
                          {v.nodeName}
                        </li>
                      ))}
                    </div>
                  )}
                  <div className={styles.list_box}>
                    {!!sourceList.current.length ? (
                      sourceList.current.map((v, idx) => (
                        <div
                          key={idx}
                          className={cls([
                            styles.list_item,
                            currentSubjectId.current === mindCode &&
                              styles.fm_item,
                          ])}
                        >
                          <ResourceCard
                            handleAddAndRemove={handleAddAndRemove}
                            resource={v}
                            handlePlayVideo={handleVideoPlay}
                            handleJumpPaper={handleJumpPaper}
                            handleFmClick={handleFmClick}
                          />
                        </div>
                      ))
                    ) : getResourceStatus === EApiStatus.failed ? (
                      <Error
                        className={styles["page-error"]}
                        tip="啊哦，遇到问题了，请重试～"
                        onClick={() => {
                          getResource();
                        }}
                      />
                    ) : (
                      <Empty
                        type="noData"
                        description={loading ? "加载中" : "暂无数据"}
                      />
                    )}
                  </div>
                </div>
              </div>
            )}
            {/* 返回顶部按钮 */}
            {showBackTop && (
              <div className={styles.back_top_container}>
                <div className={styles.back_top} onClick={onBackTop}></div>
              </div>
            )}
            {/* 当前计划底部浮动条 */}
            {!!currentPlanForSquare &&
              tabId === ETAB.selfStudySquare &&
              !!planDetail && (
                <CurrentPlanFloat
                  planDetailInfo={planDetail}
                  planList={planList}
                  currentPlan={currentPlanForSquare}
                  changePlan={(item) => changePlan(item, true)}
                  toLearning={() => {
                    // 计划同步到自习室
                    setCurrentPlanForRoom(currentPlanForSquare);
                    changeTab(ETAB.selfStudyRoom);
                    onBackTop();
                  }}
                />
              )}
          </>
        )}
      </div>
      {/* 教材选择弹窗 - 在智学广场才需要设置教材版本 */}
      {tabId === ETAB.selfStudySquare && (
        <>
          {/* 教材版本 */}
          <EditionPopup
            open={showEditionPop}
            list={allSubjectEditionRef.current}
            userSelected={userSubjectEditionRef.current}
            isError={editionError}
            onRetry={onInitErrorRetry}
            onClose={onEditionClose}
            onConfirm={onEditionConfirm}
          />
          {/* 省份选择 */}
          <ProvinceModal
            title="选择你的高考省份/地区"
            stage={currentStage.current}
            open={showProvinceModal}
            afterSetProvince={() => {
              setShowProvinceModal(false);
              hasSetProvinceInfo.current = true;
              fetchSubjectList();
            }}
          />
        </>
      )}
      {/* 挽留弹窗 */}
      <TipsModal
        state={state}
        type={type}
        open={visible}
        onClose={() => toggle(false)}
        onGo={() => {
          if (preAndCurrentWeekPlan?.pre?.planId) {
            toggle(false);
            changeTab(ETAB.selfStudyRoom);
            setCurrentPlanForRoom(preAndCurrentWeekPlan?.pre);
          }
        }}
      />
      {/* 全局的loading */}
      <PageLoading
        className={styles["page-loading"]}
        visible={loading || editionLoading}
      />
    </div>
  );
};

export default WeekStudyContent;
