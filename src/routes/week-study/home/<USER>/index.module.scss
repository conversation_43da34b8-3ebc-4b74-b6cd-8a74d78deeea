@import "~@/styles/lib.scss";

.customTopNavBar {
  // --height: 48px;
  @include adaptive-max((
    --height: 48px,
  ));
  background: #2e86ff;
  &.single {
    background-color: #fff;
    :global {
      .adm-nav-bar-right {
        flex: 0 1 auto;
      }
      .adm-nav-bar-left {
        flex: 0 1 auto;
        filter: invert(64%) sepia(0%) saturate(1%) hue-rotate(262deg)
          brightness(92%) contrast(81%);
      }
      .adm-nav-bar-title {
        color: rgba(0, 0, 0, 0.85);
      }
    }
    .content-right {
      display: flex;
      align-items: center;
      justify-content: center;
      background: #e2eeff;
      font-weight: 600;
      color: #2e86ff;
      // width: 90px;
      // height: 24px;
      // border-radius: 12px;
      // font-size: 12px;
      // line-height: 14px;
      @include adaptive-max((
        width: 90px,
        height: 24px,
        border-radius: 12px,
        font-size: 12px,
        line-height: 14px,
      ));
      :global {
        .iconqiehuan {
          // font-size: 12px;
          // margin-left: 5px;
          @include adaptive-max((
            font-size: 12px,
            margin-left: 5px,
          ));
        }
      }
    }
  }
  :global {
    .adm-nav-bar-title {
      // line-height: 22px;
      padding: 0;
      font-family: PingFangSC-SNaNpxibold;
      font-weight: bold;
      // font-size: 18px;
      color: #ffffff;
      text-align: left;
      @include adaptive-max((
        line-height: 22px,
        font-size: 18px,
      ));
    }
  }

  .switchBtn {
    display: inline-block;
    // height: 22px;
    // line-height: 22px;
    // background: #1B69D2 url('@/assets/image/holiday-selection-h5/icon_switch.png') 90% 50% no-repeat;
    // background-size: 16px 12px;
    // border-radius: 18px;
    font-weight: 600;
    // font-size: 14px;
    color: #ffffff;
    // margin-left: 16px;
    // margin-top: -5px;
    // padding: 0 0 4px 0px;
    vertical-align: middle;
    @include adaptive-max((
      height: 22px,
      line-height: 22px,
      background-size: 16px 12px,
      font-size: 14px,
      margin-left: 16px,
      margin-top: -5px,
      padding: 0 0 4px 0px,
    ));
    &.self {
      // background: #1B69D2 url('@/assets/image/holiday-selection-h5/new-header/join_icon.png') 90% 50% no-repeat;
      // background-size: 13px;
      // padding: 0 0px 4px 8px;
      @include adaptive-max((
        background-size: 13px,
        padding: 0 0px 4px 8px,
      ));
    }
  }
}

.nav-left {
  display: flex;
  align-items: center;
}

.back_top_container {
  position: fixed;
  right: 0;
  // bottom: 40px;
  // 兼容底部底化悬浮层
  // bottom: calc(40px + constant(safe-area-inset-bottom));
  // bottom: calc(40px + env(safe-area-inset-bottom));
  // width: 1px;
  // height: 1px;
  z-index: 10;
  @include adaptive-max((
    bottom: 40px,
    width: 1px,
    height: 1px,
  ));
  .back_top {
    // width: 44px;
    // height: 44px;
    position: absolute;
    // right: 12px;
    // bottom: 20px;
    background-image: url("@/assets/image/holiday-selection-h5/icon_backTop.png");
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    @include adaptive-max((
      width: 44px,
      height: 44px,
      right: 12px,
      bottom: 20px,
    ));
  }
}

.sticky-container {
  position: sticky;
  z-index: 6;
  top: 0;
}

.scrollContainer {
  overflow: auto;
  background: #fff;
  &.single {
    height: calc(100% - 48px);
    // min-height: calc(100% - 45px);
    // 难度的浮条
    .tabsBox {
      // top: 58px;
      z-index: 6;
      @include adaptive-max((
        top: 58px,
      ));
    }
  }
}

.tow-part-top {
  top: calc(58px + 48px) !important;
}
.three-part-top {
  top: calc(58px + 48px + 48px) !important;
}

.page_container {
  height: auto;
  display: flex;
  flex-direction: row;
  background: #f3f4f8;
  // padding-bottom: 20px;
  @include adaptive-max((
    padding-bottom: 20px,
  ));
}

.leftBox {
  position: sticky;
  top: 0;
  width: 90px;
  flex: 0 0 auto;
  background: #f3f4f8;
  height: 100%;
  overflow-y: scroll;
  overflow-x: hidden;
  .custom_side_bar {
    width: 90px;
    height: 100%;
    :global {
      .adm-side-bar-item {
        padding: 12px 9px;
        .adm-badge-wrapper {
          width: 100%;
          text-align: center;
          .adm-side-bar-item-title {
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 14px;
            color: #2a333a;
          }
        }
      }

      .adm-side-bar-item-active {
        border-left: 3px solid #2e86ff;

        .adm-badge-wrapper {
          .adm-side-bar-item-title {
            color: #2e86ff;
            font-weight: 600;
            font-size: 14px;
          }
        }

        .adm-side-bar-item-highlight {
          display: none;
        }
      }
    }
  }

  .left_empty {
    width: 90px;
    height: 40px;
    height: calc(40px + constant(safe-area-inset-bottom));
    height: calc(40px + env(safe-area-inset-bottom));
  }
}

.rightBox {
  flex: 1;
  background-color: #ffffff;
  height: auto;
  min-height: 60vh;
  .tabsBox {
    position: sticky;
    top: 0;
    z-index: 6;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    padding: 12px 12px 18px 12px;
    background-color: #ffffff;
    li {
      list-style: none;
      background: #f3f4f8;
      width: 79px;
      height: 22px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      line-height: 22px;
      border: 1px solid #dddddd;
      border-radius: 11px;
      text-align: center;
      font-size: 12px;
      color: #333333;
      &:nth-child(even) {
        margin: 0 12px;
      }

      &.active {
        color: #2e86ff;
        border-color: #2e86ff;
        font-weight: bold;
        background: #e2eeff;
      }
    }
  }

  .list_box {
    display: block;
    padding: 0 12px 80px;
    background-color: #ffffff;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    .list_item {
      margin-top: 12px;
      background: #ffffff;
      width: 260px;
      overflow-x: hidden;
      &.fm_item {
        box-shadow: none;
      }

      .package_name {
        background: #edf4ff;
        border-radius: 4px;
        margin: 12px 6px;
        padding: 6px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;

        .name_info {
          max-width: 182px;
          font-family: PingFangSC-SNaNpxibold;
          font-weight: 600;
          font-size: 14px;
          color: #333333;
          // overflow: hidden;
          // text-overflow: ellipsis;
          // display: -webkit-box;
          // -webkit-line-clamp: 2;
          // -webkit-box-orient: vertical;
        }

        .lesson_num {
          font-family: PingFangSC-SNaNpxibold;
          font-weight: 600;
          font-size: 12px;
          color: #666666;
        }
      }

      .lesson_item {
        margin: 0 12px 12px;
        &:not(:last-child) {
          border-bottom: 1px solid #eeeeee;
        }
        .lesson_title {
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 14px;
          color: #333333;
          // text-overflow: ellipsis;
          // display: -webkit-box;
          // -webkit-line-clamp: 2;
          // overflow: hidden;
          // -webkit-box-orient: vertical;
        }

        .lesson_tag_time {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          margin: 12px 0;

          .type_tag {
            height: 18px;
            background: #fff1b8;
            border-radius: 2px;
            padding: 0 4px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 12px;
            color: #ad6800;
          }

          .duration {
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 12px;
            color: #666666;

            & > img {
              margin: -2px 4px 0 0;
              vertical-align: middle;
            }
          }
        }

        .action_panel {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          padding-bottom: 16px;
          .lesson_btn {
            display: inline-block;
            width: 113px;
            height: 28px;
            line-height: 28px;
            background: #2e86ff;
            border-radius: 16px;
            text-align: center;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            font-size: 14px;
            color: #ffffff;
            cursor: pointer;
            &:not(:first-child) {
              margin-left: 12px;
            }

            & > img {
              display: inline-block;
              margin: -2px 4px 0 0;
              width: 16px;
              height: 16px;
              vertical-align: middle;
            }
          }
        }
      }
    }
  }
}

.page-loading {
  z-index: 1001;
  .overlay-content {
    background: none;
  }
}
