import { useNavigate, useLocation } from "react-router-dom";

export const useRouter = () => {
  const navigate = useNavigate();
  const location = useLocation();
  /** 获取 query 某个字段值 */
  function getQueryValueWithkey(queryKey) {
    try {
      const query = new URLSearchParams(location.search);
      return query.get(queryKey);
    } catch (err) {
      console.error(err);
      return "";
    }
  }
  /** 在当前 path 上添加状态 */
  function addQuery(newData: Record<string, string | number>) {
    const query = new URLSearchParams(location.search);
    Object.keys(newData).forEach((key) => {
      if (newData[key]) {
        query.set(key, newData[key] as string);
      }
    });
    navigate(
      {
        pathname: location.pathname,
        search: query.toString(),
      },
      { replace: true },
    );
  }

  return {
    location,
    navigate,
    addQuery,
    getQueryValueWithkey,
  };
};
