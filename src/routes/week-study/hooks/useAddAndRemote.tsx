/** 任务移除和加入 */

import React from "react";
import { Toast } from "antd-mobile";

import { checkNextWeek } from "../common";
import { EResourceType } from "../common";
import {
  addResource,
  IPlanListItem,
  removeResource,
  TNewResourceItem,
} from "../apis";
import { clickPv } from "~/utils/tool";

interface IUseAddAndRemote {
  setSourceList: any;
  sourceList: React.MutableRefObject<TNewResourceItem[]>;
  currentPlanForSquare: IPlanListItem;
  getPlanDetail: any;
  setCurrentPlanForSquare: any;
  setCurrentPlanForRoom: any;
  setPlanList: any;
}

export const useAddAndRemote = (props: IUseAddAndRemote) => {
  const {
    setSourceList,
    sourceList,
    currentPlanForSquare,
    getPlanDetail,
    setCurrentPlanForRoom,
    setCurrentPlanForSquare,
    setPlanList,
  } = props;
  const addAndRemoveFlag = React.useRef({});
  /**
   *
   * @param item 当前视频课｜FM｜试卷
   * @param isAdd 是否是加入计划
   * @param resourceType 资源类型
   * @param packageInfo 如果是试卷的还需要这个额外字段
   */
  async function handleAddAndRemove({
    item,
    isAdd,
    resourceType,
    setLoading,
  }: {
    item: TNewResourceItem;
    isAdd: boolean;
    resourceType: EResourceType;
    setLoading: () => void;
  }) {
    try {
      clickPv("ewt_h5_study_course_week_study_square_addOrRemove_btn_click", {
        plantype: currentPlanForSquare.currentWeek ? "本周" : "未来周",
        action: isAdd ? "加入" : "移出",
      });
      setLoading();
      const resourceId =
        resourceType === EResourceType.fm
          ? item.fmInfo.id
          : resourceType === EResourceType.paper
            ? item.paperInfo.originalPaperId
            : item.lessonInfo.lessonId;
      // 注意！不同类型id取值不同
      let reqData;
      // 加入
      if (isAdd) {
        reqData = {
          ignoreError: 1,
          resourceType,
          planId: currentPlanForSquare.planId,
          planStartTime: currentPlanForSquare.startTimestamp,
          planEndTime: currentPlanForSquare.endTimestamp,
          resourceId,
          courseId:
            resourceType === EResourceType.package
              ? item.lessonInfo.packageId
              : void 0,
        };
        // 移除
      } else {
        reqData = {
          ignoreError: 1,
          resourceId,
          recordId: item.recordId,
        };
      }
      // 时序控制
      addAndRemoveFlag.current[resourceId] = addAndRemoveFlag.current[
        resourceId
      ]
        ? addAndRemoveFlag.current[resourceId] + 1
        : 1;
      const flag = addAndRemoveFlag.current[resourceId];
      let api = isAdd ? addResource : removeResource;
      const { data } = await api(reqData);
      // 时序控制
      if (flag !== addAndRemoveFlag.current[resourceId]) {
        return;
      }
      // 加入|移除成功
      const { planId, recordId } = isAdd ? data : { recordId: "" };
      if (resourceType === EResourceType.paper) {
        sourceList.current = sourceList.current.map((item) => {
          if (item.paperInfo?.originalPaperId === reqData.resourceId) {
            return {
              ...item,
              currentAdd: isAdd,
              recordId,
            };
          }
          return item;
        });
      } else if (resourceType === EResourceType.fm) {
        sourceList.current = sourceList.current.map((item) => {
          if (item.fmInfo?.id === reqData.resourceId) {
            return {
              ...item,
              currentAdd: isAdd,
              recordId,
            };
          }
          return item;
        });
      } else {
        sourceList.current = sourceList.current.map((pg) => {
          if (pg.lessonInfo?.lessonId === reqData.resourceId) {
            return {
              ...item,
              currentAdd: isAdd,
              recordId,
            };
          }
          return pg;
        });
      }
      Toast.show({
        content: isAdd ? "加入计划成功！" : "移出计划成功！",
      });
      // 初次加入计划的场景，只有加入才会创建计划，才会有planId
      if (planId && planId !== currentPlanForSquare.planId) {
        setCurrentPlanForSquare({
          ...currentPlanForSquare,
          planId,
        });
        // 更新计划列表
        setPlanList((pre) => {
          return pre.map((item) => {
            if (
              item.startTimestamp === currentPlanForSquare.startTimestamp &&
              item.endTimestamp === currentPlanForSquare.endTimestamp
            ) {
              return {
                ...item,
                planId,
              };
            }
            return item;
          });
        });
        setCurrentPlanForRoom((pre: IPlanListItem) => {
          if (
            pre.startTimestamp === currentPlanForSquare.startTimestamp &&
            pre.endTimestamp === currentPlanForSquare.endTimestamp
          ) {
            return {
              ...currentPlanForSquare,
              planId,
            };
          }
          return pre;
        });
      }
      // 更新状态
      setSourceList([...sourceList.current]);
      // 更新计划详情
      getPlanDetail();
    } catch (error) {
      console.error(error);
      if (!checkNextWeek(error?.code)) {
        Toast.show({
          content:
            error.msg ||
            (isAdd
              ? "加入计划失败，请稍后重试！"
              : "移出计划失败，请稍后重试！"),
        });
      }
    } finally {
      setLoading();
    }
  }
  return {
    handleAddAndRemove,
  };
};
