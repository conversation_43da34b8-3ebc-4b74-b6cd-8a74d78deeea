import React, { useState, useMemo } from "react";
import { getPlanList as getP<PERSON><PERSON><PERSON><PERSON><PERSON>, IPlanListItem } from "../apis";
import { usePlanStatus } from "./usePlanStatus";

interface IUsePlanList {
  getQueryValueWithkey: (key: string) => string;
  setLoading: (boolean) => void;
  onError: (show: boolean) => void;
}

export const PLANIDFORSQUARE = "planIdForSquare";
export const PLANIDFORROOM = "planIdForRoom";

export const usePlanList = (props: IUsePlanList) => {
  const { setLoading, getQueryValueWithkey, onError } = props;
  const currentPlanForSquareRef = React.useRef<IPlanListItem>();
  const [planList, setPlanList] = useState<IPlanListItem[]>();
  // 前一周、当前周的计划
  const preAndCurrentWeekPlan = useMemo<{
    current?: IPlanListItem;
    pre?: IPlanListItem;
  }>(() => {
    if (!planList?.length) {
      return {
        current: null,
        pre: null,
      };
    }
    const currentIndex = planList.findIndex((item) => {
      return item.currentWeek;
    });
    return {
      current: currentIndex > -1 ? planList[currentIndex] : null,
      pre: currentIndex > 0 ? planList[currentIndex - 1] : null,
    };
  }, [planList]);
  // 根据计划 id, 获取计划的状态
  const { planStatusMap, updatePlanStatus, initPlansStatus } = usePlanStatus({
    planList,
  });
  const [currentPlanForSquare, setCurrentPlanForSquare] =
    useState<IPlanListItem>();
  // s-TODO 遇到闭包了 临时解决
  function tempSetCurrentPlanForSquare(plan) {
    currentPlanForSquareRef.current = plan;
    setCurrentPlanForSquare(plan);
  }
  const [currentPlanForRoom, setCurrentPlanForRoom] = useState<IPlanListItem>();
  async function getPlanList(isInit?: boolean) {
    try {
      setLoading(true);
      const { data: list } = await getPlanListApi();
      // 如果 query 上带有计划id，就以 query上 为准
      const planIdForSquare = getQueryValueWithkey(PLANIDFORSQUARE);
      const planIdForRoom = getQueryValueWithkey(PLANIDFORROOM);
      let planForRoom;
      // 给自习室使用的计划
      if (planIdForRoom) {
        // 如果 query 上指定了某个计划，就以 query 为先
        planForRoom = list.find(
          (item) =>
            `${item.startTimestamp}-${item.endTimestamp}` ===
            window.decodeURIComponent(planIdForRoom),
        );
      }
      let planForSquare;
      // 1. 先找 query 上的，给广场使用的周计划
      if (planIdForSquare) {
        planForSquare = list.find(
          (item) =>
            `${item.startTimestamp}-${item.endTimestamp}` ===
            window.decodeURIComponent(planIdForSquare),
        );
      }
      /**
       * 2. 如果 query 上没有指定计划，或者指定的计划已经过期(兼容跨周的场景)
       * 就默认选择本周计划给广场使用
       */
      if (!planForSquare || !planForSquare.valid) {
        planForSquare = list.find((item) => item.currentWeek);
      }

      /**
       * 如果当周计划都缺失，则认为数据异常，显示异常页面
       */
      if (!planForSquare) {
        onError(true);
        return;
      }
      setPlanList(list);
      const validPlanIds = list
        .map((item) => item.planId)
        .filter(Boolean)
        .reverse();
      // 要进行时序的控制
      initPlansStatus(validPlanIds, true);
      tempSetCurrentPlanForSquare(planForSquare);
      // 如果 query 上没有指定周计划，就和给广场用的计划同步
      setCurrentPlanForRoom(planForRoom || planForSquare);
      return true;
    } catch (err) {
      console.log(err);
      setLoading(false);
      onError(true);
      return false;
    } finally {
      isInit && setLoading(false);
    }
  }
  return {
    planList,
    currentPlanForRoom,
    planStatusMap,
    currentPlanForSquareRef,
    preAndCurrentWeekPlan,
    currentPlanForSquare: currentPlanForSquareRef.current,
    getPlanList,
    setPlanList,
    updatePlanStatus,
    setCurrentPlanForRoom,
    setCurrentPlanForSquare: tempSetCurrentPlanForSquare,
  };
};
