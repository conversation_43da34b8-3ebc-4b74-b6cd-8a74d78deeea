/**
 * 根据计划 ID
 * 获取 AI 推题列表
 * */
import React, { useEffect } from 'react';
import { Toast } from 'antd-mobile';
import { useAPILevel } from '~/hooks';
import { makeAnswerReportUrl, makeAnswerUrl, openWebView } from '~/utils/tool';

import {
  IAIRecommendPaperItem,
  IResourceItem,
  ResourceTypeEnum,
  getAIRecommendPaperList,
  createRecommendPaper,
  ICreateRecommendPaper,
} from '../apis';

interface IUseAIRecommendPaper {
  planId?: string;
}

export const useAIRecommendPaper = (props: IUseAIRecommendPaper) => {
  const { planId } = props;
  const [isCreateAIPaper, setIsCreateAIPaper] = React.useState(false);
  // 时序控制
  const { upLevel, getLevelKey } = useAPILevel(1);

  // ai推题创建试卷
  async function createPaper(
    item: IAIRecommendPaperItem,
    reqData: ICreateRecommendPaper,
  ) {
    try {
      let res;
      // 已经创建过试卷了，就直接跳转答题
      if (item.paperId) {
        res = item;
      } else {
        setIsCreateAIPaper(true);
        // 没创建的话就先创建试卷
        const { data } = await createRecommendPaper(reqData);
        res = data;
        setAiRecommendPaperList((list) => {
          const { sourceResourceId, sourcePaperId } = item;
          const key = `${sourceResourceId}-${sourcePaperId}`;
          // 保存创建好的试卷相关信息
          if (list[key]) {
            return {
              ...list,
              [key]: {
                ...list[key],
                ...(res || {}),
              },
            };
          }
          return list;
        });
      }
      if (!res) {
        return;
      }
      // 跳转去答题
      const config = {
        paperId: res.paperId,
        reportId: res.reportId || undefined,
        bizCode: res.bizCode,
        platform: res.platform,
        isRepeat: 1, // 防止重复作答
      };
      openWebView(
        item.hasFinish && item.reportId
          ? makeAnswerReportUrl(config)
          : makeAnswerUrl(config),
      );
    } catch (error) {
      console.error(error);
      if (error?.code && Number(error.code) !== 200) {
        Toast.show(error.msg || '创建失败，请稍后再试');
      }
    } finally {
      setIsCreateAIPaper(false);
    }
  }

  const [aiRecommendPaperList, setAiRecommendPaperList] =
    React.useState<Record<string, IAIRecommendPaperItem>>();

  // 要为 AI 推题找到归属的资源
  function getAIPaperByResource(item: IResourceItem) {
    if (
      !aiRecommendPaperList ||
      !item ||
      item.resourceType !== ResourceTypeEnum.LESSON
    ) {
      return null;
    }
    const { lessonId, paperId } = item.lessonVO || {};
    if (!lessonId || !paperId) {
      return null;
    }
    return aiRecommendPaperList[`${lessonId}-${paperId}`];
  }
  // 获取 AI 推题列表
  async function getAiPaperList() {
    try {
      if (!planId) {
        return;
      }
      const level = upLevel(1);
      const { data: list } = await getAIRecommendPaperList({ planId });
      if (level !== getLevelKey(1)) {
        return;
      }
      // 组装为 map 结构，方便检索
      setAiRecommendPaperList(
        (list || []).reduce((paperMap, nowPaper) => {
          if (nowPaper.sourceResourceId && nowPaper.sourcePaperId) {
            paperMap[`${nowPaper.sourceResourceId}-${nowPaper.sourcePaperId}`] =
              nowPaper;
          }
          return paperMap;
        }, {}),
      );
    } catch (error) {
      setAiRecommendPaperList(void 0);
      console.error(error);
    }
  }

  useEffect(() => {
    getAiPaperList();
  }, [planId]);

  return {
    isCreateAIPaper,
    aiRecommendPaperList,
    getAiPaperList,
    setAiRecommendPaperList,
    getAIPaperByResource,
    createPaper,
  };
};
