/**
 * 管理计划的状态
 */

import { useState, useRef } from 'react';

import { getPlanStatusList, IPlanStatusItem, IResourceItem } from '../apis';

export interface IUpdatePlanStatus {
  resources?: IResourceItem[];
  planId: string;
}

interface IUsePlanStatus {}

export const usePlanStatus = (props: IUsePlanStatus) => {
  /**
   * 每个计划状态的更新时序控制，因为状态的更新有时候是依赖请求的，有时候又是前端手动维护的
   * 所以为了避免状态的更新错乱，需要进行时序的控制
   */
  const planUpdateControl = useRef<Record<string, number>>({});
  function updateControl(ids: string[]) {
    (ids || []).forEach((planId) => {
      if (!planId) {
        return;
      }
      if (planUpdateControl.current[planId]) {
        planUpdateControl.current[planId] =
          planUpdateControl.current[planId] + 1;
      } else {
        planUpdateControl.current[planId] = 1;
      }
    });
    return { ...planUpdateControl.current };
  }
  const [planStatusMap, setPlanStatusMap] = useState<
    Record<string, IPlanStatusItem>
  >({});
  /**
   * 计划列表状态初始化
   * 注意这是串行发送的，要考虑请求延迟的问题，如果请求延迟并且计划的状态有更新(手动添加或移除了计划)，就丢弃请求返回的该计划的结果
   * @param planIdList 每次最多获取10个计划的状态
   * @returns
   */
  async function initPlansStatus(
    planIdList: string[],
    shouldUpdateFlags: boolean,
    initFlags?: Record<string, number>,
  ) {
    try {
      if (!planIdList.length) {
        return;
      }
      // 虽然分多次请求，但都属于同一次更新
      let flags = initFlags;
      if (shouldUpdateFlags) {
        flags = updateControl(planIdList);
      }
      // 取出10个
      const realIds = planIdList.splice(0, 10);
      const res = await getPlanStatus(realIds);
      if (res?.length) {
        setPlanStatusMap((pre) => {
          const newPlanStatus = res.reduce((total, now) => {
            const { planId } = now;
            // 如果当前的计划状态已经被更新过了，就丢弃接口返回的结果
            if (planId && flags[planId] === planUpdateControl.current[planId]) {
              total[planId] = now;
            }
            return total;
          }, {});
          return {
            ...(pre || {}),
            ...newPlanStatus,
          };
        });
        if (planIdList?.length) {
          // 延迟 500ms，避免频繁请求，最多会有30个计划
          setTimeout(() => {
            // 开始的时候已经更新过了，就不再进行时序的更新了
            initPlansStatus(planIdList, false, flags);
          }, 500);
        }
      }
    } catch (e) {
      console.error(e);
    }
  }

  async function getPlanStatus(planIdList: string[]) {
    try {
      if (!planIdList.length) {
        return;
      }
      const { data: res } = await getPlanStatusList({
        planIdList,
      });
      return res;
    } catch (e) {
      console.error(e);
    }
  }
  // 手动更新计划状态
  function updatePlanStatus(data: IUpdatePlanStatus) {
    const { resources, planId } = data;
    if (!planId) {
      return;
    }
    updateControl([planId]);
    const missionCount = (resources || []).length;
    const missionDoneCount = (resources || []).filter(
      (mission) => mission.finishStatus,
    ).length;
    setPlanStatusMap((pre) => {
      return {
        ...(pre || {}),
        [planId]: {
          planId,
          finishStatus: !!missionCount && missionCount === missionDoneCount,
          unfinishedNum: missionCount - missionDoneCount,
        },
      };
    });
  }

  return {
    planStatusMap,
    getPlanStatus,
    initPlansStatus,
    updatePlanStatus,
  };
};
