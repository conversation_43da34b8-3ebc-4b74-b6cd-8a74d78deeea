/** 获取计划详情 */

import React, { useState, useRef } from 'react';
import { getPlanInfo, IPlanInfo } from '../apis';
import { ETAB } from '../components/tabs';

interface IUsePlanDetail {
  tabId: ETAB;
  currentPlanForSquare: any;
}

export const usePlanDetail = (props: IUsePlanDetail) => {
  const { currentPlanForSquare, tabId } = props;
  const planInfoFlagRef = useRef(0);
  const [planDetail, setPlanDetail] = useState<IPlanInfo>();
  // 切换周计划、切换Tab 都要更新计划详情
  async function getPlanDetail() {
    try {
      planInfoFlagRef.current++;
      const flag = planInfoFlagRef.current;
      const { data: res } = await getPlanInfo({
        planId: currentPlanForSquare.id,
        startTimestamp: currentPlanForSquare.startTimestamp,
        endTimestamp: currentPlanForSquare.endTimestamp,
      });
      if (flag === planInfoFlagRef.current) {
        // 设置计划详情
        setPlanDetail(res);
      }
    } catch (error) {
      console.error(error);
    }
  }
  React.useEffect(() => {
    if (currentPlanForSquare) {
      getPlanDetail();
    }
  }, [currentPlanForSquare, tabId]);
  return {
    planDetail,
    getPlanDetail,
  };
};
