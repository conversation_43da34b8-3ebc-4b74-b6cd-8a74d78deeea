/** 切换周计划 */

import React from 'react';

import { IPlanListItem } from '../apis';
import { PLANIDFORROOM, PLANIDFORSQUARE } from './usePlanList';

interface IUseChangePlan {
  planList: IPlanListItem[];
  addQuery: (v: Record<string, any>) => void;
  setLoading: (v: boolean) => void;
  setCurrentPlanForSquare: (v: IPlanListItem) => void;
  setCurrentPlanForRoom: (v: IPlanListItem) => void;
  getTutorialVersionResourceList: (plan?: IPlanListItem) => any;
}

export const useChangePlan = (props: IUseChangePlan) => {
  const {
    planList,
    addQuery,
    setLoading,
    setCurrentPlanForRoom,
    setCurrentPlanForSquare,
    getTutorialVersionResourceList,
  } = props;
  function changePlan(nextPlan: IPlanListItem, isSquare: boolean) {
    if (!nextPlan) {
      return;
    }
    // 在智学广场里切换周计划
    if (isSquare) {
      setLoading(true);
      // 当前选中的计划同步到自习室
      setCurrentPlanForRoom(nextPlan);
      setCurrentPlanForSquare(nextPlan);
      // 重写获取课程，因为课程的加入状态和计划挂钩
      getTutorialVersionResourceList(nextPlan);
      // 计划记忆到 query 上
      addQuery({
        [PLANIDFORSQUARE]: `${nextPlan.startTimestamp}-${nextPlan.endTimestamp}`,
        [PLANIDFORROOM]: `${nextPlan.startTimestamp}-${nextPlan.endTimestamp}`,
      });
    } else {
      // 智习室切换，如果不是过期的计划，广场就同步，否则就切换到当前周
      const squarePlan = nextPlan.valid
        ? nextPlan
        : planList.find((item) => item.currentWeek);
      setCurrentPlanForSquare(squarePlan);
      setCurrentPlanForRoom(nextPlan);
      // 在自习室里切换周计划
      addQuery({
        [PLANIDFORROOM]: `${nextPlan.startTimestamp}-${nextPlan.endTimestamp}`,
        [PLANIDFORSQUARE]: `${squarePlan.startTimestamp}-${squarePlan.endTimestamp}`,
      });
    }
  }
  return {
    changePlan,
  };
};
