/** 提供 tab的切换能力 - 暂留*/

import React, { useState } from "react";

import { useRouter } from "./";
import { ETAB } from "../components/tabs";

export const TABID = "tabId";

interface IUseChangeTab {
  hasError: boolean;
  squareInitRef: React.MutableRefObject<boolean>;
  squareInit: () => Promise<any>;
  getResource: () => Promise<any>;
}

export const useChangeTab = (props: IUseChangeTab) => {
  const { squareInit, getResource, squareInitRef, hasError } = props;
  const { location, addQuery } = useRouter();
  // 默认选中自习室
  const [tabId, setTabId] = useState<ETAB>();
  // 初始化的逻辑
  React.useEffect(() => {
    const query = new URLSearchParams(location.search);
    const newTabId = query.get(TABID) || ETAB.selfStudyRoom;
    if (newTabId && +tabId !== +newTabId) {
      setTabId(+newTabId);
    }
  }, []);
  // 切换 tab
  // s-TOOD 切换后要重新获取广场课程、获取计划详情
  function changeTab(key: ETAB) {
    if (!key || hasError) {
      return;
    }
    if (+key !== tabId) {
      setTabId(+key);
      // 父子组件未完全拆分 -  TODO
      if (+key === ETAB.selfStudySquare) {
        if (!squareInitRef.current) {
          squareInitRef.current = true;
          squareInit();
        } else {
          getResource();
        }
      }
      addQuery({ tabId: key });
    }
  }

  return {
    tabId,
    changeTab,
  };
};
