@import "~@/styles/lib.scss";

.container {
  width: 100vw;
  margin: 0 auto;
  // padding: 4px 12px 8px 12px;
  @include adaptive-max((
    padding: 4px 12px 8px 12px,
  ));

  .top_container {
    display: flex;
    align-items: center;
    // height: 24px;
    @include adaptive-max((
      height: 24px,
    ));

    .question {
      // width: 20px;
      // height: 24px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      cursor: pointer;
      @include adaptive-max((
        width: 20px,
        height: 24px,
      ));

      i {
        // font-size: 12px;
        color: rgba(2, 30, 102, 0.5);
        @include adaptive-max((
          font-size: 12px,
        ));
      }
    }
  }

  .top {
    position: relative;
    // height: 8px;
    // border-radius: 5px;
    flex: 1;
    @include adaptive-max((
      height: 8px,
      border-radius: 5px,
    ));

    .per_bg {
      position: absolute;
      left: 0;
      top: 0;
      // height: 8px;
      background: #ffffff;
      // border: 1px solid #021e66;
      // border-radius: 5px;
      overflow: hidden;
      width: 100%;
      @include adaptive-max((
        height: 8px,
        border: 1px solid #021e66,
        border-radius: 5px,
      ));

      .per_bar {
        position: absolute;
        left: 0;
        top: 0;
        width: 50%;
        // height: 10px;
        background-color: #2e86ff;
        transition: width 1s ease;
        @include adaptive-max((
          height: 10px,
        ));
      }
    }

    .per_bar_top {
      position: absolute;
      left: 0;
      top: 0;
      width: 50%;
      // height: 8px;
      // border-radius: 5px;
      transition: width 1s ease;
      @include adaptive-max((
        height: 8px,
        border-radius: 5px,
      ));

      .bar_icon {
        position: absolute;
        top: 50%;
        right: 0;
        transform: translate(50%, -50%);
        // width: 20px;
        // height: 24px;
        @include adaptive-max((
          width: 20px,
          height: 24px,
        ));
      }
    }
  }

  .bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    // margin-top: 8px;
    @include adaptive-max((
      margin-top: 8px,
    ));

    .bottom_left {
      display: flex;
      align-items: center;

      .word_icon {
        display: flex;
        align-items: center;
        i {
          // font-size: 12px;
          color: rgba(2, 30, 102, 0.25);
          @include adaptive-max((
            font-size: 12px,
          ));
        }
      }

      .word {
        // font-size: 12px;
        color: #021e66;
        // line-height: 20px;
        // margin-left: 4px;
        @include adaptive-max((
          font-size: 12px,
          line-height: 20px,
          margin-left: 4px,
        ));
      }

      .percent {
        font-weight: bold;
        // font-size: 14px;
        color: #ffe776;
        // line-height: 20px;
        // margin-left: 4px;
        position: relative;
        z-index: 1;
        @include adaptive-max((
          font-size: 14px,
          line-height: 20px,
          margin-left: 4px,
        ));

        &::before {
          content: attr(data-text);
          position: absolute;
          // -webkit-text-stroke: 2px #021e66;
          z-index: -1;
          @include adaptive-max((
            -webkit-text-stroke: 2px #021e66,
          ));
        }
      }

      .refresh {
        // margin-left: 12px;
        cursor: pointer;
        font-weight: bold;
        // font-size: 12px;
        color: #2f86ff;
        // line-height: 20px;
        @include adaptive-max((
          margin-left: 12px,
          font-size: 12px,
          line-height: 20px,
        ));
      }
    }

    .bottom_right {
      display: flex;
      align-items: center;

      .add_btn {
        // line-height: 20px;
        color: #2e86ff;
        font-weight: bold;
        // font-size: 12px;
        @include adaptive-max((
          line-height: 20px,
          font-size: 12px,
        ));
      }

      .week_btn {
        // line-height: 20px;
        color: #2e86ff;
        font-weight: bold;
        // font-size: 12px;
        // margin-left: 12px;
        @include adaptive-max((
          line-height: 20px,
          font-size: 12px,
          margin-left: 12px,
        ));

        &.disable {
          color: #dddddd;
          pointer-events: none;
        }
      }
    }
  }
}
