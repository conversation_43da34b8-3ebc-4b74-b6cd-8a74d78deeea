import React, { useEffect, useState } from "react";
import SummaryModal from "../summary-modal";
import classNames from "classnames";
import {
  editPlanSummary,
  getPlanSummary,
  getSummaryList,
  IPlanListItem,
  IPlanSummary,
  ISummaryResponse,
} from "../../apis";
import ProgressIconPng from "@/assets/image/self-learning-for-subject/mission-progress-icon.png";

import styles from "./index.module.scss";
import { useAPILevel } from "~/hooks";
import { Toast } from "antd-mobile";
import PageLoading from "~/components/FullScreenComponents/page-loading";
// import { sleep } from '~/utils/tools';
import { checkNextWeek } from "../../common";
import ProgressModal from "../progress-modal";
import { clickPv } from "~/utils/tool";

const formatPercent = (percent: number) => {
  return Math.min(100, Math.max(0, Math.round(percent * 1000) / 10));
};

const MissionProgress = (props: {
  percent: number;
  isRefreshing: boolean;
  currentPlan: IPlanListItem;
  showRefresh: boolean;
  onRefresh?: () => void;
  onClickAdd?: () => void;
  showAdd: boolean;
  total: number;
  done: number;
}) => {
  const {
    percent,
    isRefreshing,
    onRefresh,
    currentPlan,
    showRefresh,
    onClickAdd,
    showAdd,
    total,
    done,
  } = props;
  const [summaryOpen, setSummaryOpen] = useState(false);
  const [summaryInfo, setSummaryInfo] = useState<ISummaryResponse>();
  const [planSummary, setPlanSummary] = useState<IPlanSummary>();
  const [submitting, setSubmitting] = useState(false);
  const [showQuestion, setShowQuestion] = useState(false);
  const { upLevel, getLevelKey } = useAPILevel(2);
  const format = formatPercent(total > 0 ? done / total : 0);
  const showSummary = !!summaryInfo && !!planSummary;
  // 未来的 当周状态 变为灰色不可点
  const showDisableSummary = currentPlan.valid && !currentPlan.currentWeek;
  const showWord = `${done}/${total}`;

  const initSummaryInfo = async () => {
    try {
      const { data } = await getSummaryList();
      setSummaryInfo(data);
    } catch (e) {
      console.log("获取总结枚举异常", e);
    }
  };

  const initPlanSummary = async () => {
    try {
      const level = upLevel(1);
      setPlanSummary(undefined);
      const { data } = await getPlanSummary({
        startTimestamp: currentPlan.startTimestamp,
        endTimestamp: currentPlan.endTimestamp,
      });
      if (level !== getLevelKey(1)) return;
      setPlanSummary(data || { moodCode: "", outcomeCode: "" });
    } catch (e) {
      console.log("获取计划总结异常", e);
    }
  };

  const onSubmit = async (summary: IPlanSummary) => {
    const level = upLevel(2);
    try {
      setSubmitting(true);
      // await sleep(4000);
      await editPlanSummary({
        startTimestamp: currentPlan.startTimestamp,
        endTimestamp: currentPlan.endTimestamp,
        ...summary,
        ignoreError: 1,
      });
      if (level !== getLevelKey(2)) return;
      setPlanSummary(summary);
      onCloseModal();
      setSubmitting(false);
      Toast.show({
        content: "提交成功",
      });
    } catch (e) {
      console.log("提交计划总结异常", e);
      if (level !== getLevelKey(2)) return;
      setSubmitting(false);
      if (!checkNextWeek(e?.code)) {
        Toast.show({
          content: "提交失败，请重试",
        });
      }
    }
  };

  const onOpenModal = () => {
    setSummaryOpen(true);
    clickPv("ewt_h5_study_course_week_study_weekStatus_set_entry_click");
  };

  const onCloseModal = () => {
    setSummaryOpen(false);
  };

  useEffect(() => {
    initSummaryInfo();
  }, []);

  useEffect(() => {
    setSubmitting(false);
    if (!showDisableSummary) {
      initPlanSummary();
    }
  }, [currentPlan]);

  return (
    <div className={styles.container}>
      <div className={styles.top_container}>
        <div className={styles.top}>
          <div className={styles.per_bg}>
            <div
              className={styles.per_bar}
              style={{ width: `${format}%` }}
            ></div>
          </div>
          <div className={styles.per_bar_top} style={{ width: `${format}%` }}>
            <img
              src={ProgressIconPng}
              className={styles.bar_icon}
              style={{ transform: `translate(${88 - 0.76 * format}% ,-50%)` }}
            />
          </div>
        </div>
        <div className={styles.question} onClick={() => setShowQuestion(true)}>
          <i className="iconfont iconwenhao1" />
        </div>
      </div>
      <div className={styles.bottom}>
        <div className={styles.bottom_left}>
          <div className={styles.word_icon}>
            <i className="iconfont iconxingzhuangjiehe" />
          </div>
          <div className={styles.word}>{total > 0 ? "已完成" : "无进度"}</div>
          {total > 0 && (
            <div key={showWord} className={styles.percent} data-text={showWord}>
              {showWord}
            </div>
          )}
          {showRefresh && (
            <div
              className={styles.refresh}
              style={{ cursor: isRefreshing ? "unset" : "pointer" }}
              onClick={() => {
                if (!isRefreshing) {
                  onRefresh?.();
                }
              }}
            >
              {!isRefreshing ? <span>刷新进度</span> : <span>正在刷新...</span>}
            </div>
          )}
        </div>
        <div className={styles.bottom_right}>
          {showAdd && (
            <div
              className={styles.add_btn}
              onClick={() => {
                clickPv("ewt_h5_study_course_week_study_add_resource_click", {
                  from: "添加",
                });
                onClickAdd?.();
              }}
            >
              添加内容
            </div>
          )}
          {(showSummary || showDisableSummary) && (
            <div
              className={classNames(styles.week_btn, {
                [styles.disable]: showDisableSummary,
              })}
              onClick={onOpenModal}
            >
              当周状态
            </div>
          )}
        </div>
      </div>
      {showSummary && (
        <SummaryModal
          open={summaryOpen}
          onClose={onCloseModal}
          baseInfo={summaryInfo}
          disable={!currentPlan.valid}
          currentSummary={planSummary}
          onSubmit={onSubmit}
          submitting={submitting}
        />
      )}
      <ProgressModal
        open={showQuestion}
        onClose={() => setShowQuestion(false)}
      />
      {submitting && (
        <PageLoading
          maskStyle={{ "--z-index": 1010 }}
          getContainer={() => document.body}
        />
      )}
    </div>
  );
};

export default MissionProgress;
