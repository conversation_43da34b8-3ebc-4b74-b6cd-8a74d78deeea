/**
 * lesson
 */

import React from "react";

import iconDuration from "@/assets/image/holiday-selection-h5/icon_duration.png";
import { cls } from "~/utils/tool";

import { HasAdd } from "./has-add";
import { AddRemotePlanBtn } from "./add-remote-plan-btn";
import { contentTypetoMap, EResourceType } from "../../common";
import { secondsToMinutes } from ".";
import { TNewResourceItem } from "../../apis";

import Style from "./index.module.scss";

interface ILesson {
  resourceType: EResourceType;
  resource: TNewResourceItem;
  handlePlayVideo: (values) => void;
  handleAddAndRemove: (cb: () => void) => void;
}

function Lesson(props: ILesson) {
  const { resource, resourceType, handlePlayVideo, handleAddAndRemove } = props;
  const { everAdd, currentAdd, lessonInfo } = resource;

  return (
    <>
      <div className={Style.lesson_info}>
        <p
          className={Style.lesson_name}
          onClick={() =>
            handlePlayVideo({
              courseId: lessonInfo?.packageId,
              lessonId: lessonInfo.lessonId,
            })
          }
        >
          {lessonInfo.lessonName}
        </p>
        <div className={Style.lesson_tag_time}>
          <div className={Style.left_flag_box}>
            <span className={Style.content_type}>
              {contentTypetoMap[resourceType]}
            </span>
            {!!lessonInfo.hasExercise && (
              <span className={cls([Style.content_type])}>含习题</span>
            )}
          </div>
          <span className={Style.duration}>
            <img className={Style.icon_width} src={iconDuration} />
            {secondsToMinutes(lessonInfo.playTime)}分钟
          </span>
        </div>
      </div>
      <div className={Style.action_panel}>
        <AddRemotePlanBtn
          hasAdded={currentAdd}
          addRemoveAction={handleAddAndRemove}
        />
        <HasAdd show={everAdd} />
      </div>
    </>
  );
}

export default Lesson;
