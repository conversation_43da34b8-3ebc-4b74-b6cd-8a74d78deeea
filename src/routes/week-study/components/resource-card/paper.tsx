/**
 * 试卷类型
 */

import React from "react";

import iconDuration from "@/assets/image/holiday-selection-h5/icon_duration.png";
import { HasAdd } from "./has-add";
import { AddRemotePlanBtn } from "./add-remote-plan-btn";
import { EResourceType, contentTypetoMap } from "../../common";
import { cls } from "~/utils/tool";

import { TNewResourceItem } from "../../apis";

import Style from "./index.module.scss";
// 将描述转换为分钟
const secondsToMinutes = (seconds: number) => {
  return seconds ? Math.round(seconds / 60) : 0;
};

interface IPaperItem {
  resourceType: EResourceType;
  resource: TNewResourceItem;
  handleJumpPaper: (values) => void;
  handleAddAndRemove: (cb: () => void) => void;
}
function PaperItem(props: IPaperItem) {
  const { resource, resourceType, handleAddAndRemove, handleJumpPaper } = props;
  const { everAdd, currentAdd, paperInfo } = resource;
  return (
    <>
      <div className={Style.paper_info}>
        <div className={Style.lesson_info}>
          <p
            className={cls([Style.lesson_name, Style.paper_name])}
            onClick={() => handleJumpPaper(paperInfo)}
          >
            {paperInfo.name}
          </p>
          <div
            className={Style.lesson_tag_time}
            style={{ alignItems: "center" }}
          >
            <div style={{ display: "flex", alignItems: "center" }}>
              <span className={Style.content_type}>
                {contentTypetoMap[resourceType]}
              </span>
            </div>
            <div className={Style.duration}>
              {!!paperInfo.duration && (
                <>
                  <img className={Style.icon_width} src={iconDuration} />
                  {paperInfo.duration}分钟
                </>
              )}
              {!!paperInfo.questionNum &&
                `${paperInfo.duration ? "/" : ""}${paperInfo.questionNum}题`}
            </div>
          </div>
        </div>
      </div>
      <div className={Style.action_panel}>
        <AddRemotePlanBtn
          hasAdded={currentAdd}
          addRemoveAction={handleAddAndRemove}
        />
        <HasAdd show={everAdd} />
      </div>
    </>
  );
}

export default PaperItem;
