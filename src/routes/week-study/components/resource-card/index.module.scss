@import "~@/styles/lib.scss";

.loadingContainer {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  .loading {
    display: flex;
  }
}

.grey {
  opacity: 0.3;
}

.package_image {
  // width: 260px;
  // height: 100px;
  // border-radius: 8px;
  // margin-bottom: 16px;
  @include adaptive-max((
    width: 260px,
    height: 100px,
    border-radius: 8px,
    margin-bottom: 16px,
  ));
}

.icon_width {
  // width: 16px;
  // height: 16px;
  @include adaptive-max((
    width: 16px,
    height: 16px,
  ));
}

.paper_line {
  width: 100%;
  // height: 1px;
  background: #eee;
  // margin: 8px 0;
  @include adaptive-max((
    height: 1px,
    margin: 8px 0,
  ));
}

.paper_icon {
  // width: 60px;
  // height: 60px;
  // margin-right: 12px;
  @include adaptive-max((
    width: 60px,
    height: 60px,
    margin-right: 12px,
  ));
}

.paper_info {
  display: flex;
  align-items: center;
  // margin-bottom: 5px;
  @include adaptive-max((
    margin-bottom: 5px,
  ));
}

.fm_info {
  display: flex;
  // margin-top: 16px;
  @include adaptive-max((
    margin-top: 16px,
  ));

  .fm_pic_box {
    position: relative;
  }

  .fm_img {
    // width: 60px;
    // height: 60px;
    // border-radius: 8px;
    cursor: pointer;
    // margin-right: 12px;
    flex-shrink: 0;
    @include adaptive-max((
      width: 60px,
      height: 60px,
      border-radius: 8px,
      margin-right: 12px,
    ));

    & + img {
      // width: 24px;
      // height: 24px;
      position: absolute;
      // right: 16px;
      // bottom: 15px;
      @include adaptive-max((
        width: 24px,
        height: 24px,
        right: 16px,
        bottom: 15px,
      ));
    }
  }

  .play-icon {
    position: absolute;
    // top: 60px;
    transform: translateY(-100%);
    @include adaptive-max((
      top: 60px,
    ));
  }

  .lesson_name {
    margin-left: 0 !important;
  }

  .duration {
    // font-size: 12px;
    color: #666;
    display: flex;
    align-items: center;
    @include adaptive-max((
      font-size: 12px,
    ));

    &.fm_duration {
      img {
        // margin-right: 3px;
        @include adaptive-max((
          margin-right: 3px,
        ));
      }
    }
  }
}

.paper_popover {
  right: 0;
  transform: none !important;
  bottom: 90% !important;
}

.package_name {
  background: #edf4ff;
  // border-radius: 4px;
  // margin: 12px 6px;
  // padding: 6px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  @include adaptive-max((
    border-radius: 4px,
    margin: 12px 6px,
    padding: 6px,
  ));

  .name_info {
    // max-width: 182px;
    font-weight: 600;
    // font-size: 14px;
    color: #333333;
    @include adaptive-max((
      max-width: 182px,
      font-size: 14px,
    ));
  }

  .lesson_num {
    font-weight: 600;
    // font-size: 12px;
    color: #666666;
    @include adaptive-max((
      font-size: 12px,
    ));
  }
}

.lesson_item {
  // margin: 0 12px 0px;
  // border-bottom: 1px solid #eeeeee;
  @include adaptive-max((
    margin: 0 12px 0px,
    border-bottom: 1px solid #eeeeee,
  ));

  &.fm_padding {
    margin-bottom: 0;
    border-bottom: 0 !important;
  }

  &.paper_bg {
    // background-size: 100px;
    // margin: 0px 6px 6px;
    // padding: 6px 6px 12px;
    @include adaptive-max((
      background-size: 100px,
      margin: 0px 6px 6px,
      padding: 6px 6px 12px,
    ));

    .action_panel {
      padding-bottom: 0;
    }
  }

  &:not(:last-child) {
    // border-bottom: 1px solid #eeeeee;
    @include adaptive-max((
      border-bottom: 1px solid #eeeeee,
    ));
  }

  .lesson_info {
    width: 100%;

    .lesson_name {
      font-weight: 400;
      // font-size: 14px;
      color: #333333;
      // margin-bottom: 12px;
      @include adaptive-max((
        font-size: 14px,
        margin-bottom: 12px,
      ));
    }

    .paper_name {
      font-weight: bold;
    }
  }

  .lesson_tag_time {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    // margin: 12px 0;
    @include adaptive-max((
      margin: 12px 0,
    ));

    .left_flag_box {
      display: flex;
      align-items: center;
      justify-content: start;
    }

    .learn_flag {
      // border-radius: 2px;
      // border: 1px solid #ffd2b0;
      color: #ff7f21;
      // font-size: 12px;
      // margin-left: 8px;
      // padding: 1px 3px;
      @include adaptive-max((
        border-radius: 2px,
        border: 1px solid #ffd2b0,
        font-size: 12px,
        margin-left: 8px,
        padding: 1px 3px,
      ));
    }

    .duration {
      font-weight: 400;
      // font-size: 12px;
      color: #666666;
      @include adaptive-max((
        font-size: 12px,
      ));

      img {
        // margin: -3px 4px 0 0;
        vertical-align: middle;
        @include adaptive-max((
          margin: -3px 4px 0 0,
        ));
      }
    }
  }

  .action_panel {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    display: flex;
    flex-direction: row-reverse;
    // padding-bottom: 14px;
    // padding-top: 4px;
    @include adaptive-max((
      padding-bottom: 14px,
      padding-top: 4px,
    ));

    .lesson_btn {
      display: inline-block;
      // width: 113px;
      // height: 28px;
      // line-height: 28px;
      background: #2e86ff;
      // border-radius: 16px;
      text-align: center;
      font-weight: 500;
      // font-size: 14px;
      color: #ffffff;
      cursor: pointer;
      position: relative;
      @include adaptive-max((
        width: 113px,
        height: 28px,
        line-height: 28px,
        border-radius: 16px,
        font-size: 14px,
      ));

      &:not(:first-child) {
        // margin-left: 12px;
        @include adaptive-max((
          margin-left: 12px,
        ));
      }

      & > img {
        display: inline-block;
        // margin: -2px 4px 0 0;
        // width: 16px;
        // height: 16px;
        vertical-align: middle;
        @include adaptive-max((
          margin: -2px 4px 0 0,
          width: 16px,
          height: 16px,
        ));
      }

      &.full_btn {
        width: 100%;
      }
    }

    .paper_test_btn {
      width: 100%;
      position: relative;
    }
  }
}

@keyframes translateBottom {
  from {
    transform: translateY(0);
  }
  to {
    // transform: translateY(5px);
    @include adaptive-max((
      transform: translateY(5px),
    ));
  }
}

.has-add {
  // width: 56px;
  // height: 18px;
  // margin-right: 12px;
  // border-radius: 2px;
  // font-size: 12px;
  color: #ff7f21;
  @include adaptive-max((
    width: 56px,
    height: 18px,
    margin-right: 12px,
    border-radius: 2px,
    font-size: 12px,
  ));
}

.add-remove-plan-btn {
  // width: 124px;
  // height: 28px;
  // border-radius: 14px;
  font-weight: 500;
  // font-size: 14px;
  text-align: center;
  cursor: pointer;
  // line-height: 26px;
  // border: 1px solid #2e86ff;
  // box-shadow: 2px 4px 10px 0 #6b78841a;
  @include adaptive-max((
    width: 124px,
    height: 28px,
    border-radius: 14px,
    font-size: 14px,
    line-height: 26px,
    border: 1px solid #2e86ff,
    box-shadow: 2px 4px 10px 0 #6b78841a,
  ));
}

.remove-plan {
  color: #ffffff;
  background-color: #2e86ff;
}

.add-plan {
  color: #2e86ff;
  background-color: #fff;
}

.content_type {
  // height: 18px;
  // margin-right: 4px;
  font-weight: 400;
  // font-size: 12px;
  display: inline-block;
  // line-height: 10px;
  // padding: 3px 4px;
  background: #eaf6ff;
  // border: 1px solid #c5ddff;
  // border-radius: 2px;
  color: #2e86ff;
  @include adaptive-max((
    height: 18px,
    margin-right: 4px,
    font-size: 12px,
    line-height: 10px,
    padding: 3px 4px,
    border: 1px solid #c5ddff,
    border-radius: 2px,
  ));
}

.fm-action {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: row-reverse;
  // margin-top: 5px;
  @include adaptive-max((
    margin-top: 5px,
  ));
}
