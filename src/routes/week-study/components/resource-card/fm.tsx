/**
 * FM
 */

import React from "react";

import iconDuration from "@/assets/image/holiday-selection-h5/icon_duration.png";
import { cls } from "~/utils/tool";
import PlayPng from "@/assets/image/play.png";

import { HasAdd } from "./has-add";
import { AddRemotePlanBtn } from "./add-remote-plan-btn";
import { contentTypetoMap } from "../../common";
import { secondsToMinutes } from ".";
import { TNewResourceItem } from "../../apis";

import Style from "./index.module.scss";

interface IFM {
  resource: TNewResourceItem;
  handleFmClick: (id: string) => void;
  handleAddAndRemove: (cb: () => void) => void;
}

function FM(props: IFM) {
  const { resource, handleFmClick, handleAddAndRemove } = props;
  const { everAdd, currentAdd, contentType, fmInfo } = resource;
  return (
    <div>
      <div className={Style.fm_info}>
        <div
          className={Style.fm_pic_box}
          onClick={() => handleFmClick(fmInfo?.id)}
        >
          <img src={fmInfo?.picture} className={Style.fm_img} />
          <img className={Style["play-icon"]} src={PlayPng} alt="" />
        </div>
        <div className={Style.lesson_info}>
          <p
            className={Style.lesson_name}
            style={{ marginLeft: 10, cursor: "pointer" }}
            onClick={() => handleFmClick(fmInfo?.id)}
          >
            {fmInfo?.title}
          </p>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <div style={{ display: "flex", alignItems: "center" }}>
              <span className={Style.content_type}>
                {contentTypetoMap[contentType]}
              </span>
            </div>
            <span className={cls([Style.duration, Style.fm_duration])}>
              <img className={Style.icon_width} src={iconDuration} />
              {secondsToMinutes(fmInfo?.playTime)}分钟
            </span>
          </div>
        </div>
      </div>
      <div className={Style["fm-action"]}>
        <AddRemotePlanBtn
          hasAdded={currentAdd}
          addRemoveAction={handleAddAndRemove}
        />
        <HasAdd show={everAdd} />
      </div>
    </div>
  );
}

export default FM;
