import * as React from "react";
import FM from "./fm";
import Lesson from "./lesson";
import { EResourceType } from "../../common";
import { cls } from "~/utils/tool";
import Paper from "./paper";

import { TNewResourceItem } from "../../apis";

import Style from "./index.module.scss";

export interface IResourceCard {
  resource: TNewResourceItem;
  bookName: string;
  handlePlayVideo: (values) => void;
  handleJumpPaper: (values) => void;
  handleFmClick: (id: string) => void;
  handleAddAndRemove: (v: {
    setLoading: () => void;
    item;
    isAdd: boolean;
    contentType: EResourceType;
    packageId;
  }) => void;
}

export const selfLearningFlagColor = {
  "2": Style.red_flag,
  "3": Style.blue_flag,
  "4": Style.green_flag,
  "5": Style.orange_flag,
};

// 将描述转换为分钟
export const secondsToMinutes = (seconds: number) => {
  return seconds ? Math.round(seconds / 60) : 0;
};

export interface IResourceItem {
  resource: TNewResourceItem;
  resourceType: EResourceType;
  bookName?: string;
  packageId?: string | number;
  handlePlayVideo: (values) => void;
  handleJumpPaper: (values) => void;
  handleFmClick: (id: number) => void;
  handleAddAndRemove: (cb: () => void) => void;
}

const ResourceItem: React.FC<IResourceItem> = (props) => {
  const {
    resource,
    handlePlayVideo,
    handleJumpPaper,
    handleFmClick,
    handleAddAndRemove,
  } = props;
  const { contentType } = resource;
  return (
    <div
      className={cls([
        Style.lesson_item,
        contentType === EResourceType.paper && Style.paper_bg,
        contentType === EResourceType.fm && Style.fm_padding,
      ])}
    >
      {contentType === EResourceType.paper ? (
        <Paper
          resource={resource}
          resourceType={contentType}
          handleJumpPaper={handleJumpPaper}
          handleAddAndRemove={handleAddAndRemove}
        />
      ) : contentType === EResourceType.fm ? (
        <FM
          resource={resource}
          handleFmClick={() => handleFmClick(resource.fmInfo?.id)}
          handleAddAndRemove={handleAddAndRemove}
        />
      ) : (
        <Lesson
          resourceType={contentType}
          resource={resource}
          handlePlayVideo={() => {
            handlePlayVideo({
              courseId: resource.lessonInfo?.packageId,
              lessonId: resource.lessonInfo?.lessonId,
            });
          }}
          handleAddAndRemove={handleAddAndRemove}
        />
      )}
    </div>
  );
};

export const ResourceCard: React.FC<IResourceCard> = (props) => {
  const {
    resource,
    handlePlayVideo,
    handleJumpPaper,
    handleFmClick,
    handleAddAndRemove,
  } = props;

  const { contentType } = resource;

  return (
    <ResourceItem
      resource={resource}
      handlePlayVideo={handlePlayVideo}
      handleJumpPaper={handleJumpPaper}
      handleFmClick={handleFmClick}
      handleAddAndRemove={(cb: () => void) => {
        handleAddAndRemove({
          setLoading: cb,
          item: resource,
          isAdd: !resource.currentAdd,
          resourceType: contentType,
          packageId: resource.lessonInfo?.packageId,
        });
      }}
    />
  );
};
