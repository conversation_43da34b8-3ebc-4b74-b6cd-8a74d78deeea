/** 加入｜移除计划 */

import React from 'react';
import { SpinLoading } from 'antd-mobile';
import clx from 'classnames';

import Style from './index.module.scss';

interface IAddRemotePlanBtn {
  hasAdded: boolean;
  addRemoveAction: (cb: () => void) => void;
}

export const AddRemotePlanBtn: React.FC<IAddRemotePlanBtn> = (props) => {
  const { hasAdded, addRemoveAction } = props;
  const [loading, setLoading] = React.useState<boolean>();
  return (
    <div style={{ position: 'relative' }}>
      <div
        className={clx(
          Style['add-remove-plan-btn'],
          hasAdded ? Style['remove-plan'] : Style['add-plan'],
          loading && Style['grey'],
        )}
        onClick={() => {
          addRemoveAction(() => setLoading((pre) => !pre));
        }}
      >
        <i
          className={
            hasAdded ? 'iconfont iconjianjihua' : 'iconfont iconjiajihua'
          }
          style={{ marginRight: 4 }}
        ></i>
        {hasAdded ? '移出当前计划' : '加入当前计划'}
      </div>
      {!!loading && (
        <div className={Style.loadingContainer}>
          <SpinLoading style={{ '--size': '12px' }} className={Style.loading} />
        </div>
      )}
    </div>
  );
};
