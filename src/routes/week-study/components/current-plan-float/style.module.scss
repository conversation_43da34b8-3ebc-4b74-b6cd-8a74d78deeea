@import "~@/styles/lib.scss";

.currentPlanFloat {
  // box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.1);
  z-index: 7;
  background-color: #fff;
  position: fixed;
  width: 100vw;
  // bottom: 0px;
  // padding: 8px 10px 8px 8px;
  padding-bottom: calc(8px + constant(safe-area-inset-bottom));
  padding-bottom: calc(8px + env(safe-area-inset-bottom));
  @include adaptive-max((
    box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.1),
    bottom: 0px,
    padding: 8px 10px 8px 8px,
  ));
  .content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left {
      flex: 0 0 auto;
      &-top {
        display: flex;
        align-items: center;
      }
      .tip {
        // margin-right: 8px;
        font-weight: bold;
        // font-size: 12px;
        color: #333333;
        @include adaptive-max((
          margin-right: 8px,
          font-size: 12px,
        ));
      }
      .planInfo {
        display: flex;
        font-weight: bold;
        // font-size: 14px;
        color: #2E86FF;
        align-items: center;
        @include adaptive-max((
          font-size: 14px,
        ));
        .num {
          color: #666666;
          // margin-right: 8px;
          // font-size: 12px;
          @include adaptive-max((
            margin-right: 8px,
            font-size: 12px,
          ));

          & > span {
            // margin-left: 3px;
            @include adaptive-max((
              margin-left: 3px,
            ));
          }
        }
        & > span {
          // margin-right: 8px;
          @include adaptive-max((
            margin-right: 8px,
          ));
        }
      }
    }
    .right {
      .action {
        // padding: 0 16px;
        // height: 40px;
        // border-radius: 28px;
        // line-height: 40px;
        // flex: 0 0 128px;
        cursor: pointer;

        text-align: center;
        background: #2E86FF;
        font-weight: bold;
        // font-size: 16px;
        color: #FFFFFF;
        @include adaptive-max((
          padding: 0 16px,
          height: 40px,
          border-radius: 28px,
          line-height: 40px,
          flex: 0 0 128px,
          font-size: 16px,
        ));
      }
      .disable {
        cursor: not-allowed;
        background: rgba(46, 134, 255, 0.5);
      }
    }
  }
}