/** 底部悬浮 */

import React, { useMemo } from 'react';
import cls from 'classnames';

import { formDate } from '~/utils/utils';
import { ChangePlanModal } from '../change-plan-modal';
import { IPlanInfo, IPlanListItem } from '../../apis';

import Style from './style.module.scss';
interface ICurrentPlanFloat {
  planDetailInfo: IPlanInfo;
  planList: IPlanListItem[];
  currentPlan: IPlanListItem;
  changePlan: (item: IPlanListItem) => void;
  toLearning: () => void;
}
const CurrentPlanFloat: React.FC<ICurrentPlanFloat> = (props) => {
  const { currentPlan, planList, planDetailInfo, changePlan, toLearning } =
    props;
  // 过滤出有效计划
  const validPlans = useMemo(() => {
    return (planList || []).filter((item) => item.valid);
  }, [planList]);

  return (
    <div className={Style.currentPlanFloat}>
      <div className={Style.content}>
        <div className={Style.left}>
          <div className={Style['left-top']}>
            <div className={Style.tip}>当前所选计划</div>
            {!!validPlans?.length && (
              <ChangePlanModal
                onSelect={changePlan}
                value={currentPlan}
                data={validPlans}
              />
            )}
          </div>
          <div className={Style.planInfo}>
            <span>
              {currentPlan.currentWeek
                ? '本周计划'
                : `周计划${currentPlan.index}`}
            </span>
            <span>{`${formDate(currentPlan.startTimestamp, 'MM.DD')}-${formDate(currentPlan.endTimestamp, 'MM.DD')}`}</span>
            <div className={Style.num}>
              已加
              <span style={{ color: '#2E86FF' }}>
                {planDetailInfo.totalCount || 0}个{' '}
              </span>
            </div>
          </div>
        </div>
        <div className={Style.right}>
          <div
            className={cls(
              Style.action,
              !planDetailInfo.totalCount && Style.disable,
            )}
            onClick={() => {
              if (!planDetailInfo.totalCount) {
                return;
              }
              toLearning();
            }}
          >
            立即前往学习
          </div>
        </div>
      </div>
    </div>
  );
};

export default React.memo(CurrentPlanFloat);
