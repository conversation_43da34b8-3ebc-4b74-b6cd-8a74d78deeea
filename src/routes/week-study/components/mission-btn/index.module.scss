@import "~@/styles/lib.scss";

.container {
  // height: 24px;
  background: #ffffff;
  // border: 1px solid #021e66;
  // box-shadow: 2px 2px 0 0 rgba(2, 30, 102, 0.15);
  // border-radius: 12px;
  // padding-left: 8px;
  // padding-right: 5px;
  cursor: pointer;
  display: flex;
  align-items: center;
  color: #021e66;
  @include adaptive-max((
    height: 24px,
    border: 1px solid #021e66,
    box-shadow: 2px 2px 0 0 rgba(2, 30, 102, 0.15),
    border-radius: 12px,
    padding-left: 8px,
    padding-right: 5px,
  ));

  .word {
    // font-size: 14px;
    font-weight: bold;
    // line-height: 22px;
    @include adaptive-max((
      font-size: 14px,
      line-height: 22px,
    ));

    span {
      // font-size: 12px;
      font-weight: normal;
      @include adaptive-max((
        font-size: 12px,
      ));
    }
  }

  .percent {
    // font-size: 12px;
    // line-height: 22px;
    // margin-left: 2px;
    @include adaptive-max((
      font-size: 12px,
      line-height: 22px,
      margin-left: 2px,
    ));
  }

  .done_icon {
    // width: 14px;
    // height: 14px;
    // border-radius: 50%;
    // border: 1px solid rgba(255, 255, 255, 0.45);
    // margin-left: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    @include adaptive-max((
      width: 14px,
      height: 14px,
      border-radius: 50%,
      border: 1px solid rgba(255, 255, 255, 0.45),
      margin-left: 4px,
    ));

    i {
      margin: 0;
      color: #fff;
    }
  }

  i {
    // font-size: 8px;
    color: rgba(2, 30, 102, 0.45);
    // margin-left: 4px;
    @include adaptive-max((
      font-size: 8px,
      margin-left: 4px,
    ));
  }

  &.done {
    background: #20a300;
    color: #fff;
  }
}
