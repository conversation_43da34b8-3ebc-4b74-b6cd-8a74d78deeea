// 头部的右边区域
import React from "react";

import { clickPv, getToken, openWebView } from "@/utils/tool";

import { ETAB } from "../tabs";
import { getPreviousWeekDate } from "../../common";
import { IPlanListItem } from "../../apis";

import styles from "../../home/<USER>/index.module.scss";

interface IHeaderRight {
  tabId: ETAB;
  planList?: IPlanListItem[];
  openEdition: () => void;
}

const HeaderRight: React.FC<IHeaderRight> = (props) => {
  const { tabId, planList, openEdition } = props;
  return tabId === ETAB.selfStudySquare ? (
    <div
      className={styles["content-right"]}
      onClick={() => {
        openEdition();
      }}
    >
      切换教材
      <i className="iconfont iconqiehuan" style={{ color: "#2E86FF" }} />
    </div>
  ) : (
    <span
      className={styles.switchBtn}
      onClick={async () => {
        try {
          clickPv("ewt_h5_study_course_week_study_report_entry_click");
          const token = await getToken();
          if (!token) {
            return;
          }
          const studentId = token.split("-")[0];
          const reportStartData = getPreviousWeekDate(planList || []);
          openWebView(
            `${window.location.origin}/reports/ordinary/?preview=true&seqNo=BOoiD15&studentId=${studentId}&bizdate=${reportStartData}&viewport=mobile`,
          );
        } catch (err) {
          console.error(err);
        }
      }}
    >
      智学报告
    </span>
  );
};

export default HeaderRight;
