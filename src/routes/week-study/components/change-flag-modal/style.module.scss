@import "~@/styles/lib.scss";

.change-flag-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  // top: 87px;
  position: absolute;
  // padding: 0 12px;
  background-color: #FFFFFF;
  // width: 343px;
  // height: 30px;
  left: 50%;
  transform: translateX(-50%);
  // border-radius: 8px;
  font-weight: bold;
  align-items: center;
  // font-size: 18px;
  color: #2E86FF;
  @include adaptive-max((
    top: 87px,
    padding: 0 12px,
    width: 343px,
    height: 30px,
    border-radius: 8px,
    font-size: 18px,
  ));
}
.content {
  text-align: center;
  background: rgba(255, 255, 255, 0.19);
  // border-radius: 8px;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  @include adaptive-max((
    border-radius: 8px,
  ));
}

.icon-container {
  // width: 25px;
  // height: 20px;
  display: flex;
  // padding-left: 5px;
  flex: 0 0 auto;
  align-items: center;
  @include adaptive-max((
    width: 25px,
    height: 20px,
    padding-left: 5px,
  ));
}

.edit-icon {
  // font-size: 12px !important;
  color: rgba(0, 0, 0, 0.45);
  @include adaptive-max((
    font-size: 12px !important,
  ));
}

.change-flag-modal {
  :global {
    .ant-modal-content, .ant-modal-header {
      // border-radius: 16px;
      @include adaptive-max((
        border-radius: 16px,
      ));
    }
    .ant-modal-header {
      // height: 66px;
      padding: 0;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      // font-size: 18px;
      color: #021E66;
      @include adaptive-max((
        height: 66px,
        font-size: 18px,
      ));
    }
    .ant-modal-title {
      text-align: center;
    }

    .ant-modal-body {
      padding-top: 0;
    }
  }
}

.flag-arrow {
  // width: 10px;
  // height: 7px;
  // left: 69px;
  position: absolute;
  // top: 30px;
  @include adaptive-max((
    width: 10px,
    height: 7px,
    left: 69px,
    top: 30px,
  ));
}