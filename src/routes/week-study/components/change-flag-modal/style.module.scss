.change-flag-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  top: 87px;
  position: absolute;
  padding: 0 12px;
  background-color: #FFFFFF;
  width: 343px;
  height: 30px;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 8px;
  font-weight: bold;
  align-items: center;
  font-size: 18px;
  color: #2E86FF;
}
.content {
  text-align: center;
  background: rgba(255, 255, 255, 0.19);
  border-radius: 8px;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.icon-container {
  width: 25px;
  height: 20px;
  display: flex;
  padding-left: 5px;
  flex: 0 0 auto;
  align-items: center;
}

.edit-icon {
  font-size: 12px !important;
  color: rgba(0, 0, 0, 0.45);
}

.change-flag-modal {
  :global {
    .ant-modal-content, .ant-modal-header {
      border-radius: 16px;
    }
    .ant-modal-header {
      height: 66px;
      padding: 0;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 18px;
      color: #021E66;
    }
    .ant-modal-title {
      text-align: center;
    }

    .ant-modal-body {
      padding-top: 0;
    }
  }
}

.flag-arrow {
  width: 10px;
  height: 7px;
  left: 69px;
  position: absolute;
  top: 30px;
}