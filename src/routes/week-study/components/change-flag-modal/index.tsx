import React from 'react';

import ModalSelectList from '~/components/select-list/modal-select-list';
import flagArrowPNG from '~/assets/image/self-learning-for-subject/flag-arrow2.png';

import {
  getUserFlag,
  setUserFlag as setUserFlagApi,
  getFlagList as getFlagListApi,
  IFlagItem,
} from '../../apis';

import Style from './style.module.scss';
import { Toast } from 'antd-mobile';
import { clickPv } from '~/utils/tool';

interface IChangeFlagModal {}

export const ChangeFlagModal: React.FC<IChangeFlagModal> = () => {
  const [vis, setVis] = React.useState<boolean>();
  function changeVis() {
    setVis((pre) => !pre);
  }
  // 切换
  async function setFlag(item) {
    try {
      if (!item.code || item.code === userFlag?.code) {
        return;
      }
      const { data: res } = await setUserFlagApi({ code: item.code, ignoreError: 1 });
      if (res) {
        setUserFlag(item);
      }
    } catch (error) {
      Toast.show({ content: '设置目标失败，请重试!' });
      console.error(error);
    }
  }
  // 获取 flag list
  const [flagList, setFlagList] = React.useState<IFlagItem[]>();
  async function getFlagList() {
    try {
      const { data: res } = await getFlagListApi();
      if (res) {
        setFlagList(res);
      }
    } catch (error) {
      console.error(error);
    }
  }

  const [userFlag, setUserFlag] = React.useState<IFlagItem>();
  async function getFlag() {
    try {
      const { data: flag } = await getUserFlag();
      if (flag) {
        setUserFlag(flag);
      }
    } catch (error) {
      console.error(error);
    }
  }
  React.useEffect(() => {
    getFlagList();
    getFlag();
  }, []);
  return (
    <>
      {!!flagList?.length && (
        <div className={Style['change-flag-btn']}>
          <div className={Style.content}>{userFlag?.content || '设置flag'}</div>
          <div
            className={Style.iconContainer}
            onClick={() => {
              changeVis();
              clickPv('ewt_h5_study_course_week_study_flag_set_entry_click');
            }}
          >
            <i className={`iconfont iconbianji ${Style.editIcon}`} />
          </div>
          <img className={Style['flag-arrow']} src={flagArrowPNG} alt="" />
        </div>
      )}
      <ModalSelectList
        title="切换flag"
        className={Style['change-flag-modal']}
        data={flagList || []}
        visible={vis}
        onClose={changeVis}
        onSelect={(item) => {
          setFlag(item);
          changeVis();
        }}
        renderItem={(item: IFlagItem) => {
          return item.content;
        }}
        checkIfSelect={(item: IFlagItem) => item.code === userFlag?.code}
      />
    </>
  );
};
