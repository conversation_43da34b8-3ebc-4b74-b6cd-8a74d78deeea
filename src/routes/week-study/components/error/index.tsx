import * as React from "react";
import classnames from "classnames";

import { openUrlInWebView } from "@/utils/tool";
import defaultErrorJPG from "~/assets/common/error.png";

import Style from "./style.module.scss";

interface IError {
  className?: string;
  imgUrl?: string;
  tip?: string;
  children?: React.ReactNode;
  onClick: () => void;
}

export const Error: React.FC<IError> = (props) => {
  const { onClick, imgUrl, tip, className } = props;
  return (
    <div className={classnames(Style.error, className)}>
      <img className={Style.img} src={imgUrl || defaultErrorJPG} />
      <div>{tip || "啊哦，遇到问题了，请重试～"}</div>
      <div className={Style["btns"]}>
        <div className={Style["refresh-btn"]} onClick={onClick}>
          重试
        </div>
        <div
          className={Style["network-check"]}
          onClick={() => {
            openUrlInWebView("https://web.ewt360.com/web-detector");
          }}
        >
          网络检测
        </div>
      </div>
    </div>
  );
};
