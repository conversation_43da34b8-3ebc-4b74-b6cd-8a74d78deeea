@import "~@/styles/lib.scss";

.error {
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  min-height: 50vh;
  // font-size: 12px;
  width: 100%;
  @include adaptive-max((
    font-size: 12px,
  ));

  .img {
    // width: 160px;
    // height: 160px;
    @include adaptive-max((
      width: 160px,
      height: 160px,
    ));
  }

  .btns {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .refresh-btn,
    .network-check {
      // width: 60px;
      // border-radius: 14px;
      // font-size: 14px;
      text-align: center;
      // margin-top: 10px;
      // line-height: 30px;
      font-weight: bold;
      color: #fff;
      // height: 30px;
      cursor: pointer;
      background-color: #2e86ff;
      @include adaptive-max((
        width: 60px,
        border-radius: 14px,
        font-size: 14px,
        margin-top: 10px,
        line-height: 30px,
        height: 30px,
      ));
    }

    .network-check {
      // width: 100px;
      // margin-left: 10px;
      @include adaptive-max((
        width: 100px,
        margin-left: 10px,
      ));
    }
  }
}
