/**
 * 课后习题已经提交，但未自批
 */

import React from 'react';

import Popup, { IPopup } from '~/components/popup';
import Button, { EType } from '~/components/button/blue-white-button';

import styles from './style.module.scss';

interface IUnSelfEditingAiTipModal extends IPopup {
  onClose: () => void;
  toSelfEdit: () => void;
}

const UnSelfEditingAiTipModal: React.FC<IUnSelfEditingAiTipModal> = (props) => {
  const { onClose, toSelfEdit } = props;
  return (
    <Popup onClose={onClose} {...props}>
      <div className={styles['content']}>
        当你完成课后习题，AI会自动分析错题类型，生成专属「同类训练题包」，帮你精准攻克薄弱环节！现在去自批解锁更多错题类型～
      </div>
      <div className={styles['footer']}>
        <Button
          className={styles['button']}
          onClick={toSelfEdit}
          type={EType.BLUE}
          text="先去自批"
        />
      </div>
    </Popup>
  );
};

export default UnSelfEditingAiTipModal;
