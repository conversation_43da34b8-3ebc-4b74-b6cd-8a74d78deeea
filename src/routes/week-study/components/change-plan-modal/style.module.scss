@import "~@/styles/lib.scss";

.change-plan-btn {
  cursor: pointer;
  // height: 24px;
  // line-height: 22px;
  text-align: center;
  // font-size: 12px;
  color: #2E86FF;
  display: flex;
  align-items: center;
  @include adaptive-max((
    height: 24px,
    line-height: 22px,
    font-size: 12px,
  ));
  i {
    // font-size: 12px;
    @include adaptive-max((
      font-size: 12px,
    ));
  }
}

.change-plan-modal {
  // width: 375px;
  :global {
    .ant-modal-content, .ant-modal-header {
      // border-radius: 16px;
      @include adaptive-max((
        border-radius: 16px,
      ));
    }
    .ant-modal-header {
      // height: 66px;
      padding: 0;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      // font-size: 18px;
      color: #021E66;
      @include adaptive-max((
        height: 66px,
        font-size: 18px,
      ));
    }
    .ant-modal-title {
      text-align: center;
    }

    .ant-modal-body {
      padding-top: 0;
    }
  }
}