import React from 'react';

import { formDate } from '~/utils/utils';
import ModalSelectList from '~/components/select-list/modal-select-list';

import { IPlanListItem } from '../../apis';
import Style from './style.module.scss';

interface IChangePlanModal {
  data: IPlanListItem[];
  value: IPlanListItem;
  onSelect: (item: IPlanListItem) => void;
}

export const ChangePlanModal: React.FC<IChangePlanModal> = (props) => {
  const { data, value, onSelect } = props;
  const [vis, setVis] = React.useState<boolean>();
  function changeVis() {
    setVis((pre) => !pre);
  }
  return (
    <div className={Style['change-plan-btn']}>
      <div onClick={changeVis}>
        切换
        <i style={{ marginLeft: '4px' }} className="iconfont iconqiehuan"></i>
      </div>
      <ModalSelectList
        title="切换计划"
        className={Style['change-plan-modal']}
        data={data}
        visible={vis}
        onClose={changeVis}
        onSelect={(item) => {
          onSelect(item as IPlanListItem);
          changeVis();
        }}
        renderItem={(item: IPlanListItem) => {
          return (
            <span>
              <span style={{ marginRight: 12 }}>
                {item.currentWeek ? '本周计划' : `周计划${item.index}`}
              </span>
              <span>
                {`${formDate(item.startTimestamp, 'MM.DD')}-${formDate(item.endTimestamp, 'MM.DD')}`}
              </span>
            </span>
          );
        }}
        checkIfSelect={(item: IPlanListItem) =>
          item.startTimestamp === value.startTimestamp &&
          item.endTimestamp === value.endTimestamp
        }
      />
    </div>
  );
};
