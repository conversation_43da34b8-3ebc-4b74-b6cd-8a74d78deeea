import React, { useEffect, useRef, useState } from "react";
import {
  getResourceList,
  IPlanListItem,
  IPlanStatusItem,
  removeResource,
  ResourceListResponse,
} from "../../apis";
import { useAIRecommendPaper } from "../../hooks";
import MissionItem from "../mission-item";
import { clickPv, getChannel, sleep } from "~/utils/tool";

import styles from "./index.module.scss";
import MissionProgress from "../mission-progress";
import PlanList from "../plan-list";
import { useAPILevel, usePageVisibility } from "~/hooks";
import MissionEmptyPng from "@/assets/image/self-learning-for-subject/mission-empty.png";
import EmptyPng from "@/assets/image/holiday-selection-h5/icon_no_data.png";
import { Toast } from "antd-mobile";
import PageLoading from "~/components/FullScreenComponents/page-loading";
import { checkNextWeek } from "../../common";
import { IUpdatePlanStatus } from "../../hooks/usePlanStatus";

const StudyClass = (props: {
  planStatusMap?: Record<string, IPlanStatusItem>;
  planList: IPlanListItem[];
  currentPlan: IPlanListItem;
  onChangePlan: (plan: IPlanListItem) => void;
  onClickAdd: () => void;
  updatePlanStatus: (newPlanStatus: IUpdatePlanStatus) => void;
}) => {
  const {
    planList,
    currentPlan,
    planStatusMap,
    onChangePlan,
    onClickAdd,
    updatePlanStatus,
  } = props;
  const [initInfo, setInitInfo] = useState({
    isLoading: true,
    isEmpty: false,
    isError: false,
  });
  const [missions, setMissions] = React.useState<ResourceListResponse>({
    progress: 0,
    resourceList: [],
  });
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { upLevel, getLevelKey } = useAPILevel(2);
  const deleteRef = useRef<Record<string, boolean>>({});
  const isVisible = usePageVisibility();
  // 为了异步删除任务时，处理删光的场景
  const needCheckEmptyRef = useRef(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const showRefresh = !initInfo.isLoading && !!currentPlan?.planId;

  // 初始化任务列表
  const initMissions = async () => {
    const level = upLevel(1);
    setInitInfo((info) => ({ ...info, isLoading: true }));
    setIsRefreshing(false);
    let next = {
      progress: 0,
      resourceList: [],
    };
    try {
      // await sleep(3000);
      // 有 planId 时，才能表示其有任务数据
      if (currentPlan.planId) {
        const channel = (await getChannel()) as string;
        if (level !== getLevelKey(1)) return;
        const { data } = await getResourceList(
          { planId: currentPlan.planId },
          { channel },
        );
        if (level !== getLevelKey(1)) return;
        next = data || next;
        // 更新计划状态
        updatePlanStatus({
          planId: currentPlan.planId,
          resources: next.resourceList || [],
        });
      }

      setMissions(next);
      setInitInfo({
        isLoading: false,
        isEmpty: !next.resourceList.length,
        isError: false,
      });
    } catch (e) {
      console.log("初始化任务失败", e);
      if (level !== getLevelKey(1)) return;
      setMissions(next);
      // 更新计划状态
      if (currentPlan.planId) {
        updatePlanStatus({
          planId: currentPlan.planId,
          resources: next.resourceList || [],
        });
      }
      setInitInfo({
        isLoading: false,
        isEmpty: true,
        isError: true,
      });
      Toast.show({
        content: e?.msg || "网络异常，请稍后重试",
      });
    }
  };

  const refreshMission = async (needToast = false) => {
    if (!showRefresh) {
      return;
    }
    const level = upLevel(2);
    setIsRefreshing(true);
    // 延时 2s 再刷新
    await sleep(2000);
    if (level !== getLevelKey(2)) return;
    try {
      const channel = (await getChannel()) as string;
      if (level !== getLevelKey(2)) return;
      const { data } = await getResourceList(
        { planId: currentPlan.planId },
        { channel },
      );
      if (level !== getLevelKey(2)) return;

      const next = data || {
        progress: 0,
        resourceList: [],
      };
      setMissions(next);
      updatePlanStatus({
        planId: currentPlan.planId,
        resources: next.resourceList || [],
      });
      setInitInfo({
        isLoading: false,
        isEmpty: !next.resourceList.length,
        isError: false,
      });
      setIsRefreshing(false);
      if (needToast) {
        Toast.show({
          content: "刷新成功",
        });
      }
    } catch (e) {
      console.log("刷新数据失败", e);
      if (level !== getLevelKey(2)) return;
      setIsRefreshing(false);
      Toast.show({
        content: "刷新失败，请稍后重试",
      });
    }
  };

  const onDeleteMission = async (recordId: string) => {
    try {
      if (deleteRef.current[recordId]) return;
      deleteRef.current[recordId] = true;
      setIsDeleting(true);
      // await sleep(3000);
      await removeResource({ recordId });
      delete deleteRef.current[recordId];
      setIsDeleting(false);
      Toast.show({
        content: "移出成功",
      });
      setMissions((missions) => {
        const newResourceList = missions.resourceList.filter(
          (mission) => mission.recordId !== recordId,
        );
        // 更新计划状态
        updatePlanStatus({
          planId: currentPlan.planId,
          resources: newResourceList,
        });
        const isEmpty = !newResourceList.length;
        needCheckEmptyRef.current = isEmpty;
        return {
          progress: isEmpty ? 0 : missions.progress,
          resourceList: newResourceList,
        };
      });
      refreshMission();
    } catch (e) {
      console.log("删除任务失败", e);
      delete deleteRef.current[recordId];
      setIsDeleting(false);
      Toast.show({
        content: "删除失败",
      });
      checkNextWeek(e?.code);
    }
  };

  // AI 推题
  const { isCreateAIPaper, createPaper, getAIPaperByResource, getAiPaperList } =
    useAIRecommendPaper({
      planId: currentPlan?.planId,
    });

  useEffect(() => {
    if (!showRefresh) {
      return;
    }
    if (isVisible) {
      refreshMission();
      getAiPaperList();
    }
  }, [isVisible]);

  useEffect(() => {
    if (currentPlan) {
      initMissions();
    }
    setIsRefreshing(false);
  }, [currentPlan]);

  useEffect(() => {
    if (needCheckEmptyRef.current) {
      const isEmpty = !missions.resourceList.length;
      setInitInfo((info) => ({ ...info, isEmpty }));
      needCheckEmptyRef.current = false;
    }
  }, [needCheckEmptyRef.current]);

  const missionCount = (missions.resourceList || []).length;
  const missionDoneCount = (missions.resourceList || []).filter(
    (mission) => mission.finishStatus,
  ).length;

  if (!currentPlan) {
    return null;
  }

  return (
    <div className={styles.container}>
      <div className={styles.top}>
        <div className={styles.top_main}>
          <PlanList
            planList={planList}
            planStatusMap={planStatusMap}
            currentPlan={currentPlan}
            onChange={(p) => onChangePlan(p)}
          />
          <MissionProgress
            percent={missions.progress}
            currentPlan={currentPlan}
            isRefreshing={isRefreshing}
            showRefresh={showRefresh}
            onRefresh={() => refreshMission(true)}
            showAdd={currentPlan?.valid}
            onClickAdd={onClickAdd}
            total={missionCount}
            done={missionDoneCount}
          />
        </div>
      </div>
      <div className={styles.content}>
        {initInfo.isError || initInfo.isEmpty ? (
          <div className={styles.error_container}>
            <img
              src={
                currentPlan.valid && !initInfo.isError
                  ? MissionEmptyPng
                  : EmptyPng
              }
            />
            <div className={styles.desc}>
              {initInfo.isError
                ? "网络异常，请稍后重试"
                : currentPlan.valid
                  ? "积跬步以至千里，快来构建你的学习蓝图吧"
                  : "过去的周计划无法修改，前往未来开启你的智学计划吧"}
            </div>
            {currentPlan.valid && !initInfo.isError && (
              <div
                className={styles.add}
                onClick={() => {
                  clickPv("ewt_h5_study_course_week_study_add_resource_click", {
                    from: "空态添加",
                  });
                  onClickAdd();
                }}
              >
                为当周添加内容
              </div>
            )}
          </div>
        ) : (
          <div className={styles.main}>
            {missions.resourceList.map((mission) => (
              <MissionItem
                key={mission.recordId}
                aiRecommendPaper={getAIPaperByResource(mission)}
                resourceItem={mission}
                disableDelete={!currentPlan.valid}
                currentPlan={currentPlan}
                onDelete={() => {
                  onDeleteMission(mission.recordId);
                }}
                toPractice={(aiRecommendPaper, reqData) => {
                  createPaper(aiRecommendPaper, {
                    ...reqData,
                    planId: currentPlan.planId,
                  });
                }}
              />
            ))}
          </div>
        )}
      </div>
      {(initInfo.isLoading || isDeleting || isCreateAIPaper) && <PageLoading />}
    </div>
  );
};

export default StudyClass;
