@import "~@/styles/lib.scss";

.container {
  .top {
    background-color: #fff;

    .top_main {
      width: 100vw;
      margin: 0 auto;
    }
  }

  .content {
    background-color: #f3f4f8;

    .error_container {
      min-height: 60vh;
      width: 100vw;
      display: flex;
      align-items: center;
      flex-direction: column;
      // padding-top: 40px;
      @include adaptive-max((
        padding-top: 40px,
      ));

      img {
        // width: 160px;
        // height: 160px;
        @include adaptive-max((
          width: 160px,
          height: 160px,
        ));
      }

      .desc {
        // font-size: 12px;
        color: #021e66;
        // line-height: 20px;
        // margin-top: 12px;
        @include adaptive-max((
          font-size: 12px,
          line-height: 20px,
          margin-top: 12px,
        ));
      }

      .add {
        // height: 28px;
        background: #2e86ff;
        // border-radius: 14px;
        // padding: 0 12px;
        font-weight: bold;
        // font-size: 14px;
        color: #ffffff;
        cursor: pointer;
        // line-height: 28px;
        // margin-top: 16px;
        @include adaptive-max((
          height: 28px,
          border-radius: 14px,
          padding: 0 12px,
          font-size: 14px,
          line-height: 28px,
          margin-top: 16px,
        ));
      }
    }

    .main {
      width: 100vw;
      min-height: 60vh;
      margin: 0 auto;
      // padding: 12px 8px 80px 12px;
      @include adaptive-max((
        padding: 12px 8px 80px 12px,
      ));
    }
  }
}
