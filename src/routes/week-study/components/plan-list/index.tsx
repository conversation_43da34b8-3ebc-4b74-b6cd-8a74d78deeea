import React, { useEffect, useRef } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';

// Import Swiper styles
import 'swiper/css';

import MissionDonePng from '@/assets/image/self-learning-for-subject/mission-done.png';
import styles from './index.module.scss';
import classNames from 'classnames';
import { IPlanListItem, IPlanStatusItem } from '../../apis';
import dayjs from 'dayjs';

const formatDay = (timestamp: string) => {
  return dayjs(+timestamp).format('MM.DD');
};

const PlanList = (props: {
  planStatusMap?: Record<string, IPlanStatusItem>;
  planList: IPlanListItem[];
  currentPlan: IPlanListItem;
  onChange: (plan: IPlanListItem) => void;
}) => {
  const { planList, currentPlan, planStatusMap, onChange } = props;
  const swiperRef = useRef<any>();
  const count = (planList || []).length;

  const onItemClick = (index: number, plan: IPlanListItem) => {
    swiperRef.current.slideTo(index);
    onChange?.(plan);
  };

  useEffect(() => {
    setTimeout(() => {
      if (swiperRef.current) {
        const index = planList.findIndex(
          (plan) => plan.startTimestamp === currentPlan.startTimestamp,
        );
        if (index > -1) {
          swiperRef.current.slideTo(index);
        }
      }
    }, 100);
  }, []);

  return (
    <div className={styles.container}>
      <Swiper
        onSwiper={(swr) => {
          swiperRef.current = swr;
        }}
        slidesPerView="auto"
        centeredSlides
        centeredSlidesBounds
        threshold={10}
      >
        {planList.map((plan, index) => (
          <SwiperSlide key={index} virtualIndex={index}>
            <div
              key={index}
              className={classNames(styles.item, {
                [styles.active]:
                  plan.startTimestamp === currentPlan.startTimestamp,
              })}
              onClick={() => onItemClick(index, plan)}
            >
              {!!planStatusMap?.[plan.planId]?.finishStatus && (
                <img src={MissionDonePng} className={styles.item_finish} />
              )}
              <div className={styles.item_title}>
                {plan.currentWeek ? '本周计划' : `周计划${plan.index || ''}`}
              </div>
              <div className={styles.item_desc}>
                {formatDay(plan.startTimestamp)}-{formatDay(plan.endTimestamp)}
              </div>
            </div>
          </SwiperSlide>
        ))}
        <SwiperSlide key={count} virtualIndex={count}>
          <div className={styles.more}>更远的事情未来再考虑吧~</div>
        </SwiperSlide>
      </Swiper>
    </div>
  );
};

export default PlanList;
