@import "~@/styles/lib.scss";

.container {
  width: 100%;
  // height: 68px;
  position: relative;
  overflow: hidden;
  @include adaptive-max((
    height: 68px,
  ));

  :global {
    .swiper {
      // height: 68px;
      // padding: 8px 0 0;
      @include adaptive-max((
        height: 68px,
        padding: 8px 0 0,
      ));

      .swiper-slide {
        width: auto !important;
      }
    }
  }

  .item {
    position: relative;
    // width: 90px;
    // height: 52px;
    background: #E2EEFF;
    // border-radius: 8px;
    // border: 1px solid #E2EEFF;
    color: rgba(2, 30, 102, 0.65);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    // margin-left: 8px;
    flex-shrink: 0;
    user-select: none;
    @include adaptive-max((
      width: 90px,
      height: 52px,
      border-radius: 8px,
      border: 1px solid #E2EEFF,
      margin-left: 8px,
    ));

    // 完成状态
    .item_finish {
      // top: -4px;
      // right: -4px;
      position: absolute;
      // width: 16px;
      // height: 16px;
      @include adaptive-max((
        top: -4px,
        right: -4px,
        width: 16px,
        height: 16px,
      ));
    }

    .item_title {
      font-weight: bold;
      // font-size: 16px;
      // line-height: 18px;
      @include adaptive-max((
        font-size: 16px,
        line-height: 18px,
      ));
    }

    .item_desc {
      // font-size: 12px;
      text-align: center;
      // line-height: 14px;
      // margin-top: 4px;
      @include adaptive-max((
        font-size: 12px,
        line-height: 14px,
        margin-top: 4px,
      ));
    }

    &.active {
      border-color: #021E66;
      background: #fff;
      // box-shadow: 2px 2px 0 0 rgba(2, 30, 102, 0.15);
      @include adaptive-max((
        box-shadow: 2px 2px 0 0 rgba(2, 30, 102, 0.15),
      ));

      .item_title {
        color: #021E66;
      }

      .item_desc {
        color: #2E86FF;
      }
    }
  }

  .more {
    // font-size: 12px;
    color: #999999;
    // line-height: 52px;
    flex-shrink: 0;
    // width: 150px;
    // margin-left: 16px;
    user-select: none;
    @include adaptive-max((
      font-size: 12px,
      line-height: 52px,
      width: 150px,
      margin-left: 16px,
    ));
  }

}
