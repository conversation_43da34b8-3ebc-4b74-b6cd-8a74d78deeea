.container {
  padding: 0 16px 16px;
  .title {
    font-weight: bold;
    font-size: 18px;
    color: #021e66;
    line-height: 18px;
  }
  .body{
    position: relative;
    margin-bottom: 26px;
    padding: 12px 0;
    .content{
      color: #333;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding-left: 16px;
      height: 82px; 
      background: #EEF1F6;
      border-radius: 8px;
      .title{
        font-weight: 600;
        font-size: 24px;
        line-height: 32px;
      }
      .hig{
        font-weight: 600;
        font-size: 14px;
        color: #2D86FE;
      }
      .desc {
        font-size: 14px;
        line-height: 22px;
        word-wrap: break-word;
        margin-top:4px;
      }
    }
    .bg{
      position: absolute;
      display: block;
      width: 100px;
      right: 0;
      top:0;
    }
  }

  .action{
    display: flex;
    .btn{
      flex: 1;
      margin-right: 15px;
      &:last-child{
        margin-right: 0;
      }
    }
  }
  
  .safe {
    width: 100px;
    height: constant(safe-area-inset-bottom);
    height: env(safe-area-inset-bottom);
  }
}

.modal {
  :global {
    .adm-popup-body {
      border-top-left-radius: 16px;
      border-top-right-radius: 16px;
    }
  }
}
