import React, { useState, useEffect, useRef } from 'react';
import dayjs from 'dayjs';
import Popup from '~/components/popup';
import Button, { EType, IButton } from '~/components/button/blue-white-button';
import { getPlanStatusList, IPlanStatusItem } from '../../apis';

import TpBg from '@/assets/image/self-learning-for-subject/tips.png';
import TpBg1 from '@/assets/image/self-learning-for-subject/tips1.png';

import styles from './index.module.scss';

import { getLocalStorage, setLocalStorage } from '~/utils/tool';

export enum TEYPE {
  current = 'current',
  prev = 'prev',
}

const TipsModal = (props: {
  open: boolean;
  type: TEYPE;
  state: any;
  onClose: () => void;
  onGo: () => void;
}) => {
  const { open, onClose, type, state, onGo } = props;

  const contentSource = {
    prev: {
      title: '同学~',
      desc: (
        <span>
          上周还有<span className={styles.hig}> {state.unfinishedNum}个 </span>
          任务在偷偷等你噢
        </span>
      ),
      bg: TpBg1,
      actions: [
        {
          text: '前往查看',
          type: EType.BLUE,
          onClick: onGo,
        },
      ],
    },
    current: {
      title: '等等~',
      desc: (
        <span>
          本周还有<span className={styles.hig}> {state.unfinishedNum}个 </span>
          任务没有做完呢
        </span>
      ),
      bg: TpBg,
      actions: [
        {
          text: '确认离开',
          type: EType.WHITE,
          onClick: () => {
            try {
              onClose();
              window.mstJsBridge.closeWebview();
            } catch (error) {
              console.error('关闭页面失败', error);
            }
          },
        },
        {
          text: '继续学习',
          type: EType.BLUE,
          onClick: () => {
            onClose();
          },
        },
      ],
    },
  };

  const current: {
    title?: JSX.Element | string;
    desc?: JSX.Element | string;
    bg?: string;
    actions?: IButton[];
  } = contentSource[type] || {};

  return (
    <Popup visible={open} title="温馨提示" onClose={onClose}>
      <div className={styles.container}>
        <div className={styles.body}>
          <div className={styles.content}>
            <div className={styles.title}>{current.title}</div>
            <div className={styles.desc}>{current.desc}</div>
          </div>
          <img className={styles.bg} src={current.bg} />
        </div>
        <div className={styles.action}>
          {current?.actions?.map((v, i) => (
            <Button
              className={styles.btn}
              key={i}
              text={v.text}
              type={v.type}
              onClick={v.onClick}
            />
          ))}
        </div>
      </div>
    </Popup>
  );
};

export const useTips = (props) => {
  const { userUniKey } = props || {};
  const planRef = useRef<{
    current?: any;
    pre?: any;
  }>({});
  planRef.current = props.plan;
  const [showTipsModal, setShowTipsModal] = useState<boolean>(false);
  const [type, setType] = useState<TEYPE>(TEYPE.current);

  const [currentPlan, setCurrentPlan] = useState<Partial<IPlanStatusItem>>({});
  const currentLoading = useRef<boolean>(false);
  const [prevPlan, setPrevPlan] = useState<Partial<IPlanStatusItem>>({});

  async function getPlanStatus(planIdList: string[]) {
    try {
      if (!planIdList.length) {
        return;
      }
      const { data: res } = await getPlanStatusList({
        planIdList,
      });
      return res;
    } catch (e) {
      console.error(e);
    }
  }
  const runCurrentPlan = async () => {
    if (currentLoading.current) return;
    if (currentPlan?.planId) return;
    if (!planRef.current.current?.planId) return;
    currentLoading.current = true;
    const res = await getPlanStatus([planRef.current.current?.planId]);
    const currentState = res?.find(
      (v) => v.planId === planRef.current.current?.planId,
    );
    setCurrentPlan(currentState);
    currentLoading.current = false;
    return currentState;
  };

  const runPrevPlan = async () => {
    if (prevPlan?.planId) return;
    if (!planRef.current.pre?.planId) return;
    const res = await getPlanStatus([planRef.current.pre?.planId]);
    const currentState = res?.find(
      (v) => v.planId === planRef.current.pre?.planId,
    );
    setPrevPlan(currentState);
    return currentState;
  };

  const toggle = (state: boolean) => {
    if (state) {
      setShowTipsModal(state);
      return;
    }
    setShowTipsModal(!showTipsModal);
  };
  /**
   * 1. 手动触发、2.本周已创建计划、3.localStorage 里没有本周的已检测标记
   */
  const checkCurrent = async () => {
    const currentTipsKey = `${userUniKey}_current`;
    const catchStorage: {
      checkTime: string;
    } = JSON.parse(getLocalStorage(currentTipsKey))?.value || {};
    const currentTime = dayjs().format('YYYY-MM-DD');
    const check = currentTime === catchStorage.checkTime;
    if (check) {
      return Promise.resolve(false);
    }
    const plan = await runCurrentPlan();
    if (plan?.unfinishedNum > 0) {
      setLocalStorage(currentTipsKey, {
        checkTime: dayjs().format('YYYY-MM-DD'),
      });
      setType(TEYPE.current);
      return Promise.resolve(true);
    }
    return Promise.resolve(false);
  };

  /**
   * 1.当前 tab 为智学室、2.上周创建过计划、3.localStorage 里没有上一周的已检测标记
  //  */
  const checkPrev = async () => {
    const prevTipsKey = `${userUniKey}_prev`;
    const catchStorage: {
      startTimestamp: string;
      endTimestamp: string;
    } = JSON.parse(getLocalStorage(prevTipsKey))?.value || {};
    const check =
      planRef.current.pre?.startTimestamp === catchStorage.startTimestamp &&
      planRef.current.pre?.endTimestamp === catchStorage.endTimestamp;
    if (check) {
      return Promise.resolve(false);
    }
    setLocalStorage(prevTipsKey, {
      startTimestamp: planRef.current.pre.startTimestamp,
      endTimestamp: planRef.current.pre.endTimestamp,
    });
    const plan = await runPrevPlan();
    if (plan?.unfinishedNum > 0) {
      setType(TEYPE.prev);
      return Promise.resolve(true);
    }
    return Promise.resolve(false);
  };

  useEffect(() => {
    if (!planRef.current.pre?.planId) return;
    checkPrev().then((res) => {
      if (res) {
        toggle(true);
      }
    });
  }, [planRef.current.pre?.planId]);

  const state = {
    current: currentPlan,
    prev: prevPlan,
  };
  return {
    showTipsModal,
    visible: showTipsModal,
    type,
    toggle,
    state: state[type] || {},
    run: {
      runCurrentPlan,
      runPrevPlan,
    },
    check: {
      checkCurrent,
      checkPrev,
    },
  };
};

export default TipsModal;
