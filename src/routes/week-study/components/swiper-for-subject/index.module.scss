@import "~@/styles/lib.scss";

.week-study-subject-swiper {
  background-color: #fff;
  // border-bottom: 2px solid #f3f4f8;
  @include adaptive-max((
    border-bottom: 2px solid #f3f4f8,
  ));
  :global {
    .swiper-slide {
      width: auto !important;
    }
  }
}

.menuItem {
  // border-radius: 6px;
  color: #434f59;
  // font-size: 18px;
  position: relative;
  // padding: 14px 16px;
  @include adaptive-max((
    border-radius: 6px,
    font-size: 18px,
    padding: 14px 16px,
  ));

  &.active {
    font-weight: 600;
    color: #2d86fe;

    &::after {
      content: " ";
      display: block;
      background: #2d86fe;
      width: 50%;
      // border-radius: 8px;
      height: 0.53vw;
      margin: 0 auto;
      @include adaptive-max((
        border-radius: 8px,
      ));
    }
  }

  .menuActiveImg {
    // width: 30px;
    // height: 26px;
    position: absolute;
    // right: -15px;
    bottom: 0;
    @include adaptive-max((
      width: 30px,
      height: 26px,
      right: -15px,
    ));
  }
}
