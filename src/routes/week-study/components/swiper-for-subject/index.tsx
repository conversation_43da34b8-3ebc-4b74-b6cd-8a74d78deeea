import React, { useEffect, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";

import { cls } from "~/utils/tool";

import { TSubjectItem } from "../../apis";

import styles from "./index.module.scss";

interface ISwiperForSubject {
  initialSlide?: number;
  list: any[];
  handleClick: (item: TSubjectItem) => void;
}

/**
 * 课程切换 swiper
 */
const SwiperForSubject: React.FC<ISwiperForSubject> = (props) => {
  const { initialSlide, list = [], handleClick } = props;
  const [swiperObj, setSwiperObj] = useState<{
    slideTo: (idx: number, s: number) => void;
  }>();

  let fixedIndex = list?.findIndex(
    (item: any) => item.subjectId === initialSlide,
  );
  fixedIndex = fixedIndex == -1 ? 0 : fixedIndex;

  useEffect(() => {
    if (swiperObj) {
      // 移动到指定位置
      swiperObj.slideTo(fixedIndex, 0);
    }
  }, [initialSlide]);

  return (
    <Swiper
      initialSlide={fixedIndex}
      slidesPerView={"auto"}
      spaceBetween={0}
      centeredSlides
      centeredSlidesBounds
      className={styles["week-study-subject-swiper"]}
      onSwiper={(swiper) => setSwiperObj(swiper)}
    >
      {list.map((item: TSubjectItem, index: number) => (
        <SwiperSlide key={item.subjectId}>
          <div
            className={cls([
              styles.menuItem,
              initialSlide === item.subjectId && styles.active,
            ])}
            onClick={() => handleClick(item)}
          >
            {item.subjectName}
          </div>
        </SwiperSlide>
      ))}
    </Swiper>
  );
};

export default SwiperForSubject;
