@import "~@/styles/lib.scss";

.headerContainer {
  display: flex;
  // height: 166px;
  @include adaptive-max((
    height: 166px,
  ));
  position: relative;
  justify-content: space-between;
  flex-shrink: 0;
  background: #2e86ff;
  &.noTabs {
    // height: 140px;
    @include adaptive-max((
      height: 140px,
    ));
  }
  &.single {
    // height: 87px;
    @include adaptive-max((
      height: 87px,
    ));
  }
  .self_logo {
    position: absolute;
    // margin-left: 12px;
    // top: 9px;
    // width: 116px;
    // height: 42px;
    // top: 32px;
    @include adaptive-max((
      margin-left: 12px,
      width: 116px,
      height: 42px,
      top: 32px,
    ));
  }
  .rightImg {
    // width: 206px;
    // height: 95px;
    // margin-left: -16px;
    @include adaptive-max((
      width: 206px,
      height: 95px,
      margin-left: -16px,
    ));
  }

  .self_right {
    // width: 180px;
    // height: 87px;
    // margin-right: 16px;
    @include adaptive-max((
      width: 180px,
      height: 87px,
      margin-right: 16px,
    ));
  }

  .question {
    display: flex;
    position: absolute;
    // width: 20px;
    // height: 20px;
    // left: 144px;
    // top: 42px;
    @include adaptive-max((
      width: 20px,
      height: 20px,
      left: 144px,
      top: 42px,
    ));
    i {
      // font-size: 16px;
      @include adaptive-max((
        font-size: 16px,
      ));
      color: rgba(255, 255, 255, 0.3);
    }
  }
}
