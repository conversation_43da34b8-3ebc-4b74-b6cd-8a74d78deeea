import React, { forwardRef } from "react";
import SelfTitlePng from "@/assets/image/self-learning-for-subject/new-week-study-title.png";
import Tabs, { ETAB } from "../tabs";
import SelfRight from "@/assets/image/self-learning-for-subject/week-study-header.png";
import { ChangeFlagModal } from "../change-flag-modal";

import styles from "./index.module.scss";
import { cls } from "~/utils/tool";

/**
 * header
 * @param props
 */

interface IHeaderBannerForSubject {
  onShowQuestion: () => void;
}

const headerBanner = (props: IHeaderBannerForSubject, ref) => {
  const { onShowQuestion } = props;

  return (
    <div
      ref={ref}
      className={cls([styles.headerContainer, styles.noTabs, styles.single])}
    >
      <div className={styles.leftBox}></div>
      <img src={SelfTitlePng} alt="高一自主学习" className={styles.self_logo} />
      <img src={SelfRight} alt="" className={styles.self_right} />
      <div className={styles.question} onClick={onShowQuestion}>
        <i className="iconfont iconwenhao1" />
      </div>
    </div>
  );
};

export default forwardRef(headerBanner);
