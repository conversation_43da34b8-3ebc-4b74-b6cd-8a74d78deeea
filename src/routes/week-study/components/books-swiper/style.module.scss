@import "~@/styles/lib.scss";

.container {
  width: 100vw;
  // height: 48px;
  display: flex;
  align-items: center;
  background: #f3f4f8;
  @include adaptive-max((
    height: 48px,
  ));
}

.logo {
  display: flex;
  align-items: center;
  // font-size: 24px;
  color: rgba(0, 0, 0, 0.25);
  // padding: 0 10px 0 12px;
  @include adaptive-max((
    font-size: 24px,
    padding: 0 10px 0 12px,
  ));
  .book {
    // margin-right: 6px;
    @include adaptive-max((
      margin-right: 6px,
    ));
  }
  .arrow {
    // font-size: 10px;
    transform: rotate(270deg);
    transform-origin: center center;
    @include adaptive-max((
      font-size: 10px,
    ));
  }
}

.swiper-container {
  flex: 1;
  min-width: 0;
}

.books-swiper {
  // border-bottom: 2px solid #f3f4f8;
  @include adaptive-max((
    border-bottom: 2px solid #f3f4f8,
  ));
  :global {
    .swiper-slide {
      width: auto !important;
    }
  }
}

.menuItem {
  // height: 28px;
  // padding: 0 12px;
  // border-radius: 14px;
  text-align: center;
  // line-height: 26px;
  color: #333333;
  // font-size: 12px;
  position: relative;
  // border: 1px solid #dddddd;
  background: #ffffff;
  // margin-left: 12px;
  @include adaptive-max((
    height: 28px,
    padding: 0 12px,
    border-radius: 14px,
    line-height: 26px,
    font-size: 12px,
    border: 1px solid #dddddd,
    margin-left: 12px,
  ));

  &.active {
    font-weight: 600;
    color: #2e86ff;
    background: #e2eeff;
    font-weight: bold;
    border-color: #2e86ff;
  }
}
