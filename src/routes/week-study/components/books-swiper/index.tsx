// 书本列表

import React, { useEffect, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";

import { IconSvg } from "@/components/icon-svg";
import { cls } from "~/utils/tool";

import { TNode } from "../../apis";

import styles from "./style.module.scss";

interface ISwiperForSubject {
  initialSlide?: string;
  list: TNode[];
  handleClick: (item: TNode) => void;
}

const BooksSwiper: React.FC<ISwiperForSubject> = (props) => {
  const { initialSlide, list = [], handleClick } = props;
  const [swiperObj, setSwiperObj] = useState<{
    slideTo: (idx: number, s: number) => void;
  }>();

  let fixedIndex = list?.findIndex((item) => item.nodeId === initialSlide);
  fixedIndex = fixedIndex == -1 ? 0 : fixedIndex;

  useEffect(() => {
    if (swiperObj) {
      // 移动到指定位置
      swiperObj.slideTo(fixedIndex, 0);
    }
  }, [initialSlide]);

  return (
    <div className={styles["container"]}>
      <div className={styles["logo"]}>
        <IconSvg className={styles["book"]} name="icon-jiaocai" />
        <IconSvg className={styles["arrow"]} name="icon-xiajiantou" />
      </div>
      <div className={styles["swiper-container"]}>
        <Swiper
          initialSlide={fixedIndex}
          slidesPerView={"auto"}
          spaceBetween={0}
          centeredSlides={false}
          // centeredSlidesBounds
          className={styles["books-swiper"]}
          onSwiper={(swiper) => setSwiperObj(swiper)}
        >
          {list.map((item: TNode) => (
            <SwiperSlide key={item.nodeId}>
              <div
                className={cls([
                  styles.menuItem,
                  initialSlide === item.nodeId && styles.active,
                ])}
                onClick={() => handleClick(item)}
              >
                {item.nodeName}
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  );
};

export default BooksSwiper;
