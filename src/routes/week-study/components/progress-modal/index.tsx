import React from 'react';
import { Popup } from 'antd-mobile';

import styles from './index.module.scss';

const ProgressModal = (props: { open: boolean; onClose: () => void }) => {
  const { open, onClose } = props;

  return (
    <Popup visible={open} onClose={onClose} className={styles.modal}>
      <div className={styles.container}>
        <div className={styles.title}>关于学习进度</div>
        <div className={styles.desc}>
          亲爱的同学，若你在最近60天学习过这份计划中的内容，系统将自动同步这些学习进度。
        </div>
        <div className={styles.btn} onClick={onClose}>
          我知道了
        </div>
        <div className={styles.safe}></div>
        <div className={styles.close} onClick={onClose}>
          <i className="iconfont iconguanbi1" />
        </div>
      </div>
    </Popup>
  );
};

export default ProgressModal;
