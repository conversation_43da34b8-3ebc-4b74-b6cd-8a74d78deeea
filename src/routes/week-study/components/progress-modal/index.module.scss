.container {
  width: 375px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px 16px 16px;

  .title {
    font-weight: bold;
    font-size: 18px;
    color: #021e66;
    line-height: 18px;
  }

  .desc {
    font-size: 14px;
    color: #021e66;
    line-height: 22px;
    padding: 0 4px;
    word-wrap: break-word;
    margin-top: 20px;
  }

  .btn {
    width: 343px;
    height: 40px;
    background: #2f86ff;
    border: 1px solid #021e66;
    box-shadow: 0 4px 0 0 rgba(2, 30, 102, 0.15);
    border-radius: 20px;
    cursor: pointer;

    font-weight: bold;
    font-size: 16px;
    color: #ffffff;

    display: flex;
    align-items: center;
    justify-content: center;

    margin-top: 42px;
  }

  .close {
    width: 54px;
    height: 54px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    right: 0;

    i {
      font-size: 16px;
      color: rgba(2, 30, 102, 0.5);
    }
  }

  .safe {
    width: 100px;
    height: constant(safe-area-inset-bottom);
    height: env(safe-area-inset-bottom);
  }
}

.modal {
  :global {
    .adm-popup-body {
      border-top-left-radius: 16px;
      border-top-right-radius: 16px;
    }
  }
}
