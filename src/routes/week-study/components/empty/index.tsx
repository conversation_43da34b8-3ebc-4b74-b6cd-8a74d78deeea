/* eslint-disable react/prop-types */
import React from "react";
import styles from "./index.module.scss";
import errorImg from "@/assets/image/holiday-selection-h5/icon_error.png";
import noDataImg from "@/assets/image/holiday-selection-h5/icon_no_data.png";

const Empty = ({ type, description }) => {
  return (
    <div className={styles.empty_box}>
      <img src={type === "error" ? errorImg : noDataImg} />
      {description && <p>{description}</p>}
    </div>
  );
};

export default Empty;
