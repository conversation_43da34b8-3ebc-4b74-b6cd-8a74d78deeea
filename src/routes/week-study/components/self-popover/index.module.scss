.self_tooltip {
  position: absolute;
  z-index: 5;
  display: flex;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
}

@import "~@/styles/lib.scss";

/* 内容样式 */
.popover_content {
  position: relative;
  background-color: #29AE5B; // 绿色背景
  color: #fff;
  // padding: 1px 5px;
  // border-radius: 4px;
  // font-size: 12px;
  // line-height: 18px;
  @include adaptive-max((
    padding: 1px 5px,
    border-radius: 4px,
    font-size: 12px,
    line-height: 18px,
  ));
}

/* 箭头 */
.arrow {
  position: absolute;
  width: 0;
  height: 0;
}

.arrow::after {
  content: '';
  position: absolute;
  border-style: solid;
}

/* 顶部箭头 */
.top {
  bottom: 100%;
  // left: 50%;
  transform: translateX(-50%);
}

.top .arrow {
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
}

.top .arrow::after {
  // border-width: 4px 4px 0 4px;
  border-color: #29AE5B transparent transparent transparent;
  left: 50%;
  transform: translateX(-50%);
  @include adaptive-max((
    border-width: 4px 4px 0 4px,
  ));
}

/* 底部箭头 */
.bottom {
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
}

.bottom .arrow {
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
}

.bottom .arrow::after {
  // border-width: 0 6px 6px 6px;
  border-color: transparent transparent #29AE5B transparent;
  left: 50%;
  transform: translateX(-50%);
  @include adaptive-max((
    border-width: 0 6px 6px 6px,
  ));
}

/* 左侧箭头 */
.left {
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
}

.left .arrow {
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
}

.left .arrow::after {
  // border-width: 6px 0 6px 6px;
  border-color: transparent transparent transparent #29AE5B;
  top: 50%;
  transform: translateY(-50%);
  @include adaptive-max((
    border-width: 6px 0 6px 6px,
  ));
}

/* 右侧箭头 */
.right {
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
}

.right .arrow {
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
}

.right .arrow::after {
  // border-width: 6px 6px 6px 0;
  border-color: transparent #29AE5B transparent transparent;
  top: 50%;
  transform: translateY(-50%);
  @include adaptive-max((
    border-width: 6px 6px 6px 0,
  ));
}
