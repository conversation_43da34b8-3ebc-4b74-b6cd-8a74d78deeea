import * as React from 'react';
import Style from './index.module.scss';
import { cls } from '@/utils/tool';

export interface ISelfTooltipProps {
  title: string;
  visible: boolean;
  placement?: 'top' | 'bottom' | 'left' | 'right'; // 位置
  className?: string;
}

export const SelfTooltip: React.FC<ISelfTooltipProps> = ({
  title,
  visible,
  placement = 'top',
  className = '',
}) => {
  if (!visible) return null; // 不显示时不渲染

  return (
    <div
      className={cls([Style.self_tooltip, Style[placement], className])}
    >
      <div className={Style.popover_content}>
        {/* 箭头 */}
        <div className={cls([Style.arrow, Style[placement]])} />
        {/* 内容 */}
        <div className={Style.content}>{title}</div>
      </div>
    </div>
  );
};
