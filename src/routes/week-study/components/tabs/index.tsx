/** 智学广场、我的智习室切换 Tab */

import React from "react";
import { cls } from "@/utils/tool";

import { EStageEnum } from "../../apis";

import Style from "./style.module.scss";

export enum ETAB {
  selfStudyRoom = 1,
  selfStudySquare,
}

export const tabTextMap = {
  [ETAB.selfStudyRoom]: "我的智学室",
  [ETAB.selfStudySquare]: "智学广场",
};

export const squareText = {
  [EStageEnum.one]: "周末智学 · 高一上",
  [EStageEnum.tow]: "周末智学 · 高二上",
};

interface ITabs {
  selectedId: ETAB;
  changeTab: (tabId: ETAB) => void;
}

const Tabs: React.FC<ITabs> = (props) => {
  const { selectedId, changeTab } = props;
  return (
    <div className={Style.tabs}>
      {Object.keys(tabTextMap).map((key) => {
        const beSelected = selectedId === +key;
        return (
          <div
            className={cls([Style.item, beSelected && Style.selected])}
            onClick={() => {
              changeTab(key as unknown as ETAB);
            }}
            key={key}
          >
            {tabTextMap[key]}
            {beSelected && <div className={Style.tip} />}
          </div>
        );
      })}
    </div>
  );
};

export default React.memo(Tabs);
