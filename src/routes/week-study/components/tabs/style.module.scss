@import "~@/styles/lib.scss";

.tabs {
  display: flex;
  position: absolute;
  bottom: 0;
  .item {
    position: relative;
    cursor: pointer;
    text-align: center;
    // line-height: 48px;
    width: 50vw;
    // height: 48px;
    font-weight: bold;
    // font-size: 18px;
    color: rgba(255, 255, 255, 0.5);
    @include adaptive-max((
      line-height: 48px,
      height: 48px,
      font-size: 18px,
    ));
  }
  .tip {
    left: 50%;
    transform: translateX(-50%);
    // bottom: 2px;
    position: absolute;
    // width: 40px;
    // height: 4px;
    background: #FFFFFF;
    // border-radius: 2px;
    @include adaptive-max((
      bottom: 2px,
      width: 40px,
      height: 4px,
      border-radius: 2px,
    ));
  }
  .selected {
    color: #FFFFFF;
  }
}