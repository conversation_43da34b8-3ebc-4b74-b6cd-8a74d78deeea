// 阶段切换
import React, { useState } from "react";
import { Picker } from "antd-mobile";

import { stageText, TStageItem } from "../../apis";

import style from "./style.module.scss";

interface IStageSelect {
  stageList: TStageItem[];
  value: number;
  onChange: (code: number) => void;
}
const StageSelect: React.FC<IStageSelect> = (props) => {
  const { stageList, value, onChange } = props;
  const [visible, setVisible] = useState<boolean>();

  return stageList?.length > 1 ? (
    <>
      <div className={style["stage-tag"]} onClick={() => setVisible(true)}>
        切换
      </div>
      <Picker
        visible={visible}
        value={[value]}
        onCancel={() => {
          setVisible(false);
        }}
        onConfirm={(value) => {
          onChange(value[0] as number);
          setVisible(false);
        }}
        columns={[
          (stageList || []).map((item) => ({
            label: stageText[item.stageCode],
            value: item.stageCode,
          })),
        ]}
      />
    </>
  ) : null;
};

export default StageSelect;
