@import "~@/styles/lib.scss";

.container {
  position: relative;
  // margin-bottom: 12px;
  @include adaptive-max((
    margin-bottom: 12px,
  ));

  .actions {
    display: flex;
    align-items: center;
    flex-grow: 0;
    flex-shrink: 0;
    // padding-right: 18px;
    // height: 38px;
    // line-height: 38px;
    @include adaptive-max((
      padding-right: 18px,
      height: 38px,
      line-height: 38px,
    ));

    .offline {
      font-weight: bold;
      // font-size: 14px;
      color: #333333;
      // margin-left: 10px;
      @include adaptive-max((
        font-size: 14px,
        margin-left: 10px,
      ));
    }

    .delete {
      // font-size: 12px;
      color: #FF4D4F;
      // margin-left: 10px;
      cursor: pointer;
      @include adaptive-max((
        font-size: 12px,
        margin-left: 10px,
      ));
    }
  }

  .main {
    .main_top {
      // height: 40px;
      background: url(../../../../assets/image/self-learning-for-subject/mission-subject.png) left/contain no-repeat, #FFFFFF;
      // border: 1px solid #021E66;
      // border-radius: 8px 8px 0 0;
      border-bottom: none ;
      display: flex;
      align-items: center;
      @include adaptive-max((
        height: 40px,
        border: 1px solid #021E66,
        border-radius: 8px 8px 0 0,
      ));

      .subject {
        // width: 38px;
        // height: 38px;
        // margin-right: 4px;
        flex-grow: 0;
        flex-shrink: 0;
        position: relative;
        @include adaptive-max((
          width: 38px,
          height: 38px,
          margin-right: 4px,
        ));

        img {
          // width: 38px;
          // height: 38px;
          @include adaptive-max((
            width: 38px,
            height: 38px,
          ));
        }

        .subject_name {
          // height: 22px;
          // padding: 0 4px;
          background: #FFFFFF;
          // border: 1px solid #021E66;
          // box-shadow: 4px 4px 0 0 rgba(2, 30, 102, 0.15);
          // border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          // font-size: 12px;
          color: #021E66;
          position: absolute;
          // top: -5px;
          // left: -4px;
          @include adaptive-max((
            height: 22px,
            padding: 0 4px,
            border: 1px solid #021E66,
            box-shadow: 4px 4px 0 0 rgba(2, 30, 102, 0.15),
            border-radius: 4px,
            font-size: 12px,
            top: -5px,
            left: -4px,
          ));
        }
      }

      .title {
        // line-height: 38px;
        // font-size: 14px;
        color: #021E66;
        font-weight: bold;
        flex-grow: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        @include adaptive-max((
          line-height: 38px,
          font-size: 14px,
        ));
      }

      .empty {
        visibility: hidden;
      }
    }

    .main_bottom {
      // height: 39px;
      background: #FFFFFF;
      // border: 1px solid #021E66;
      border-top: 0;
      // border-radius: 0 0 8px 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      @include adaptive-max((
        height: 39px,
        border: 1px solid #021E66,
        border-radius: 0 0 8px 8px,
      ));

      .info {
        display: flex;
        align-items: center;
        .info_icon {
          // padding: 6px 8px;
          // height: 38px;
          line-height: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          @include adaptive-max((
            padding: 6px 8px,
            height: 38px,
          ));

          img {
            // width: 28px;
            // height: 28px;
            @include adaptive-max((
              width: 28px,
              height: 28px,
            ));
          }
        }

        .info_name {
          font-weight: bold;
          // font-size: 12px;
          color: rgba(2, 30, 102, 0.85);
          @include adaptive-max((
            font-size: 12px,
          ));
        }

        .info_desc {
          // font-size: 12px;
          color: rgba(2, 30, 102, 0.85);
          // margin-left: 8px;
          @include adaptive-max((
            font-size: 12px,
            margin-left: 8px,
          ));
        }
      }

      .btns {
        display: flex;
        align-items: center;
        // padding-right: 10px;
        @include adaptive-max((
          padding-right: 10px,
        ));

        .btn_split {
          // width: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          @include adaptive-max((
            width: 14px,
          ));
          i {
            // font-size: 6px;
            color: #021E66;
            @include adaptive-max((
              font-size: 6px,
            ));
          }
        }
      }
    }

  }

  .top {
    position: absolute;
    // top: 1px;
    right: 0;
    @include adaptive-max((
      top: 1px,
    ));

    .done {
      position: absolute;
      // width: 22px;
      // height: 22px;
      // top: -7px;
      // right: -6px;
      @include adaptive-max((
        width: 22px,
        height: 22px,
        top: -7px,
        right: -6px,
      ));
    }
  }
}

// AI 推题按钮
.ai-paper-btn {
  // margin-left: 5px;
  // padding-right: 8px !important;
  @include adaptive-max((
    margin-left: 5px,
    padding-right: 8px,
  ));
  &-text {
    // font-size: 12px !important;
    @include adaptive-max((
      font-size: 12px,
    ));
  }
}

.ai-paper-btn-lottie {
  // width: 62px;
  // margin-left: 5px;
  display: flex;
  @include adaptive-max((
    width: 62px,
    margin-left: 5px,
  ));
}

.ai-paper-btn-disabled {
  opacity: 0.5;
}
