/** 学科下的子节点，目前是只有语文、英语下有教材同步、专项提升 */
import React from "react";

import { IconSvg } from "@/components/icon-svg";
import { cls } from "@/utils/tool";

import {
  TSubjectChildNode,
  TSubjectItem,
  ESubjectChildNodeType,
} from "../../apis";
import style from "./style.module.scss";

interface ISubjectChildNode {
  value?: string;
  subject: TSubjectItem;
  onChange: (item: TSubjectChildNode) => void;
}

const SubjectChildNode: React.FC<ISubjectChildNode> = (props) => {
  const { subject, value, onChange } = props;
  return subject?.children?.length === 2 ? (
    <div className={style["subject-children"]}>
      {subject.children.map((item, idx) => {
        return (
          <div
            className={cls([
              style["item"],
              idx ? style["right"] : style["left"],
              value === item.nodeId && style["selected"],
              value === item.nodeId && idx && style["right-select-border"],
              value === item.nodeId && !idx && style["left-select-border"],
            ])}
            onClick={() => {
              onChange(item);
            }}
            key={item.nodeId}
          >
            <IconSvg
              className={style["icon"]}
              name={
                item.nodeType === ESubjectChildNodeType.textbook
                  ? "icon-jiaocai"
                  : "icon-zhuanxiangtisheng"
              }
            />
            <div className={style["name"]}>{item.nodeName}</div>
            <div className={style["desc"]}>{item.desc}</div>
          </div>
        );
      })}
    </div>
  ) : null;
};

export default SubjectChildNode;
