@import "~@/styles/lib.scss";

.subject-children {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  // height: 48px;
  background: #ffffff;
  @include adaptive-max((
    height: 48px,
  ));
  .item {
    // width: 176px;
    // height: 32px;
    // border: 1px solid #dddddd;
    display: flex;
    align-items: center;
    justify-content: center;
    @include adaptive-max((
      width: 176px,
      height: 32px,
      border: 1px solid #dddddd,
    ));
    .icon {
      // font-size: 20px;
      color: #2e86ff;
      @include adaptive-max((
        font-size: 20px,
      ));
    }
    .name {
      font-weight: 400;
      // font-size: 14px;
      color: rgba(0, 0, 0, 0.85);
      // margin: 0 12px;
      @include adaptive-max((
        font-size: 14px,
        margin: 0 12px,
      ));
    }
    .desc {
      font-weight: 400;
      // font-size: 12px;
      color: rgba(0, 0, 0, 0.25);
      // line-height: 14px;
      @include adaptive-max((
        font-size: 12px,
        line-height: 14px,
      ));
    }
  }
  .left {
    // border-radius: 100px 0 0 100px;
    border-right: none;
    @include adaptive-max((
      border-radius: 100px 0 0 100px,
    ));
  }
  .right {
    // border-radius: 0 100px 100px 0;
    border-left: none;
    @include adaptive-max((
      border-radius: 0 100px 100px 0,
    ));
  }
  .selected {
    background: #e2eeff;
    border-color: #2e86ff;
    .name {
      font-weight: bold;
      color: rgba(0, 0, 0, 0.85);
    }
    .desc {
      color: rgba(0, 0, 0, 0.65);
    }
  }

  .left-select-border {
    // border-right: 1px solid #2e86ff;
    @include adaptive-max((
      border-right: 1px solid #2e86ff,
    ));
  }

  .right-select-border {
    // border-left: 1px solid #2e86ff;
    @include adaptive-max((
      border-left: 1px solid #2e86ff,
    ));
  }
}
