/**
 * 省份选择弹窗
 */

import React, { useState, useEffect } from "react";
import { <PERSON>er, Toast, DotLoading } from "antd-mobile";

import { clickPv } from "@/utils/tool";
import Popup, { IPopup } from "~/components/popup";
import But<PERSON>, { EType } from "~/components/button/blue-white-button";

import {
  EStageEnum,
  getProvinces as getProvinceList,
  stageText,
  updateUserProvinceInfo,
} from "../../apis";
import { Error } from "../error";

import styles from "./style.module.scss";

interface IProvinceModal extends IPopup {
  afterSetProvince: () => void;
  stage: EStageEnum;
  open: boolean;
}

const ProvinceModal: React.FC<IProvinceModal> = (props) => {
  const { open, title, stage, afterSetProvince } = props;
  const [provinces, setProvinces] =
    useState<{ label: string; value: number }[][]>();
  const [isError, setIsError] = useState(false);
  const [loading, setLoading] = useState(false);
  async function getProvinces() {
    try {
      setLoading(true);
      setIsError(void 0);
      const { data } = await getProvinceList();
      setProvinces([
        (data || []).map((item) => ({
          label: item.areaName,
          value: item.areaCode,
        })),
      ]);
    } catch (e) {
      setIsError(true);
      console.error(e);
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    if (open) {
      getProvinces();
    }
  }, [open]);

  const [showPicker, setShowPicker] = useState(false);
  async function updateProvinceInfo(code: string) {
    try {
      if (!code) {
        Toast.show({
          icon: "fail",
          content: "请选择省份",
        });
        return;
      }
      if (loading) {
        return;
      }
      setLoading(true);
      await updateUserProvinceInfo({ provinceCode: code });
      setShowPicker(false);
      afterSetProvince && afterSetProvince();
    } catch (e) {
      console.error(e);
    } finally {
      setLoading(false);
    }
  }

  return (
    <>
      <Popup
        visible={open}
        title={title}
        titleClassName={styles["title"]}
        showCloseButton={false}
        closeOnMaskClick={false}
      >
        {isError ? (
          <Error
            className={styles["page-error"]}
            tip="啊哦，遇到问题了，请重试～"
            onClick={() => {
              getProvinces();
            }}
          ></Error>
        ) : (
          <>
            <div className={styles["content"]}>
              <div>系统将根据你所选的省份/地区显示试卷。</div>
              <div>请注意，提交后不可修改</div>
            </div>
            <div className={styles["footer"]}>
              <Button
                className={styles["button"]}
                onClick={() => {
                  if (loading) {
                    return;
                  }
                  setShowPicker(true);
                }}
                type={EType.BLUE}
                text={
                  loading ? <DotLoading color="currentColor" /> : "立即选择"
                }
              />
            </div>
          </>
        )}
      </Popup>
      <Picker
        onCancel={() => {
          setShowPicker(false);
        }}
        onConfirm={(value: string[]) => {
          clickPv("ewt_h5_study_course_week_study_province_select_click", {
            content_grade: stageText[stage] || "",
          });
          if (loading) {
            return;
          }
          updateProvinceInfo(value[0]);
        }}
        popupClassName={styles["province-picker"]}
        columns={provinces}
        visible={showPicker}
        confirmText={loading ? <DotLoading color="#2f86ff" /> : "确定"}
      />
    </>
  );
};

export default ProvinceModal;
