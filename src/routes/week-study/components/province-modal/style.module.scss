@import "~@/styles/lib.scss";

.title {
  color: #333333;
  font-weight: bold;
}

.content {
  // font-size: 14px;
  color: #ff4d4f;
  text-align: center;
  // line-height: 22px;
  margin-bottom: 20px;
  @include adaptive-max((
    font-size: 14px,
    line-height: 22px,
  ));
}

.footer {
  // height: 64px;
  // padding: 0 16px;
  @include adaptive-max((
    height: 64px,
    padding: 0 16px,
  ));
}

.province-picker {
  :global {
    .adm-picker-header {
      .adm-picker-header-button {
        color: #2f86ff;
      }
    }
  }
}
