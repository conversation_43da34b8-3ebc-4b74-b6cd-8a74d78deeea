@import "~@/styles/lib.scss";

.container {
  // width: 375px;
  display: flex;
  flex-direction: column;
  align-items: center;
  // padding: 24px 0 16px;
  @include adaptive-max((
    width: 375px,
    padding: 24px 0 16px,
  ));

  .title {
    font-weight: bold;
    // font-size: 18px;
    color: #021e66;
    // line-height: 18px;
    @include adaptive-max((
      font-size: 18px,
      line-height: 18px,
    ));
  }

  .desc {
    // font-size: 12px;
    color: #999999;
    // line-height: 20px;
    // margin-top: 6px;
    @include adaptive-max((
      font-size: 12px,
      line-height: 20px,
      margin-top: 6px,
    ));
  }

  .mt_12 {
    // margin-top: 12px;
    @include adaptive-max((
      margin-top: 12px,
    ));
  }

  .mt_16 {
    // margin-top: 16px;
    @include adaptive-max((
      margin-top: 16px,
    ));
  }

  .mood_container {
    display: flex;
    flex-wrap: wrap;

    .mood_item {
      display: flex;
      flex-direction: column;
      align-items: center;
      // padding: 0 10px;
      // margin-top: 8px;
      cursor: pointer;
      @include adaptive-max((
        padding: 0 10px,
        margin-top: 8px,
      ));

      .icon {
        // width: 48px;
        // height: 48px;
        // border-radius: 50%;
        background: #f5f5f5;
        display: flex;
        justify-content: center;
        align-items: center;
        @include adaptive-max((
          width: 48px,
          height: 48px,
          border-radius: 50%,
        ));

        img {
          // width: 32px;
          // height: 32px;
          @include adaptive-max((
            width: 32px,
            height: 32px,
          ));
        }
      }

      .word {
        // font-size: 14px;
        color: #333333;
        // line-height: 22px;
        // margin-top: 2px;
        @include adaptive-max((
          font-size: 14px,
          line-height: 22px,
          margin-top: 2px,
        ));
      }

      &.active {
        .icon {
          background: #2f86ff;
        }
      }
    }
  }

  .outcome_container {
    display: flex;
    flex-wrap: wrap;

    .outcome_item {
      // padding: 0 12px;
      // height: 32px;
      // border: 1px solid #dddddd;
      // border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      // font-size: 14px;
      color: #333330;
      cursor: pointer;
      // margin: 8px 8px 0;
      @include adaptive-max((
        padding: 0 12px,
        height: 32px,
        border: 1px solid #dddddd,
        border-radius: 16px,
        font-size: 14px,
        margin: 8px 8px 0,
      ));

      &.active {
        border-color: #2f86ff;
        background-color: #2f86ff;
        color: #fff;
      }
    }
  }

  .btn {
    // width: 343px;
    // height: 40px;
    background: #2f86ff;
    // border-radius: 20px;
    text-align: center;
    // line-height: 40px;
    font-weight: bold;
    // font-size: 16px;
    color: #ffffff;
    // margin-top: 29px;
    opacity: 0.5;
    cursor: not-allowed;
    @include adaptive-max((
      width: 343px,
      height: 40px,
      border-radius: 20px,
      line-height: 40px,
      font-size: 16px,
      margin-top: 29px,
    ));
  }

  .close {
    // width: 54px;
    // height: 54px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    right: 0;
    @include adaptive-max((
      width: 54px,
      height: 54px,
    ));

    i {
      // font-size: 16px;
      color: rgba(2, 30, 102, 0.5);
      @include adaptive-max((
        font-size: 16px,
      ));
    }
  }

  &.ready {
    .btn {
      cursor: pointer;
      opacity: 1;
    }
  }

  &.disable {
    .mood_container,
    .outcome_container {
      pointer-events: none;
    }

    .btn {
      opacity: 0.5;
      pointer-events: none;
    }
  }

  .safe {
    // width: 100px;
    height: constant(safe-area-inset-bottom);
    height: env(safe-area-inset-bottom);
    @include adaptive-max((
      width: 100px,
    ));
  }
}

.modal {
  :global {
    .adm-popup-body {
      // border-top-left-radius: 16px;
      // border-top-right-radius: 16px;
      @include adaptive-max((
        border-top-left-radius: 16px,
        border-top-right-radius: 16px,
      ));
    }
  }
}
