import React, { useEffect, useState } from 'react';
import { IPlanSummary, ISummaryResponse } from '../../apis';
import { Popup } from 'antd-mobile';

import classNames from 'classnames';

import styles from './index.module.scss';

const SummaryModal = (props: {
  baseInfo: ISummaryResponse;
  open: boolean;
  submitting: boolean;
  disable: boolean;
  currentSummary: IPlanSummary;
  onClose: () => void;
  onSubmit: (summary: IPlanSummary) => void;
}) => {
  const {
    open,
    onClose,
    baseInfo,
    disable,
    currentSummary,
    onSubmit,
    submitting,
  } = props;
  const [summary, setSummary] = useState<IPlanSummary>(currentSummary);

  const canEdit = !disable;
  const isReady = canEdit && !!summary.moodCode && !!summary.outcomeCode;

  useEffect(() => {
    if (open) {
      setSummary(currentSummary);
    }
  }, [currentSummary, open]);

  return (
    <Popup visible={open} onClose={onClose} className={styles.modal}>
      <div
        className={classNames(styles.container, {
          [styles.disable]: !canEdit,
          [styles.ready]: isReady,
        })}
      >
        <div className={styles.title}>当周状态</div>
        <div
          className={styles.desc}
          style={{ visibility: canEdit ? 'visible' : 'hidden' }}
        >
          选择一个符合你学习状态的选项吧
        </div>
        <div className={classNames(styles.desc, styles.mt_12)}>学习状态</div>
        <div className={styles.mood_container}>
          {baseInfo.moodList.map((mood) => (
            <div
              className={classNames(styles.mood_item, {
                [styles.active]: mood.code === summary.moodCode,
              })}
              key={mood.code}
              onClick={() =>
                setSummary((summary) => ({ ...summary, moodCode: mood.code }))
              }
            >
              <div className={styles.icon}>
                <img src={mood.imgUrl} />
              </div>
              <div className={styles.word}>{mood.content}</div>
            </div>
          ))}
        </div>
        <div className={classNames(styles.desc, styles.mt_16)}>学习效果</div>
        <div className={styles.outcome_container}>
          {baseInfo.outcomeList.map((outcome) => (
            <div
              className={classNames(styles.outcome_item, {
                [styles.active]: outcome.code === summary.outcomeCode,
              })}
              key={outcome.code}
              onClick={() =>
                setSummary((summary) => ({
                  ...summary,
                  outcomeCode: outcome.code,
                }))
              }
            >
              {outcome.content}
            </div>
          ))}
        </div>
        <div
          className={styles.btn}
          onClick={() => {
            if (isReady) {
              onSubmit(summary);
            }
          }}
        >
          {canEdit ? '提交' : '过去的周就不能再改了~'}
        </div>
        <div className={styles.safe}></div>
        <div className={styles.close} onClick={onClose}>
          <i className="iconfont iconguanbi1" />
        </div>
      </div>
    </Popup>
  );
};

export default SummaryModal;
