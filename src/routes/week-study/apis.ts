import request from "~/utils/request";
import { EResourceType } from "./common";

/** 批量查询用户计划的状态 */
export interface IPlanStatusItem {
  /** 计划id */
  planId: string;
  /** 是否完成 */
  finishStatus: boolean;
  /** 未完成数 */
  unfinishedNum: number;
}

export const getPlanStatusList = (data: { planIdList: string[] }) =>
  request({
    url: "/api/studyprod/istudy/plan/app/status/list",
    method: "post",
    data,
  });

/** 创建推题试卷 */
export interface ICreateRecommendPaper {
  reportId: string;
  hasWrong: boolean;
  resourceId: string;
  resourceType: EResourceType;
  paperId: string;
  // 计划 ID
  planId: string;
}

export interface IRecommendPaperInfo {
  /** 试卷快照ID */
  paperId: string;
  /** 试卷报告ID */
  reportId: string;
  /** 答题业务码 */
  bizCode: string;
  /** 答题platform */
  platform: string;
}

export const createRecommendPaper = (data: ICreateRecommendPaper) =>
  request({
    url: "/api/studyprod/istudy/plan/app/createRecommendPaper",
    method: "post",
    data,
  });

/** 获取计划的 AI 推题列表 */
export interface IAIRecommendPaperItem {
  /** 来源资源id */
  sourceResourceId: string;
  /** 来源试卷快照id */
  sourcePaperId: string;
  /** 是否有同类题 */
  hasRecommend: boolean;
  /** 推荐试卷快照id */
  paperId?: string;
  /** 推荐试卷报告id */
  reportId?: string;
  platform?: string;
  bizCode?: string;
  /** 是否完成：true 已完成， false未完成  */
  hasFinish?: boolean;
  /** 是否已批改  true 已批改；false未批改 */
  correctResult?: boolean;
}

export const getAIRecommendPaperList = (params: { planId: string }) =>
  request({
    url: "/api/studyprod/istudy/plan/app/getRecommendPaperList",
    method: "get",
    params: {
      ...params,
      ignoreError: 1,
    },
  });

/** 获取目标列表 */
export interface IFlagItem {
  code: string;
  content: string;
}

export const getFlagList = () =>
  request({
    url: "/api/studyprod/istudy/common/flag/list",
    method: "get",
  });

/** 获取用户目标 */
export const getUserFlag = () =>
  request({
    url: "/api/studyprod/istudy/flag/app/get",
    method: "get",
  });

/** 设置用户目标 */
export const setUserFlag = (data: { code: string; ignoreError: number }) =>
  request({
    url: "/api/studyprod/istudy/flag/app/set",
    method: "post",
    data,
  });

export interface IPlanInfo {
  /** 计划总数量 */
  totalCount: number;
  /** 完成总数量 */
  finishCount: number;
}

export const getPlanInfo = (params: any) =>
  request({
    url: "/api/studyprod/istudy/plan/app/info",
    method: "get",
    params,
  });

/** 获取周计划列表 */
export interface IPlanListItem {
  /** 计划开始时间戳，毫秒 */
  startTimestamp: string;
  /** 计划结束时间戳，毫秒 */
  endTimestamp: string;
  /** 计划id， 如果未创建计划，为null */
  planId?: string;
  /** 是否当周 */
  currentWeek: boolean;
  /** 是否可以加入资源 */
  canAddResource: boolean;
  /** 是否有效 */
  valid: boolean;
  /** 第几周 周数 从1开始 */
  index: number;
}

export const getPlanList = () =>
  request({
    url: "/api/studyprod/istudy/plan/app/list",
    method: "get",
  });

/** 添加资源到计划 */
export interface IAddResourceReq {
  resourceId: string;
  resourceType: string;
  planId?: string;
  planStartTime?: number;
  planEndTime?: number;
  // 课程包id， resourceType是课程的时候必传
  courseId?: string;
}
export const addResource = (data: IAddResourceReq) =>
  request({
    url: "/api/studyprod/istudy/plan/app/addResource/v2",
    method: "post",
    data,
  });

export interface ResourceListResponse {
  /** 资源列表 */
  resourceList: IResourceItem[];
  /** 计划完成进度 0-1小时 */
  progress: number;
}

export enum ResourceTypeEnum {
  /** 讲 */
  LESSON = 1,
  /** 试卷 */
  PAPER = 2,
  /** fm */
  FM = 3,
}

export interface IResourceItem {
  /** 资源类型   1 讲 2 试卷 3 fm */
  resourceType: ResourceTypeEnum;
  /** 资源状态：false 失效 true 有效 */
  resourceStatus: boolean;
  /** 完成状态：false未完成 true 已完成 */
  finishStatus: boolean;
  /** 记录ID */
  recordId: string;
  /** 学科id */
  subjectId: number;
  /** 学科名称 */
  subjectName: string;
  /** 讲  */
  lessonVO?: LessonVO;
  /** 试卷 */
  paperVO?: PaperVO;
  /** fm */
  fmVO?: FmVO;
  /** 是否禁用：false 否 true 是 */
  hasForbid: boolean;
  /** 学科图标 */
  subjectIcon?: number;
}

export interface FmVO {
  /** fmId */
  fmId: string;
  /** fm名称 */
  fmName: string;
  /** 时长 秒 */
  duration: number;
}

export interface PaperVO {
  /** 试卷ID */
  paperId: string;
  /** 试卷名称 */
  paperName: string;
  /** 试卷时长 分钟 */
  duration: number;
  /** 试卷题目数 */
  questionNum: string;
  /** 试卷作答码 */
  bizCode: string;
  /** 平台 */
  platform: string;
  /** 报告id */
  reportId?: string;
  /** 正确率 0～1小数 */
  rightRate?: number;
  /** 是否已批改  true 已批改；false未批改 */
  correctResult?: boolean;
}

export interface LessonVO {
  /** 包ID */
  courseId: string;
  /** 讲ID */
  lessonId: string;
  /** 讲名称 */
  lessonName: string;
  /** 时长 秒 */
  duration: number;
  /** 用户看课的进度  0～1小数 */
  progressTime?: number;
  /** 试卷id */
  paperId?: string;
  /** 试卷是否完成： false未完成 true 已完成 */
  hasFinish?: boolean;
  /** 报告id */
  reportId?: string;
  /** 是否已批改， true 已批改；false未批改 */
  correctResult?: boolean;
  /** 正确率 小数 */
  rightRate?: number;
}

export const getResourceList = (
  params: { planId: string },
  headers: Record<string, string>,
) =>
  request({
    url: "/api/studyprod/istudy/plan/app/getResourceList",
    method: "get",
    params,
    headers,
  });

export const removeResource = (data: { recordId: string }) =>
  request({
    url: "/api/studyprod/istudy/plan/app/removeResource",
    method: "post",
    data,
  });

export interface ISummaryResponse {
  /** 注释 */
  moodList: IMoodItem[];
  /** 注释 */
  outcomeList: IOutcomeItem[];
}

export interface IOutcomeItem {
  /** 成就code */
  code: string;
  /** 成就内容 */
  content: string;
}

export interface IMoodItem {
  /** 心情code */
  code: string;
  /** 图片url */
  imgUrl: string;
  /** 心情内容 */
  content: string;
}

export const getSummaryList = () =>
  request({
    url: "/api/studyprod/istudy/common/summary/dim/list",
    method: "get",
  });

export interface IPlanSummary {
  /** 心情code */
  moodCode: string;
  /** 成就code */
  outcomeCode: string;
}

export const getPlanSummary = (params: {
  /** 开始时间戳 */
  startTimestamp: string;
  /** 结束时间戳 */
  endTimestamp: string;
}) =>
  request({
    url: "/api/studyprod/istudy/summary/app/get",
    method: "get",
    params,
  });

export const editPlanSummary = (data: {
  /** 开始时间戳 */
  startTimestamp: string;
  /** 结束时间戳 */
  endTimestamp: string;
  /** 心情code */
  moodCode: string;
  /** 成就code */
  outcomeCode: string;
  ignoreError?: number;
}) =>
  request({
    url: "/api/studyprod/istudy/summary/app/submit",
    method: "post",
    data,
  }) as unknown as Promise<boolean>;

/**
 * fm完成
 */
export function postFmComplete(params: {
  contentId: string;
  contentType: number;
}) {
  return request({
    url: "/api/homeworkprod/homework/student/updateMission",
    method: "post",
    data: params,
  });
}

// 高一上，高二上 新增接口

export interface IGetUserProvinceRes {
  memberProvinceCode: number;
  schoolProvinceCode: number;
}
// 获取用户高考省份信息
export function getUserProvinceInfo() {
  return request({
    url: "/api/studyprod/istudy/common/user/info",
    method: "get",
  }) as Promise<{ data: IGetUserProvinceRes }>;
}

// 更新用户高考省份
export function updateUserProvinceInfo(data: { provinceCode: string }) {
  return request({
    url: "/api/studyprod/istudy/common/update/user/province",
    method: "post",
    data,
  });
}

// 获取省份列表
export type TProvinceITem = {
  /** code */
  areaCode?: number;
  /** 名称 */
  areaName?: string;
  /** 首字母 */
  acronym?: string;
  /** 简称 */
  abbrName: string;
};
export function getProvinces() {
  return request({
    url: "/api/commondata/area/getProvinces",
    method: "get",
    params: {
      isSorted: true,
    },
  }) as Promise<{ data: TProvinceITem[] }>;
}

// 获取阶段列表
export enum EStageEnum {
  one = 1,
  tow = 3,
}
export const stageText = {
  [EStageEnum.one]: "高一上",
  [EStageEnum.tow]: "高二上",
};
export type TStageItem = {
  stageCode: number;
  whetherDefault: boolean;
};
export function getStageList() {
  return request({
    url: "/api/studyprod/istudy/plan/resource/app/stage/list",
    method: "get",
  }) as Promise<{ data: TStageItem[] }>;
}

// 获取学科列表
export type TSubjectItem = {
  /** 学科id, 心理100 */
  subjectId: number;
  /** 学科名称 */
  subjectName: string;
  /** 节点id */
  nodeId: string;
  /** 是否有书本 */
  hasBook: boolean;
  /** 子节点列表 */
  children?: TSubjectChildNode[];
};

export type TSubjectChildNode = {
  /** 节点id */
  nodeId: string;
  /** 节点名称 */
  nodeName: string;
  /** 节点类型  1 教材   2 专项提升 */
  nodeType: number;
  /** 是否有书本 */
  hasBook: boolean;
  /** 节点描述， 按课本，按题型 */
  desc: string;
};

export enum ESubjectChildNodeType {
  textbook = 1,
  targeted,
}
export function getSubjects(params: { stageCode: number; channel: string }) {
  return request({
    url: "/api/studyprod/istudy/plan/resource/app/subject/list",
    method: "get",
    headers: {
      channel: params.channel,
    },
    params,
  }) as Promise<{ data: TSubjectItem[] }>;
}

export type TNode = {
  nodeId: string;
  nodeName: string;
};

// 获取书本
export function getBookList(params: {
  stageCode: number;
  subjectId: number;
  nodeId: number; // 节点id, 语文或英语，传的是教材同步的节点id， 否则其他学科传的是学科列表的节点id
}) {
  return request({
    url: "/api/studyprod/istudy/plan/resource/app/book/list",
    method: "get",
    params,
  }) as Promise<{ data: TNode[] }>;
}

// 获取章节列表和难度
export type TChapterNode = {
  nodeId: number;
  nodeName: number;
  // 前端构造的id, 为了防止和心理的nodeId 重复
  feKey?: string;
  children: TNode[];
};
export function getChapterList(params: {
  stageCode: number;
  subjectId: number;
  nodeId: number; // 节点id， 语文或英语 教材同步的书本id或者专项提升节点id， 其他学科是书本id； 心理的是学科的节点id
}) {
  return request({
    url: "/api/studyprod/istudy/plan/resource/app/chapter/list",
    method: "get",
    params,
  }) as Promise<{ data: TChapterNode[] }>;
}

export type TPaperInfo = {
  /** 试卷id */
  id?: string;
  /** 试卷名称 */
  name?: string;
  /** 试卷时长 */
  duration?: number;
  /** 试卷题目数 */
  questionNum?: number;
  /** 试卷作答码 */
  bizCode?: number;
  /** 试卷的作答平台 */
  platform?: number;
  /** 类型：0预习1复习 */
  typeFlag?: number;
  /** 试卷原卷id */
  originalPaperId: string;
};
export type TFmInfo = {
  /** fmid */
  id: string;
  /** 标题 */
  title: string;
  /** 播放时长 */
  playTime: number;
  /** 图片地址 */
  picture: string;
};

export type TLessonItem = {
  /** 课程讲id */
  lessonId?: number;
  /** 课程讲名称 */
  lessonName?: string;
  /** 报告id */
  reportId?: string;
  /** 时长，单位是秒 */
  playTime: number;
  /** 记录id */
  recordId: string;
  /** 是否有课后习题 */
  hasExercise: boolean;
  /** 课程包id */
  packageId: string;
};
export type TNewResourceItem = {
  /** 试卷和报告信息 */
  paperInfo?: TPaperInfo;
  /** fm信息 */
  fmInfo?: TFmInfo;
  /** 模块id ,PackageInfoVO */
  lessonInfo?: TLessonItem;
  /** 1 课程包；  2试卷；3 fm */
  contentType?: number;
  /** 是否曾经添加过 true 添加过 */
  everAdd: boolean;
  /** 当前添加状态 true当前添加 */
  currentAdd: boolean;
  /** 记录id */
  recordId: string;
  hasAdd: boolean;
};
export function getNewResourceList(data: {
  startTimestamp: string;
  endTimestamp: string;
  stageCode: number;
  subjectId: number;
  nodeId: string; // 节点id， 语文或英语 教材同步的书本id或者专项提升节点id， 其他学科是书本id； 心理的是学科的节点id
}) {
  return request({
    url: "/api/studyprod/istudy/plan/resource/app/getResourceList",
    method: "post",
    data,
  }) as Promise<{ data: TNewResourceItem[] }>;
}
