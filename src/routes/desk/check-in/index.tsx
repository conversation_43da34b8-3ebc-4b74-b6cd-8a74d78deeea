import React, {
  useEffect,
  useState,
  useRef,
  Fragment,
  useReducer,
} from "react";
import { Layout, Spin } from "@/components";
import Styles from "./index.module.scss";
import { NetworkErrorStatus } from "@/components/empty-status";
import PageBackIcon from "@/components/page-back-icon";
import CheckInTitlePng from "@/assets/image/check-in/month-check-in-title.png";
import {
  IPopupRef as IMilestoneReceivePrizeCardRef,
  MilestoneReceivePrizeCardComponent,
} from "./components/milestone-receive-prize-card";
import TopBarRightMenu from "./components/top-bar-right-menu";
import { closeWebview } from "@/utils/bridge-utils";
import { IconSvg } from "@/components";
import { Picker, Toast } from "antd-mobile";
import SafeLogger from "@/utils/safe-logger";

import {
  getDeskCheckInStatus,
  getDeskSystemConfig,
  postDeskReceiveCheckInPrize,
} from "@/service/desk/check-in";
import { TCheckInSystemConfig } from "./types.d";
import {
  getDaysInMonth,
  getMonths,
  getYears,
  makeSnakeDays,
  defaultDailyInfo,
  AWARD_MEDAL_POPOVER_KEY,
  STUDY_DURATION,
} from "./context";
import { getUserInfo } from "@/utils/tool";
import MedalDetailModal from "./components/medal-detail-modal";
import DailyModal from "./components/daily-modal";
import CheckInMedalModal from "./components/medal-modal";
import {
  DateReminder,
  IPopupRef as IDateReminderRef,
} from "./components/date-reminder";
import { SetCalenderRemindPop } from "./components/set-calender-remind-pop";
import { useDateReminder } from "./hooks/useDateReminder";
import { useCheckInData } from "./hooks/useCheckInData";
import { useMedalManagement } from "./hooks/useMedalManagement";
import { useCheckInCalendar } from "./hooks/useCheckInCalendar";
import { useMonthPicker } from "./hooks/useMonthPicker";
import CheckInButton from "./components/check-in-button";
import { CheckInCalender } from "./components/check-in-calender";

export const Component: React.FC = () => {
  const milestoneReceivePrizeCardRef = useRef<IMilestoneReceivePrizeCardRef>();
  const safeTopRef = useRef(0);
  const [loading, setLoading] = useState(true);
  const [isPageError, setIsPageError] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const loggerRef = useRef<SafeLogger>();
  const systemConfigRef = useRef<TCheckInSystemConfig>();
  const [_, forceUpdate] = useReducer((x) => x + 1, 0);
  // 日签弹窗
  const [dailyVisible, setDailyVisible] = useState(false);
  const [dailyInfo, setDailyInfo] = useState<any>(defaultDailyInfo);
  const [calenderRemindVis, setCalenderRemindVis] = useState(false);
  const dateReminderRef = useRef<IDateReminderRef>();
  const { checkCalenderSetStatus, calcDiffDays, saveCalenderInfo } =
    useDateReminder();
  const dailyFrom = useRef("");

  // 使用日历管理 hook
  const {
    snakeDays,
    setSnakeDays,
    currentTimeRef,
    checkInList,
    handleChangeMonth,
    refreshCalendar,
    goCurrentMonth,
    // 检测当前是否是当前月份
    checkIsCurrentMonth,
  } = useCheckInCalendar({
    systemConfigRef,
    onCalendarDataChange: (data) => {
      const { params } = data;
      if (params?.isHandleRefresh) {
        Toast.show("刷新成功");
        // 刷新完成后延迟0.5秒停止旋转
        setTimeout(() => {
          setIsRefreshing(false);
        }, 500);
      }
    },
  });

  // 带旋转效果的刷新函数
  const handleRefreshWithAnimation = () => {
    if (isRefreshing) return; // 防止重复点击
    setIsRefreshing(true);
    refreshCalendar();
  };

  // 处理刷新失败的情况，确保旋转状态能正确重置
  useEffect(() => {
    if (isRefreshing) {
      // 设置一个超时，防止接口异常时旋转状态无法重置
      const timeout = setTimeout(() => {
        setIsRefreshing(false);
      }, 10000); // 10秒超时

      return () => clearTimeout(timeout);
    }
  }, [isRefreshing]);

  // 使用打卡数据管理 hook
  const { checkInInfo, handleCheckIn, setCheckInInfo } = useCheckInData({
    onCheckInSuccess: () => {
      // 刷新时先回到当前年月,如果已经是当前时间不会有任何变化
      goCurrentMonth();
      // 后端无法同步的及时刷新，需要前端延迟一点时间调用
      window.setTimeout(() => {
        // 打卡成功后刷新三年里程碑信息
        milestoneReceivePrizeCardRef.current?.refresh();
      }, 300);
    },
    onReceivePrize: (params) => {
      receivePrize({
        ...params,
        fn: () => {
          // 领取奖励后刷新一次奖品列表
          // 自动打卡时需要传递时间信息，手动打卡时只需要取当前信息
          const time = params.timeInfo || currentTimeRef.current;
          checkInList({
            year: time.currentYear,
            month:
              params.timeInfo?.currentMonth ||
              currentTimeRef.current.currentMonth + 1,
            days: time.currentMonthDays,
          });
        },
      });
    },
    toastMaskClassName: Styles.cannotCheckInToast,
  });

  // 使用月份选择器管理 hook
  const {
    changeMonthVisible,
    setChangeMonthVisible,
    pickerColumns,
    pickerValue,
    handleClose,
    handleConfirm,
  } = useMonthPicker({
    systemConfigRef,
    currentTimeRef,
    onMonthChange: handleChangeMonth,
  });

  // 使用勋章管理 hook
  const {
    checkInMedalVisible,
    setCheckInMedalVisible,
    receiveMedalVisible,
    setReceiveMedalVisible,
    medalInfo,
    setMedalInfo,
    medalFrom,
    setMedalFrom,
    receiveMedal,
    handleCheckInMedalClick,
  } = useMedalManagement({
    onMedalReceived: () => {
      // 勋章领取后刷新日历
      checkInList({
        year: currentTimeRef.current.currentYear,
        month: currentTimeRef.current.currentMonth + 1,
        days: currentTimeRef.current.currentMonthDays,
      });
    },
  });

  const setPageError = () => {
    setIsPageError(true);
    setLoading(false);
  };

  // 初始化数据
  const initData = async () => {
    let isError = false;
    try {
      setLoading(true);
      const { data } = await getDeskSystemConfig();
      if (!data || !data.systemTime || !data.configTime) {
        setPageError();
        return;
      }
      const { systemTime, configTime } = data;
      const serverDate = new Date(systemTime);
      const currentYear = serverDate.getFullYear();
      const currentMonth = serverDate.getMonth();
      // 书桌的起始时间
      const startDate = new Date(configTime);
      const startYear = startDate.getFullYear();
      const startMonth = startDate.getMonth();
      // 获取年份列表
      const yearList = getYears(systemTime, startYear);
      // 获取默认的月份列表
      const monthList = getMonths({
        serverTimestamp: systemTime,
        selectedYear: currentYear,
        startMonth: startMonth + 1,
        startYear,
      });
      // 获取当前月份的天数
      const currentMonthDayCount = getDaysInMonth(currentYear, currentMonth);
      systemConfigRef.current = {
        serverYear: currentYear,
        serverMonth: currentMonth,
        systemTime,
        configTime,
        yearList,
        monthList,
        currentMonthDayCount,
      };
      // 初始化当前时间状态
      currentTimeRef.current = {
        ...currentTimeRef.current,
        currentYear,
        currentMonth,
        currentMonthDays: currentMonthDayCount,
      };
      const days = Array.from({ length: currentMonthDayCount }, (_, index) => ({
        day: index + 1,
        isCheckIn: false,
        medalInfo: null,
      }));
      const result = makeSnakeDays(days);
      setSnakeDays(result);
      checkInList({
        year: currentYear,
        month: currentMonth + 1,
        days: currentMonthDayCount,
      });
    } catch (error) {
      setPageError();
      isError = true;
      loggerRef.current?.error("page-blocking", {
        reason: "初始化失败",
        error,
      });
    }

    if (isError) {
      setLoading(false);
      return;
    }

    try {
      const { data } = await getDeskCheckInStatus({
        ignoreError: true,
      });
      if (!data?.checkInStatus) {
        handleCheckIn({
          isAutoCheckIn: true,
        });
      } else {
        // todo 全部的15都需要抽离到context内
        const targetDuration = data?.targetDuration || STUDY_DURATION;
        setCheckInInfo({
          checkInStatus: true,
          // 这里是为了数据完整性，保存为何目标值一样
          // 如果后续需要展示真的学习时长，后端要返回来
          studyDuration: targetDuration,
          targetDuration: targetDuration,
        });
      }
      setLoading(false);
    } catch (error) {
      console.log("获取打卡状态失败", error);
      setLoading(false);
    }
  };

  const receivePrize = async (params: {
    isAutoCheckIn?: boolean;
    fn?: () => void;
  }) => {
    const { isAutoCheckIn, fn } = params;

    try {
      // 领取勋章或者日签奖励
      const { data: prizeData } = await postDeskReceiveCheckInPrize();
      if (prizeData?.medalAward?.id) {
        setMedalInfo(prizeData.medalAward);
        setMedalFrom(isAutoCheckIn ? "首页打卡按钮" : "打卡页面打卡按钮");
        setCheckInMedalVisible(true);
      } else {
        setDailyInfo({
          ...defaultDailyInfo,
          ...prizeData?.daily,
        });
        dailyFrom.current = isAutoCheckIn ? "首页打卡按钮" : "打卡页面打卡按钮";
        setDailyVisible(true);
      }
    } catch (error) {
      console.error("领取勋章或者日签奖励失败", error);
      setDailyInfo({
        ...defaultDailyInfo,
      });
      dailyFrom.current = isAutoCheckIn ? "首页打卡按钮" : "打卡页面打卡按钮";
      setDailyVisible(true);
      loggerRef.current?.warn("check-in-receive-prize-warn", {
        reason: "警告:领取勋章或者日签奖励失败",
        error,
      });
    } finally {
      // 无论领取成功与否都需要刷新一次日历，让巡展和日历信息都更新一次
      fn?.();
    }
  };

  const checkShowAwardMedalPopover = () => {
    const userId = getUserInfo();
    if (userId) {
      const cacheKey = `${AWARD_MEDAL_POPOVER_KEY}-${userId}`;
      const popoverTime = localStorage.getItem(cacheKey);
      if (popoverTime) {
        return false;
      }
      return true;
    }
    return false;
  };

  const handleCloseAwardMedalPopover = () => {
    const userId = getUserInfo();
    if (userId) {
      const cacheKey = `${AWARD_MEDAL_POPOVER_KEY}-${userId}`;
      localStorage.setItem(cacheKey, `${Date.now()}`);
      forceUpdate();
    }
  };

  useEffect(() => {
    loggerRef.current = new SafeLogger("check-in");
    initData();
  }, []);

  return (
    <Layout
      showHeader
      setChildrenContainerHeight={({ top, bottom }) => {
        safeTopRef.current = top || 0;
        return `calc(100vh - ${top || 0}px - ${bottom || 0}px)`;
      }}
      className={Styles["check-in-layout"]}
      headerProps={{
        children: "书桌每日打卡",
        className: Styles["header"],
        back: <PageBackIcon />,
        onBack: () => {
          const { isSetCalender, cacheData } = checkCalenderSetStatus();
          // 如果没设置过提醒
          if (!isSetCalender) {
            if (cacheData?.popupTime) {
              const diffDays = calcDiffDays(cacheData.popupTime);
              if (diffDays >= 7) {
                setCalenderRemindVis(true);
                saveCalenderInfo({
                  popupTime: Date.now(),
                });
              } else {
                closeWebview();
              }
            } else {
              // 没设置过也没有缓存信息，则代表第一次，直接提醒
              setCalenderRemindVis(true);
              saveCalenderInfo({
                popupTime: Date.now(),
              });
            }
          } else {
            closeWebview();
          }
        },
        right: <TopBarRightMenu />,
      }}
    >
      <div
        className={Styles["content"]}
        style={{
          height: `calc(100vh - 12.8vw - ${safeTopRef.current}px)`,
        }}
      >
        {/* 网络异常或者其他阻断性问题时友好提示，可以重新加载 */}
        {!loading && isPageError && (
          <div className={Styles["error-status-container"]}>
            <NetworkErrorStatus
              buttonOption={{
                handleClick: () => {
                  setLoading(true);
                  window.location.reload();
                },
              }}
            />
          </div>
        )}
        {!isPageError && !loading && (
          <Fragment>
            {/* 月度打卡 */}
            <div className={Styles["month-check-in-container"]}>
              <div className={Styles["month-check-in-header"]}>
                <div className={Styles["header-left"]}>
                  <div className={Styles["left-top"]}>
                    <img
                      src={CheckInTitlePng}
                      alt=""
                      className={Styles["month-check-in-title"]}
                    />
                    <div
                      className={Styles["left-top-button-box"]}
                      onClick={handleRefreshWithAnimation}
                    >
                      <IconSvg
                        name="icon-a-bianzu32beifen2x"
                        className={
                          isRefreshing ? Styles["refresh-icon-rotating"] : ""
                        }
                      />
                      <span>刷新</span>
                    </div>
                  </div>

                  <div className={Styles["left-bottom"]}>
                    <span>每日打卡，点亮月度勋章</span>
                    <span className={Styles["tip-text"]}>(仅限当月领取)</span>
                  </div>
                </div>

                <DateReminder ref={dateReminderRef} />
              </div>

              <CheckInCalender
                currentTimeRef={currentTimeRef}
                snakeDays={snakeDays}
                systemConfigRef={systemConfigRef}
                setChangeMonthVisible={setChangeMonthVisible}
                handleCloseAwardMedalPopover={handleCloseAwardMedalPopover}
                checkShowAwardMedalPopover={checkShowAwardMedalPopover}
                handleCheckInMedalClick={handleCheckInMedalClick}
              />

              {/* 三年里程碑 */}
              <MilestoneReceivePrizeCardComponent
                ref={milestoneReceivePrizeCardRef}
              />
            </div>

            {/* 打卡按钮 */}
            <CheckInButton
              checkInInfo={checkInInfo}
              onCheckIn={handleCheckIn}
            />
          </Fragment>
        )}
      </div>
      {/* 全局加载中 */}
      {loading && <Spin />}

      {/* 打卡成功的勋章弹窗 */}
      {systemConfigRef.current?.systemTime && (
        <CheckInMedalModal
          visible={checkInMedalVisible}
          onCancel={() => setCheckInMedalVisible(false)}
          medalInfo={medalInfo}
          serverTimestamp={systemConfigRef.current.systemTime}
          from={medalFrom}
        />
      )}

      {/* 手动领取的勋章弹窗 */}
      <MedalDetailModal
        visible={receiveMedalVisible}
        onCancel={() => setReceiveMedalVisible(false)}
        medalInfo={medalInfo}
      />

      {/* 日签的弹窗 */}
      {systemConfigRef.current?.systemTime && (
        <DailyModal
          visible={dailyVisible}
          onCancel={() => setDailyVisible(false)}
          dailyInfo={dailyInfo}
          // 服务器时间戳是必须存在值的，否则日签时间会不准确
          serverTimestamp={systemConfigRef.current.systemTime}
          from={dailyFrom.current}
        />
      )}

      {/* 月份切换的弹窗 */}
      <Picker
        columns={pickerColumns}
        visible={changeMonthVisible}
        onClose={handleClose}
        value={pickerValue}
        onConfirm={handleConfirm}
      />

      {/* 设置日历提醒的弹窗 */}
      <SetCalenderRemindPop
        visible={calenderRemindVis}
        onClose={() => setCalenderRemindVis((pre) => !pre)}
        onCancel={() => closeWebview()}
        onOk={() => {
          setCalenderRemindVis((pre) => !pre);
          dateReminderRef?.current.setVisible(true);
        }}
      />
    </Layout>
  );
};
