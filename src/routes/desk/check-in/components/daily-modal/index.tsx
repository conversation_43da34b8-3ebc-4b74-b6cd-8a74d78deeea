import React, { useEffect } from "react";
import { Mask } from "antd-mobile";
import solarLunar from "solarlunar";
import CloseIcon from "../close-icon";
import { ClosePositionEnum } from "../../types.d";
import ModalHeaderPng from "@/assets/image/check-in/check-in-popup-top.png";
import DefaultDailyPng from "@/assets/image/check-in/default-daily.png";
import { defaultDailyInfo, WEEK_MAP } from "../../context";

import Styles from "./index.module.scss";
import dayjs from "dayjs";
import { clickPv, expPv } from "@/utils/tool";
import { IDaily } from "@/service/desk/check-in";
import { openRoute } from "@/utils/bridge-utils";

export interface IDailyModalProps {
  visible: boolean;
  onCancel: (position: ClosePositionEnum) => void;
  dailyInfo: IDaily;
  serverTimestamp: number;
  from: string;
}

const DailyModal: React.FC<IDailyModalProps> = (props) => {
  const { visible, onCancel, dailyInfo, serverTimestamp, from } = props;
  const dailyContent = dailyInfo?.quote || defaultDailyInfo.quote;
  const time = +dailyInfo?.layoutDate || serverTimestamp;
  const dayNum = dayjs(time).format("D");
  const month = dayjs(time).format("MM");
  const year = dayjs(time).format("YYYY");
  const week = dayjs(time).day();
  const weekStr = WEEK_MAP[week];
  const lunarTime = solarLunar.solar2lunar(year, month, dayNum);

  useEffect(() => {
    if (visible) {
      expPv("ewt_h5_base_plan_desk_check_in_page_daily_modal_expo", {
        from,
      });
    }
  }, [visible]);

  const handleOpenFM = () => {
    try {
      clickPv("ewt_h5_base_plan_desk_check_in_page_daily_modal_close_click", {
        from,
        button: "FM",
      });
      if (dailyInfo.routeUrl) {
        const routeObjStr = dailyInfo.routeUrl.replace(/^mistong:\/\//, "");
        if (routeObjStr) {
          const routeObj = JSON.parse(routeObjStr);
          openRoute(routeObj);
        }
      }
      onCancel(ClosePositionEnum.FM);
    } catch (error) {
      console.error("打开FM失败", error);
    }
  };

  return (
    <Mask visible={visible} className={Styles.dailyModalMask}>
      <div className={Styles.dailyModalContainer}>
        <img src={ModalHeaderPng} alt="" className={Styles.modalHeader} />
        <div className={Styles.modalContentBox}>
          <div className={Styles.modalContent}>
            {/* 日签时间和文案 */}
            <div className={Styles.dailyInfo}>
              {/* 左侧 - 日签时间 */}
              <div className={Styles.dailyTime}>
                {/* 日签 - 公历信息 */}
                <p className={Styles.currentTime}>
                  <span className={Styles.day}>{dayNum}</span>
                  <span className={Styles.month}>/{month}</span>
                  <span className={Styles.week}>{weekStr}</span>
                </p>
                {/* 日签 - 农历信息 */}
                <p className={Styles.lunarTime}>
                  农历
                  {`${lunarTime.monthCn}${lunarTime.dayCn}`}
                </p>
              </div>
              {/* 右侧 - 日签文案 */}
              <div className={Styles.dailyContent}>{dailyContent}</div>
            </div>
            {/* 日签 - 图片区域 */}
            <div
              className={Styles.dailyImageBox}
              onClick={() => handleOpenFM()}
            >
              <img
                src={dailyInfo.backgroundImage || DefaultDailyPng}
                className={Styles.dailyImage}
                alt=""
              />
              {/* 日签 - 播放按钮 */}
              <div className={Styles.playButton}>
                <div className={Styles.playButtonIcon} />
              </div>
            </div>
          </div>
        </div>
        <CloseIcon
          className={Styles.closeIcon}
          onCancel={() => {
            clickPv(
              "ewt_h5_base_plan_desk_check_in_page_daily_modal_close_click",
              {
                from,
                button: "关闭",
              },
            );
            onCancel(ClosePositionEnum.CLOSE_BUTTON);
          }}
        />
      </div>
    </Mask>
  );
};

export default DailyModal;
