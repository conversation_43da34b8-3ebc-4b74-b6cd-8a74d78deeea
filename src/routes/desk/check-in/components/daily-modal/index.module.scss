.daily-modal-mask {
  z-index: 1050;
}
.daily-modal-container {
  width: 335px;
  height: 404px;
  max-height: 70vh;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -53%);
  z-index: 1;
  border-radius: 20px;
  overflow: visible;

  .modal-header {
    width: 335px;
    display: block;
  }

  .modal-content-box {
    overflow: hidden;
    background-image: linear-gradient(179deg, #FFE46A 0%, #FFE46A 30%);
    margin-top: -1px;
    border-radius: 0 0 20px 20px;

    .modal-content {
      margin: 6px;
      background-color: #fff;
      border-radius: 20px;
      padding-bottom: 20px;

      .daily-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 20px 0;

        .daily-time {
          text-align: center;

          .current-time {
            font-size: 12px;
            color: #333333;
            line-height: 1;

            .day {
              font-size: 30px;
            }

            .month {
              font-size: 14px;
            }

            .week {
              margin-left: 4px;
            }
          }

          .lunar-time {
            font-size: 12px;
            color: #8A8A8A;
            margin-top: 3px;
          }
        }

        .dailyContent {
          width: 180px;
          font-size: 12px;
          color: #333333;
          // 最大显示4行，超出显示省略号
          display: -webkit-box;
          -webkit-line-clamp: 4;
          line-clamp: 4;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .daily-image-box {
        width: 214px;
        height: 214px;
        border-radius: 20px;
        margin: 20px auto 0;
        position: relative;

        .daily-image {
          width: 214px;
          height: 214px;
          border-radius: 20px;
        }

        .play-button {
          width: 50px;
          height: 50px;
          position: absolute;
          bottom: 16px;
          right: 16px;
          border-radius: 50%;
          background-color: rgba(255, 255, 255, 0.7);
          display: flex;
          justify-content: center;
          align-items: center;

          .play-button-icon {
            position: absolute;
            width: 0;
            height: 0;
            border-left: 18px solid #6b4a09;
            border-top: 12px solid transparent;
            border-bottom: 12px solid transparent;
            margin-left: 8px;
          }
        }
      }
    }
  }

  .close-icon {
    position: absolute;
    right: 0;
    top: -52px;
  }
}
