import React from "react";
import CloseIconPng from "@/assets/image/check-in/close-icon.png";

import Styles from "./index.module.scss";
import { cls } from "@/utils/tool";

export interface ICloseIconProps {
  onCancel: () => void;
  className?: string;
}

const CloseIcon: React.FC<ICloseIconProps> = (props) => {
  const { onCancel, className } = props;

  return (
    <div
      className={cls([Styles.closeIconContainer, className])}
      onClick={onCancel}
    >
      <img src={CloseIconPng} alt="" className={Styles.closeIcon} />
    </div>
  );
};

export default CloseIcon;
