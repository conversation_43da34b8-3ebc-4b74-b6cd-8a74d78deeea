import React from "react";
import { IconSvg } from "@/components";
import { openUrlInWebView } from "@/utils/tool";

import Styles from "./index.module.scss";

const TopBarRightMenu: React.FC<object> = () => {
  return (
    <div
      className={Styles["rule-mark-text"]}
      onClick={() =>
        openUrlInWebView(
          "https://web.ewt360.com/themeTemplateClient/index.html?id=1970107692026126336&showTopBar=false",
          "书桌每日打卡规则说明",
        )
      }
    >
      规则说明
      <IconSvg name="icon-a-question-circle-fill2x" />
    </div>
  );
};

export default TopBarRightMenu;
