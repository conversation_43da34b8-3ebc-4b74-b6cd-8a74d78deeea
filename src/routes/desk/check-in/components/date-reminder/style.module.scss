.data-reminder-btn {
  font-weight: 400;
  font-size: 14px;
  color: #000;
  position: relative;

  .small-point {
    position: absolute;
    right: -4px;
    top: -2px;
    width: 4px;
    height: 4px;
    display: block;
    background-color: #F5222D;
    border-radius: 50%;

    &.holiday-yellow {
      background-color: #FFBD32;;
    }
  }

  &.holiday-reminder-btn {
    color: #fff;
  }
}

.tip {
  width: 327px;
  margin: 0 auto;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  line-height: 22px;
  margin-bottom: 12px;
}

.content {
  margin: 0 auto;
  width: 335px;
  height: 80px;
  background: rgba(45, 134, 254, 0.05);
  border: 1px solid #2D86FE;
  border-radius: 8px;
}

.button-item {
  width: 162px;
  height: 80px;
  background: rgba(45, 134, 254, 0.05);
  border: 1px solid #2D86FE;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;

  &:first-child {
    margin-right: 11px;
  }

  &.only-one-button-item {
    width: 335px;
  }
}

.no-value {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 400;
  font-size: 16px;
  color: #2D86FE;
  margin-bottom: 40px;
}

.have-value {
  width: 327px;
  margin: 0 auto;
  padding-top: 8px;
  padding-bottom: 6px;
  padding-left: 12px;
  background: #2d86fe0d;
  border: 1px solid #2D86FE;
  border-radius: 8px;
}

.range-text {
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  line-height: 22px;
}

.range-value, .alarm-time {
  font-weight: bold;
  font-size: 14px;
  color: #2D86FE;
  line-height: 22px;
}

.alarm-time {
  span {
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    line-height: 20px;
  }
}

.calendar-picker {
  height: 564px;
  max-height: 60vh;
  overflow: auto;

  :global {
    .adm-calendar-picker-view-header {
      display: none;
    }
  }
}

.icon {
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  border-radius: 11px;
  background: #F4F9FF;
  border: 1px solid #2D86FE;
}


.confirm-btn-wrapper {
  height: 73px;
  background-color: #fff;
  display: flex;
  align-items: center;
  .set-date-confirm-btn {
    flex: 0 0 351px;
    margin: auto;
    background: #1677FF;
    border-radius: 2px;
    border-radius: 4px;
  }
}

.actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 76px;
  padding: 0 16px;
  margin-top: 20px;

  .edit-btn, .delete-btn {
    width: 164px;
    height: 44px;
  }
}

.page-loading {
  z-index: 1001;
}

.cannot-checkIn-toast {
  :global {
    .adm-toast-main {
      max-width: 300px;
    }
  }
}

.set-calender-tip-popover-box {
  z-index: 999;

  :global {
    .adm-popover-inner-content {
      padding: 5px 8px;
    }
  }
}

.set-calender-popover {
  display: flex;
  align-items: center;
  font-size: 12px;

  svg {
    margin-left: 5px;
  }
}

// 右侧-打卡提醒
.month-check-in-header-right {
  position: absolute;
  top: 14.5px;
  right: 12px;
  z-index: 2;
  display: flex;
  align-items: center;

  .check-in-switch {
    width: 8vw;
    height: 4.26667vw;

    :global {
      .adm-switch-checkbox {
        background-color: #D8D8D8;

        &::before {
          background-color: #D8D8D8;
        }
      }
    }

    &.has-set-calender {
      :global {
        .adm-switch-checkbox {
          background-color: #FF7F21;

          &::before {
            background-color: #FF7F21;
          }
        }
      }
    }
  }

  span {
    margin-left: 4px;
    color: #333;
    font-size: 12px;
    font-weight: bold;
  }
}
