/** 日历提醒组件 */
import * as React from "react";
import dayjs from "dayjs";
import { CalendarPickerView, Popover, Switch, Toast } from "antd-mobile";
import { EButtonType, IconSvg } from "@/components";
import { useVisibilitychange } from "@/hooks";
import { Button, Popup } from "@/components";
import PageLoading from "@/components/page-loading";
import {
  clickPv,
  cls,
  convertDateFormat,
  debounce,
  expPv,
  versionCompare,
} from "@/utils/tool";
import { useDateReminder } from "../../hooks/useDateReminder";

import Style from "./style.module.scss";

export interface IDateReminderProps {
  onClose?: () => void;
  isHoliday?: boolean;
}

export interface IPopupRef {
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
}

export enum CalenderTypeEnum {
  isOnlyWeek = "week",
  isEveryDay = "day",
}

export const CalenderType = [
  {
    type: CalenderTypeEnum.isOnlyWeek,
    title: "仅周六日提醒",
  },
  {
    type: CalenderTypeEnum.isEveryDay,
    title: "每日提醒",
  },
];

export const DateReminder = React.forwardRef<IPopupRef, IDateReminderProps>(
  function Popups(props, ref) {
    const { onClose } = props;
    const {
      loading,
      dateRange,
      setDate,
      getDate,
      deleteDate,
      checkCalenderSetStatus,
      saveCalenderInfo,
    } = useDateReminder();
    const appVersionRef = React.useRef<string>("");
    const setCalenderPopoverRef = React.useRef<HTMLDivElement>(null);

    const [setCalenderVisible, setSetCalenderVisible] = React.useState(false);
    const [isSetCalender, setIsSetCalender] = React.useState(false);
    const [calenderType, setCalenderType] = React.useState<CalenderTypeEnum>(
      CalenderTypeEnum.isEveryDay,
    );

    /** 设置日历提醒 pop */
    const [dateReminderVis, setDateReminderVis] =
      React.useState<boolean>(false);
    function changeDateReminderVis() {
      const data = checkCalenderSetStatus();
      const { isSetCalender } = data;
      setIsSetCalender(isSetCalender);
      setDateReminderVis((pre) => !pre);
    }
    /** 日历 pop */
    const [calenderVis, setCalenderVis] = React.useState<boolean>(false);
    function changeCalenderVis() {
      setCalenderVis((pre) => !pre);
    }

    // 日历提醒校验
    const checkDateReminderStatus = () => {
      const data = checkCalenderSetStatus();
      const { cacheData, isSetCalender } = data;
      setIsSetCalender(isSetCalender);
      // 如果没有设置过日历提醒，那一周要提出一次Popover
      if (cacheData.popoverTip && !isSetCalender) {
        // 上次点击关闭的时间
        const popupTime = dayjs(cacheData.popoverTip);
        // 当前时间
        const currentDate = dayjs();
        // 计算差值
        const diffDays = currentDate.diff(popupTime, "weeks");
        // 大于等于1周代表需要显示提醒
        if (diffDays >= 1) {
          setSetCalenderVisible(true);
          // 更新时间
          saveCalenderInfo({
            popoverTip: Date.now(),
          });
          return;
        }
      }

      // 如果没有缓存信息，那直接弹出Popover并记录当前时间
      if (!cacheData.popoverTip) {
        // 本地没有缓存记录，直接显示提示
        setSetCalenderVisible(true);
        saveCalenderInfo({
          popoverTip: Date.now(),
        });
      }
    };

    const isNewApp = () => {
      if (appVersionRef.current) {
        return versionCompare(appVersionRef.current, "11.3.0");
      }
      return false;
    };

    // 获取app版本
    const getAppVersion = async () => {
      try {
        // 这里不用通用的方法，以为是需要异步即可，不用同步锁
        const { data } = await mstJsBridge.getAppVersion();
        if (data?.version) {
          appVersionRef.current = data.version;
        }
      } catch (error) {
        // 不是核心链路，失败了就从h5领取，因此通过arms收集即可
        console.warn("获取app版本号失败", error);
      }
    };

    /** 日历范围，受控处理 */
    const [value, setValue] = React.useState<[Date, Date]>();

    // 初始化数据
    const initData = () => {
      getAppVersion();
      // 先同步的和App交互，检测并更新日历的信息后再检测
      getDate(() => checkDateReminderStatus());
    };

    React.useEffect(() => {
      initData();
    }, []);

    /** 日历值的重置 */
    React.useEffect(() => {
      if (!calenderVis) {
        setValue(null);
      }
    }, [calenderVis]);

    /** 获取设置的日期范围 */
    React.useEffect(() => {
      if (dateReminderVis) {
        expPv("ewt_h5_base_plan_desk_check_in_page_choice_calender_mode_expo", {
          status: dateRange ? "已设置" : "未设置",
        });
      }

      if (!dateReminderVis) {
        onClose?.();
      }
    }, [dateReminderVis]);

    const handleCloseCalenderPopover = () => {
      setSetCalenderVisible(false);
      saveCalenderInfo({
        popoverTip: Date.now(),
      });
    };

    /** 把弹窗的调度能力暴露出去 */
    React.useImperativeHandle(ref, () => {
      return {
        setVisible: setDateReminderVis,
      };
    }, []);

    const handleSetCalenderType = (type: CalenderTypeEnum) =>
      setCalenderType(type);

    // 刷新日历的开关状态
    const refreshSwitch = () => {
      // 等路由操作完成
      getDate(() => checkDateReminderStatus());
    };

    const handleRefreshSwitch = (isShow: boolean) => isShow && refreshSwitch();

    // 页面的可见/不可见监控
    useVisibilitychange({
      handleVisibilitychange: handleRefreshSwitch,
    });

    return (
      <>
        <Popover
          className={Style["set-calender-tip-popover-box"]}
          content={
            <div className={Style["set-calender-popover"]}>
              担心忘了学习？设个提醒吧！
              <IconSvg
                name="icon-guanbi"
                onClick={handleCloseCalenderPopover}
              />
            </div>
          }
          placement="bottom-start"
          mode="dark"
          visible={setCalenderVisible}
          getContainer={() => setCalenderPopoverRef.current}
        >
          <div
            className={Style["month-check-in-header-right"]}
            onClick={() => {
              setDateReminderVis(true);
            }}
            ref={setCalenderPopoverRef}
          >
            <Switch
              checked={isSetCalender}
              style={{
                "--width": "8vw",
                "--height": "4.26667vw",
                "--checked-color": "#FF7F21",
              }}
              className={cls([
                Style["check-in-switch"],
                isSetCalender && Style["has-set-calender"],
              ])}
            />
            <span>{isSetCalender ? "已提醒" : "打卡提醒"}</span>
          </div>
        </Popover>

        <Popup
          visible={dateReminderVis}
          title="设置日历提醒"
          onClose={changeDateReminderVis}
          destroyOnClose
        >
          <div className={Style["tip"]}>
            你可以选择起止日期来设置日历提醒，每天提醒你前来打卡学习
          </div>
          <div
            className={cls([
              isNewApp ? Style["more-button-box"] : Style["content"],
              dateRange ? Style["have-value"] : Style["no-value"],
            ])}
          >
            {dateRange ? (
              <>
                <div className={Style["range-text"]}>提醒周期：</div>
                <div className={Style["range-value"]}>
                  {convertDateFormat(dateRange.start_date)}-
                  {convertDateFormat(dateRange.end_date)}
                </div>
                <div className={Style["alarm-time"]}>
                  {dateRange?.isOnlyWeek === CalenderTypeEnum.isOnlyWeek
                    ? "周六、周日"
                    : "每天"}
                  上午9:00提醒 <span>（时间不可修改）</span>
                </div>
              </>
            ) : (
              <>
                {isNewApp ? (
                  <>
                    {CalenderType.map((item) => (
                      <div
                        className={Style["button-item"]}
                        key={item.type}
                        onClick={() => {
                          clickPv(
                            "ewt_h5_base_plan_desk_check_in_page_choice_calender_mode_home_click",
                            {
                              mode:
                                item.type === CalenderTypeEnum.isEveryDay
                                  ? "每日提醒"
                                  : "仅周六日提醒",
                            },
                          );
                          handleSetCalenderType(item.type);
                          changeCalenderVis();
                        }}
                      >
                        <div className={Style["icon"]}>
                          <IconSvg name="icon-jiajihua" />
                        </div>
                        <div>{item.title}</div>
                      </div>
                    ))}
                  </>
                ) : (
                  <React.Fragment>
                    <div
                      className={cls([
                        Style["button-item"],
                        Style["only-one-button-item"],
                      ])}
                      onClick={() => {
                        clickPv(
                          "ewt_h5_base_plan_desk_check_in_page_choice_calender_mode_home_click",
                          {
                            mode: "每日提醒",
                          },
                        );
                        handleSetCalenderType(CalenderTypeEnum.isEveryDay);
                        changeCalenderVis();
                      }}
                    >
                      <div className={Style["icon"]}>
                        <IconSvg name="icon-jiajihua" />
                      </div>
                      <div>新增一个提醒时间段</div>
                    </div>
                  </React.Fragment>
                )}
              </>
            )}
          </div>
          {dateRange ? (
            <div className={Style["actions"]}>
              <Button
                className={Style["delete-btn"]}
                type={EButtonType.grey}
                text="删除提醒"
                onClick={debounce(() => {
                  clickPv(
                    "ewt_h5_base_plan_desk_check_in_page_choice_calender_mode_button_click",
                    {
                      type: "删除提醒",
                    },
                  );
                  deleteDate(true);
                }, 300)}
              />
              <Button
                className={Style["edit-btn"]}
                text="修改周期"
                onClick={debounce(() => {
                  clickPv(
                    "ewt_h5_base_plan_desk_check_in_page_choice_calender_mode_button_click",
                    {
                      type: "修改提醒",
                    },
                  );
                  changeCalenderVis();
                }, 300)}
              />
            </div>
          ) : null}
        </Popup>
        <Popup
          destroyOnClose
          visible={calenderVis}
          title="日期选择"
          onClose={changeCalenderVis}
        >
          <CalendarPickerView
            title={null}
            className={Style["calendar-picker"]}
            selectionMode="range"
            /** 最多可以选择当前日期往后推59天 */
            max={dayjs().add(60, "days").toDate()}
            min={dayjs().toDate()}
            onChange={(val) => {
              setValue(val);
            }}
            shouldDisableDate={(date) => {
              const today = dayjs();
              return (
                date < today.startOf("day").toDate() ||
                date > today.add(60, "days").startOf("day").toDate()
              );
            }}
          />
          <div className={Style["confirm-btn-wrapper"]}>
            <Button
              text="确认"
              className={Style["set-date-confirm-btn"]}
              onClick={debounce(async () => {
                if (value?.length) {
                  const startDate = dayjs(value[0]).format("YYYY-MM-DD");
                  const endDate = dayjs(value[1]).format("YYYY-MM-DD");
                  clickPv(
                    "ewt_h5_study_course_self_learning_calendar_pop_add_confirm_button_click",
                    {
                      startDate,
                      endDate,
                    },
                  );
                  const res = await setDate(startDate, endDate, calenderType);
                  if (res) {
                    setValue(null);
                    changeCalenderVis();
                  }
                } else {
                  Toast.show({
                    content: "请选择一段时间区间",
                  });
                }
              }, 300)}
            />
          </div>
        </Popup>
        <PageLoading className={Style["page-loading"]} visible={loading} />
      </>
    );
  },
);
