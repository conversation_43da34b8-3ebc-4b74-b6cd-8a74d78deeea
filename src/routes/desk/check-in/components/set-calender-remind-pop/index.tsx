/** 设置日历提醒底抽 */

import * as React from "react";
import NoLeavePNG from "@/assets/common/do-not-go.png";
import { Button, IPopup, Popup } from "@/components";
import { expPv, cls, clickPv } from "@/utils/tool";

import Style from "./style.module.scss";

interface ISetCalenderRemindPop extends IPopup {
  onCancel: () => void;
  onOk: () => void;
}

export const SetCalenderRemindPop: React.FC<ISetCalenderRemindPop> = (
  props,
) => {
  const { visible, onCancel, onOk, onClose } = props;

  React.useEffect(() => {
    if (visible) {
      expPv("ewt_h5_base_plan_desk_check_in_page_not_leave_modal_expo");
    }
  }, [visible]);

  return (
    <Popup onClose={onClose} title="先别走！" visible={visible}>
      <div className={Style["set-calender-remind-content"]}>
        <div>我怕你把我给忘了T-T</div>
        <div>设置个日程提醒你吧</div>
        <img src={NoLeavePNG} />
      </div>
      <div className={Style["footer"]}>
        <Button
          className={cls([Style["button"], Style["cancel"]])}
          text="残忍离开"
          onClick={() => {
            clickPv(
              "ewt_h5_base_plan_desk_check_in_page_not_leave_modal_button_click",
              {
                button: "残忍离开",
              },
            );
            onCancel();
          }}
        />
        <Button
          className={cls([Style["button"], Style["ok"]])}
          text="设置日历提醒"
          onClick={() => {
            clickPv(
              "ewt_h5_base_plan_desk_check_in_page_not_leave_modal_button_click",
              {
                button: "设置日历提醒",
              },
            );
            onOk();
          }}
        />
      </div>
    </Popup>
  );
};
