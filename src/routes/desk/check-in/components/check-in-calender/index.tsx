// 生成组件的基本结构
import React, { Fragment, useState } from "react";
import { Popover } from "antd-mobile";
import { clickPv, cls } from "@/utils/tool";
import { IconSvg } from "@/components";
import LightPng from "@/assets/image/check-in/light.png";
import CheckInPng from "@/assets/image/check-in/check-in.png";
import NoCheckInPng from "@/assets/image/check-in/no-check-in.png";
import { MedalModal } from "../milestone-receive-prize-card/medal-modal";

import Styles from "./index.module.scss";
import { TCheckInSystemConfig, ICalendarDayItem } from "../../types.d";

export interface ICheckInCalenderProps {
  currentTimeRef: React.RefObject<{
    currentYear: number;
    currentMonth: number;
    currentMonthDays: number;
    checkInDays: number;
    prizeInfo: any[];
  }>;
  snakeDays: ICalendarDayItem[];
  systemConfigRef: React.RefObject<TCheckInSystemConfig>;
  setChangeMonthVisible: (visible: boolean) => void;
  handleCloseAwardMedalPopover: () => void;
  checkShowAwardMedalPopover: () => boolean;
  handleCheckInMedalClick: (dayItem: ICalendarDayItem) => void;
}

export const CheckInCalender: React.FC<ICheckInCalenderProps> = (props) => {
  const {
    currentTimeRef,
    systemConfigRef,
    snakeDays,
    setChangeMonthVisible,
    handleCloseAwardMedalPopover,
    checkShowAwardMedalPopover,
    handleCheckInMedalClick,
  } = props;

  const medalModalRef = React.useRef<any>();
  const [medalModalInfo, setMedalModalInfo] = useState({
    image: "",
    isGrey: false,
  });

  const makeMedalBtnText = (dayItem: ICalendarDayItem) => {
    const { day, isCheckIn, medalInfo, isFirstNotAwardMedal } = dayItem;
    let btnText = `${day}天`;

    // 首先要判断今天是否已打卡，没打卡那直接显示天数即可
    if (isCheckIn) {
      if (!medalInfo.awardStatus) {
        if (medalInfo?.isExpired) {
          btnText = "过期";
        } else if (isFirstNotAwardMedal) {
          btnText = "领奖";
        } else {
          btnText = "待领";
        }
      }
    }
    return btnText;
  };

  const makeMedalBtnClass = (dayItem: ICalendarDayItem) => {
    const { isCheckIn, medalInfo, isFirstNotAwardMedal, hasNotAwardMedal } =
      dayItem;
    let btnClass = "";
    // 首先要判断今天是否已打卡，没打卡那直接显示天数即可
    if (isCheckIn) {
      if (!medalInfo.awardStatus) {
        if (medalInfo?.isExpired) {
          btnClass = "expired";
        } else if (!isFirstNotAwardMedal && hasNotAwardMedal) {
          btnClass = "disabled";
        }
      }
    }
    return btnClass;
  };

  const handleMedalImgClick = (info: any) => {
    setMedalModalInfo({
      image: info.mainImgUrl,
      isGrey: !info.awardStatus,
    });
    medalModalRef?.current?.setVisible(true);
  };

  // 是否可以切换月份,年份或者月份列表大于1个就可以切换，否则不可以切换
  const canChangeMonth =
    systemConfigRef.current?.monthList?.length > 1 ||
    systemConfigRef.current?.yearList?.length > 1;

  return (
    <Fragment>
      <div className={Styles["month-check-in-content-container"]}>
        <div className={Styles["month-check-in-content"]}>
          <div className={Styles["check-in-base-info"]}>
            <div
              className={Styles["month-change-container"]}
              onClick={() => {
                clickPv(
                  "ewt_h5_base_plan_desk_check_in_page_change_calender_month_click",
                );
                if (canChangeMonth) {
                  setChangeMonthVisible(true);
                }
              }}
            >
              <span>
                {currentTimeRef.current.currentYear || "--"}年
                {currentTimeRef.current.currentMonth
                  ? currentTimeRef.current.currentMonth + 1
                  : "--"}
                月
              </span>
              {canChangeMonth && <IconSvg name="icon-xiala" />}
            </div>

            <div className={Styles["check-in-days"]}>
              坚持打卡
              <span className={Styles["check-in-days-number"]}>
                {currentTimeRef.current.checkInDays === -1
                  ? "--"
                  : currentTimeRef.current.checkInDays}
              </span>
              天
            </div>
          </div>

          <hr className={Styles["check-in-base-info-hr"]} />

          <div
            className={cls([
              Styles["check-in-calendar-container"],
              Styles[`bg${currentTimeRef.current.currentMonthDays || 31}`],
            ])}
          >
            <ul>
              {snakeDays.map((dayItem: ICalendarDayItem, index: number) => {
                const { day, isCheckIn, medalInfo, isFirstNotAwardMedal } =
                  dayItem;

                return (
                  <li key={index}>
                    {medalInfo ? (
                      <div className={Styles["medal-box"]}>
                        <div className={Styles["medal-img-show-box"]}>
                          <img
                            src={medalInfo.mainImgUrl}
                            alt=""
                            className={cls([
                              Styles["check-in-item-img"],
                              Styles["medal-img"],
                              // 如果是未领取状态，勋章图片需要时灰色的
                              !medalInfo.awardStatus && Styles["grey"],
                            ])}
                            onClick={() => handleMedalImgClick(medalInfo)}
                          />
                          {isCheckIn &&
                            !medalInfo.awardStatus &&
                            isFirstNotAwardMedal &&
                            !medalInfo?.isExpired && (
                              <img
                                src={LightPng}
                                alt=""
                                className={cls([
                                  Styles["light-img"],
                                  Styles["ring"],
                                ])}
                                onClick={() => handleMedalImgClick(medalInfo)}
                              />
                            )}
                        </div>
                        {/* 如果已经领取了，或者是还未打卡，那么直接显示天数即可 */}
                        {medalInfo.awardStatus || !isCheckIn ? (
                          <span
                            className={cls([
                              // 默认全是没打卡的
                              Styles["no-check-in-text"],
                              // 如果已打卡，则显示打卡状态
                              isCheckIn && Styles["check-in-text"],
                            ])}
                          >
                            {day}天
                          </span>
                        ) : (
                          <Popover
                            mode="dark"
                            content={
                              <div
                                className={
                                  Styles["award-medal-popover-content"]
                                }
                              >
                                立即领取，未领勋章次月失效
                                <IconSvg
                                  name="icon-guanbi"
                                  onClick={handleCloseAwardMedalPopover}
                                />
                              </div>
                            }
                            className={Styles["award-medal-popover"]}
                            // 打过卡 第一个 没领取 没展示过才显示
                            visible={
                              isFirstNotAwardMedal &&
                              isCheckIn &&
                              !medalInfo.awardStatus &&
                              checkShowAwardMedalPopover()
                            }
                            placement="bottom"
                          >
                            <div
                              className={cls([
                                Styles["award-medal-btn"],
                                Styles[makeMedalBtnClass(dayItem)],
                              ])}
                              onClick={() => handleCheckInMedalClick(dayItem)}
                            >
                              <span>{makeMedalBtnText(dayItem)}</span>
                            </div>
                          </Popover>
                        )}
                      </div>
                    ) : (
                      <>
                        <img
                          src={isCheckIn ? CheckInPng : NoCheckInPng}
                          alt=""
                          className={Styles["check-in-item-img"]}
                        />
                        <span
                          className={cls([
                            Styles["no-check-in-text"],
                            isCheckIn && Styles["check-in-text"],
                          ])}
                        >
                          {day}天
                        </span>
                      </>
                    )}
                  </li>
                );
              })}
            </ul>
          </div>
        </div>
      </div>

      <MedalModal ref={medalModalRef} {...medalModalInfo} />
    </Fragment>
  );
};
