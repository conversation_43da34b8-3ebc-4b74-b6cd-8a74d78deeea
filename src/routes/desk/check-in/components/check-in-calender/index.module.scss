.month-check-in-content-container {
  background-image: linear-gradient(180deg, #ffd52f 0%, #fff5a6 100%);
  box-shadow: 0 2px 4px 0 #c8c8c880;
  padding-bottom: 4px;
  border-radius: 0 0 16px 16px;

  .month-check-in-content {
    min-height: 300px;
    background-color: #fff;
    border-radius: 16px;
    margin: 0 4px;

    .check-in-base-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;

      .month-change-container {
        display: flex;
        align-items: center;
        cursor: pointer;

        span {
          font-size: 16px;
          color: #333;
          font-weight: bold;
          margin-right: 10px;
        }
      }

      .check-in-days {
        font-size: 14px;
        color: #333;
        font-weight: bold;

        .check-in-days-number {
          color: #ff7f21;
          margin: 0 4px;
        }
      }
    }

    .check-in-base-info-hr {
      margin: 0 16px;
      height: 1px;
      border: 1px solid #fff9d4;
    }

    .check-in-calendar-container {
      margin: 0 10px;
      background-repeat: no-repeat;
      background-size: 100%;
      background-position-y: 18px;
      margin-top: 11.5px;

      &.bg28 {
        background-image: url("@/assets/image/check-in/calender-28.png");
      }

      &.bg29 {
        background-image: url("@/assets/image/check-in/calender-29.png");
      }

      &.bg30 {
        background-image: url("@/assets/image/check-in/calender-30.png");
      }

      &.bg31 {
        background-image: url("@/assets/image/check-in/calender-31.png");
      }

      ul {
        display: flex;
        flex-wrap: wrap;
        margin-left: 10px;

        li {
          width: 8.6%;
          margin-right: 19.11px;
          height: 66px;
          text-align: center;

          .check-in-item-img {
            width: 30px;
            height: 30px;
          }

          .medal-img {
            position: relative;

            &.grey {
              filter: grayscale(100%);
            }
          }

          .no-check-in-text {
            color: #999;
          }

          .check-in-text {
            color: #333;
            font-weight: bold;
          }

          .award-medal-btn {
            width: 30px;
            height: 17px;
            border-radius: 16px;
            text-align: center;
            line-height: 17px;
            background-color: #FF605E;

            span {
              color: #fff;
              // 文字需要缩小到11号字，需要使用缩放属性
              font-size: 12px;
              transform: scale(0.9);
              display: inline-block;
            }

            &.disabled {
              background-color: rgba(255, 96, 94, 0.5);
            }

            &.expired {
              background-color: #999;
            }
          }

          .medal-box {
            .medal-img-show-box {
              position: relative;

              .light-img {
                position: absolute;
                top: 0;
                left: 0;
                width: 30px;
                height: 30px;
              }
            }
          }
        }

        li:nth-child(7n) {
          margin-right: 0;
        }
      }
    }
  }
}

.award-medal-popover {
  z-index: 1000;

  :global {
    .adm-popover-inner-content {
      padding: 5px 8px;
    }
  }

  .award-medal-popover-content {
    font-size: 12px;

    svg {
      margin-left: 5px;
    }
  }
}

/* 底部光圈：旋转 */
.ring {
  z-index: 1;
  will-change: transform;
  animation: ring-combo 2s linear infinite;
}

@keyframes ring-combo {
  from { transform: rotate(0deg); }
  to   { transform: rotate(360deg); }
}
