import React, { useState } from "react";
import styles from "./index.module.scss";
import { ImageViewer } from "antd-mobile";
import { cls } from "@/utils/tool";

export interface IMedalModalProps {
  maskClassName?: string;
  image: string;
  isGrey: boolean;
}

export interface IPopupRef {
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
}

export const MedalModal = React.forwardRef<IPopupRef, IMedalModalProps>(
  function Popups(props, ref) {
    const { maskClassName, image, isGrey = true } = props;
    const [visible, setVisible] = useState(false);
    /** 把弹窗的调度能力暴露出去 */
    React.useImperativeHandle(ref, () => {
      return {
        setVisible,
      };
    }, []);

    return (
      <ImageViewer
        getContainer={() => document.body}
        classNames={{
          mask: maskClassName,
          body: cls([styles["medal-img"], isGrey && styles["grey"]]),
        }}
        maxZoom={1}
        image={image}
        visible={visible}
        onClose={() => setVisible(false)}
      />
    );
  },
);
