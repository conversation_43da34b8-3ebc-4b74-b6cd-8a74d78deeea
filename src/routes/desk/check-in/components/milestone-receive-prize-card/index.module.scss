.milestone-receive-prize-box {
  padding-bottom: 6px;
  background-image: url("@/assets/image/check-in/three-grade-bg.png");
  background-repeat: no-repeat;
  background-size: 100%;
  margin: 16px 0;
  box-shadow: 0 2px 4px 0 #c8c8c880;
  border-radius: 16px;

  // 里程碑打卡标题图片
  .check-in-milestone-title {
    width: 144px;
    margin: 12px 0 4px 16px;
  }

  // 里程碑打卡信息
  .check-in-milestone-content {
    font-size: 14px;
    color: #333;
    margin-left: 16px;
    margin-bottom: 12px;

    .check-in-milestone-days {
      color: #FF7F21;
    }
  }

  // 领奖按钮
  .receive-prize-button {
    padding: 5px 12px;

    // 按钮内部的容器
    & > div {
      font-size: 16px;
      font-weight: bold;
    }

    // 可以领奖状态
    &.have-prize {
      background-color: #FF605E;
    }

    // 未达标状态
    &.no-prize {
      background-color: transparent;

      & > div {
        color: #BFC8CF;
      }
    }
    &.received-green {
      background-color: transparent;

      & > div {
        color: #73D13D;
      }
    }
  }
}

// 奖品容器
.prize-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  // 勋章盒子
  .medal-box {
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    // 勋章图片
    & > img {
      max-width: 40px;
      max-height: 40px;
      display: block;
    }
  }

  // 没有获得时的灰色
  &.no-get img:first-child {
    filter: grayscale(100%);
  }
}

// 动画光效图片
.light-img {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 50px;
  height: 50px;
  pointer-events: none;
  z-index: 1;
}

// 旋转动画
.ring {
  animation: ring-combo 2s linear infinite;
}

@keyframes ring-combo {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

// 里程碑打卡的奖励时间轴
.milestone-prize-box {
  padding-left: 6px;
}

// 时间轴上已完成的图片样式
.finish-img {
  width: 24px;
  height: 24px;
  border: 2px solid #fff;

  // 完成且已领取过的灰色样式
  &.grey {
    border: 0;
  }
}

// 全局加载中
.loading-box {
  width: 100%;
  height: 129px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  & > span {
    margin-top: 10px;
  }
}
