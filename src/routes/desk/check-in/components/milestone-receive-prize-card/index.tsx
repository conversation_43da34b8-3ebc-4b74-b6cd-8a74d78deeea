import React, { useEffect, useRef, useState } from "react";
import { SpinLoading, Toast } from "antd-mobile";
import Style from "./index.module.scss";
import HorizontalTimeLine, {
  IHorizontalTimeLineConfig,
} from "@/components/horizontal-time-line";
import { Button } from "@/components/button";
import FinishImg from "@/assets/common/finish-package.png";
import ReceivedPrizeIconImg from "@/assets/common/received-prize-icon.png";
import { clickPv, cls, openRoute, versionCompare } from "@/utils/tool";
import SafeLogger from "@/utils/safe-logger";
import {
  getClockInPrizeConfig,
  IClockInPrizeConfigRes,
  IClockInPrizeList,
} from "@/service/self-learning/drawer";
import {
  ReceivePrizeCommonPopup,
  IPopupRef as IReceiveCommonRef,
} from "@/components/receive-prize-common-popup";
import { NetworkErrorStatus } from "@/components/empty-status";
import { MedalModal } from "./medal-modal";
import mstJsBridge from "mst-js-bridge";
import { ErrorCodeEnum } from "@/utils/constant";
import CheckInMilestonePng from "@/assets/image/check-in/three-grade-title.png";
import LightPng from "@/assets/image/check-in/light.png";
import { useVisibilitychange } from "@/hooks";
import { EVENT_CODE } from "../../context";

const logger = new SafeLogger("check-in");

export interface IPopupRef {
  refresh: () => void;
}

export const MilestoneReceivePrizeCardComponent = React.forwardRef<
  IPopupRef,
  object
>(function MilestoneReceivePrizeCardComponent(props, ref) {
  // 奖励的配置列表数据
  const [showList, setShowList] = useState<IHorizontalTimeLineConfig[]>();
  // 通用领奖popup ref
  const commonReceiveRef = React.useRef<IReceiveCommonRef>();
  const [receiveBaseInfo, setReceiveBaseInfo] = useState<any>();
  const [isNetworkError, setIsNetworkError] = useState(false);
  const [loading, setLoading] = useState(false);
  const pageBaseInfo = useRef({
    version: "",
    initialSlide: 0,
  });
  const medalModalRef = useRef<any>();
  const [medalModalInfo, setMedalModalInfo] = useState({
    image: "",
    isGrey: false,
  });
  // 没有e游记nodeId节点类型错误，默认为false，根据getClockInPrizeConfig接口返回决定
  const [noNodeIdError, setNoNodeIdError] = useState(false);
  const isReceivePrizeRef = useRef(false);
  const [checkInDays, setCheckInDays] = useState<number>(0);

  /** 把弹窗的调度能力暴露出去 */
  React.useImperativeHandle(ref, () => {
    return {
      refresh: initData,
    };
  }, []);

  // 判断是否能领奖
  // 奖励等级小于等于能领取的最大等级，且等于已完成的下一级
  // 代表之前的都已经领取，并且当前奖励未领取
  const canIReceivePrize = (
    item: IClockInPrizeList,
    rest: IClockInPrizeConfigRes,
  ) =>
    item.level > rest.receivedMaxLevel && item.level <= rest.finishedMaxLevel;

  // 生成领取按钮文案
  const makeReceiveButtonText = (
    item: IClockInPrizeList,
    rest: IClockInPrizeConfigRes,
  ) => {
    if (canIReceivePrize(item, rest)) {
      return "领奖";
    }
    if (item.level <= rest.receivedMaxLevel) {
      return "已领";
    }
    return "未达标";
  };

  const makePrizeList = (receiveInfo: IClockInPrizeConfigRes) => {
    const { milestoneLevelConfigVOs, ...rest } = receiveInfo;
    const newList = [];
    const dataList = Array.isArray(milestoneLevelConfigVOs)
      ? milestoneLevelConfigVOs
      : [];
    // 后端返回的列表顺序有可能不是正排的，使用sort做任务等级的简单排序
    dataList.sort((a, b) => a?.level - b?.level);
    // 是否已经设置过定位了
    // 比如第一个设置过了，且索引是0，第二个也需要定位，是否感叹号判断时会认为没设置过
    let isSet = false;
    // 找到第一个可领取的勋章索引
    let firstCanReceiveIndex = -1;
    dataList.forEach((item: IClockInPrizeList, index: number) => {
      if (canIReceivePrize(item, receiveInfo) && firstCanReceiveIndex === -1) {
        firstCanReceiveIndex = index;
      }
    });

    // 组装时间轴需要的数据list
    dataList.forEach((item: IClockInPrizeList, index: number) => {
      const havePrizeStatus = canIReceivePrize(item, receiveInfo);

      // 如果当前奖励等级大于已领取的等级，就是灰色的
      const imageIsGrey = item.level > rest.receivedMaxLevel;
      // 如果有奖励，且不是第一个，需要定位到指定的位置
      if (havePrizeStatus && !pageBaseInfo.current.initialSlide && !isSet) {
        isSet = true;
        pageBaseInfo.current.initialSlide = index;
      }

      // 时间轴上的标志点为状态，如果当前等级小于等于已完成的最大等级，就都是对勾
      // 至于是灰色还是绿色用其他字段判断
      const circleStatus =
        item.level <= rest.finishedMaxLevel ? FinishImg : null;
      // 完成标志是否灰色：当前等级小于等于已经领取的等级
      const circleIsGrey = item.level > rest.finishedMaxLevel;

      newList.push({
        topContent: (
          <div
            className={cls([
              Style["prize-box"],
              imageIsGrey && Style["no-get"],
            ])}
          >
            <span>打卡{item.targetValue}天</span>
            <div className={Style["medal-box"]}>
              <img
                src={item?.mainImgUrl}
                alt=""
                onClick={() => {
                  setMedalModalInfo({
                    image: item?.mainImgUrl,
                    isGrey: imageIsGrey,
                  });
                  medalModalRef?.current?.setVisible(true);
                }}
              />
              {/* 为第一个可领取的勋章添加动画效果 */}
              {havePrizeStatus && index === firstCanReceiveIndex && (
                <img
                  src={LightPng}
                  alt=""
                  className={cls([Style["light-img"], Style["ring"]])}
                />
              )}
            </div>
          </div>
        ),
        bottomContent: (
          <Button
            onClick={() => handlePrizeClick(item, receiveInfo)}
            text={makeReceiveButtonText(item, receiveInfo)}
            className={cls([
              Style["receive-prize-button"],
              havePrizeStatus ? Style["have-prize"] : Style["no-prize"],
              item.level <= rest.receivedMaxLevel && Style["received-green"],
            ])}
          />
        ),
        customCircle: circleStatus && (
          <img
            src={circleIsGrey ? ReceivedPrizeIconImg : circleStatus}
            alt="完成"
            className={cls([
              Style["finish-img"],
              circleIsGrey && Style["grey"],
            ])}
          />
        ),
        otherData: {
          ...item,
          ...rest,
        },
      });
    });

    return newList;
  };

  // 数据初始化
  const initData = async () => {
    const eventCode = EVENT_CODE.THREE_GRADE_CHECK_IN;
    try {
      setLoading(true);
      const { data } = await getClockInPrizeConfig({
        eventCode,
        ignoreError: true,
      });
      if (
        data?.taskId ||
        data?.nodeId ||
        !!data?.milestoneLevelConfigVOs?.length
      ) {
        const timelineList = makePrizeList(data);
        // 设置累积打卡天数
        setCheckInDays(data.processNum || 0);
        setIsNetworkError(false);
        setShowList(timelineList);
      } else {
        // 如果没有任务、节点、配置信息的话，就显示网络异常，因为后续没法走流程了
        setIsNetworkError(true);
      }
    } catch (error) {
      setIsNetworkError(true);
      if (error?.code === ErrorCodeEnum.NO_NODE_ID) {
        setNoNodeIdError(true);
      } else {
        Toast.show(error?.msg || error?.message || error);
      }
      logger?.warn("get-three-grade-config-warn", {
        reason: "警告:高中三年累计打卡获取失败",
        error,
        eventCode,
      });
    } finally {
      setLoading(false);
    }
  };

  // 领奖+未达标点击事件
  const handlePrizeClick = (
    prizeInfo: IClockInPrizeList,
    restConfig: IClockInPrizeConfigRes,
  ) => {
    if (isReceivePrizeRef.current) {
      return;
    }
    isReceivePrizeRef.current = true;
    clickPv("ewt_h5_base_plan_desk_check_in_page_get_prize_button_click", {
      scene: "高中三年打卡",
    });
    if (canIReceivePrize(prizeInfo, restConfig)) {
      if (prizeInfo.level !== (restConfig?.receivedMaxLevel || 0) + 1) {
        Toast.show("不要着急，奖品要按顺序一个个领取哦～");
        isReceivePrizeRef.current = false;
        return;
      }
      // 如果在app内，且拿到了版本号，且版本号是目标版本, 且有节点信息
      if (
        mstJsBridge.isInMstApp() &&
        pageBaseInfo.current.version &&
        versionCompare(pageBaseInfo.current.version, "10.6.0") &&
        restConfig?.nodeId
      ) {
        const { nodeId, taskId, taskRewardLevelId } = restConfig;
        // 这里是要调用app，因此直接延迟1秒再释放锁
        window.setTimeout(() => {
          isReceivePrizeRef.current = false;
        }, 1000);
        openRoute({
          domain: "user",
          action: "task_rewards",
          params: {
            // 接口如果没返回就传递一个0，原因是app内部没有对这个字段的缺省做处理，不传会导致app的crash
            nodeId: nodeId || 0,
            taskId,
            // 原因同上
            taskRewardLevelId: taskRewardLevelId || 1,
            processNum: prizeInfo.rewardValue || 0,
            moduleName: "学生自主学习计划",
            rewardStatus: 1,
          },
        });
        // 如果去app领取就不继续执行h5的领取了
        return;
      }

      setReceiveBaseInfo({
        ...restConfig,
        ...prizeInfo,
      });
      if (commonReceiveRef?.current) {
        commonReceiveRef.current?.setVisible(true);
      }
      isReceivePrizeRef.current = false;
    } else {
      isReceivePrizeRef.current = false;
    }
  };

  // 获取app版本
  const getAppVersion = async () => {
    try {
      // 这里不用通用的方法，以为是需要异步即可，不用同步锁
      const { data } = await mstJsBridge.getAppVersion();
      if (data?.version) {
        pageBaseInfo.current.version = data.version;
      }
    } catch (error) {
      // 不是核心链路，失败了就从h5领取，因此通过arms收集即可
      console.warn("获取app版本号失败", error);
    }
  };

  useEffect(() => {
    getAppVersion();
    initData();
  }, []);

  // 打开app后再回来刷新下奖励状态
  const handleRefreshConfig = (isShow: boolean) => isShow && initData();

  // 页面的可见/不可见监控，变更后查询最新的奖励状态
  useVisibilitychange({
    handleVisibilitychange: handleRefreshConfig,
  });

  // 如果没有奖励列表（比如异常了）就不展示该组件内容了
  if (!showList?.length) {
    return null;
  }

  return (
    <div className={Style["milestone-receive-prize-box"]}>
      <img
        src={CheckInMilestonePng}
        alt=""
        className={Style["check-in-milestone-title"]}
      />
      <div className={Style["check-in-milestone-content"]}>
        已累计打卡
        <span className={Style["check-in-milestone-days"]}>{checkInDays}</span>
        天，每一天我们都更接近梦想
      </div>

      {loading && (
        <div className={Style["loading-box"]}>
          <SpinLoading color="primary" />
          <span>加载中...</span>
        </div>
      )}

      {/* 如果是网络错误、数据不符合预期就展示错误界面等待刷新 */}
      {isNetworkError && !loading ? (
        <NetworkErrorStatus
          text={noNodeIdError ? "任务暂不支持查看和领取奖励，请稍后重试" : ""}
          buttonOption={{
            handleClick: () => initData(),
          }}
        />
      ) : null}

      {/* 如果数据正常，且加载完毕就展示横向时间轴的奖励配置 */}
      {!isNetworkError && !!showList?.length && !loading && (
        <HorizontalTimeLine
          initialSlide={pageBaseInfo.current.initialSlide}
          list={showList}
          className={Style["milestone-prize-box"]}
        />
      )}

      {/* 前端H5的领取奖励抽屉 */}
      <ReceivePrizeCommonPopup
        ref={commonReceiveRef}
        receiveBaseInfo={receiveBaseInfo}
        closeBefore={() => initData()}
      />

      <MedalModal ref={medalModalRef} {...medalModalInfo} />
    </div>
  );
});
