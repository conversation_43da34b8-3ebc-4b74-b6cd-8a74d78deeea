import { Mask } from "antd-mobile";
import React from "react";
import styles from "./index.module.scss";
import CloseImg from "@/assets/common/close-black-aircle.png";
import MedalDetail from "./medal-detail";
import { IMedalAward } from "@/service/desk/check-in";

interface ICustomizeMedalModal {
  visible?: boolean;
  onCancel: () => void;
  medalInfo: IMedalAward;
}

const CustomizeMedalModal: React.FC<ICustomizeMedalModal> = (props) => {
  const { visible, onCancel, medalInfo } = props;

  return (
    <Mask visible={visible} className={styles.customizeMedalModalMask}>
      <div className={styles.customizeMedalModalContainer}>
        <img
          className={styles.closeBtn}
          src={CloseImg}
          alt="关闭"
          onClick={onCancel}
        />
        <div className={styles.outerBox}>
          <div className={styles.innerLayerBox}>
            {!!medalInfo && (
              <MedalDetail medalInfo={medalInfo} onCancel={onCancel} />
            )}
          </div>
        </div>
      </div>
    </Mask>
  );
};

export default CustomizeMedalModal;
