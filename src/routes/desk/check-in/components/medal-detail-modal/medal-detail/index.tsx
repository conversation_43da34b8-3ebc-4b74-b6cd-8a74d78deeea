import React from "react";
import { IMedalData } from "@/service/medal";
import styles from "./index.module.scss";
import { cls } from "@/utils/tool";
import LevelFrameImg from "@/assets/medal/level-frame.png";
import LoadingImage from "@/components/loading-image";
import MedalDefaultPng from "@/assets/medal/medal-default.png";
import MedalFlowerPng from "@/assets/image/check-in/sa-hua.png";

import { IMedalAward } from "@/service/desk/check-in";

interface IMedalDetail {
  medalInfo: IMedalAward;
  onCancel: () => void;
}

const MedalDetail: React.FC<IMedalDetail> = (props) => {
  const { medalInfo, onCancel } = props;

  return (
    <div className={styles.medalListBox}>
      <div className={styles.medalDetailContainer}>
        {/* 勋章图、勋章等级展示区域 */}
        <div className={styles.medalImageBox}>
          <LoadingImage
            src={medalInfo.mainImgUrl}
            fallback={MedalDefaultPng}
            className={styles.medalImg}
          />

          {/* 勋章组时才显示勋章等级 */}
          {Boolean(medalInfo.level) && (
            <div className={styles.levelFrameBox}>
              <img src={LevelFrameImg} alt="" className={styles.levelFrame} />
              <span>LV{medalInfo.level}</span>
            </div>
          )}
          <img src={MedalFlowerPng} alt="" className={styles.medalBg} />
        </div>

        {/* 勋章信息区域，包括名称、说明、编号等 */}
        {medalInfo && (
          <div className={styles.medalInfo}>
            <p className={styles.medalNameBox}>
              <span className={styles.medalName}>{medalInfo.name}</span>
            </p>
            <p className={styles.medalDescribe}>{medalInfo.medalDescribe}</p>
          </div>
        )}
      </div>
      {/* 按钮 */}
      <button
        onClick={onCancel}
        className={cls([styles.confirmButton, styles.active, styles.cancel])}
      >
        收进我的勋章
      </button>
    </div>
  );
};

export default React.memo(MedalDetail);
