import React, { Fragment, useRef, useCallback } from "react";
import { IconSvg } from "@/components";
import { clickPv, cls, debounce } from "@/utils/tool";
import { IPostDeskCheckInRes } from "@/service/desk/check-in";
import { IPopupRef as IRuleMarkDrawerRef } from "../rule-mark-drawer";
import RuleMarkDrawer from "../rule-mark-drawer";
import { STUDY_DURATION } from "../../context";

import Styles from "./index.module.scss";

interface ICheckInButtonProps {
  /** 打卡信息 */
  checkInInfo?: IPostDeskCheckInRes;
  /** 打卡处理函数 */
  onCheckIn: () => void;
}

const CheckInButton: React.FC<ICheckInButtonProps> = ({
  checkInInfo,
  onCheckIn,
}) => {
  const ruleMarkDrawerRef = useRef<IRuleMarkDrawerRef>();
  // 是否已打卡
  const { checkInStatus, studyDuration, targetDuration } = checkInInfo;
  const isCheckIn = !!checkInStatus;

  // 未打卡场景才去调用打卡接口
  const handleCheckIn = useCallback(
    debounce(() => {
      if (!isCheckIn) {
        onCheckIn();
      } else {
        clickPv("ewt_h5_base_plan_desk_check_in_page_check_in_button_click", {
          status: "已打卡",
        });
      }
    }, 300),
    [isCheckIn, onCheckIn],
  );

  return (
    <Fragment>
      <div className={Styles["check-in-button-container"]}>
        <div className={Styles["button-box"]}>
          <div
            className={cls([
              Styles["check-in-button"],
              isCheckIn && Styles["check-in-button-disabled"],
            ])}
            onClick={() => handleCheckIn()}
          >
            {isCheckIn ? <IconSvg name="icon-a-wancheng2x1" /> : null}
            <span>{isCheckIn ? "已打卡" : "打卡"}</span>
          </div>
        </div>

        <div className={Styles["study-time-box"]}>
          {/* 已经打完卡或者学习时长已满 皆显示已学满目标时长 */}
          {isCheckIn || +studyDuration >= +targetDuration ? (
            <>
              <span>今日已学满</span>
              <span className={Styles["study-time-number"]}>
                {targetDuration || STUDY_DURATION}
              </span>
              分钟
            </>
          ) : (
            <span>
              再学
              <span className={Styles["study-time-number"]}>
                {/* 接口正常返回时计算目标和已学时长的差值，如果异常则显示兜底15 */}
                {/* 再学x分钟的时长需要向上取整 */}
                {+targetDuration && +studyDuration
                  ? Math.ceil(+targetDuration - +studyDuration)
                  : STUDY_DURATION}
              </span>
              分钟可打卡
            </span>
          )}
          <IconSvg
            name="icon-wenhao"
            onClick={() => ruleMarkDrawerRef.current.setVisible(true)}
          />
        </div>
      </div>

      <RuleMarkDrawer ref={ruleMarkDrawerRef} targetTime={targetDuration} />
    </Fragment>
  );
};

export default CheckInButton;
