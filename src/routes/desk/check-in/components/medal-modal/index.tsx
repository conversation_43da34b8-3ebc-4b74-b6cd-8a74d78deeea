import React, { Fragment, useEffect } from "react";
import { Mask } from "antd-mobile";
import CloseIcon from "../close-icon";
import solarLunar from "solarlunar";
import { ClosePositionEnum } from "../../types.d";
import ModalHeaderPng from "@/assets/image/check-in/check-in-popup-medal-top.png";

import Styles from "./index.module.scss";
import dayjs from "dayjs";
import { WEEK_MAP } from "../../context";
import LoadingImage from "@/components/loading-image";
import MedalDefaultPng from "@/assets/medal/medal-default.png";
import LevelFrameImg from "@/assets/medal/level-frame.png";
import MedalFlowerPng from "@/assets/image/check-in/sa-hua.png";
import { IconSvg } from "@/components";
import { clickPv, cls, expPv } from "@/utils/tool";
import { IMedalAward } from "@/service/desk/check-in";

export interface ICheckInMedalModalProps {
  visible: boolean;
  onCancel: (position: ClosePositionEnum) => void;
  medalInfo: IMedalAward;
  serverTimestamp: number;
  from: string;
}

const CheckInMedalModal: React.FC<ICheckInMedalModalProps> = (props) => {
  const { visible, onCancel, medalInfo, serverTimestamp, from } = props;
  const time = +medalInfo?.awardDate || serverTimestamp;
  const dayNum = dayjs(time).format("D");
  const month = dayjs(time).format("MM");
  const year = dayjs(time).format("YYYY");
  const week = dayjs(time).day();
  const weekStr = WEEK_MAP[week];
  const lunarTime = solarLunar.solar2lunar(year, month, dayNum);

  useEffect(() => {
    if (visible) {
      expPv("ewt_h5_base_plan_desk_check_in_page_medal_modal_expo", {
        from,
      });
    }
  }, [visible]);

  return (
    <Mask visible={visible} className={Styles.medalModalMask}>
      <div className={Styles.medalModalContainer}>
        <img src={ModalHeaderPng} alt="" className={Styles.modalHeader} />
        <div className={Styles.modalContentBox}>
          <div className={Styles.modalContent}>
            <div className={Styles.dailyTime}>
              {/* 日签 - 公历信息 */}
              <p className={Styles.currentTime}>
                <span className={Styles.day}>{dayNum}</span>
                <span className={Styles.month}>/{month}</span>
                <span className={Styles.week}>{weekStr}</span>
              </p>
              {/* 日签 - 农历信息 */}
              <p className={Styles.lunarTime}>
                农历
                {`${lunarTime.monthCn}${lunarTime.dayCn}`}
              </p>
            </div>

            <div className={Styles.medalContent}>
              {/* 勋章信息区域，包括名称、说明、编号等 */}

              {medalInfo && (
                <Fragment>
                  <div className={Styles.medalImageBox}>
                    <LoadingImage
                      src={medalInfo.mainImgUrl}
                      fallback={MedalDefaultPng}
                      className={Styles.medalImg}
                    />

                    {/* 勋章组时才显示勋章等级 */}
                    {Boolean(medalInfo.level) && (
                      <div className={Styles.levelFrameBox}>
                        <img
                          src={LevelFrameImg}
                          alt=""
                          className={Styles.levelFrame}
                        />
                        <span>LV{medalInfo.level}</span>
                      </div>
                    )}
                    <img
                      src={MedalFlowerPng}
                      alt=""
                      className={Styles.medalBg}
                    />
                  </div>

                  <div className={Styles.medalInfo}>
                    <p className={Styles.medalNameBox}>
                      <span className={Styles.medalName}>{medalInfo.name}</span>
                    </p>

                    {/* 勋章描述 */}
                    {medalInfo?.medalDescribe && (
                      <p className={Styles.medalDescribe}>
                        <IconSvg name="icon-a-wancheng2x1" />
                        {medalInfo.medalDescribe}
                      </p>
                    )}
                  </div>
                </Fragment>
              )}
            </div>
          </div>
        </div>
        {/* 按钮 */}
        <button
          onClick={() => {
            clickPv(
              "ewt_h5_base_plan_desk_check_in_page_medal_modal_close_click",
              {
                from,
                button: "领取勋章",
              },
            );
            onCancel(ClosePositionEnum.CONFIRM_BUTTON);
          }}
          className={cls([Styles.confirmButton, Styles.active, Styles.cancel])}
        >
          收进我的勋章
        </button>
        <CloseIcon
          className={Styles.closeIcon}
          onCancel={() => {
            clickPv(
              "ewt_h5_base_plan_desk_check_in_page_medal_modal_close_click",
              {
                from,
                button: "关闭",
              },
            );
            onCancel(ClosePositionEnum.CLOSE_BUTTON);
          }}
        />
      </div>
    </Mask>
  );
};

export default CheckInMedalModal;
