.medal-modal-mask {
  z-index: 1050;
}

.medal-modal-container {
  width: 335px;
  height: 500px;
  max-height: 70vh;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -53%);
  z-index: 1;
  border-radius: 20px;
  overflow: visible;

  .modal-header {
    width: 335px;
    display: block;
  }

  .modal-content-box {
    overflow: hidden;
    background-image: linear-gradient(179deg, #ffe46a 0%, #ffe46a 30%);
    margin-top: -1px;
    border-radius: 0 0 20px 20px;

    .modal-content {
      margin: 6px;
      background-color: #fff;
      border-radius: 20px;
      padding-bottom: 20px;
      background-image: linear-gradient(180deg, #FFFFFF 0%, #FFE4E4 100%);

      .daily-time {
        text-align: center;
        padding-top: 15px;

        .current-time {
          font-size: 12px;
          color: #333333;
          line-height: 1;

          .day {
            font-size: 30px;
          }

          .month {
            font-size: 14px;
          }

          .week {
            margin-left: 4px;
          }
        }

        .lunar-time {
          font-size: 12px;
          color: #8a8a8a;
          margin-top: 3px;
        }
      }
    }
  }

  .close-icon {
    position: absolute;
    right: 0;
    top: -52px;
  }
}

// 勋章内容
.medal-content {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  .medal-info {
    font-size: 14px;
    text-align: center;
    margin-top: 16px;

    .medal-name-box {
      display: flex;
      align-items: center;
      justify-content: center;
      max-width: 280px;

      .medal-name {
        font-weight: bold;
        font-size: 18px;
        color: rgba(0, 0, 0, 0.8);
        line-height: 18px;
        // 超出省略
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        & + span {
          display: block;
          padding: 0 5px;
          height: 18px;
          line-height: 18px;
          color: #fff;
          margin-left: 4px;
          background-image: linear-gradient(270deg, #fd5e40 1%, #fe3649 99%);
          border-radius: 4px;
        }
      }
    }

    .medal-code {
      color: rgba(0, 0, 0, 0.45);
      line-height: 12px;
      min-height: 14px;
      font-size: 12px;
      margin-top: 8px;
    }

    .medal-describe {
      color: rgba(0, 0, 0, 0.85);
      margin-top: 24px;
      max-width: 280px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 16px;
      // 超出省略
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      svg {
        margin-right: 5px;
      }
    }

    .medal-get-time {
      color: rgba(0, 0, 0, 0.45);
      font-size: 12px;
      margin-top: 4px;
      min-height: 16.8px;
    }

    .no-get-medal {
      height: 84px;
    }
  }

  .medal-image-box {
    width: 240px;
    height: 240px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    .medal-bg {
      position: absolute;
      top: 15px;
      left: 50%;
      width: 323px;
      transform: translateX(-50%);
    }

    .medal-img {
      max-width: 240px;
      max-height: 240px;
      background-color: transparent;

      &.grey {
        filter: grayscale(100%);
      }
    }

    .level-frame-box {
      width: 48px;
      height: 18px;
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      z-index: 1;

      .level-frame {
        width: 48px;
        height: 18px;
        position: absolute;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%);

        & + span {
          position: absolute;
          bottom: 0;
          display: block;
          left: 50%;
          width: 48px;
          height: 18px;
          transform: translateX(-50%);
          text-align: center;
          line-height: 18px;
          font-size: 14px;
          color: #fff;
          font-weight: bold;
        }
      }
    }
  }
}

// 确认按钮
.confirm-button {
  outline: none;
  width: 180px;
  height: 48px;
  border-radius: 24px;
  background-color: rgba(0, 0, 0, .5);
  border: 1px solid rgba(255, 255, 255, 0.8);
  color: #fff;
  font-size: 18px;
  font-weight: bold;
  position: absolute;
  z-index: 2;
  left: 50%;
  bottom: -98px;
  transform: translate(-50%, -50%);

  &.active {
    background-image: linear-gradient(270deg, #FD5E40 1%, #FE3649 99%);
    box-shadow: 0 0 12px 0 #FD5C41;
  }

  &.cancel {
    background-color: rgba(0, 0, 0, 0.8);
    border-width: 1px;
    border-style: solid;
    border-color: rgba(255, 255, 255, 0.8);
    border-radius: 24px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: bold;
    font-size: 18px;
  }
}
