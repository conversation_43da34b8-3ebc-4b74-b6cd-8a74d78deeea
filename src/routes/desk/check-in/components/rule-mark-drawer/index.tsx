import * as React from "react";
import Style from "./style.module.scss";
import { Popup } from "@/components";
import { STUDY_DURATION } from "../../context";

interface IRuleMarkDrawerProps {
  targetTime: number;
}

export interface IPopupRef {
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
}

const RuleMarkDrawer = React.forwardRef<IPopupRef, IRuleMarkDrawerProps>(
  (props, ref) => {
    const { targetTime } = props;
    const [visible, setVisible] = React.useState(false);

    /** 把弹窗的调度能力暴露出去 */
    React.useImperativeHandle(ref, () => {
      return {
        setVisible,
      };
    }, []);

    return (
      <Popup
        visible={visible}
        onClose={() => setVisible(false)}
        title="打卡说明"
      >
        <div className={Style["rule-mark-popup-content"]}>
          <div className={Style["text-container"]}>
            当日学满{targetTime || STUDY_DURATION}
            分钟即可进行打卡；学习时长统计存在5分钟左右的延迟，请耐心等待
          </div>
          <div
            className={Style["footer-button"]}
            onClick={() => setVisible(false)}
          >
            我知道了
          </div>
        </div>
      </Popup>
    );
  },
);

RuleMarkDrawer.displayName = "RuleMarkDrawer";

export default RuleMarkDrawer;
