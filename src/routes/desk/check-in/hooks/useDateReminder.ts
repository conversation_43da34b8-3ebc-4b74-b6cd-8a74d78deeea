import * as React from "react";
import { Toast } from "antd-mobile";
import { getNativeData, getUserInfo } from "@/utils/tool";
import mstJsBridge from "mst-js-bridge";
import dayjs from "dayjs";
import { CalenderTypeEnum } from "../components/date-reminder";

const KEY = "desk-check-in-date-reminder";

/**
 * 本地存储内的日历信息
 * @param id 本地日历的id
 * @param start_date 日历的开始时间
 * @param end_date 日历的结束时间
 * @param isOnlyWeek 是否仅周六日提醒，默认是false
 * @param popupTime 挽留底抽弹窗时间，一周弹出一次，需要用来计算时间
 * @param popoverTip 打卡提醒的Popover弹出时间
 */
export interface ICalenderStorageCache {
  id?: string;
  start_date?: string;
  end_date?: string;
  isOnlyWeek?: boolean | string;
  popupTime?: number;
  popoverTip?: number;
}

export function useDateReminder() {
  const keyRef = React.useRef<string>();
  const userId = getUserInfo();
  if (userId) {
    const key = `${KEY}-${userId}`;
    keyRef.current = key;
  }
  const [loading, setLoading] = React.useState<boolean>(false);
  const [dateRange, setDateRange] = React.useState<{
    id: string;
    start_date: string;
    end_date: string;
    isOnlyWeek: boolean | string;
    popupTime: number;
    popoverTip: number;
  } | null>();
  /**
   * 向本地存储内持久化新的日历信息
   * @param params 需要存储的日历信息
   */
  function saveCalenderInfo(params: ICalenderStorageCache = {}) {
    if (keyRef.current) {
      // 默认的缓存信息
      const defaultCacheObj = {
        id: "", // 日历id，默认为空，当设置时会存
        start_date: "", // 开始时间，默认为空，当用户存储过会有
        end_date: "", // 结束时间，默认为空，当用户存储过会有
        isOnlyWeek: false, // 是否仅周六日提醒，默认是false
        popupTime: 0, // 弹窗时间，一周弹出一次，需要用来计算时间
        popoverTip: 0, // 打卡提醒的Popover弹出时间
      };
      const tempCacheStr: string | undefined = localStorage.getItem(
        keyRef.current,
      );
      let tempCacheData = {};
      if (tempCacheStr) {
        tempCacheData = JSON.parse(tempCacheStr);
      }
      const newParams = {
        ...defaultCacheObj,
        ...tempCacheData,
        ...(params || {}),
      };
      localStorage.setItem(keyRef.current, JSON.stringify(newParams || {}));
    }
  }

  const removeCache = () => {
    setDateRange(null);
    // 如果日历不存在了那需要清空其他信息，但要保留对象
    // 原因：退出时的挽留信息依赖的popupTime信息还需要存在
    // 也可以理解为但凡存储过那就一直存在一个缓存信息，不会完全清空
    saveCalenderInfo({
      id: "",
      start_date: "",
      end_date: "",
      isOnlyWeek: false,
    });
  };

  /**
   * 获取日历范围
   * 如果从app获取该日历不存在，就清空本地缓存
   * */
  async function getDate(fn?: (params: ICalenderStorageCache) => void) {
    try {
      if (!keyRef.current) {
        return;
      }
      const res = localStorage.getItem(keyRef.current);
      if (res) {
        const dateRangeCache = JSON.parse(res);
        if (dateRangeCache?.id) {
          setLoading(true);
          const res = (await getNativeData({
            domain: "web",
            action: "get_schedule",
            params: {
              identifier: dateRangeCache.id,
            },
          })) as { code: number; data?: { identifier: string } };
          /** 如果获取日历失败，就清空之前的缓存 */
          if (res.code !== 200) {
            removeCache();
            fn?.({});
          } else {
            setDateRange(dateRangeCache);
            fn?.(dateRangeCache);
          }
        } else {
          // 如果没有日历信息那就认为未设置过日历
          removeCache();
          fn?.({});
        }
      } else {
        // 如果没有日历信息那就认为未设置过日历
        removeCache();
        fn?.({});
      }
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false);
    }
  }
  /** 删除日历 */
  async function deleteDate(showToast?: boolean, keepLoading?: boolean) {
    try {
      if (dateRange?.id) {
        mstJsBridge.config.isReckonTime = false;
        setLoading(true);
        const res = (await getNativeData({
          domain: "web",
          action: "cancel_schedule",
          params: {
            identifier: dateRange.id,
          },
        })) as { code: number };
        if (res?.code === 200 && showToast) {
          // 清理掉原有的日历id
          saveCalenderInfo({
            id: "",
            start_date: "",
            end_date: "",
            isOnlyWeek: false,
          });
          /** 删除后，重置提醒范围 */
          setDateRange(null);
          Toast.show("删除成功!");
        }
      }
    } catch (err) {
      Toast.show("删除失败");
      console.error(err);
    } finally {
      mstJsBridge.config.isReckonTime = true;
      !keepLoading && setLoading(false);
    }
  }
  /** 设置日历 */
  async function setDate(start_date, end_date, isOnlyWeek): Promise<boolean> {
    try {
      setLoading(true);
      // 首次授权场景，不设置响应超时时间
      mstJsBridge.config.isReckonTime = false;
      /** 设置之前，先把之前设置的日历删除掉 */
      await deleteDate(false, true);
      const routeParams: {
        domain: string;
        action: string;
        params: {
          title: string;
          notes: string;
          reminder_minutes: number;
          start_date: string;
          stop_date: string;
          end_date?: string;
          repeat_type?: number;
          daysOfWeek?: string;
        };
      } = {
        domain: "web",
        action: "save_schedule",
        params: {
          title: "【EWT打卡】坚持学习打卡，赢取勋章奖励！",
          notes:
            "学习没有捷径，但坚持必有回报！坚持打卡即可解锁专属勋章奖励。现在打开 APP 点击打卡，让每一次学习都有迹可循，离目标更近一步！",
          reminder_minutes: 10,
          start_date: `${start_date} 9:00:00`,
          stop_date: `${end_date}  22:00:00`,
        },
      };
      // 重复提醒方式：如果是仅周六日提醒，那就设置为2，否则就是1-每日提醒
      routeParams.params.repeat_type =
        isOnlyWeek === CalenderTypeEnum.isOnlyWeek ? 2 : 1;
      if (isOnlyWeek === CalenderTypeEnum.isOnlyWeek) {
        // 仅周六日新增的路由参数，通过竖线分割，仅周六就6，如果是两个就是6|7，其他同理
        routeParams.params.daysOfWeek = "6|7";
        // 得到开始日期是周几，范围是0-6，周日是0，周一到周六是1-6
        const startDateWeek = dayjs(start_date).day();
        // 如果start_date不是周六，那要设置到周六的那一天，比如今天是25号周日，那就要减一天到24号
        // 如果今天是23号周五，那就要加1天到24号周六
        if (startDateWeek !== 6) {
          // 如果是周日，那就要减去一天到周六
          if (startDateWeek === 0) {
            routeParams.params.start_date =
              dayjs(start_date).format("YYYY-MM-DD 9:00:00");
          } else {
            // 周一到周五就直接和6相减，直接得到差值
            const diffDays = 6 - startDateWeek;
            routeParams.params.start_date = dayjs(start_date)
              .add(diffDays, "days")
              .format("YYYY-MM-DD 9:00:00");
          }
        }
      }

      const tempStart = dayjs(routeParams.params.start_date);
      const tempEnd = dayjs(end_date);

      if (
        tempStart.isAfter(tempEnd) &&
        !tempStart.isSame(tempEnd, "day") && // 不是同一天才拦截
        isOnlyWeek === CalenderTypeEnum.isOnlyWeek
      ) {
        Toast.show("所选时间段不含周六日，请重新选择包含周末的时段");
        return;
      }

      const newEndData = dayjs(routeParams.params.start_date);
      routeParams.params.end_date = newEndData.format("YYYY-MM-DD 22:00:00");

      const res = (await getNativeData(routeParams)) as {
        code: number;
        data?: { identifier: string };
      };
      if (res.data?.identifier) {
        const newDateRange = {
          ...dateRange,
          id: res.data.identifier,
          start_date,
          end_date,
          isOnlyWeek,
        };
        saveCalenderInfo(newDateRange);
        setDateRange(newDateRange);
        Toast.show("日历设置成功");
        return true;
      } else {
        console.error("日历设置失败，拿到的信息是：", JSON.stringify(res));
        Toast.show("日历设置失败，如需授权，请允许后重试");
        return false;
      }
    } catch (err) {
      console.error(err);
      return false;
    } finally {
      mstJsBridge.config.isReckonTime = true;
      setLoading(false);
    }
  }
  /**
   * 计算当前日期和目标日期之间的差值，单位是天的维度
   * @param targetTime 目标值是YYYY-MM-DD的格式
   */
  function calcDiffDays(targetTime: string) {
    return dayjs(dayjs().format("YYYY-MM-DD")).diff(dayjs(targetTime), "days");
  }
  /**
   * 检测日历设置状态
   * 判断是否设置过日历和日历是否已过期
   */
  function checkCalenderSetStatus() {
    if (!keyRef.current) {
      return;
    }
    let isSetCalender = false; // 是否设置了日历，默认为false
    let isCalenderExpired = true; // 日历是否过期了，默认为true
    const cacheDataStr: string = localStorage.getItem(keyRef.current) || "{}";
    const cacheData = JSON.parse(cacheDataStr);

    if (cacheData?.end_date) {
      // 有截止日期代表设置过日历，此时重置状态为已设置过的true
      isSetCalender = true;
      const diffDays = calcDiffDays(cacheData.end_date);
      // 拿到当前时间和存储的时间差值，当天和未来都是0~负数，过期后是1开始的正整数
      // 如果没有过期时重置状态到未过期
      if (diffDays <= 0) {
        isCalenderExpired = false;
      }
    }
    return {
      isSetCalender,
      isCalenderExpired,
      cacheData,
    };
  }

  return {
    loading,
    dateRange,
    setDateRange,
    setDate,
    deleteDate,
    getDate,
    calcDiffDays,
    checkCalenderSetStatus,
    saveCalenderInfo,
  };
}
