import { useState } from "react";
import {
  TCheckInSystemConfig,
  ICurrentTimeState,
  MonthPickerValue,
} from "../types.d";

export interface IUseMonthPickerProps {
  systemConfigRef: React.MutableRefObject<TCheckInSystemConfig | undefined>;
  currentTimeRef: React.MutableRefObject<ICurrentTimeState>;
  onMonthChange: (v: MonthPickerValue) => void;
}

export interface IUseMonthPickerReturn {
  // 月份选择器状态
  changeMonthVisible: boolean;
  setChangeMonthVisible: (visible: boolean) => void;

  // 月份选择器配置
  pickerColumns: Array<{ label: string; value: number }[]>;
  pickerValue: [number, number];

  // 月份选择器事件处理
  handleClose: () => void;
  handleConfirm: (v: MonthPickerValue) => void;
}

export function useMonthPicker(
  props: IUseMonthPickerProps,
): IUseMonthPickerReturn {
  const { systemConfigRef, currentTimeRef, onMonthChange } = props;

  // 月份选择器状态
  const [changeMonthVisible, setChangeMonthVisible] = useState(false);

  // 生成月份选择器配置
  const pickerColumns = [
    systemConfigRef.current?.yearList?.map((item) => ({
      label: `${item}年`,
      value: item,
    })) || [],
    systemConfigRef.current?.monthList?.map((item) => ({
      label: `${item}月`,
      value: item,
    })) || [],
  ];

  // 当前选中的值
  const pickerValue: MonthPickerValue = [
    currentTimeRef.current.currentYear,
    currentTimeRef.current.currentMonth + 1,
  ];

  // 关闭月份选择器
  const handleClose = () => {
    setChangeMonthVisible(false);
  };

  // 确认选择月份
  const handleConfirm = (v: MonthPickerValue) => {
    onMonthChange(v);
    setChangeMonthVisible(false);
  };

  return {
    // 月份选择器状态
    changeMonthVisible,
    setChangeMonthVisible,

    // 月份选择器配置
    pickerColumns,
    pickerValue,

    // 月份选择器事件处理
    handleClose,
    handleConfirm,
  };
}
