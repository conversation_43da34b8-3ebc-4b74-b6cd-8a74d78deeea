import { useState, useRef } from "react";
import dayjs from "dayjs";
import {
  getDeskCheckInList,
  ICheckInListAwardConfig,
} from "@/service/desk/check-in";
import {
  TCheckInSystemConfig,
  ICalendarDayItem,
  ICurrentTimeState,
  ICheckInListAwardConfigWithExpired,
} from "../types.d";
import { getDaysInMonth, makeSnakeDays } from "../context";
import { useAPILevel } from "@/hooks";
import { debounce } from "@/utils/tool";
import SafeLogger from "@/utils/safe-logger";

const logger = new SafeLogger("check-in");

export interface IUseCheckInCalendarProps {
  systemConfigRef: React.MutableRefObject<TCheckInSystemConfig | undefined>;
  onCalendarDataChange?: (data: {
    checkInDays: number;
    prizeInfo: ICheckInListAwardConfig[];
    params?: {
      year?: number;
      month?: number;
      days?: number;
      isHandleRefresh?: boolean;
    };
  }) => void;
}

export interface IUseCheckInCalendarReturn {
  // 日历数据
  snakeDays: ICalendarDayItem[];
  setSnakeDays: (days: ICalendarDayItem[]) => void;

  // 当前时间状态
  currentTimeRef: React.MutableRefObject<ICurrentTimeState>;

  // 日历相关方法
  checkInList: (params: {
    year: number;
    month: number;
    days: number;
  }) => Promise<void>;
  handleChangeMonth: (v: [number, number]) => void;
  refreshCalendar: () => void;
  goCurrentMonth: () => void;
  checkIsCurrentMonth: () => boolean;
}

export function useCheckInCalendar(
  props: IUseCheckInCalendarProps,
): IUseCheckInCalendarReturn {
  const { systemConfigRef, onCalendarDataChange } = props;

  // 日历数据
  const [snakeDays, setSnakeDays] = useState<ICalendarDayItem[]>([]);

  // 当前时间状态
  const currentTimeRef = useRef<ICurrentTimeState>({
    currentYear: 0,
    currentMonth: 0,
    currentMonthDays: 0,
    checkInDays: -1,
    prizeInfo: [],
  });

  // 时序控制+点击控制(防止重复点击)
  const { upLevel, getLevelKey } = useAPILevel(1);

  const checkIsCurrentMonth = () => {
    if (!systemConfigRef?.current?.systemTime) {
      return false;
    }
    // 通过服务器端的时间去除年月，再和当前时间的年月对比，不一致则代表不是当前月份
    const serverYear = new Date(
      systemConfigRef.current.systemTime,
    ).getFullYear();
    const serverMonth = new Date(systemConfigRef.current.systemTime).getMonth();
    // 当前年月需要从currentTimeRef.current中获取
    const { currentYear, currentMonth } = currentTimeRef.current;
    return currentYear === serverYear && currentMonth === serverMonth;
  };

  /**
   * 获取打卡日历信息
   * @param params month 需要1-12的月份，days 需要1-31的日期
   * @param isHandleRefresh 是否手动刷新？用户主动刷新需要toast提示
   */
  const checkInList = async (params: {
    year: number;
    month: number;
    days: number;
    isHandleRefresh?: boolean;
  }) => {
    try {
      const levelKey = upLevel(1);
      const { year, month, days } = params;
      const startTime = dayjs(`${year}-${month}-01 00:00:00`).valueOf();
      const endTime = dayjs(`${year}-${month}-${days} 23:59:59`).valueOf();
      const queryParams = {
        startTime,
        endTime,
      };
      const { data } = await getDeskCheckInList(queryParams);
      if (levelKey !== getLevelKey(1)) {
        return;
      }
      const { monthCheckInCount = 0, awardConfigList } = data;
      // 第一个未领取的勋章信息
      const firstNotAwardMedalInfo = awardConfigList?.find(
        (item) => item.awardStatus === 0,
      );
      // 是否已过期
      const isCurrentMonth = checkIsCurrentMonth();
      const tempSnakeDays: ICalendarDayItem[] = Array.from({
        length: days,
      }).map((_, dayIndex: number) => {
        const day = dayIndex + 1;
        const isCheckIn = day <= monthCheckInCount;
        const medalInfo = awardConfigList?.find(
          (item) => +item.targetValue === day,
        );
        if (medalInfo) {
          (medalInfo as ICheckInListAwardConfigWithExpired).isExpired =
            !medalInfo.awardStatus && !isCurrentMonth;
        }
        return {
          day,
          isCheckIn,
          medalInfo: medalInfo as ICheckInListAwardConfigWithExpired,
          isFirstNotAwardMedal: +firstNotAwardMedalInfo?.targetValue === day,
          hasNotAwardMedal: !!firstNotAwardMedalInfo,
        };
      });
      const newSnakeDays = makeSnakeDays(tempSnakeDays as any);
      currentTimeRef.current = {
        ...currentTimeRef.current,
        checkInDays: monthCheckInCount || 0,
        prizeInfo: awardConfigList || [],
      };
      setSnakeDays(newSnakeDays as unknown as ICalendarDayItem[]);

      // 通知父组件数据变化
      onCalendarDataChange?.({
        checkInDays: monthCheckInCount || 0,
        prizeInfo: awardConfigList || [],
        params,
      });
    } catch (error) {
      logger?.warn("check-in-calendar-fail-warn", {
        reason: "警告:获取打卡日历信息失败",
        error,
      });
    }
  };

  const goCurrentMonth = () => {
    // 先判断当前年月和服务器的年月是否一致（比如切换了月份那就不一致了）
    // 如果不一致：需要先将当前年月设置为服务器年月和对应的天数，然后刷新日历
    // 如果一致：直接刷新日历
    const { serverYear, serverMonth } = systemConfigRef.current;
    const { currentYear, currentMonth } = currentTimeRef.current;
    if (serverYear !== currentYear || serverMonth !== currentMonth) {
      currentTimeRef.current = {
        ...currentTimeRef.current,
        currentYear: serverYear,
        currentMonth: serverMonth,
        currentMonthDays: getDaysInMonth(serverYear, serverMonth),
      };
    }
  };

  const refreshCalendar = debounce(() => {
    // 刷新时先回到当前年月
    goCurrentMonth();
    // 重置时间之后再刷新日历
    checkInList({
      year: currentTimeRef.current.currentYear,
      month: currentTimeRef.current.currentMonth + 1,
      days: currentTimeRef.current.currentMonthDays,
      isHandleRefresh: true,
    });
  }, 600);

  // 切换月份
  const handleChangeMonth = (v: [number, number]) => {
    const [year, month] = v;
    const { currentYear, currentMonth } = currentTimeRef.current;
    const newMonth = month - 1;
    // 如果时间一样，则不处理
    if (year === currentYear && newMonth === currentMonth) {
      return;
    }
    currentTimeRef.current = {
      ...currentTimeRef.current,
      currentYear: year,
      currentMonth: newMonth,
    };
    const days = getDaysInMonth(year, newMonth);
    checkInList({
      year,
      month: month,
      days,
    });
  };

  return {
    // 日历数据
    snakeDays,
    setSnakeDays,

    // 当前时间状态
    currentTimeRef,

    // 日历相关方法
    checkInList,
    handleChangeMonth,
    refreshCalendar,
    goCurrentMonth,
    checkIsCurrentMonth,
  };
}
