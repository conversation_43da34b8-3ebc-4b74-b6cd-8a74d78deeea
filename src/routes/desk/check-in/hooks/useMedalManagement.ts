import { useState, useRef } from "react";
import { Toast } from "antd-mobile";
import { clickPv } from "@/utils/tool";
import {
  postDeskReceiveCheckInPrize,
  IMedalAward,
} from "@/service/desk/check-in";
import { ICalendarDayItem } from "../types.d";

export interface IUseMedalManagementProps {
  onMedalReceived?: () => void;
}

export interface IUseMedalManagementReturn {
  // 勋章弹窗状态
  checkInMedalVisible: boolean;
  setCheckInMedalVisible: (visible: boolean) => void;
  receiveMedalVisible: boolean;
  setReceiveMedalVisible: (visible: boolean) => void;

  // 勋章信息
  medalInfo: IMedalAward | undefined;
  setMedalInfo: (info: IMedalAward | undefined) => void;

  // 勋章来源追踪
  medalFrom: string;
  setMedalFrom: (from: string) => void;

  // 勋章相关方法
  receiveMedal: (dayItem: ICalendarDayItem) => Promise<void>;
  handleCheckInMedalClick: (dayItem: ICalendarDayItem) => void;
}

export function useMedalManagement(
  props: IUseMedalManagementProps = {},
): IUseMedalManagementReturn {
  const { onMedalReceived } = props;

  // 勋章弹窗状态
  const [checkInMedalVisible, setCheckInMedalVisible] = useState(false);
  const [receiveMedalVisible, setReceiveMedalVisible] = useState(false);

  // 勋章信息
  const [medalInfo, setMedalInfo] = useState<IMedalAward | undefined>();

  // 勋章来源追踪
  const medalFromRef = useRef("");
  const setMedalFrom = (from: string) => {
    medalFromRef.current = from;
  };

  // 领取勋章
  const receiveMedal = async (dayItem: ICalendarDayItem) => {
    try {
      // 领取勋章或者日签奖励
      const { data: prizeData } = await postDeskReceiveCheckInPrize();
      if (prizeData?.medalAward?.id) {
        setMedalInfo(prizeData.medalAward);
        setReceiveMedalVisible(true);
        onMedalReceived?.();
      }
    } catch (error) {
      console.log("领取勋章失败", error);
    }
  };

  // 处理勋章点击
  const handleCheckInMedalClick = (dayItem: ICalendarDayItem) => {
    const { medalInfo, isCheckIn, isFirstNotAwardMedal, hasNotAwardMedal } =
      dayItem;

    clickPv("ewt_h5_base_plan_desk_check_in_page_get_prize_button_click", {
      scene: "月度打卡挑战",
    });

    if (!isCheckIn) {
      return;
    }
    if (medalInfo.isExpired && !medalInfo.awardStatus) {
      Toast.show("勋章已过期，仅当月可领取");
      return;
    }

    if (!medalInfo.awardStatus && !isFirstNotAwardMedal && hasNotAwardMedal) {
      Toast.show("有勋章未领取");
      return;
    }

    receiveMedal(dayItem);
  };

  return {
    // 勋章弹窗状态
    checkInMedalVisible,
    setCheckInMedalVisible,
    receiveMedalVisible,
    setReceiveMedalVisible,

    // 勋章信息
    medalInfo,
    setMedalInfo,

    // 勋章来源追踪
    medalFrom: medalFromRef.current,
    setMedalFrom,

    // 勋章相关方法
    receiveMedal,
    handleCheckInMedalClick,
  };
}
