import { useState } from "react";
import { Toast } from "antd-mobile";
import { clickPv } from "@/utils/tool";
import { postDeskCheckIn, IPostDeskCheckInRes } from "@/service/desk/check-in";
import { ICurrentTimeState } from "../types.d";
import SafeLogger from "@/utils/safe-logger";

const logger = new SafeLogger("check-in");

// 是否领取过了打卡奖励，领取一次之后再也不弹打卡弹窗了，避免多次点击多个弹窗
// 因为打卡返回勋章接口后端必然能返回日签，或者使用兜底的，因此额外增加一个限制
let isReceivedPrize = false;

export interface IUseCheckInDataProps {
  onCheckInSuccess?: (timeInfo?: ICurrentTimeState) => void;
  onReceivePrize?: (params: {
    isAutoCheckIn?: boolean;
    timeInfo?: ICurrentTimeState;
    fn?: () => void;
  }) => void;
  toastMaskClassName?: string;
}

export interface IUseCheckInDataReturn {
  checkInInfo: IPostDeskCheckInRes;
  handleCheckIn: (params?: {
    isAutoCheckIn?: boolean;
    timeInfo?: ICurrentTimeState;
  }) => Promise<void>;
  setCheckInInfo: (info: IPostDeskCheckInRes) => void;
}

export function useCheckInData(
  props: IUseCheckInDataProps = {},
): IUseCheckInDataReturn {
  const { onCheckInSuccess, onReceivePrize, toastMaskClassName } = props;

  const [checkInInfo, setCheckInInfo] = useState<IPostDeskCheckInRes>(
    {} as IPostDeskCheckInRes,
  );

  const handleCheckIn = async (params?: {
    isAutoCheckIn?: boolean;
    timeInfo?: ICurrentTimeState;
  }) => {
    const { isAutoCheckIn, timeInfo } = params || {};
    try {
      // 如果已经打卡了就不再处理了, 前提不是自动打卡，自动打卡不上报
      // 这里判断连续点击后进入
      if (checkInInfo?.checkInStatus) {
        return;
      }

      const { data } = await postDeskCheckIn({
        // 如果是进入页面后的自动打卡，则忽略错误
        // 如果是手动打卡，需要提示用户错误信息
        ignoreError: !!isAutoCheckIn,
      });

      // 如果是已经领取过了打卡奖励，则不再处理
      // 这里判断连续进入并且请求完接口了
      if (isReceivedPrize) {
        return;
      }

      // 更新打卡信息
      setCheckInInfo(data || ({} as IPostDeskCheckInRes));

      // 如果打卡成功了。需要刷新日历、里程碑、领取奖励
      if (data?.checkInStatus) {
        isReceivedPrize = true;
        clickPv("ewt_h5_base_plan_desk_check_in_page_check_in_button_click", {
          status: "已满足条件未打卡",
        });

        // 调用成功回调
        onCheckInSuccess?.(timeInfo);

        // 领取奖励
        onReceivePrize?.({
          isAutoCheckIn,
          timeInfo,
          fn: () => {
            // 领取奖励后的回调逻辑由父组件处理
          },
        });
      } else {
        if (data?.studyDuration < data?.targetDuration && !isAutoCheckIn) {
          Toast.show({
            content: "今日学习15分钟才能打卡哦~",
            maskClassName: toastMaskClassName,
          });
          clickPv("ewt_h5_base_plan_desk_check_in_page_check_in_button_click", {
            status: "未满足条件不可打卡",
          });
        }
      }
    } catch (error) {
      logger?.warn("check-in-fail-warn", {
        reason: "警告:打卡失败",
        checkInType: isAutoCheckIn ? "自动打卡" : "手动打卡",
        error,
      });
    }
  };

  return {
    checkInInfo,
    handleCheckIn,
    setCheckInInfo,
  };
}
