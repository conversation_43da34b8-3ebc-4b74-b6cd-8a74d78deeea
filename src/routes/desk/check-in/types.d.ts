import {
  ICheckInSystemConfigRes,
  ICheckInListAwardConfig,
  IMedalAward,
  IDaily,
} from "@/service/desk/check-in";

/** 书桌-打卡页-系统配置 */
export type TCheckInSystemConfig = ICheckInSystemConfigRes & {
  /** 当前月份的天数 */
  currentMonthDayCount: number;
  /** 当前年份的列表 */
  yearList: number[];
  /** 当前月份的列表 */
  monthList: number[];
  /** 服务器年 */
  serverYear: number;
  /** 服务器月 */
  serverMonth: number;
};

/** 扩展的勋章配置（包含过期状态） */
export interface ICheckInListAwardConfigWithExpired
  extends ICheckInListAwardConfig {
  /** 是否已过期 */
  isExpired?: boolean;
}

/** 日历天数项 */
export interface ICalendarDayItem {
  /** 天数 */
  day: number;
  /** 是否已打卡 */
  isCheckIn: boolean;
  /** 勋章信息 */
  medalInfo?: ICheckInListAwardConfigWithExpired | null;
  /** 是否为第一个未领取的勋章 */
  isFirstNotAwardMedal?: boolean;
  /** 是否有未领取的勋章 */
  hasNotAwardMedal?: boolean;
}

/** 当前时间状态 */
export interface ICurrentTimeState {
  /** 当前年份 */
  currentYear: number;
  /** 当前月份 */
  currentMonth: number;
  /** 当前月份天数 */
  currentMonthDays: number;
  /** 打卡天数 */
  checkInDays: number;
  /** 奖品信息列表 */
  prizeInfo: ICheckInListAwardConfig[];
}

/** 月份选择器值 */
export type MonthPickerValue = [number, number];

/** 关闭位置 */
export enum ClosePositionEnum {
  FM = "fm",
  // 右上角的关闭按钮
  CLOSE_BUTTON = "close-button",
  // 收进我的勋章按钮
  CONFIRM_BUTTON = "confirm-button",
}
