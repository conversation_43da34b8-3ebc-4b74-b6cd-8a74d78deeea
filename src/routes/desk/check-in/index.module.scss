.check-in-layout {
  background-image: url("@/assets/image/check-in/check-in-bg.png");
  background-size: 100% 220px;
  background-repeat: no-repeat;
  height: 100vh;
  background-color: #F8F8F8;

  .header {
    background: transparent;

    :global {
      .adm-nav-bar-left {
        flex: none;
        width: 20px;
      }

      .adm-nav-bar-title {
        text-align: left;
        padding-left: 0;
        font-weight: bold;
      }

      .adm-nav-bar-right {
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
    }
  }

  .content {
    height: calc(100vh - 12.8vw);
    overflow-x: hidden;
    overflow-y: auto;
    padding-bottom: 100px;

    .month-check-in-container {
      margin: 0 12px;

      // 打卡日历头部
      .month-check-in-header {
        width: 100%;
        height: 60px;
        background-image: url("@/assets/image/check-in/calender-bg-top.png");
        background-size: 100% 72px;
        background-repeat: no-repeat;
        position: relative;
        z-index: 1;

        // 左侧-标题、刷新按钮
        .header-left {
          position: absolute;
          top: 12px;
          left: 16px;
          z-index: 2;

          .left-top {
            display: flex;
            align-items: center;

            .month-check-in-title {
              width: 108px;
            }

            // 刷新按钮和文字
            .left-top-button-box {
              cursor: pointer;

              svg {
                font-size: 12px;
                color: #000;
                margin-left: 12px;
                transition: transform 0.3s ease;
              }

              span {
                font-size: 12px;
                color: #000;
                margin-left: 4px;
              }
            }

            // 刷新图标旋转动画
            .refresh-icon-rotating {
              animation: refresh-rotate 1s linear infinite;
            }

            @keyframes refresh-rotate {
              from {
                transform: rotate(0deg);
              }
              to {
                transform: rotate(360deg);
              }
            }
          }

          // 下方的副标题和说明
          .left-bottom {
            font-size: 12px;
            color: #333;
            font-weight: bold;
            margin-top: 4px;

            .tip-text {
              color: #d0021b;
            }
          }
        }
      }
    }
  }
}

//阻断行错误时显示的容器
.error-status-container {
  width: 100%;
  height: 100%;
  display: flex;
  background-color: #fff;
  justify-content: center;
  align-items: center;
}

.set-calender-tip-popover-box {
  z-index: 999;

  :global {
    .adm-popover-inner-content {
      padding: 5px 8px;
    }
  }
}

.set-calender-popover {
  display: flex;
  align-items: center;
  font-size: 12px;

  svg {
    margin-left: 5px;
    font-size: 12px;
  }
}


/* 底部光圈：旋转 */
.ring {
  z-index: 1;
  will-change: transform;
  animation: ring-combo 2s linear infinite;
}

@keyframes ring-combo {
  from { transform: rotate(0deg); }
  to   { transform: rotate(360deg); }
}

// 覆盖和定义Toast的样式，目的是不换行
.cannot-check-in-toast {

  :global {
    .adm-toast-main {
      max-width: 220px;
      font-size: 14px;
    }
  }
}
