import { getUserInfo } from "@/utils/tool";

export const AWARD_MEDAL_POPOVER_KEY = "award_medal_popover";

// 兜底的默认学习时长，只有在接口异常情况下采用，否则使用接口返回的值
export const STUDY_DURATION = 15;

export const EVENT_CODE = {
  // 高中三年累计打卡事件
  THREE_GRADE_CHECK_IN: "800012",
};

export const WEEK_MAP = {
  0: "周日",
  1: "周一",
  2: "周二",
  3: "周三",
  4: "周四",
  5: "周五",
  6: "周六",
};

// 默认日签信息
export const defaultDailyInfo = {
  quote:
    "他在高三一年进步100多分，最终考上浙大。他说：只有拼出来的美丽，没有等出来的辉煌。",
  routeUrl:
    'mistong://{"domain":"fm", "action":"open_detail","params":{"id":"30115"}}',
};

// 不再提醒的配置key
export const NO_REMIND_ME_CONFIG_KEY = "no_remind_me_config";

// 本地缓存中写入或删除不再提醒的配置
export const operateNoRemindMeConfig = (checked: boolean) => {
  try {
    const userId = getUserInfo();
    if (!userId) {
      return;
    }
    const cacheKey = `${NO_REMIND_ME_CONFIG_KEY}_${userId}`;
    if (checked) {
      localStorage.setItem(cacheKey, "true");
    } else {
      localStorage.removeItem(cacheKey);
    }
  } catch (error) {
    console.error("操作不再提醒的配置失败", error);
  }
};
// 获取本地缓存中的不再提醒的配置
export const getNoRemindMeConfig = () => {
  try {
    const userId = getUserInfo();
    if (!userId) {
      return false;
    }
    const cacheKey = `${NO_REMIND_ME_CONFIG_KEY}_${userId}`;
    const value = localStorage.getItem(cacheKey);
    return Boolean(value);
  } catch (error) {
    console.error("获取不再提醒的配置失败", error);
    return false;
  }
};

/**
 * 获取年份列表
 * @param serverTimestamp 服务器时间
 * @param startYear 开始年份
 */
export const getYears = (
  serverTimestamp: number,
  startYear: number,
): number[] => {
  const serverDate = new Date(serverTimestamp);
  const currentYear = serverDate.getFullYear();

  return Array.from(
    { length: currentYear - startYear + 1 },
    (_, i) => startYear + i,
  );
};

/**
 * 获取月份列表
 * @param serverTimestamp 服务器时间戳
 * @param selectedYear 选中的年份
 * @param startMonth 开始月份
 * @param startYear 开始年份
 */
export const getMonths = (params: {
  serverTimestamp: number;
  selectedYear: number;
  startMonth: number;
  startYear: number;
}) => {
  const { serverTimestamp, selectedYear, startMonth, startYear } = params;
  const serverDate = new Date(serverTimestamp);
  const currentYear = serverDate.getFullYear();
  const currentMonth = serverDate.getMonth() + 1;

  let months: number[] = [];

  if (selectedYear === startYear) {
    if (currentYear === startYear) {
      months = Array.from(
        {
          length:
            currentMonth - startMonth + 1 > 0
              ? currentMonth - startMonth + 1
              : 0,
        },
        (_, i) => startMonth + i,
      ).filter((m) => m <= 12);
    } else {
      months = [9, 10, 11, 12];
    }
  } else if (selectedYear === currentYear) {
    months = Array.from({ length: currentMonth }, (_, i) => i + 1);
  } else if (selectedYear < currentYear) {
    months = Array.from({ length: 12 }, (_, i) => i + 1);
  } else {
    months = [];
  }

  return months;
};

/**
 * 获取指定年份和月份的天数
 * @param year 年份
 * @param month 月份
 * @returns 该月份的总天数（1-31）
 * @returns 当年份或月份无效时，返回0
 */
export const getDaysInMonth = (year: number, month: number): number => {
  if (!year || !month) {
    return 0;
  }
  // 构造下个月的第一天，再减去1天得到当前月最后一天
  const lastDay = new Date(year, month + 1, 0);
  return lastDay.getDate();
};

/**
 * 将日期数据转换为蛇形数据
 * @param days 日期数据
 * @returns 蛇形数据
 */
export const makeSnakeDays = <T extends Record<string, unknown>>(
  days: T[],
): T[] => {
  const snakeDays: T[] = [];
  // 计算需要展示多少行，28号是4行，29-31是5行
  const lineCount = Math.ceil(days.length / 7);
  // 每周的天数
  const weekCount = 7;
  for (let r = 0; r < lineCount; r++) {
    const start = r * weekCount;
    const end = Math.min(start + weekCount, days.length);
    const rowData = days.slice(start, end);
    if (r % 2 === 1) {
      rowData.reverse();
    }
    snakeDays.push(...rowData);
  }
  return snakeDays;
};
