.plan-list-layout {
  .header {
    .tabs{
      width: 100%;
      height: 40px;
      display: flex;
      align-items: center;
      .tab{
        position: relative;
        flex: 1;
        line-height: 40px;
        text-align: center;
       font-weight: bold;
        font-size: 16px;
        color: #666666;
        height: 100%;
        &::after{
          content: "";
          display: none;
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 16px;
          height: 4px;
          background: #2F86FF;
          border-radius: 3px;
        }
        &.active{
          color: #2F86FF;
          :global{
            .svg-icon{
              margin-left: 6px;
              color:rgba(47, 134, 255, 0.45);
            }
          }
          &::after{
            display: block;
          }
        }
      }
    }
  }
  :global{
    .my-task{
      max-height:100%;
      height: 100%;
    }
  }
  .content {
    overflow: auto;
    height: calc(100% - 12.8vw);
    background: #fff;
    .plan-block{
      position: relative;
      display: flex;
      flex-direction: column;
      height: 100%;
      .empty-box{
        position: absolute;
        display: flex;
        flex-direction: column;
        align-items: center;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        .empty-img{
          display: block;
          width: 160px;
          height: 160px;
        }
        .empty-text{
          margin-top: 12px;
          font-weight: 400;
          font-size: 12px;
          color: #999999;
          line-height: 20px;
        }
      }
      .status-block{
        padding: 0 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .manage-block{
          font-size: 14px;
          color: #2F86FF;
          :global{
            .svg-icon{
              margin-left: 4px;
            }
          }
        }
      }
      .plan-list-block{
        overflow: auto;
        max-height: calc(100vh - 12.8vw);
        height: calc(100vh - 12.8vw);
        background: #F5F5F5;
        padding: 0 12px;
        .week-banner{
          display: block;
          margin-top: 12px;
          width: 100%;
          border-radius: 4px;
        }
        .plan-item-wrapper{
          background-color: #F5F5F5;
          :global{
            .adm-list-body{
              border: none;
              background-color: #F5F5F5;
            }
          }
        }
        .plan-item{
          position: relative;
          margin-top: 12px;
          padding: 12px 12px 10px;
          width: 100%;
          min-height: 66px;
          background-color: #fff;
          border-radius: 4px;
          border: 1px solid rgba(0, 0, 0, 0.08);
          &.complete{
            border-color: rgba(146, 202, 122, 1);
            .progress{
              color: #389E0D;
            }
          }
          .plan-info{
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
            width: 90%;
            margin-bottom: 10px;
            .subject{
              flex-shrink: 0;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 12px;
              margin-right: 6px;
              color: #2F86FF;
              padding: 3px 4px;
              line-height: 12px;
              background: rgba(47, 134, 255, 0.15);
              border-radius: 2px;
            }
            .name{
             font-weight: bold;
              font-size: 16px;
              color: #333333;
              line-height: 20px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
          .complete-status{
            position: absolute;
            right: 0;
            bottom: 0;
            width: 53px;
          }
          .source{
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            line-height: 12px;
            margin-bottom: 8px;
          }
          .plan-info-block{
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            line-height: 12px;
            .progress{
              margin-left: 12px;
            }
          }
          .delete{
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(0, 0, 0, 0.25);
            color: #FF4D4F;
          }
          .right-to{
            position: absolute;
            right: 12px;
            bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 52px;
            height: 22px;
            background: #2F86FF;
            border: 1px solid #021E66;
            box-shadow: 0 4px 0 0 rgba(2, 30, 102, 0.15);
            border-radius: 11px;
            font-weight: bold;
            font-size: 12px;
            color: #FFFFFF;
          }
          .right{
            position: absolute;
            right: 12px;
            bottom: 10px;
          }
          :global{
            .svg-icon{
              position: absolute;
              right: 12px;
              top: 10px;
              color: rgba(0, 0, 0, 0.25);
            }
          }
        }
      }
      .status-tabs{
        height: 40px;
        display: flex;
        align-items: center;
        .tab{
          position: relative;
          line-height: 40px;
          text-align: center;
          font-weight: 400;
          font-size: 12px;
          color: #666666;
          margin-right: 24px;
          height: 100%;
          &::after{
            content: "";
            display: none;
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: #2F86FF;
            border-radius: 2px;
          }
          :global{
            .svg-icon{
              margin-left: 6px;
            }
          }
          &.active{
           font-weight: bold;
            font-size: 14px;
            color: #333333;
            &::after{
              display: block;
            }
          }
        }
      }
    }
  }
}
.delete-modal-body{
  padding: 24px 0 16px;
  width: 343px;
  border-radius: 24px !important;
  background: #FFFFFF;
  :global{
    .adm-center-popup-close{
      position: absolute;
      right: 20px;
      top: 20px;
    }
    .adm-modal-title{
     font-weight: bold;
      font-size: 18px;
      color: #333333;
      line-height: 18px;
      margin-bottom: 24px;
      padding: 0 16px;
    }
    .adm-modal-content{
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      line-height: 22px;
      padding: 0 16px;
      margin-bottom: 36px;
    }
    .adm-space.adm-space-block.adm-space-vertical.adm-modal-footer{
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      padding: 0 16px;
      .adm-space-item{
        margin-bottom: 0;
        width: 148px;
        height: 44px;
        .adm-button{
          display: flex;
          align-items: center;
          justify-content: center;
          width: 148px;
          height: 44px;
         font-weight: bold;
          color: #021E66;
          background: #FFFFFF;
          border: 1px solid #021E66;
          box-shadow: 0 4px 0 0 rgba(2, 30, 102, 0.15);
          border-radius: 30px;
          font-size: 16px;
        }
        .adm-modal-button-primary{
          background: #2F86FF;
          border: 1px solid #021E66;
          box-shadow: 0 4px 0 0 rgba(2, 30, 102, 0.15);
          border-radius: 30px;
          color: #fff;
        }
        &:first-child{
          order: 1;
        }
      }

    }
  }
}
