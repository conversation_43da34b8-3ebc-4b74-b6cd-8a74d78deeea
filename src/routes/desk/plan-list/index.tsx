import React, { useEffect, useState, useRef, Suspense } from "react";
import { useRequest, useSetState } from "ahooks";

import { Toast, InfiniteScroll, SpinLoading, List, Modal } from "antd-mobile";
import dayjs from "dayjs";
import { useSearchParams } from "react-router-dom";
import { ErrorBoundary } from "react-error-boundary";

import { useVisibilitychange } from "@/hooks";
import Empty from "@/components/empty-status";
import errorPNG from "@/assets/common/error.png";
import SafeLogger from "@/utils/safe-logger";

import { Layout, IconSvg } from "@/components";
import * as planApi from "@/service/desk/plan";
import { getDeskPlanInfoProcess } from "@/service/desk/plan-resource";
import WeekImg from "@/assets/image/plan-list/week.png";
import CompleteImg from "@/assets/image/plan-list/complete.png";
import PlanEmpty from "@/assets/common/plan-empty.png";

import { clickPv, openWebView } from "@/utils/tool";

import Styles from "./style.module.scss";
import PageBackIcon from "@/components/page-back-icon";

export enum E_STATUS {
  progress = 0,
  completed = 1,
  notStarted = 2,
}

export enum E_TABS {
  school = 1,
  self = 2,
}

const sourceTypeToName = {
  1: "自主创建",
  2: "计划广场",
  3: "智能学习规划",
};

const tabOptions = [
  {
    label: "我的任务",
    icon: <IconSvg name="icon-xuexiao" />,
    value: E_TABS.school,
  },
  {
    label: "自习计划",
    icon: <IconSvg name="icon-zixi" />,
    value: E_TABS.self,
  },
];

export const Component: React.FC = () => {
  const [tab, setTab] = useState<number>(E_TABS.school);
  const planIdRef = useRef<number>();
  const planListRef = useRef<Desk.plan.list.Data[]>([]);

  const [searchParams, setSearchParams] = useSearchParams({
    tabId: String(E_TABS.school),
    status: String(E_STATUS.progress),
    showTopBar: "false",
  });

  const tabId = searchParams.get("tabId");
  const status = searchParams.get("status");
  const showTopBar = searchParams.get("showTopBar");
  const activeTab = searchParams.get("activeTab");
  const token = searchParams.get("token");

  const [params, setParams] = useSetState({
    pageSize: 10,
    pageIndex: 1,
    status: +status || E_STATUS.progress,
    token: token,
  });
  const [editStatue, setEditStatue] = useState<boolean>(false);
  const [hasMore, setHasMore] = useState<boolean>(false);
  const [data, setData] = useState<Desk.plan.list.Data[]>([]);
  const [planListLoading, setPlanListLoading] = useState<boolean>(false);
  const [changeLoading, setChangeLoading] = useState<boolean>(false);

  const { runAsync: planRemoveRun, loading: planRemoveLoading } = useRequest(
    planApi.postPlanRemove,
    {
      manual: true,
    },
  );

  const { data: { data: planCount } = {}, run: planCountRun } = useRequest(
    planApi.getPlanCount,
  );

  const { data: { data: isStudy } = {} } = useRequest(() =>
    planApi.getIstudy({ needProcess: false }),
  );

  const BendH5App = React.lazy(() => import("bendH5/app"));

  const planListRun = async (payload) => {
    if (planListLoading) return;
    setPlanListLoading(true);
    setChangeLoading(true);
    setData([]);
    try {
      const {
        data: { data: list, haveNextPage },
      } = await planApi.getPlanList(payload);
      setData(list || []);
      setHasMore(haveNextPage);
      setPlanListLoading(false);
      setChangeLoading(false);
    } catch (error) {
      setPlanListLoading(false);
      setChangeLoading(false);
      SafeLogger.baseLogger.error("plan-list", {
        error,
        message: error.message,
        stack: error.stack,
      });
    }
  };

  const updatePlanItem = async (show) => {
    if (!show) return;
    if (!planIdRef.current) return;
    try {
      const { data: planInfo } = await getDeskPlanInfoProcess({
        planId: String(planIdRef.current),
        ignoreError: true,
      });
      const aData = planListRef.current;
      if (planInfo?.hasDeleted) {
        setData(aData.filter((v) => v.planId !== planIdRef.current));
        planCountRun();
      } else {
        const index = aData?.findIndex((v) => v.planId === planIdRef.current);
        if (index !== -1) {
          aData[index] = {
            ...aData[index],
            finishAllCount: planInfo.completeTaskCount,
            taskAllCount: planInfo.taskCount,
          };
          setData([...aData]);
        }
      }
    } catch (error) {}
  };

  // 页面的可见/不可见监控，变更后查询最新的奖励状态
  useVisibilitychange({
    handleVisibilitychange: updatePlanItem,
  });

  const loadMore = async () => {
    if (!hasMore) return;
    if (planListLoading) return;
    const payload = {
      ...params,
      pageIndex: params.pageIndex + 1,
    };
    setPlanListLoading(true);
    try {
      const {
        data: { data: list, haveNextPage },
      } = await planApi.getPlanList(payload);
      setParams(payload);
      setHasMore(haveNextPage);
      setData([...data, ...(list || [])]);
      setPlanListLoading(false);
    } catch (error) {
      setPlanListLoading(false);
      SafeLogger.baseLogger.error("plan-list-loadMore", {
        error,
        message: error.message,
        stack: error.stack,
      });
      throw new Error("request failed");
    }
  };

  const onDelete = (record) => {
    Modal.confirm({
      className: Styles["delete-modal"],
      content: "删除后将无法恢复，确定删除该计划吗？",
      title: "提示",
      showCloseButton: true,
      bodyClassName: Styles["delete-modal-body"],
      onConfirm: async () => {
        try {
          const { success } = await planRemoveRun({
            planId: +record.planId,
          });
          if (!success) {
            Toast.show({
              content: "删除失败",
            });
            return;
          }
          const cData = data.filter((v) => v.planId !== record.planId);
          setData(cData);
          Toast.show({
            content: "删除成功",
          });
          planCountRun();
        } catch (error) {
          Toast.show({
            content: "删除失败",
          });
          SafeLogger.baseLogger.error("plan-list-remove-plan", {
            error,
            message: error.message,
            stack: error.stack,
          });
        }
      },
    });
  };

  useEffect(() => {
    setTab(+tabId);
    if (+tabId === E_TABS.self) {
      const aParams = {
        status: +status,
        pageIndex: 1,
        pageSize: 10,
      };
      setParams(aParams);
      planListRun(aParams);
    }
  }, [tabId]);

  const onBack = () => {
    if (mstJsBridge.isInMstApp()) {
      mstJsBridge.closeWebview();
    } else {
      history.back();
    }
  };

  const E_STATUS_OPTIONS = [
    {
      label: `进行中`,
      count: planCount?.inProgressCount || 0,
      value: E_STATUS.progress,
    },
    {
      label: `未开始`,
      count: planCount?.notStartedCount || 0,
      value: E_STATUS.notStarted,
    },
    {
      label: `已截止`,
      count: planCount?.expireCount || 0,
      value: E_STATUS.completed,
    },
  ];

  const nOptions = E_STATUS_OPTIONS.find((v) => v.value === params.status);

  return (
    <Layout
      showHeader
      setChildrenContainerHeight={({ top, bottom }) => {
        return `calc(100vh - ${top || 0}px - ${bottom || 0}px)`;
      }}
      className={Styles["plan-list-layout"]}
      headerProps={{
        children: (
          <div className={Styles["tabs"]}>
            {tabOptions.map((t) => (
              <div
                className={`${Styles["tab"]} ${t.value === tab ? Styles["active"] : ""}`}
                key={t.value}
                onClick={() => {
                  clickPv("ewt_h5_base_plan_desk_plan_list_plan_type_click", {
                    type: t.label,
                  });
                  if (t.value === tab) return;
                  if (planListLoading) return;
                  setTab(t.value);
                  const pa = {
                    status,
                    tabId: String(t.value),
                    showTopBar,
                  };
                  if (token) {
                    pa["token"] = token;
                  }
                  setSearchParams(pa, { replace: true });
                  if (t.value === E_TABS.self) {
                    const aParams = {
                      status: params.status || E_STATUS.progress,
                      pageIndex: 1,
                      pageSize: 10,
                    };
                    setParams(aParams);
                    planListRun(aParams);
                  }
                }}
              >
                {t.label}
              </div>
            ))}
          </div>
        ),
        className: Styles.header,
        back: <PageBackIcon />,
        onBack,
      }}
    >
      <div className={Styles["content"]}>
        {tab === E_TABS.school && (
          <ErrorBoundary
            fallback={
              <div className={Styles["content"]}>
                <div className={Styles["plan-block"]}>
                  <Empty
                    image={errorPNG}
                    text="糟糕，遇到了点问题，点击“重新加载”试试"
                    className="empty-error-img"
                    buttonOption={{
                      text: "重新加载",
                      handleClick: () => {
                        window.location.reload();
                      },
                    }}
                  />
                </div>
              </div>
            }
            onError={(error) => {
              SafeLogger.baseLogger.error("render-error", {
                error,
                message: `render-bend-h5: ${error.message}`,
                stack: error.stack,
              });
            }}
          >
            <Suspense
              fallback={
                <div className={Styles["content"]}>
                  <div className={Styles["plan-block"]}>
                    <div className={Styles["empty-box"]}>
                      <SpinLoading color="primary" />
                    </div>
                  </div>
                </div>
              }
            >
              <BendH5App defaultActiveTab={activeTab} name="campus-plan" />
            </Suspense>
          </ErrorBoundary>
        )}
        {tab === E_TABS.self && (
          <div className={Styles["plan-block"]}>
            <div className={Styles["status-block"]}>
              <div className={Styles["status-tabs"]}>
                {E_STATUS_OPTIONS.map((t) => (
                  <div
                    className={`${Styles["tab"]} ${t.value === params.status ? Styles["active"] : ""}`}
                    key={t.value}
                    onClick={() => {
                      clickPv(
                        "ewt_h5_base_plan_desk_plan_list_self_plan_type_click",
                        {
                          type:
                            t.value === E_STATUS.progress ? "进行中" : "已截止",
                        },
                      );
                      if (t.value === params.status) return;
                      if (planListLoading) return;
                      setParams({
                        status: t.value,
                        pageIndex: 1,
                        pageSize: 10,
                      });
                      const pa = {
                        status: String(t.value),
                        tabId,
                        showTopBar,
                      };
                      if (token) {
                        pa["token"] = token;
                      }
                      setSearchParams(pa, { replace: true });
                      planListRun({
                        status: t.value,
                        pageIndex: 1,
                        pageSize: 10,
                      });
                    }}
                  >
                    {t.label}({t.count})
                  </div>
                ))}
              </div>
              {data?.length > 0 && (
                <div
                  className={Styles["manage-block"]}
                  onClick={() => {
                    setEditStatue(!editStatue);
                    if (!editStatue) {
                      clickPv(
                        "ewt_h5_base_plan_desk_plan_list_manage_button_click",
                      );
                    }
                  }}
                >
                  {editStatue ? "确认" : "管理"}
                  {!editStatue && <IconSvg name="icon-guanli" />}
                </div>
              )}
            </div>
            <div className={Styles["plan-list-block"]}>
              {isStudy?.h5Route && params.status === E_STATUS.progress && (
                <img
                  className={Styles["week-banner"]}
                  onClick={() => {
                    openWebView(isStudy?.h5Route);
                  }}
                  src={WeekImg}
                />
              )}
              {data?.length <= 0 ? (
                <div className={Styles["empty-box"]}>
                  {changeLoading ? (
                    <SpinLoading color="primary" />
                  ) : (
                    <>
                      <img className={Styles["empty-img"]} src={PlanEmpty} />
                      <span className={Styles["empty-text"]}>
                        目前还没有
                        {nOptions.label}
                        的计划～
                      </span>
                    </>
                  )}
                </div>
              ) : (
                <>
                  <List className={Styles["plan-item-wrapper"]}>
                    {data?.map?.((v) => {
                      const isFinish = Boolean(
                        v.finishAllCount === v.taskAllCount &&
                          v.taskAllCount > 0,
                      );
                      return (
                        <div
                          key={v.planId}
                          className={`${Styles["plan-item"]} ${isFinish ? Styles["complete"] : ""}`}
                          onClick={() => {
                            planListRef.current = data;
                            planIdRef.current = v.planId;
                            const url = `${location.origin}/ewtcustomerh5/desk/plan-resource?planId=${v.planId}&showTopBar=false`;
                            openWebView(url);
                          }}
                        >
                          <div className={Styles["plan-info"]}>
                            {v.subjectName && (
                              <div className={Styles["subject"]}>
                                {v.subjectName}
                              </div>
                            )}
                            <div className={Styles["name"]}>{v.planName}</div>
                          </div>
                          {sourceTypeToName[v.sourceType] && (
                            <div className={Styles["source"]}>
                              来自：{sourceTypeToName[v.sourceType]}
                            </div>
                          )}
                          <div className={Styles["plan-info-block"]}>
                            <span className={Styles["time"]}>
                              {params.status === E_STATUS.progress && (
                                <>
                                  截止时间：
                                  {dayjs(+v.endTime).format("MM-DD HH:mm")}
                                </>
                              )}
                              {params.status === E_STATUS.notStarted && (
                                <>
                                  开始时间：
                                  {dayjs(+v.startTime).format("MM-DD HH:mm")}
                                </>
                              )}
                              {params.status === E_STATUS.completed && (
                                <>
                                  {dayjs(+v.startTime).format("MM-DD HH:mm")}
                                  &nbsp;至 &nbsp;
                                  {dayjs(+v.endTime).format("MM-DD HH:mm")}
                                </>
                              )}
                            </span>
                            <span className={Styles["progress"]}>
                              总进度：{v.finishAllCount}/{v.taskAllCount}
                            </span>
                            {editStatue ? (
                              <span
                                className={Styles["delete"]}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  clickPv(
                                    "ewt_h5_base_plan_desk_plan_list_delete_button_click",
                                  );
                                  onDelete(v);
                                }}
                              >
                                删除
                              </span>
                            ) : (
                              <>
                                {!isFinish && (
                                  <>
                                    {params.status === E_STATUS.progress && (
                                      <div className={Styles["right-to"]}>
                                        去学习
                                      </div>
                                    )}
                                    {params.status === E_STATUS.notStarted && (
                                      <div className={Styles["right"]}>
                                        {dayjs(+v.startTime).diff(
                                          dayjs(),
                                          "day",
                                        ) + 1}
                                        天后开始
                                      </div>
                                    )}
                                    {params.status === E_STATUS.completed && (
                                      <div className={Styles["right"]}>
                                        未完成
                                      </div>
                                    )}
                                  </>
                                )}
                                {isFinish && (
                                  <img
                                    className={Styles["complete-status"]}
                                    src={CompleteImg}
                                  />
                                )}
                                {(isFinish ||
                                  params.status !== E_STATUS.progress) && (
                                  <IconSvg name="icon-a-jinru2x" />
                                )}
                              </>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </List>
                  <InfiniteScroll
                    threshold={5}
                    loadMore={loadMore}
                    hasMore={hasMore}
                  >
                    {(more, failed, retry) => {
                      if (failed) {
                        return (
                          <span>
                            加载失败！
                            <a onClick={retry}>重试</a>
                          </span>
                        );
                      }
                      if (more) {
                        return <span>正在加载更多内容...</span>;
                      }
                      return <span>没有更多内容了</span>;
                    }}
                  </InfiniteScroll>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};
