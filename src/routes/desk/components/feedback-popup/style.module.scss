.feedback-popup-wrapper {
  .form-block {
    position: relative;
    flex: 1;
    padding: 0 20px;
    background: #fff;
    border-radius: 8px;
    max-height: 60vh;
    overflow: auto;
    .form-item {
      position: relative;
      z-index: 2;
      margin-bottom: 20px;
      .form-item-control {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-left: 16px;
        padding-right: 12px;
        height: 44px;
        background: #ffffff;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 8px;
        font-weight: 400;
        font-size: 14px;
        color: #333;
        &-value{
          color: #2F86FF;
          display: flex;
          align-items: center;
          :global{
            .svg-icon{
              margin-left: 12px;
              font-size: 12px;
              zoom: 0.8;
              color: rgba(0, 0, 0, 0.25);
            }
          }
        }
      }
      .form-item-label-inner {
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .form-item-label-right {
          font-weight: 400;
          font-size: 12px;
          visibility: hidden;
          text-decoration: underline;
        }
      }
      .form-item-label {
        position: relative;
        display: flex;
        align-items: center;
        height: 22px;
        line-height: 22px;
        font-weight: 400;
        font-size: 16px;
        color: #333333;
        &::before {
          display: block;
          content: "";
          width: 4px;
          left: 0;
          height: 8px;
          background: #2f86ff;
          border-radius: 8px;
          border-radius: 2px;
          margin-right: 8px;
        }
        .form-item-label-help {
          font-weight: 400;
          font-size: 12px;
          color: #999999;
        }
      }
    }
    .mood_container {
      display: flex;
      flex-wrap: wrap;

      .mood_item {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;
        cursor: pointer;

        .icon {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          background: #f5f5f5;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 32px;
            height: 32px;
          }
        }

        .word {
          font-size: 12px;
          color: #333333;
          line-height: 16px;
          margin-top: 6px;
        }

        &.active {
          .icon {
            background: #2f86ff;
          }
        }
      }
    }
    .text_area{
      padding: 10px;
      height: 150px;
      background: rgba(255, 255, 255, 0.08);
      border: 1px solid #CCCCCC;
      border-radius: 8px;
      :global{
        .adm-text-area-element::placeholder{
          font-size: 14px;
          color: #CCCCCC;
        }
        .adm-text-area-element{
          font-size: 14px;
          line-height: 22px;
        }
        .adm-text-area-count{
          font-weight: 400;
          font-size: 12px;
          color: #CCCCCC;
          line-height: 20px;
        }
      }
    }

  }
  .footer {
    min-height: 72px;
    padding: 12px 16px;
    box-shadow: 0 0 10px 0px rgba(2, 30, 102, 0.15);
    &.safe {
      padding-bottom: calc(env(safe-area-inset-bottom) + 16px);
      padding-bottom: calc(constant(safe-area-inset-bottom) + 16px);
    }
    > div {
      border: 1px solid #021e66;
      box-shadow: 0 4px 0 0 rgba(2, 30, 102, 0.15);
    }
  }
}
.feedback-popup-title {
  color: #333333;
}
