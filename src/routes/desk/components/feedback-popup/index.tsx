import React, { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, TextArea, PopupProps, Toast } from "antd-mobile";
import { useMount, useToggle, useRequest } from "ahooks";
import { IconSvg, <PERSON>ton, EButtonType } from "@/components";
import classNames from "classnames";

import {
  getSummaryInfo,
  postUpdateSummaryOptions,
} from "@/service/desk/plan-resource";

import Popup from "@/components/popup";
import Styles from "./style.module.scss";

interface IFeedbackPopupProps extends PopupProps {
  onConfirm: (val) => void;
  questions?: any[];
  submitLoading: boolean;
}

const defaultOptions = [
  {
    questionText: "该计划内容是否符合你的预期？",
    questionId: "1",
    questionType: 1,
    optionList: [
      {
        code: "1",
        imageUrl: "http://file.ewt360.com/file/2019200138879057935",
        content: "不符合",
      },
      {
        code: "2",
        imageUrl: "http://file.ewt360.com/file/2019200405167022138",
        content: "一般",
      },
      {
        code: "3",
        imageUrl: "http://file.ewt360.com/file/2019201968535118182",
        content: "还可以",
      },
      {
        code: "4",
        imageUrl: "http://file.ewt360.com/file/2019202191873392681",
        content: "符合",
      },
      {
        code: "5",
        imageUrl: "http://file.ewt360.com/file/2019202398031822886",
        content: "超出预期",
      },
    ],
  },
];

export enum QUESTION_TYPE {
  SELECT = 1,
  TEXT = 2,
}

const FeedbackPopup: React.FC<IFeedbackPopupProps> = (props) => {
  const {
    onConfirm,
    visible,
    questions = defaultOptions,
    submitLoading,
    ...restProps
  } = props;
  const [value, setValue] = useState([]);

  useEffect(() => {
    if (!visible || questions?.length <= 0) return;
    const val = questions?.map(
      ({ questionId, questionType, questionText }) => ({
        questionId,
        questionType,
        questionText,
        content: undefined,
      }),
    );
    setValue(val);
  }, [visible, questions]);

  const onChangeValue = ({ val, key }, qus) => {
    const { questionId } = qus;
    const index = value.findIndex((v) => v.questionId === questionId);
    if (index === -1) return;
    const currentVal = value.slice();
    const question = { ...currentVal[index] };
    question[key] = val;
    currentVal[index] = question;
    setValue(currentVal);
  };

  return (
    <Popup
      title="反馈"
      {...restProps}
      visible={visible}
      titleClassName={Styles["feedback-popup-title"]}
    >
      <div className={Styles["feedback-popup-wrapper"]}>
        <div className={Styles["form-block"]}>
          {questions.map((qus) => {
            const currentChangeQuestion =
              value?.find?.((v) => v.questionId === qus.questionId) || {};

            return (
              <div key={qus.id} className={Styles["form-item"]}>
                <div className={Styles["form-item-label-inner"]}>
                  <div className={Styles["form-item-label"]}>
                    <span className={Styles["form-item-label-help"]}>
                      {qus.questionText}
                    </span>
                  </div>
                </div>
                {qus.questionType === QUESTION_TYPE.SELECT && (
                  <div className={Styles.mood_container}>
                    {qus.optionList.map((opt) => (
                      <div
                        className={classNames(Styles.mood_item, {
                          [Styles.active]:
                            opt.code === currentChangeQuestion.optionCode,
                        })}
                        key={opt.code}
                        onClick={() =>
                          onChangeValue(
                            { val: opt.code, key: "optionCode" },
                            qus,
                          )
                        }
                      >
                        <div className={Styles.icon}>
                          <img src={opt.imageUrl} />
                        </div>
                        <div className={Styles.word}>{opt.content}</div>
                      </div>
                    ))}
                  </div>
                )}
                {qus.questionType === QUESTION_TYPE.TEXT && (
                  <div className={Styles.text_area}>
                    <TextArea
                      value={currentChangeQuestion.content || ""}
                      placeholder="最多输入100个汉字"
                      showCount
                      maxLength={100}
                      rows={5}
                      onChange={(val) =>
                        onChangeValue({ val, key: "content" }, qus)
                      }
                    />
                  </div>
                )}
              </div>
            );
          })}
        </div>
        <div className={`${Styles["footer"]} ${Styles["safe"]}`}>
          <Button
            text="提交"
            loading={submitLoading}
            onClick={() => {
              /** check */
              const unSelectedArr = (value || []).filter(
                (v) => v.questionType === QUESTION_TYPE.SELECT && !v.optionCode,
              );
              if (unSelectedArr.length) {
                const [first] = unSelectedArr;
                Toast.show({
                  content: `请选择${first.questionText}`,
                  maskStyle: {
                    zIndex: 1050,
                  },
                  position: "bottom",
                });
                return;
              }
              const filterValue = value
                ?.filter((v) => v.optionCode || v.content)
                ?.map(({ questionText, ...rest }) => ({
                  ...rest,
                }));
              onConfirm(filterValue);
            }}
            type={EButtonType.Blue}
          />
        </div>
      </div>
    </Popup>
  );
};

export const useFeedBack = () => {
  const [visible, { toggle }] = useToggle(false);

  const {
    runAsync: summaryInfoAsyncRun,
    loading: SummaryInfoLoading,
    data: { data: summaryInfo } = {},
  } = useRequest(getSummaryInfo, {
    manual: true,
  });

  const {
    runAsync: updateSummaryOptionsAsyncRun,
    loading: updateSummaryOptionsLoading,
  } = useRequest(postUpdateSummaryOptions, {
    manual: true,
  });

  return {
    visible,
    toggle,
    state: {
      summaryInfo,
    },
    loading: {
      updateSummaryOptionsLoading,
    },
    run: {
      summaryInfoAsyncRun,
      updateSummaryOptionsAsyncRun,
    },
  };
};

export default FeedbackPopup;
