import React, { useEffect, useRef } from "react";
import LottieWeb, { RendererType } from "lottie-web";
import Styles from "./index.module.scss";
import { ILottieConfig } from "@/routes/desk/plan-resource/hooks/useLottiePopup";

interface LottiePopupProps {
  onClose: () => void;
  lottieList: ILottieConfig[];
  isPlaying: boolean;
  currentIndex: number;
  onSkip: () => void;
  onComplete: () => void;
  renderer?: RendererType;
  getContainer?: HTMLElement | (() => HTMLElement);
}

const PositiveFeedbackPopup: React.FC<LottiePopupProps> = ({
  lottieList,
  currentIndex,
  onSkip,
  onComplete,
  renderer = "svg",
}) => {
  const lottieBoxRef = useRef<HTMLDivElement>(null);

  if (!lottieList?.[currentIndex]?.animationDataUrl) {
    return null;
  }

  useEffect(() => {
    if (!lottieBoxRef.current) return;

    const bodyMove = LottieWeb?.loadAnimation({
      container: lottieBoxRef.current,
      path: lottieList[currentIndex].animationDataUrl,
      loop: false,
      autoplay: true,
      renderer,
    });

    bodyMove?.addEventListener("complete", onComplete);

    return () => {
      bodyMove?.removeEventListener("complete", onComplete);
      bodyMove?.destroy();
    };
  }, [currentIndex, lottieList]);

  return (
    <div className={Styles["positive-feedback-lottie-popup"]}>
      <div className={Styles["lottie-container"]}>
        <div ref={lottieBoxRef} />
      </div>
      <div className={Styles["skip-button"]} onClick={onSkip}>
        跳过动画
      </div>
    </div>
  );
};

export default PositiveFeedbackPopup;
