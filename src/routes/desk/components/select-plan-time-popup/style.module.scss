.select-plan-time-popup-wrapper {
  .select-plan-popup-desc{
    text-align: center;
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    line-height: 18px;
    margin-bottom: 20px;
  }
  .form-block {
    position: relative;
    flex: 1;
    padding: 0 20px;
    background: #fff;
    border-radius: 8px;
    .form-item {
      position: relative;
      z-index: 2;
      margin-bottom: 24px;
      .form-item-control {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-left: 16px;
        padding-right: 12px;
        height: 44px;
        background: #ffffff;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 8px;
        font-weight: 400;
        font-size: 14px;
        color: #333;
        &-value{
          color: #2F86FF;
          display: flex;
          align-items: center;
          :global{
            .svg-icon{
              margin-left: 12px;
              font-size: 12px;
              zoom: 0.8;
              color: rgba(0, 0, 0, 0.25);
            }
          }
        }
      }
      .form-item-label-inner {
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .form-item-label-right {
          font-weight: 400;
          font-size: 12px;
          visibility: hidden;
          text-decoration: underline;
        }
      }
      .form-item-label {
        position: relative;
        display: flex;
        align-items: center;
        height: 14px;
        line-height: 14px;
        font-weight: 400;
        font-size: 16px;
        color: #333333;
        &::before {
          display: block;
          content: "";
          width: 4px;
          left: 0;
          height: 8px;
          background: #2f86ff;
          border-radius: 8px;
          border-radius: 2px;
          margin-right: 8px;
        }
        .form-item-label-help {
          font-weight: 400;
          font-size: 12px;
          color: #999999;
        }
      }
    }
  }
  .footer {
    min-height: 72px;
    padding: 12px 16px;
    box-shadow: 0 0 10px 0px rgba(2, 30, 102, 0.15);
    &.safe {
      padding-bottom: calc(env(safe-area-inset-bottom) + 16px);
      padding-bottom: calc(constant(safe-area-inset-bottom) + 16px);
    }
    > div {
      border: 1px solid #021e66;
      box-shadow: 0 4px 0 0 rgba(2, 30, 102, 0.15);
    }
  }
}
.select-rest-popup-wrapper{
  .select-rest-popup-desc{
    text-align: center;
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    line-height: 12px;
    margin-bottom: 24px;
  }
  .checkbox-options-wrapper{
    display: flex;
    flex-wrap: wrap;
    padding: 0 20px;
    .checkbox-options-item{
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 103px;
      height: 40px;
      background: #F3F4F8;
      border-radius: 20px;
      font-weight: bold;
      font-size: 14px;
      color: #333333;
      line-height: 14px;
      margin-right: 12px;
      margin-bottom: 12px;
      border: 1px solid transparent;
      &:nth-child(3n){
        margin-right: 0;
      }
      &.active{
        border: 1px solid #2E86FF;
        color: #2E86FF;
        background-color: #fff;
        &::before{
          position: absolute;
          right: -1px;
          bottom: -1px;
          width: 20px;
          height: 16px;
          display: block;
          content: '';
          background-image: url('../../../../assets/image/plan-list/selected.png');
          background-size: 100% 100%;
        }
      }
    }
  }
  .footer {
    min-height: 72px;
    padding: 12px 16px;
    box-shadow: 0 0 10px 0px rgba(2, 30, 102, 0.15);
    &.safe {
      padding-bottom: calc(env(safe-area-inset-bottom) + 16px);
      padding-bottom: calc(constant(safe-area-inset-bottom) + 16px);
    }
    > div {
      border: 1px solid #021e66;
      box-shadow: 0 4px 0 0 rgba(2, 30, 102, 0.15);
    }
  }
}
.select-plan-time-popup-title,
.select-rest-popup-title {
  color: #333333;
}

.date-picker-popup {
  z-index: 1010;
  :global {
    .adm-popup-body {
      pointer-events: unset !important;
    }
  }
}
