import React, { useMemo, useState } from "react";
import { CalendarPicker, PopupProps, Toast } from "antd-mobile";
import { useMount, useToggle } from "ahooks";
import { IconSvg, Button, EButtonType } from "@/components";
import dayjs from "dayjs";

import Popup from "@/components/popup";
import Styles from "./style.module.scss";

export enum D_ENUM {
  NO = 0,
  ONE = 1,
  TWO = 2,
  THREE = 3,
  FOUR = 4,
  FIVE = 5,
  SIX = 6,
  SEVEN = 7,
}

const radioOptions = [
  {
    label: "不设置",
    value: D_ENUM.NO,
    sub: "不设置休息日",
  },
  {
    label: "每周一",
    value: D_ENUM.ONE,
    sub: "一",
  },
  {
    label: "每周二",
    value: D_ENUM.TWO,
    sub: "二",
  },
  {
    label: "每周三",
    value: D_ENUM.THREE,
    sub: "三",
  },
  {
    label: "每周四",
    value: D_ENUM.FOUR,
    sub: "四",
  },
  {
    label: "每周五",
    value: D_ENUM.FIVE,
    sub: "五",
  },
  {
    label: "每周六",
    value: D_ENUM.SIX,
    sub: "六",
  },
  {
    label: "每周日",
    value: D_ENUM.SEVEN,
    sub: "日",
  },
];

interface ISelectPlanTimePopup extends PopupProps {
  onConfirm: (val) => void;
  confirmLoading?: boolean;
}
const MAX_COUNT = 5;
const SelectPlanTimePopup: React.FC<ISelectPlanTimePopup> = (props) => {
  const { onConfirm, visible, confirmLoading, ...restProps } = props;

  const [startTimeSelectVisible, { toggle: startTimeSelectToggle }] =
    useToggle(false);
  const [startTime, setStartTime] = useState<number>();
  const [restDaysCatch, setRestDaysCatch] = useState<number[]>([]);
  const [restDays, setRestDays] = useState<number[]>([]);
  const [restVisible, { toggle: restToggle }] = useToggle(false);

  useMount(() => {
    setStartTime(dayjs().hour(0).minute(0).second(0).valueOf());
    setRestDays([0]);
  });

  const onSelectRest = (record) => {
    let cRestDays = restDaysCatch.slice();
    /** 处理互斥  */
    if (record.value === D_ENUM.NO) {
      setRestDaysCatch([D_ENUM.NO]);
      return;
    }
    /** 初始默认值 D_ENUM.NO 处理互斥  */
    if (restDaysCatch.includes(D_ENUM.NO)) {
      cRestDays = cRestDays.filter((r) => r !== D_ENUM.NO);
    }
    if (
      !restDaysCatch.includes(D_ENUM.NO) &&
      restDaysCatch.includes(record.value) &&
      restDaysCatch.length === 1
    ) {
      setRestDaysCatch([D_ENUM.NO]);
      return;
    }

    /** 默认逻辑 */
    if (restDaysCatch.includes(record.value)) {
      cRestDays = cRestDays.filter((r) => r !== record.value);
    } else {
      cRestDays.push(record.value);
    }
    /** max */
    if (cRestDays.length > MAX_COUNT) {
      Toast.show({
        content: `最多选择${MAX_COUNT}个`,
        maskStyle: {
          zIndex: 1050,
        },
        position: "bottom",
      });
      return;
    }
    setRestDaysCatch(cRestDays);
  };

  const restDaysString = useMemo(() => {
    if (restDays.includes(D_ENUM.NO)) {
      return radioOptions[0].sub;
    }
    const subS = radioOptions
      .filter((v) => restDays.includes(v.value))
      .map((v) => v.sub)
      .join("、");
    return `每周${subS}`;
  }, [restDays]);

  return (
    <Popup
      title="设置计划时间"
      visible={visible}
      {...restProps}
      titleClassName={Styles["select-plan-time-popup-title"]}
    >
      <div className={Styles["select-plan-time-popup-wrapper"]}>
        <div className={Styles["select-plan-popup-desc"]}>
          休息日不会展示学习任务，系统将自动跳过休息日，
          <br />
          并为你生成连续的学习计划
        </div>
        <div className={Styles["form-block"]}>
          <div className={Styles["form-item"]}>
            <div className={Styles["form-item-label-inner"]}>
              <div className={Styles["form-item-label"]}>
                <span className={Styles["form-item-label-help"]}>
                  点击选择计划开始时间
                </span>
              </div>
            </div>
            <div
              onClick={startTimeSelectToggle}
              className={Styles["form-item-control"]}
            >
              <span className={Styles["form-item-control-label"]}>
                计划开始日期
              </span>
              <span className={Styles["form-item-control-value"]}>
                {startTime ? dayjs(+startTime).format("MM月DD日") : "请选择"}
                <IconSvg name="icon-a-jinru2x" />
              </span>
            </div>
          </div>
          <div className={Styles["form-item"]}>
            <div className={Styles["form-item-label-inner"]}>
              <div className={Styles["form-item-label"]}>
                <span className={Styles["form-item-label-help"]}>
                  点击选择休息日
                </span>
              </div>
            </div>
            <div
              onClick={() => {
                restToggle();
                setRestDaysCatch(restDays);
              }}
              className={Styles["form-item-control"]}
            >
              <span className={Styles["form-item-control-label"]}>休息日</span>
              <span className={Styles["form-item-control-value"]}>
                {restDaysString}
                <IconSvg name="icon-a-jinru2x" />
              </span>
            </div>
          </div>
        </div>
        <div className={`${Styles["footer"]} ${Styles["safe"]}`}>
          <Button
            text="完成"
            loading={confirmLoading}
            onClick={() => {
              onConfirm({
                startTime,
                restDays,
              });
            }}
            type={EButtonType.Blue}
          />
        </div>
        {/*  选择弹出 */}
        <CalendarPicker
          selectionMode="single"
          visible={startTimeSelectVisible}
          popupClassName={Styles["date-picker-popup"]}
          defaultValue={startTime ? dayjs(startTime).toDate() : undefined}
          min={dayjs().toDate()}
          max={dayjs().hour(0).minute(0).second(0).add(29, "day").toDate()}
          title="日期选择"
          allowClear={false}
          onMaskClick={startTimeSelectToggle}
          onClose={startTimeSelectToggle}
          onConfirm={(val) => {
            const valSt = dayjs(val)
              .hour(0)
              .minute(0)
              .second(0)
              .millisecond(0)
              .valueOf();
            setStartTime(valSt);
          }}
        />
        <Popup
          onClose={restToggle}
          title="选择休息日"
          visible={restVisible}
          titleClassName={Styles["select-rest-popup-title"]}
          style={{
            zIndex: 1010,
          }}
        >
          <div className={Styles["select-rest-popup-wrapper"]}>
            <div className={Styles["select-rest-popup-desc"]}>
              休息日不安排课程，可多选以下部分选项，最多选择{MAX_COUNT}个
            </div>
            <div className={Styles["checkbox-options-wrapper"]}>
              {radioOptions.map((v) => (
                <div
                  key={v.value}
                  className={`${Styles["checkbox-options-item"]} ${restDaysCatch.includes(v.value) ? Styles["active"] : ""}`}
                  onClick={() => onSelectRest(v)}
                >
                  {v.label}
                </div>
              ))}
            </div>
            <div className={`${Styles["footer"]} ${Styles["safe"]}`}>
              <Button
                text="完成"
                onClick={() => {
                  setRestDays(restDaysCatch);
                  restToggle();
                }}
                type={EButtonType.Blue}
              />
            </div>
          </div>
        </Popup>
      </div>
    </Popup>
  );
};

export default SelectPlanTimePopup;
