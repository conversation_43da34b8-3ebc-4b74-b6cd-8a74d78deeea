import * as React from "react";
import { cls } from "@/utils/tool";
import Style from "./style.module.scss";
import { Button } from "@/components";

export enum EButtonType {
  Blue,
  grey,
}
export interface IButton {
  id?: string;
  className?: string;
  type?: EButtonType;
  // 按钮文案
  text: string | React.ReactNode;
  onClick?: (e: any) => void;
}
const CancelButton: React.FC<IButton> = (props) => {
  const {
    text = "确定",
    className,
    type = EButtonType.Blue,
    onClick,
    id = "",
    ...restProps
  } = props;

  return (
    <Button
      text={text}
      className={cls([Style["primary-button"], className])}
      type={type}
      onClick={onClick}
      id={id}
      {...restProps}
    />
  );
};

export default CancelButton;
