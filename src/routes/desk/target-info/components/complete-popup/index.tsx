import * as React from "react";
import { Popup, EButtonType } from "@/components";
import type { IPopup } from "@/components";
import CancelButton from "@/routes/desk/components/cancel-button";
import PrimaryButton from "@/routes/desk/components/primary-button";

import Style from "./style.module.scss";

interface ICompletePopup extends IPopup {
  onOk: () => void;
  onClose: () => void;
}

export const CompletePopup: React.FC<ICompletePopup> = (props) => {
  const { onOk, onClose, ...rest } = props;
  return (
    <Popup title="提示" destroyOnClose onClose={onClose} {...rest}>
      <div className={Style["complete-target-pop"]}>
        确定要完结我的目标吗？
        <div className={Style["button"]}>
          <CancelButton text="取消" onClick={onClose} type={EButtonType.grey} />
          <PrimaryButton text="确定" onClick={onOk} />
        </div>
      </div>
    </Popup>
  );
};
