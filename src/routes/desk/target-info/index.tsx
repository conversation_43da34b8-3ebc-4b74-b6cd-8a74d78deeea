import React, { useEffect, useRef, useState } from "react";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import dayjs from "dayjs";
import { Layout } from "@/components";
import PageLoading from "@/components/page-loading";
import PageBackIcon from "@/components/page-back-icon";
import SafeLogger from "@/utils/safe-logger";

import {
  getDeskTargetDetail,
  postDeskCompleteTarget,
  TDeskTargetDetailRes,
} from "@/service/desk/create-target";

import TargetInfoImg from "@/assets/image/target-info/target-bg.png";
// 目标进行中图
import ProgressIngPng from "@/assets/image/target-info/progress-ing.png";
// 目标完结图
import ProgressEndPng from "@/assets/image/target-info/progress-end.png";

import Styles from "./style.module.scss";
import { Ellipsis } from "antd-mobile";
import { closeWebview } from "@/utils/bridge-utils";
import CancelButton from "../components/cancel-button";
import PrimaryButton from "../components/primary-button";
import { clickPv, createURLByType, EJumpType } from "@/utils/tool";
import { CompletePopup } from "./components/complete-popup";
import { NetworkErrorStatus } from "@/components/empty-status";

export const Component: React.FC = () => {
  // 日志
  const loggerRef = useRef<SafeLogger>();
  const location = useLocation();
  const navigate = useNavigate();
  const [searchParams, _] = useSearchParams();
  // 编辑状态时的目标id
  const targetIdParam = searchParams.get("targetId");
  const [targetDetail, setTargetDetail] = useState<TDeskTargetDetailRes>();
  const [loading, setLoading] = useState(true);
  const [pageError, setPageError] = useState(false);
  const [completePopupVisible, setCompletePopupVisible] = useState(false);
  const [requestLoading, setRequestLoading] = useState(false);
  // 初始化数据
  const initData = async () => {
    // 查询详情数据
    try {
      loggerRef.current = new SafeLogger("desk-target-info");
      if (!targetIdParam) {
        setPageError(true);
        loggerRef.current?.error("page-blocking", {
          reason: "地址栏未查询到目标id参数",
        });
        return;
      }
      const { data } = await getDeskTargetDetail({
        targetId: targetIdParam,
        ignoreError: true,
      });
      if (data?.targetId) {
        setPageError(false);
        setTargetDetail(data);
      } else {
        setPageError(true);
        loggerRef.current?.error("page-blocking", {
          reason: "目标详情接口未查询到目标信息",
          params: {
            targetIdParam,
          },
        });
      }
    } catch (error) {
      console.error(error);
      setPageError(true);
    } finally {
      setLoading(false);
    }
  };

  // 跳转至编辑目标页
  const jumpToEdit = () => {
    try {
      clickPv("ewt_h5_base_plan_desk_target_info_modify_button_click");
      const targetInfoPage = createURLByType({
        path: "/desk/create-target",
        type: EJumpType.inside,
        originSearch: location.search,
        // 先移出targetId
        removeQueryKeys: ["targetId"],
        // 跳转目标信息页时，增加最新的targetId参数
        addQueryObject: {
          targetId: targetDetail.targetId,
        },
      });
      navigate(targetInfoPage);
    } catch (error) {}
  };

  // 跳转至新建目标页
  const jumpToCreateTarget = () => {
    try {
      clickPv(
        "ewt_h5_base_plan_desk_target_info_create_new_target_button_click",
      );
      const targetInfoPage = createURLByType({
        path: "/desk/create-target",
        type: EJumpType.inside,
        originSearch: location.search,
        // 先移出targetId
        removeQueryKeys: ["targetId"],
      });
      navigate(targetInfoPage);
    } catch (error) {}
  };

  const onComplete = async () => {
    try {
      if (requestLoading) {
        return;
      }
      setRequestLoading(true);
      await postDeskCompleteTarget({
        targetId: targetDetail.targetId,
      });
      setCompletePopupVisible(false);
      // 完结后冲洗查询详情
      initData();
    } catch (error) {
      console.error(error);
    } finally {
      setRequestLoading(false);
    }
  };

  // 页面渲染
  useEffect(() => {
    initData();
  }, []);

  const progressImg = targetDetail?.completeStatus
    ? ProgressEndPng
    : ProgressIngPng;

  return (
    <Layout
      showHeader
      // 给安全区染色
      setChildrenContainerHeight={({ top }) => {
        return `calc(100vh - ${top || 0}px)`;
      }}
      className={Styles["target-info-layout"]}
      headerProps={{
        children: "我的目标",
        className: Styles.header,
        back: <PageBackIcon />,
        onBack: () => {
          try {
            // 如果是跳转过来的，就退回去，否则就关闭 webview
            const isFrom = location.search.indexOf("from") !== -1;
            if (isFrom) {
              navigate(-1);
            } else {
              closeWebview();
            }
          } catch (error) {
            console.error("关闭webview失败", error);
          }
        },
      }}
    >
      {loading && (
        <div className={Styles.loading}>
          <PageLoading />
        </div>
      )}

      {!loading && pageError && (
        <div
          style={{
            height: `calc(100vh - 12.8vw)`,
          }}
        >
          <NetworkErrorStatus
            buttonOption={{
              handleClick: () => {
                initData();
              },
            }}
          />
        </div>
      )}

      {!loading && !pageError && (
        <>
          <div className={Styles["content"]}>
            <div className={Styles["form-block"]}>
              {/* 截止日期 */}
              <div className={Styles["form-item"]}>
                <div className={Styles["form-item-label-inner"]}>
                  <div className={Styles["form-item-label"]}>截止日期</div>
                </div>
                <div className={Styles["date-picker-c"]}>
                  {!!targetDetail?.endTime &&
                    dayjs(+targetDetail.endTime).format("YYYY-MM-DD")}
                </div>
              </div>
              {/* 我的目标 */}
              <div className={Styles["form-item"]}>
                <div className={Styles["form-item-label-inner"]}>
                  <div className={Styles["form-item-label"]}>我的目标</div>
                </div>
              </div>
              <div className={Styles["target-info-detail-box"]}>
                {/* 背景图 */}
                <img
                  src={TargetInfoImg}
                  alt=""
                  className={Styles["target-info-bg-img"]}
                />
                {/* 进行中戳 */}
                <img
                  src={progressImg}
                  alt=""
                  className={Styles["target-info-progress-img"]}
                />
                {/* 目标信息 */}
                <div className={Styles["target-info-detail-content"]}>
                  <div className={Styles["subject-name"]}>
                    {targetDetail?.subjectName}
                  </div>
                  <div className={Styles["action-text"]}>
                    <Ellipsis
                      direction="end"
                      rows={2}
                      content={targetDetail?.actionName}
                    />
                  </div>
                  <div className={Styles["amount-text"]}>
                    <div>{targetDetail?.amountName}</div>
                    {!!targetDetail?.unit && (
                      <div className={Styles["amount-unit"]}>
                        {targetDetail.unit}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className={Styles["footer"]}>
            {targetDetail?.completeStatus ? (
              <PrimaryButton
                text="新建目标"
                onClick={jumpToCreateTarget}
                className={Styles["create-target-btn"]}
              />
            ) : (
              <>
                <CancelButton text="修改" onClick={jumpToEdit} />
                <PrimaryButton
                  text="完结"
                  onClick={() => {
                    clickPv(
                      "ewt_h5_base_plan_desk_target_info_complete_button_click",
                    );
                    setCompletePopupVisible(true);
                  }}
                />
              </>
            )}
          </div>
        </>
      )}

      <CompletePopup
        visible={completePopupVisible}
        onOk={onComplete}
        onClose={() => setCompletePopupVisible(false)}
      />
    </Layout>
  );
};
