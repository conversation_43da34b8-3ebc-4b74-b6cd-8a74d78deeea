.target-info-layout {
  .header {
    :global {
      .adm-nav-bar-left {
        flex: 0 1 auto;
        .adm-nav-bar-back {
          margin-right: 0;
        }
      }
      .adm-nav-bar-title {
        text-align: left;
      }
    }
  }
  .content {
    overflow-x: hidden;
    overflow-y: auto;
    height: calc(100vh - 12.8vw - 72px);
    background-color: #cc926d;
    background-image: url("@/assets/image/target-info/bg.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    .form-block {
      width: 350px;
      position: relative;
      padding: 12px 16px;
      margin: 19px 12px;
      background: #fff;
      border: 1px solid #000000;
      border-radius: 8px;
      .form-item {
        position: relative;
        z-index: 2;
        margin-bottom: 24px;
        .date-picker-c {
          display: flex;
          align-items: center;
          padding-left: 12px;
          height: 40px;
          background: #F3F4F8;
          border: 1px solid #021e66;
          border-radius: 8px;
          font-weight: bold;
          font-size: 14px;
          color: #333;
        }

        .form-item-label-inner {
          margin-bottom: 12px;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
        .form-item-label {
          position: relative;
          display: flex;
          align-items: center;
          height: 22px;
          line-height: 22px;
          font-weight: bold;
          font-size: 16px;
          color: #333333;
          &::before {
            display: block;
            content: "";
            width: 4px;
            left: 0;
            height: 10px;
            background: #2f86ff;
            border-radius: 8px;
            border-radius: 2px;
            margin-right: 8px;
          }
        }
      }

      .target-info-detail-box {
        width: 318px;
        min-height: 253px;
        position: relative;
        z-index: 1;

        .target-info-bg-img {
          width: 318px;
          height: 253px;
          position: absolute;
          z-index: 2;
        }

        .target-info-progress-img {
          width: 120px;
          height: 84px;
          position: absolute;
          z-index: 3;
          top: 45px;
          right: 0;
        }

        .target-info-detail-content {
          position: absolute;
          z-index: 3;
          top: 80px;
          left: 16px;
          width: 180px;
          height: 180px;

          .subject-name {
            font-weight: bold;
            font-size: 30px;
            color: #333333;
            line-height: 42px;
            height: 42px;
          }

          .action-text {
            font-weight: bold;
            font-size: 20px;
            color: #666;
            line-height: 28px;
          }

          .amount-text {
            font-weight: bold;
            font-size: 50px;
            color: #2E86FF;
            display: flex;
            align-items: flex-end;

            .amount-unit {
              font-size: 20px;
              padding-bottom: 14px;
            }
          }
        }
      }
    }
  }
  .footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    width: 100%;
    padding: 16px 15px;
    padding-bottom: calc(constant(safe-area-inset-bottom) + 16px);
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fff;
    > div {
      border: 1px solid #021e66;
      box-shadow: 0 4px 0 0 #021e6626;
    }

    .create-target-btn {
      width: 343px;
    }
  }
}
