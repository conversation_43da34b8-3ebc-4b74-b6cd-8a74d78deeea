.white-back {
  color: #fff!important;
}

.plan-detail-bg {
  background-repeat: no-repeat;
  background-size: 100%;
  filter: blur(4px);
  transform: scale(1.1);
  &::before {
    display: block;
    content: "";
    position: absolute;
    width: 100vw;
    height: 180px;
    background: rgba(0, 0, 0, 0.5);
  }
}

.plan-detail-layout {
  .header-block {
    position: relative;
    padding: 0 12px;
    margin-top: 12px;
    color: rgba(255, 255, 255, 0.85);
    margin-bottom: 20px;
    .desc-block {
      margin-bottom: 4px;
    }
    .extra-info-wrapper {
      display: flex;
    }
    .extra-info {
      flex: 1;
      display: flex;
      .tag {
        height: 16px;
        line-height: 16px;
        padding: 0 3px;
        border-radius: 2px;
        margin-right: 12px;
        background: rgba(255, 255, 255, 0.25);
        &.week {
          background-color: #ffd52f;
          color: rgba(0, 0, 0, 0.85);
        }
      }
      .time {
        margin-right: 12px;
      }
      .subject {
        white-space: nowrap;
        text-overflow: ellipsis;
        flex: 1;
        overflow: hidden;
      }
    }
    .more {
      white-space: nowrap;
      cursor: pointer;
    }
    .select {
      z-index: 2;
      position: absolute;
      width: 158px;
      line-height: 22px;
      height: 22px;
      color: #ffffff;
      text-align: center;
      background: #52c41a;
      border-radius: 30px;
      left: 50%;
      transform: translate(-50%, 50%);

      .in-icon {
        font-size: 12px;
        scale: 0.8;
        margin-left: 2px;
      }
    }
  }
  .header {
    background-color: transparent;
    height: 44px;
    :global {
      .adm-nav-bar-left {
        flex: 0 1 auto;
        margin-right: 6px;
        .adm-nav-bar-back {
          margin-right: 0;
        }
      }
      .adm-nav-bar-title {
        text-align: left;
        color: #fff;
        padding: 0;
      }
    }
  }
  .content {
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    overflow: auto;
    display: flex;
    flex-direction: column;
    height: calc(100% - 112px);
    background-color: #fff;
    .detail-block {
      display: flex;
      flex: 1;
      overflow: auto;
      .list-block {
        padding: 12px 12px 0;
        flex: 1;
        overflow: auto;
        -ms-overflow-style: none; /* IE/Edge */
        scrollbar-width: none; /* Firefox */
        .ext-status-block {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
          flex-direction: column;
          .empty-img {
            display: block;
            width: 120px;
            height: 120px;
          }
          .empty-text {
            margin-top: 12px;
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            line-height: 20px;
          }
        }
        &::-webkit-scrollbar {
          display: none;
        }
        .list-item-wrapper {
          padding: 12px 0;
          border-bottom: 1px solid #eeeeee;
        }
        .block-b {
          margin-top: 8px;
        }
        .block,
        .block-b {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .name {
            width: 223px;
            font-weight: 400;
            line-height: 20px;
            font-size: 14px;
            color: #333333;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
          }
          .link {
            cursor: pointer;
            display: flex;
            align-items: center;
            font-size: 12px;
            color: #999999;
            :global {
              .svg-icon {
                margin-left: 4px;
                zoom: 0.8;
              }
            }
          }
          .tags {
            display: flex;
            .tag {
              padding: 0 4px;
              margin-right: 4px;
              text-align: center;
              height: 18px;
              background: #eaf6ff;
              border: 1px solid #c5ddff;
              border-radius: 2px;
              font-weight: 400;
              font-size: 12px;
              color: #2f86ff;
              line-height: 18px;
            }
          }
          .ext-info {
            font-weight: 400;
            font-size: 12px;
            color: #666666;
            line-height: 18px;
            display: flex;
            align-items: center;
            :global{
              .svg-icon{
                font-size: 14px;
                margin-right: 4px;
              }
            }
          }
        }
      }
      :global {
        .adm-list-body {
          border: none;
        }
        .adm-side-bar {
          width: 72px;
          -ms-overflow-style: none; /* IE/Edge */
          scrollbar-width: none; /* Firefox */
          &::-webkit-scrollbar {
            display: none;
          }
        }
        .adm-badge-wrapper {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
          width: 100%;
        }
        .adm-side-bar-item-highlight {
          left: 0;
          top: 0;
          width: 2px;
          height: 50px;
        }
        .adm-side-bar-item {
          padding: 0;
          height: 50px;
          font-weight: 400;
          font-size: 14px;
          color: #2a333a;
          text-align: center;
          line-height: 22px;
          &-active {
            font-weight: 600;
            color: #2e86ff;
          }
          &:first-child {
            padding-top: 12px;
            height: 62px;
          }
        }
      }
    }
    .footer {
      min-height: 72px;
      padding: 16px 15px;
      &.safe {
        padding-bottom: calc(constant(safe-area-inset-bottom) + 16px);
        padding-bottom: calc(env(safe-area-inset-bottom) + 16px);
        border-top: 1px solid #ddd;
      }
      > div {
        border: 1px solid #021e66;
        box-shadow: 0 4px 0 0 rgba(2, 30, 102, 0.15);
      }
    }
  }
}

.confirm-modal-body {
  padding: 24px 0 16px;
  width: 343px;
  border-radius: 24px !important;
  background: #ffffff;
  :global {
    .adm-center-popup-close {
      position: absolute;
      right: 20px;
      top: 20px;
    }
    .adm-modal-title {
      font-weight: 600;
      font-size: 18px;
      color: #333333;
      line-height: 18px;
      margin-bottom: 24px;
      padding: 0 16px;
    }
    .adm-modal-content {
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      line-height: 22px;
      padding: 0 16px;
      margin-bottom: 36px;
    }
    .adm-space.adm-space-block.adm-space-vertical.adm-modal-footer {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      padding: 0 16px;
      .adm-space-item {
        margin-bottom: 0;
        width: 148px;
        height: 44px;
        .adm-button {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 148px;
          height: 44px;
          font-weight: 600;
          color: #021e66;
          background: #ffffff;
          border: 1px solid #021e66;
          box-shadow: 0 4px 0 0 rgba(2, 30, 102, 0.15);
          border-radius: 30px;
          font-size: 16px;
        }
        .adm-modal-button-primary {
          background: #2f86ff;
          border: 1px solid #021e66;
          box-shadow: 0 4px 0 0 rgba(2, 30, 102, 0.15);
          border-radius: 30px;
          color: #fff;
        }
        &:first-child {
          order: 1;
        }
      }
    }
  }
}
.popup-title{
  color: #333333;
}
.popup-container {
  .body {
    max-height: 500px;
    overflow: auto;
    padding: 0 12px;
    margin-top: 12px;
    margin-bottom: 20px;
    .name-block {
      color: #333333;
      font-weight: 600;
      font-size: 14px;
      margin-bottom: 8px;
    }
    .desc-block {
      margin-bottom: 8px;
      color: #666666;
      font-size: 12px;
    }
    .extra-info {
      font-size: 12px;
      color: #999999;
      line-height: 16px;
    }
  }
}

.delete-plan-btn {
  background: #FFFFFF;

  span {
    color: #021E66!important;
    font-weight: bold!important;
    font-size: 16px!important;
  }
}