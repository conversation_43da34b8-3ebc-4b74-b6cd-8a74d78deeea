import React, { useRef, useState } from "react";
import { useRequest, useSetState, useMount } from "ahooks";
import dayjs from "dayjs";
import { useSearchParams } from "react-router-dom";
import {
  SideBar,
  InfiniteScroll,
  List,
  SpinLoading,
  Modal,
  Toast,
  Ellipsis,
} from "antd-mobile";

import { Layout, Button, IconSvg, EButtonType } from "@/components";
import Popup from "@/components/popup";

import * as planApi from "@/service/desk/plan";
import PlanEmpty from "@/assets/common/desk/transition-bg-error.png";
import Styles from "./style.module.scss";
import {
  openRoute,
  openWebView,
  makeAnswerReportUrl,
  makeAnswerUrl,
  getChannel,
  parseDeskPlanTime,
  clickPv,
  expPv,
  separatelyReport,
  getLocalStorageToValue,
} from "@/utils/tool";
import PageBackIcon from "@/components/page-back-icon";
import { useVisibilitychange } from "@/hooks";

import { LOCAL_KEY } from "@/routes/desk/chat/components/DialogInterface";

import Logger from "@/utils/safe-logger";

const SafeLogger = new Logger("desk-plan-detail");

import SelectPlanTimePopup from "../components/select-plan-time-popup";

// 资源类型
export enum EResourceType {
  package = 1,
  paper = 2,
  fm = 3,
}
// 学习模式，1 周计划, 2 日计划, 3 无周期计划
export enum ELearnModeType {
  week = 1,
  day = 2,
  none = 3,
}

// 来源
export enum sourceType {
  template = 2,
  chat = 3,
}

const sourceTypeToName = {
  // 1: "自主创建",
  [sourceType.template]: "计划广场",
  [sourceType.chat]: "智能诊断",
};

/**
 * 智学计划
 */
export const contentTypeToMap = {
  [EResourceType.package]: "视频课",
  [EResourceType.paper]: "试卷",
  [EResourceType.fm]: "FM",
};

const transformMToV = (m) => {
  if (Object.keys(m)?.length <= 0) return {};
  const { paperVO, lessonVO, fmVO, ...restM } = m;
  const sourceToMap = {
    [EResourceType.package]: lessonVO,
    [EResourceType.paper]: paperVO,
    [EResourceType.fm]: fmVO,
  };
  const sourceM = sourceToMap[m.resourceType] || {};
  const extInfoToMap = {
    [EResourceType.package]: `${Math.max(1, Math.round(sourceM?.duration / 60))}分钟`,
    [EResourceType.paper]: `${sourceM?.duration}分钟/${sourceM?.questionNum}题`,
    [EResourceType.fm]: `${Math.max(1, Math.round(sourceM?.duration / 60))}分钟`,
  };
  const sourceNameToMap = {
    [EResourceType.package]: lessonVO?.lessonName,
    [EResourceType.paper]: paperVO?.paperName,
    [EResourceType.fm]: fmVO?.fmName,
  };
  const v = {
    ...restM,
    source: {
      ...sourceM,
      extInfo: extInfoToMap[m.resourceType],
      name: sourceNameToMap[m.resourceType],
    },
    resourceName: contentTypeToMap[m.resourceType],
  };
  return v;
};

export const Component: React.FC = () => {
  const toDetailId = useRef<number | null>(null);
  const [searchParams, setSearchParams] = useSearchParams();
  const templatePlanId = searchParams.get("templatePlanId");
  const diagnosticTemplatePlanId = searchParams.get("diagnosticTemplatePlanId");

  const [selectSideBarKey, setSelectSideBarKey] = useState("");
  const [visible, setVisible] = useState<boolean>(false);
  const [selectPlanTimeVisible, setSelectPlanTimeVisible] =
    useState<boolean>(false);
  const [hasMore, setHasMore] = useState<boolean>(false);

  const [params, setParams] = useSetState({
    pageSize: 10,
    pageIndex: 1,
    groupId: undefined,
    templatePlanId: undefined,
  });
  const [resourceListLoading, setResourceListLoading] =
    useState<boolean>(false);
  const [changeLoading, setChangeLoading] = useState<boolean>(false);
  const [resourceList, setResourceList] = useState<
    Desk.templatePlan.resourceList.Data[]
  >([]);

  const api = async () => {
    if (diagnosticTemplatePlanId) {
      return await planApi.getSynthesisPlanDetail({
        diagnosticTemplatePlanId: diagnosticTemplatePlanId,
      });
    }
    return await planApi.getTemplatePlanDetail({
      templatePlanId: templatePlanId,
    });
  };

  const { loading, data, runAsync } = useRequest(api, {
    manual: true,
  });

  useMount(async () => {
    try {
      const res = await runAsync();
      separatelyReport("ewt_h5_base_plan_desk_plan_square_detail_view", {
        sourceTypeName: diagnosticTemplatePlanId
          ? sourceTypeToName[sourceType.chat]
          : sourceTypeToName[sourceType.template],
        subject: res?.data?.subjectList?.map((v) => v.subjectName)?.join("、"),
      });
      const [select] = res?.data?.resourceGroupList || [];
      const payload = {
        ...params,
        templatePlanId: select?.templatePlanId,
      };
      if (select) {
        const defaultFirstKey = "0";
        setSelectSideBarKey(defaultFirstKey);
        payload.groupId = select.groupId;
        setParams(payload);
        resourceRun(payload);
      }
    } catch (error) {
      SafeLogger.error("get-detail-info", {
        error,
        diagnosticTemplatePlanId,
        templatePlanId,
      });
    }
  });

  const detail = (data?.data ||
    {}) as Desk.templatePlan.detail.Response["data"];

  const isWeekAndDayPlan = [ELearnModeType.day, ELearnModeType.week].includes(
    detail?.learnMode,
  );

  const { runAsync: remove, loading: removeLoading } = useRequest(
    planApi.postPlanRemove,
    {
      manual: true,
    },
  );

  const { runAsync: add, loading: addLoading } = useRequest(planApi.planAdd, {
    manual: true,
  });

  const onBack = () => {
    if (mstJsBridge.isInMstApp()) {
      mstJsBridge.closeWebview();
    } else {
      history.back();
    }
  };

  const resourceRun = async (payload) => {
    setResourceListLoading(true);
    setChangeLoading(true);
    setResourceList([]);
    const channel = await getChannel();
    const defaultPayload = {
      ...params,
      channel,
      ...payload,
    };
    try {
      const {
        data: { data: list, haveNextPage },
      } = await planApi.getResourceList(defaultPayload);
      setResourceList(list || []);
      setHasMore(haveNextPage);
      setResourceListLoading(false);
      setChangeLoading(false);
    } catch (error) {
      setResourceListLoading(false);
      setChangeLoading(false);
      SafeLogger.error("get-resource-list", { error });
    }
  };

  const loadMore = async () => {
    if (!hasMore) return;
    setResourceListLoading(true);
    const channel = await getChannel();
    const payload = {
      ...params,
      pageIndex: params.pageIndex + 1,
      channel,
    };
    try {
      const {
        data: { data: list, haveNextPage },
      } = await planApi.getResourceList(payload);
      setParams(payload);
      setHasMore(haveNextPage);
      setResourceList([...resourceList, ...(list || [])]);
      setResourceListLoading(false);
    } catch (error) {
      setResourceListLoading(false);
      SafeLogger.error("get-resource-list", { error });
      throw new Error("request failed");
    }
  };

  const onToClick = (data) => {
    if (data?.hasForbid) {
      Toast.show({
        content: "该资源不支持校园版App播放，请于学生版App查看",
      });
      return;
    }
    const source = data.source;
    const clickToMap = {
      [EResourceType.package]: () => {
        openRoute({
          domain: "media",
          action: "commonPlayer",
          params: {
            playVideoId: source.lessonId,
            isOffLineMode: false,
          },
        });
      },
      [EResourceType.paper]: () => {
        const config = {
          paperId: source.paperId,
          reportId: source.reportId || undefined,
          bizCode: source.bizCode,
          platform: source.platform,
          homeworkId: source.planId,
        };
        openWebView(
          data.finishStatus && source.reportId
            ? makeAnswerReportUrl(config)
            : makeAnswerUrl(config),
        );
      },
      [EResourceType.fm]: () => {
        openRoute({
          domain: "fm",
          action: "open_detail",
          params: {
            id: `${source.fmId}`,
          },
        });
      },
    };
    clickToMap?.[data.resourceType]?.();
  };

  const onDelete = () => {
    clickPv(
      "ewt_h5_base_plan_desk_plan_square_detail_create_plan_button_click",
      {
        status: "移出计划",
        sourceTypeName: diagnosticTemplatePlanId
          ? sourceTypeToName[sourceType.chat]
          : sourceTypeToName[sourceType.template],
        subject: detail?.subjectList?.map((v) => v.subjectName)?.join("、"),
      },
    );
    Modal.confirm({
      className: Styles["confirm-modal"],
      content: "删除后将无法恢复，确定删除该计划吗？",
      title: "提示",
      showCloseButton: true,
      bodyClassName: Styles["confirm-modal-body"],
      onConfirm: async () => {
        try {
          const { success } = await remove({
            planId: detail?.planId,
            templatePlanId: diagnosticTemplatePlanId
              ? undefined
              : detail?.templatePlanId,
            diagnosticTemplatePlanId,
          });
          if (!success) {
            Toast.show({
              content: "删除失败",
            });
            return;
          }
          runAsync();
          Toast.show({
            content: "删除成功",
          });
        } catch (error) {
          Toast.show({
            content: "删除失败",
          });
          SafeLogger.error("remove-plan", { error });
        }
      },
    });
  };

  const onRun = async (payload) => {
    try {
      const { success } = await add(payload);

      if (!success) {
        Toast.show({
          content: "创建失败",
        });
        return;
      }
      const detailCurrentRes = await runAsync();
      setSelectPlanTimeVisible(false);
      Toast.show({
        content: "创建成功",
      });
      setTimeout(() => {
        if (detailCurrentRes?.data.planId) {
          toDetailId.current = detailCurrentRes?.data.planId;
          const url = `${location.origin}/ewtcustomerh5/desk/plan-resource?planId=${detailCurrentRes?.data.planId}&showTopBar=false`;
          openWebView(url);
        }
      }, 500);
    } catch (error) {
      console.error(error);
      const { code } = error || {};
      if (code && code == 7774007) {
        Toast.clear();
        Toast.show({
          content: "计划已创建，页面即将刷新",
          duration: 0,
          maskClickable: false,
        });
        setTimeout(() => {
          window.location.reload();
        }, 3000);
      } else {
        Toast.show({
          content: "创建失败",
        });
      }
      SafeLogger.error("add-plan", { error });
    }
  };

  const onAdd = async () => {
    clickPv(
      "ewt_h5_base_plan_desk_plan_square_detail_create_plan_button_click",
      {
        status: "加入计划",
        sourceTypeName: diagnosticTemplatePlanId
          ? sourceTypeToName[sourceType.chat]
          : sourceTypeToName[sourceType.template],
        subject: detail?.subjectList?.map((v) => v.subjectName)?.join("、"),
      },
    );

    const today = dayjs(+detail?.currentTime).day();
    const offsetM = {
      0: 1, // 周日
      1: 0, // 周一
      2: 6,
      3: 5,
      4: 4,
      5: 3,
      6: 2,
    };
    /** 日计划 */
    if ([ELearnModeType.day].includes(detail?.learnMode)) {
      setSelectPlanTimeVisible(true);
      expPv("ewt_h5_base_plan_desk_plan_square_detail_day_plan_popup_expo", {
        sourceTypeName: diagnosticTemplatePlanId
          ? sourceTypeToName[sourceType.chat]
          : sourceTypeToName[sourceType.template],
      });
      return;
    }

    /** 无计划 || 周一 */
    if ([ELearnModeType.none].includes(detail?.learnMode) || !offsetM[today]) {
      onRun({
        templatePlanId: detail?.templatePlanId,
        beginFromThisWeek: true,
      });
      return;
    }
    /** 周计划 非周一 */
    Modal.show({
      className: Styles["confirm-modal"],
      content: `如果今天开始，就需要用本周剩余的【${offsetM[today]}天】完成第一周的内容；如果下周一开始，就可以完整7天学习一周的内容。请选择你的计划生效时间。`,
      title: "提示",
      showCloseButton: true,
      bodyClassName: Styles["confirm-modal-body"],
      closeOnAction: true,
      actions: [
        {
          key: "2",
          text: "今天开始",
          primary: true,
          onClick: () => {
            onRun({
              templatePlanId: detail?.templatePlanId,
              beginFromThisWeek: true,
            });
          },
        },
        {
          key: "1",
          text: "下周一开始",
          onClick: () => {
            onRun({
              templatePlanId: detail?.templatePlanId,
              beginFromThisWeek: false,
            });
          },
        },
      ],
    });
  };

  const onSubmit = () => {
    if (removeLoading || addLoading) {
      return;
    }
    detail?.planId ? onDelete() : onAdd();
  };

  // 返回后重新刷新包状态
  const handleRefreshConfig = (isShow: boolean) => {
    if (isShow && toDetailId.current) {
      toDetailId.current = null;
      runAsync();
    }
  };

  // 页面的可见/不可见监控，变更后查询最新的奖励状态
  useVisibilitychange({
    handleVisibilitychange: handleRefreshConfig,
  });

  const description = diagnosticTemplatePlanId
    ? getLocalStorageToValue(LOCAL_KEY)
    : detail.desc;

  return (
    <Layout
      showHeader
      setChildrenContainerHeight={({ top, bottom }) => {
        return `calc(100vh - ${top || 0}px - ${bottom || 0}px)`;
      }}
      className={Styles["plan-detail-layout"]}
      headerProps={{
        children: detail?.name,
        className: Styles.header,
        back: <PageBackIcon className={Styles["white-back"]} />,
        onBack,
      }}
      wrapperHidden
      bgClassName={Styles["plan-detail-bg"]}
      bgProps={{
        style: {
          backgroundImage: detail?.backgroundImg
            ? `url(${detail?.backgroundImg})`
            : "none",
        },
      }}
    >
      <div className={Styles["header-block"]}>
        <div className={Styles["desc-block"]}>
          <Ellipsis content={description} />
        </div>
        <div className={Styles["extra-info-wrapper"]}>
          <div className={Styles["extra-info"]}>
            <div className={`${Styles["tag"]} ${Styles["week"]}`}>
              {detail?.weekCount}
              {detail?.learnMode === ELearnModeType.week ? "周" : "天"}
              计划
            </div>
            {/* <div className={Styles["tag"]}>{detail.stage}</div> */}
            <div className={Styles["time"]}>
              {parseDeskPlanTime(detail.learnTime)}
            </div>
            <div className={Styles["subject"]}>
              {detail?.subjectList?.map((v) => v.subjectName)?.join("、")}
            </div>
          </div>
          <div className={Styles["more"]} onClick={() => setVisible(true)}>
            完整介绍 <IconSvg name="icon-a-jinru2x" />
          </div>
        </div>
        {detail.planId && (
          <div
            className={Styles["select"]}
            onClick={() => {
              clickPv(
                "ewt_h5_base_plan_desk_plan_square_detail_jump_plan_resource_click",
                {
                  sourceTypeName: diagnosticTemplatePlanId
                    ? sourceTypeToName[sourceType.chat]
                    : sourceTypeToName[sourceType.template],
                  subject: detail?.subjectList
                    ?.map((v) => v.subjectName)
                    ?.join("、"),
                },
              );
              toDetailId.current = detail.planId;
              const url = `${location.origin}/ewtcustomerh5/desk/plan-resource?planId=${detail.planId}&showTopBar=false`;
              openWebView(url);
            }}
          >
            <IconSvg name="icon-yijiajihua" /> 已建该计划 点击前往
            <IconSvg name="icon-a-jinru2x" className={Styles["in-icon"]} />
          </div>
        )}
      </div>
      <div className={Styles["content"]}>
        <div className={Styles["detail-block"]}>
          {isWeekAndDayPlan && (
            <SideBar
              activeKey={selectSideBarKey}
              className={Styles["side-block"]}
              onChange={(v) => {
                if (resourceListLoading) return;
                const it = detail?.resourceGroupList[v];
                setParams({
                  pageSize: 10,
                  pageIndex: 1,
                  groupId: it.groupId,
                  templatePlanId: it?.templatePlanId,
                });
                resourceRun({
                  pageSize: 10,
                  pageIndex: 1,
                  groupId: it.groupId,
                  templatePlanId: it?.templatePlanId,
                });
                setSelectSideBarKey(v);
              }}
            >
              {detail?.resourceGroupList?.map?.((item, i) => (
                <SideBar.Item key={i} title={item.groupName} />
              ))}
            </SideBar>
          )}
          <div className={Styles["list-block"]}>
            {resourceList.length <= 0 && !loading ? (
              <div className={Styles["ext-status-block"]}>
                {changeLoading ? (
                  <SpinLoading color="primary" />
                ) : (
                  <>
                    <img className={Styles["empty-img"]} src={PlanEmpty} />
                    <span className={Styles["empty-text"]}>暂无内容</span>
                  </>
                )}
              </div>
            ) : (
              <>
                <List>
                  {resourceList.map((v, i) => {
                    const viewSource = transformMToV(v);
                    return (
                      <div key={i} className={Styles["list-item-wrapper"]}>
                        <div className={Styles["block"]}>
                          <span
                            className={Styles["name"]}
                            onClick={() => onToClick(viewSource)}
                          >
                            {viewSource?.source?.name}
                          </span>
                          <div
                            className={Styles["link"]}
                            onClick={() => onToClick(viewSource)}
                          >
                            预览 <IconSvg name="icon-a-jinru2x" />
                          </div>
                        </div>
                        <div className={Styles["block-b"]}>
                          <span className={Styles["tags"]}>
                            <span className={Styles["tag"]}>
                              {viewSource.subjectName}
                            </span>
                            <span className={Styles["tag"]}>
                              {viewSource.resourceName}
                            </span>
                          </span>
                          <div className={Styles["ext-info"]}>
                            <IconSvg name="icon-linear-a-shijianlishijilu" />
                            {viewSource?.source?.extInfo}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </List>
                <InfiniteScroll
                  threshold={5}
                  loadMore={loadMore}
                  hasMore={hasMore}
                >
                  {(more, failed, retry) => {
                    if (failed) {
                      return (
                        <span>
                          加载失败！
                          <a onClick={retry}>重试</a>
                        </span>
                      );
                    }
                    if (more) {
                      return <span>正在加载更多内容...</span>;
                    }
                    return <span>没有更多内容了</span>;
                  }}
                </InfiniteScroll>
              </>
            )}
          </div>
        </div>
        {Object.keys(detail || {}).length > 0 && (
          <div className={`${Styles["footer"]} ${Styles["safe"]}`}>
            <Button
              loading={removeLoading || addLoading || loading}
              onClick={onSubmit}
              text={detail?.planId ? "删除创建的计划" : "创建该计划"}
              type={detail?.planId ? EButtonType.grey : EButtonType.Blue}
              className={detail?.planId ? Styles["delete-plan-btn"] : ""}
            />
          </div>
        )}
      </div>
      <SelectPlanTimePopup
        visible={selectPlanTimeVisible}
        confirmLoading={addLoading || loading}
        onClose={() => setSelectPlanTimeVisible(false)}
        onConfirm={(val) => {
          const { startTime, restDays } = val || {};
          clickPv(
            "ewt_h5_base_plan_desk_plan_square_detail_day_plan_popup_click",
            {
              sourceTypeName: diagnosticTemplatePlanId
                ? sourceTypeToName[sourceType.chat]
                : sourceTypeToName[sourceType.template],
            },
          );
          onRun({
            startTime,
            restDayList: restDays,
            diagnosticTemplatePlanId: diagnosticTemplatePlanId || undefined,
            templatePlanId: templatePlanId || undefined,
            sourceType: diagnosticTemplatePlanId ? sourceType.chat : undefined,
          });
        }}
      />
      <Popup
        visible={visible}
        title="完整介绍"
        titleClassName={Styles["popup-title"]}
        onClose={() => setVisible(false)}
      >
        <div className={Styles["popup-container"]}>
          <div className={Styles["body"]}>
            <div className={Styles["name-block"]}>{detail.name}</div>
            <div className={Styles["desc-block"]}>{description}</div>
            <div className={Styles["extra-info"]}>
              {detail.weekCount}
              {detail?.learnMode === ELearnModeType.week ? "周" : "天"}计划
              {/* {detail.stage}*/} {parseDeskPlanTime(detail.learnTime)}&nbsp;
              {detail?.subjectList?.map((v) => v.subjectName)?.join("、")}
            </div>
          </div>
        </div>
      </Popup>
    </Layout>
  );
};
