.target-summary-layout {
  .header {
    :global {
      .adm-nav-bar-left {
        flex: 0 1 auto;
        .adm-nav-bar-back {
          margin-right: 0;
        }
      }
      .adm-nav-bar-title {
        text-align: left;
      }
    }
  }
  .content {
    overflow: auto;
    max-height: calc(100vh - 12.8vw - 72px);
    height: calc(100vh - 12.8vw - 72px);
    background-color: #cc926d;
    .form-block {
      padding: 12px 16px;
      margin: 19px 12px;
      background: #ffffff;
      border: 1px solid #000000;
      border-radius: 2px;
      .form-item {
        margin-bottom: 32px;
        .text-area {
          padding: 12px;
          box-sizing: border-box;
          min-height: 84px;
          background: #ffffff;
          border: 1px solid #021e66;
          border-radius: 8px;
        }
        .radio-block{
          display: flex;
          justify-content: space-between;
          .radio{
            cursor: pointer;
            font-weight: 600;
            font-size: 16px;
            color: #373737;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 154px;
            height: 40px;
            border-radius: 24px;
            background: #FFFFFF;
            border: 1px solid #021E66;
            &.checked{
              background: #EEF4FF;
              color: #2E86FF;
              border: 1px solid #2E86FF;
            }
          }
        }
        :global {
          .adm-text-area-count {
            padding: 0;
            font-weight: 600;
            font-size: 12px;
            color: #999999;
          }
          .adm-text-area:has(>.adm-text-area-element[disabled]){
            background-color: #F4F6FA;
          }
          .adm-text-area-element {
            line-height: 25px;
            font-weight: 500;
            font-size: 18px;
            color: #333333;
            min-height: 60px;

            &:disabled{
              font-weight: 500;
              font-size: 14px;
              color: #333333;
            }
          }
          .adm-text-area-element::placeholder {
            line-height: 20px;
            font-weight: 400;
            font-size: 14px;
            color: #999999;
          }
        }
        .form-item-label {
          margin-bottom: 12px;
          display: flex;
          align-items: center;
          height: 22px;
          line-height: 22px;
          font-weight: 600;
          font-size: 16px;
          color: #333333;

        }
      }
    }
    .normal-block{
      position: relative;
      height: 60px;
      background: #F4F6FA;
      border: 1px solid #021E66;
      border-radius: 8px;
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      line-height: 60px;
      padding-left: 12px;
    }
    .time-picker-block {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 25px;
      line-height: 25px;
      margin-bottom: 6px;
      font-weight: 600;
      font-size: 18px;
      color: #333333;
    }
    .border-b{
      border-bottom: 1px solid #021E66;;
    }
    .promise-action-block{
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: 400;
      font-size: 14px;
      color: #999999;
      > div:last-child{
        display: flex;
        align-items: center;
      }
      .switch{
        --height: 16px;
        --width: 28px;
      }
    }
  }
  .footer {
    display: flex;
    min-height: 72px;
    padding: 16px 15px;
    padding-bottom: calc(constant(safe-area-inset-bottom) + 16px);
    padding-bottom: calc(env(safe-area-inset-bottom) + 16px);
    > div{
      flex: 1;
      border: 1px solid #021E66;
      box-shadow: 0 4px 0 0 #021e6626;
      &:first-child{
        margin-right: 15px;
        background: #FFFFFF;
        font-weight: 600;
        font-size: 16px;
        color: #021E66;
      }
    }

  }
}
