import React, { useRef } from "react";
import { TextArea } from "antd-mobile";
import html2canvas from "html2canvas";
// @ts-ignore
import { EWTFileService, ACLEnum } from "@tip/fs";
import { Layout, ScrollToTop } from "@/components";
import dayjs from "dayjs";

import type { ILayout } from "@/components";
import { Button, EButtonType, IconSvg } from "@/components";

import BackBlackImg from "@/assets/common/back_black.png";
import { useLocation, useNavigate } from "react-router-dom";
import { EJumpType, createURLByType, getUrlParam } from "@/utils/tool";
import Styles from "./style.module.scss";
import MstQtAnalytics from "mst-analytics";
import mstJsBridge from "mst-js-bridge";

const options = {
  appName: "ewt-customer-h5",
  // bucket:
  //   DEPLOYMENT_ENV === "prod"
  //     ? "ewt-infrastructure"
  //     : "ewt-test-infrastructure",
  expire: 900,
};
const client = new EWTFileService(options);

export const Component: React.FC = () => {
  const location = useLocation();
  const courseId = getUrlParam("courseId", location.search);
  const navigate = useNavigate();
  const currentTime = dayjs().format("YYYY-MM-DD");
  const contentRef = useRef(null);

  return (
    <Layout
      showHeader
      // 给安全区染色
      setChildrenContainerHeight={({ top, bottom }) => {
        return `calc(100vh - ${top || 0}px - ${bottom || 0}px)`;
      }}
      className={Styles["target-summary-layout"]}
      headerProps={{
        children: "目标完成情况",
        className: Styles.header,
        backIconUrl: BackBlackImg,
      }}
    >
      <div className={Styles["content"]} ref={contentRef}>
        <div className={Styles["tips-block"]}></div>
        <div className={Styles["form-block"]}>
          <div className={`${Styles["form-item"]} ${Styles["border-b"]}`}>
            <div className={Styles["form-item-label"]}>阶段目标起止时间</div>
            <div className={Styles["time-picker-block"]}>
              <div className={Styles["start-time"]}>{currentTime}</div>
              <div className={Styles["end-time"]}>{currentTime}</div>
            </div>
          </div>
          <div className={Styles["form-item"]}>
            <div className={Styles["form-item-label"]}>我的决心</div>
            <div className={Styles["normal-block"]}>
              每周在EWT学习达到：4小时
            </div>
          </div>
          <div className={Styles["form-item"]}>
            <div className={Styles["form-item-label"]}>承诺达标情况</div>
            <div className={Styles["promise-action-block"]}>
              <div>我的承诺：每周学习40分钟</div>
            </div>
            <div className={Styles["promise-list-block"]}></div>
          </div>
          <div className={Styles["form-item"]}>
            <div className={Styles["form-item-label"]}>感想</div>
            <TextArea
              disabled
              className={Styles["text-area"]}
              value="感想感想文案感想感想文案感想感想文案感想感想文案感想文案感想文案感想文感想感想文案感想感想文案感想感想文案感想感"
            />
          </div>
        </div>
      </div>
      <div className={Styles["footer"]}>
        <Button
          type={EButtonType.grey}
          text="分享"
          onClick={async () => {
            const canvas = await html2canvas(contentRef.current, {
              useCORS: true,
              scale: 2,
              logging: true, // 调试时查看日志
            });
            canvas.toBlob(
              async (blob) => {
                const file = new File([blob], "filename.png", {
                  type: "image/png",
                });
                const url: string = await client.upload(file, null, {
                  acl: ACLEnum.PUBLIC_READ,
                  multipart: false,
                });
                console.log(url, "获取图片url");
              },
              "image/png",
              1,
            );
          }}
        />
        <Button text="设置新目标" />
      </div>
    </Layout>
  );
};
