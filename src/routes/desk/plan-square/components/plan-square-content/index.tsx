import React, { useEffect, useRef, useState } from "react";
import { useSearchParams } from "react-router-dom";
import dayjs from "dayjs";
import { DotLoading, InfiniteScroll, Toast } from "antd-mobile";
import { Spin, InfiniteScrollBottom } from "@/components";
import EmptyStatus, { NetworkErrorStatus } from "@/components/empty-status";
import { SubjectTabSwiper } from "./components/subject-tab-swiper";
import { ResourceItem } from "./components/resource-item";
import { ConfirmModal } from "@/components";
import { throttle, expPv, clickPv } from "@/utils/tool";
import { useVisibilitychange } from "@/hooks";
import SelectPlanTimePopup from "../../../components/select-plan-time-popup";

import "swiper/css";
import styles from "./style.module.scss";

import { ISubjectItem } from "@/service/common";
import {
  getPlanSquareResourceList,
  getPlanSquareSubjectList,
  IPlanSquareResListData,
} from "@/service/desk/plan-square";
import EmptyResultImg from "@/assets/common/desk/transition-bg-error.png";
import {
  getTemplatePlanDetail,
  planAdd,
  postPlanRemove,
} from "@/service/desk/plan";
import { LearnModelEnum } from "@/service/desk/plan-resource";
import EditionPopup, {
  IUserSubjectEditionMap,
} from "@/components/edition-popup";
import useBaseEdition from "@/components/edition-popup/base-edition-hook";

import Logger from "@/utils/safe-logger";
import { PopupType } from "@/routes/subject-channel/components/subject-book-manager";
import { closeWebview } from "@/utils/bridge-utils";
import { ErrorCodeEnum } from "@/utils/constant";
const logger = new Logger("plan-square");

/** 推荐方案的内容组件参数 */
interface IPlanSquareContentProps {
  grade?: string;
  topHeight?: number;
  emptyHeight?: string;
  networkErrorHeight?: string;
  scrollElementId?: string;
  onConditionReady?: (list: any[]) => void;
}

const EmptyResult = () => {
  return (
    <EmptyStatus
      text="很抱歉，计划广场暂无可选择计划"
      image={EmptyResultImg}
      className={styles["empty-result"]}
    />
  );
};

/** 内容组件 */
const PlanSquareContent: React.FC<IPlanSquareContentProps> = (props) => {
  const { emptyHeight, scrollElementId, topHeight, onConditionReady, grade } =
    props;
  const [searchParams, _] = useSearchParams();
  const urlParamsForSubjectId = searchParams.get("subjectId");
  /** 是否加载中。第一次进入时使用 */
  const [loading, setLoading] = useState(true);
  /** 操作加载中不允许多次点击 */
  const [operateLoading, setOperateLoading] = useState(false);
  // 学科切换loading态
  const [isChangeSubject, setIsChangeSubject] = useState(false);
  // 资源加载中
  const [resourceListLoading, setResourceListLoading] = useState(false);
  /** 选中的学科id*/
  const [selectedSubject, setSelectedSubject] = useState(-1);
  /** 选中的学科名称，上报QT用 */
  const [selectedSubjectName, setSelectedSubjectName] = useState("");
  /** 学科列表 */
  const [subjectList, setSubjectList] = useState<ISubjectItem[]>([]);
  /** 学科下对应的资源列表 */
  const [resourceList, setResourceList] = useState<IPlanSquareResListData[]>(
    [],
  );
  const [selectPlanTimeVisible, setSelectPlanTimeVisible] =
    useState<boolean>(false);
  /** 资源分页信息，默认在第一页且有下一页数据，后续根据接口来覆盖 */
  const packagePageInfo = useRef({
    haveNextPage: true,
    pageIndex: 1,
    pageSize: 10,
  });
  // 存储点击的资源id信息，用于更新
  const globalResourceInfo = useRef({
    resourceList: [],
    resourceId: "",
  });

  // 当前操作的资源信息， 用于日计划创建Popup入参
  const currentResourceInfo = useRef<IPlanSquareResListData>();

  /** 学科列表的 swiper 对象 */
  const swiperRef = useRef<any>(null);
  // 学科列表网络错误标识
  const [subjectNetWorkError, setSubjectNetWorkError] = useState(false);
  // 资源列表的网络错误标识
  const [resourceListNetWork, setResourceListNetWork] = useState(false);
  // 请求时序控制，防止展示数据和筛选条件不符合
  const requestFlag = React.useRef<number>(0);
  // 教材版本相关 - 西药弹出教材版本弹窗ref
  const needShowPopupTypeRef = useRef(PopupType.CLOSE);
  // 教材版本相关 - 弹窗类型
  const [popupType, setPopupType] = useState(PopupType.CLOSE);

  // 教材版本相关
  const {
    loading: editionLoading,
    isError: editionError,
    initEditions,
    allSubjectEditionRef,
    userSubjectEditionRef,
    onSaveEdition,
  } = useBaseEdition({ logger });

  // 检查用户 是否对所有 学科都设置了 教材版本
  const checkAllSubjectEdition = async (popupType: PopupType) => {
    needShowPopupTypeRef.current = popupType;
    const isReady = await initEditions();
    if (!isReady) {
      setPopupType(PopupType.EDITION);
      return;
    }
    // 当前是否所有学科都设置了教材版本
    const isAllSubjectHasEdition =
      allSubjectEditionRef.current.length ===
      Object.keys(userSubjectEditionRef.current).length;
    if (!isAllSubjectHasEdition) {
      // 如果存在有学科未选择 教材版本，则必然 弹出 教材版本 弹窗
      setPopupType(PopupType.EDITION);
    } else {
      setPopupType(needShowPopupTypeRef.current);
    }
  };

  // 根据学科id查询资源列表
  const queryResourceListBySubjectId = async (
    subjectId?: number,
    isChangeSubject?: boolean,
  ) => {
    // 第一次查询时需要指定id，只是页数不同时取选中的学科即可
    const reqSubjectId = subjectId || selectedSubject;
    if (reqSubjectId === -1) {
      return;
    }
    requestFlag.current++;
    const nowReqFlag = requestFlag.current;
    setResourceListLoading(true);

    // 设置网络错误的提示和上报
    const setNetError = (params: any) => {
      packagePageInfo.current.haveNextPage = false;
      setResourceListNetWork(true);
      setIsChangeSubject(false);
    };
    try {
      const { data } = await getPlanSquareResourceList({
        ...packagePageInfo.current,
        subjectId: reqSubjectId,
        grade: grade ? Number(grade) : "",
      });

      if (nowReqFlag !== requestFlag.current) {
        return;
      }
      // 判断数据是否符合预期，包含分页数据才能继续
      if (data?.list) {
        const { data: reList, haveNextPage } = data?.list;
        // 如果是自动加载的下一页，就拼接数组显示
        // 如果是切换学科就只显示当前数据
        const newList = subjectId ? [...reList] : [...resourceList, ...reList];
        packagePageInfo.current.haveNextPage = Boolean(haveNextPage);
        globalResourceInfo.current.resourceList = newList;
        setResourceList(newList as IPlanSquareResListData[]);
        setResourceListNetWork(false);
        setIsChangeSubject(false);
      } else {
        setResourceList([]);
        setResourceListNetWork(false);
        setIsChangeSubject(false);
        setLoading(false);
      }

      // 给资源点加载的时间
      window.setTimeout(() => {
        try {
          if (isChangeSubject) {
            const dom = document.getElementById(scrollElementId);
            if (dom) {
              dom.scrollTop = 0;
            }
          }
        } catch (error) {
          console.warn("滚动到顶部出错", error);
        }
      }, 300);
    } catch (error) {
      if (nowReqFlag !== requestFlag.current) {
        return;
      }
      setResourceList([]);
      setNetError({
        error,
        ...packagePageInfo.current,
        subjectId: reqSubjectId,
      });
    } finally {
      if (nowReqFlag !== requestFlag.current) {
        return;
      }
      setResourceListLoading(false);
      setLoading(false);
      setIsChangeSubject(false);
    }
  };
  /** 获取学科列表 */
  const querySubjectList = async () => {
    // 设置网络错误的提示和上报
    const setNetError = (error: any, isNetworkError?: boolean) => {
      if (isNetworkError) {
        setSubjectNetWorkError(true);
      }
      setLoading(false);
      // 无学科列表是阻断类错误，需要告警
      logger.error("page-block-error", {
        reason: "计划广场-学科信息列表为空",
        error,
      });
    };

    try {
      setLoading(true);
      const { data } = await getPlanSquareSubjectList();
      if (data?.subjectList?.length) {
        const [firstSubject] = data.subjectList;
        // 如果url有学科id，则优先使用url的学科id
        const hasTargetSubject = urlParamsForSubjectId
          ? data.subjectList.find(
              (item: ISubjectItem) =>
                item.subjectId === Number(urlParamsForSubjectId),
            )
          : null;
        const saveSubject = hasTargetSubject || firstSubject;
        setSelectedSubject(saveSubject.subjectId);
        setSelectedSubjectName(saveSubject.subjectName);
        setSubjectList([...data.subjectList]);
        setSubjectNetWorkError(false);
        // 有学科列表再进行年级列表初始化，否则没有变更的意义
        onConditionReady?.(data.gradeList || []);
        queryResourceListBySubjectId(saveSubject.subjectId);
        return;
      } else {
        // 异常没数据就提示网络错误，让用户重新刷新
        setNetError(data);
      }
    } catch (error) {
      // 异常没数据就提示网络错误，让用户重新刷新
      setNetError(error, true);
    }
  };

  const handleSubjectItemClick = (subjectItem: ISubjectItem, index: number) => {
    if (subjectItem.subjectId === selectedSubject) {
      return;
    }
    setIsChangeSubject(true);
    setSelectedSubject(subjectItem.subjectId);
    setSelectedSubjectName(subjectItem.subjectName);
    // 切换学科时将页数重置为 1 和有下一页的状态
    packagePageInfo.current = {
      ...packagePageInfo.current,
      pageIndex: 1,
      haveNextPage: true,
    };
    if (swiperRef?.current) {
      // 因为学科名称内容容器偏小，且中间间距较大，容易看不到上一个选项，因此多移动一个
      swiperRef.current?.slideTo(index - 1);
    }
    setResourceList([]);
    queryResourceListBySubjectId(subjectItem.subjectId, true);
  };

  const autoQueryPackageList = async () => {
    if (
      packagePageInfo.current.haveNextPage &&
      !loading &&
      subjectList?.length &&
      !isChangeSubject &&
      !resourceListLoading
    ) {
      packagePageInfo.current = {
        ...packagePageInfo.current,
        pageIndex: packagePageInfo.current.pageIndex + 1,
      };
      await queryResourceListBySubjectId();
    }
  };

  const throttleLoadMore = throttle(autoQueryPackageList, 600);

  const handleLoadMore = async () => {
    throttleLoadMore();
  };

  const updateResourcePlanId = (
    targetResourceId: string | number,
    targetPlanId: number,
  ) => {
    const targetResourceIndex = resourceList.findIndex(
      (item: IPlanSquareResListData) =>
        item.templatePlanId === targetResourceId,
    );
    if (targetResourceIndex !== -1) {
      const newResourceList = [...resourceList];
      newResourceList[targetResourceIndex].planId = targetPlanId;
      setResourceList(newResourceList);
    }
  };

  const addPlan = async (
    params: any,
    currentResource: IPlanSquareResListData,
  ) => {
    try {
      const { data } = await getPlanSquareResourceList({
        ...packagePageInfo.current,
        subjectId: selectedSubject,
        grade: grade ? Number(grade) : "",
      });
      const addPlanParams = {
        ...params,
      };

      let confirmModalObj = null;

      const requestAddPlan = async () => {
        try {
          const { data: newPlanId } = await planAdd(addPlanParams);
          updateResourcePlanId(currentResource.templatePlanId, newPlanId);
          Toast.show("创建计划成功");
        } catch (error) {
          console.error("创建计划失败", error);
        } finally {
          confirmModalObj?.close();
          confirmModalObj = null;
        }
      };
      // 如果返回了服务器时间，就要判断和周一差几天
      if (data?.currentTime) {
        const weekDay = dayjs(Number(data.currentTime)).day();
        if (weekDay !== 1) {
          // 和周一相差的天数，如果是周日那就是1天（dayjs周日是0），否则就计算差值+1
          const diffDays = weekDay === 0 ? 1 : 7 - weekDay + 1;
          confirmModalObj = ConfirmModal({
            title: "提示",
            content: `如果今天开始，就需要用本周剩余的【${diffDays}天】完成第一周的内容；如果下周一开始，就可以完整7天学习一周的内容。请选择你的计划生效时间。`,
            confirmText: "今天开始",
            cancelText: "下周一开始",
            onConfirm: async () => {
              addPlanParams.beginFromThisWeek = true;
              requestAddPlan();
            },
            onCancel: () => {
              // 下周一开始的回调
              addPlanParams.beginFromThisWeek = false;
              requestAddPlan();
            },
          });
        } else {
          addPlanParams.beginFromThisWeek = true;
          requestAddPlan();
        }
      }
    } catch (error) {
      console.error("创建计划失败", error);
    }
  };

  const onDeletePlan = async (currentResource: IPlanSquareResListData) => {
    try {
      let confirmModalObj = null;
      const removePlanParams = {
        planId: currentResource.planId,
        templatePlanId: currentResource.templatePlanId,
      };
      confirmModalObj = ConfirmModal({
        title: "提示",
        content: "删除后将无法恢复，确定删除该计划吗？",
        onConfirm: async () => {
          try {
            await postPlanRemove(removePlanParams);
            updateResourcePlanId(currentResource.templatePlanId, null);
            Toast.show("删除成功");
          } catch (error) {
            console.error("删除计划失败", error);
          } finally {
            confirmModalObj?.close();
            confirmModalObj = null;
          }
        },
        onCancel: () => {
          confirmModalObj?.close();
          confirmModalObj = null;
        },
      });
    } catch (error) {
      console.error("删除计划失败", error);
    }
  };

  const handleAddButtonClick = async (
    callbackItem?: IPlanSquareResListData,
  ) => {
    try {
      event?.stopPropagation();
      const currentResource = callbackItem;
      if (operateLoading || !currentResource) {
        return;
      }
      if (currentResource.learnMode === LearnModelEnum.DAY) {
        currentResourceInfo.current = currentResource;
        setSelectPlanTimeVisible(true);
        expPv("ewt_h5_base_plan_desk_plan_square_detail_day_plan_popup_expo", {
          sourceTypeName: "计划广场",
        });
        return;
      }
      setOperateLoading(true);
      const addPlanParams: any = {
        learnMode: currentResource?.learnMode,
        templatePlanId: currentResource?.templatePlanId,
      };
      if (currentResource.learnMode !== LearnModelEnum.WEEK) {
        try {
          addPlanParams.beginFromThisWeek = true;
          const { data: newPlanId } = await planAdd(addPlanParams);
          updateResourcePlanId(currentResource.templatePlanId, newPlanId);
          Toast.show("创建计划成功");
        } catch (error) {
          console.error("创建计划失败", error);
        }
        return;
      } else {
        addPlan(addPlanParams, currentResource);
      }
    } catch (error) {
      console.error("添加计划失败", error);
    } finally {
      setOperateLoading(false);
    }
  };

  useEffect(() => {
    querySubjectList();
    checkAllSubjectEdition(PopupType.CLOSE);
  }, []);

  useEffect(() => {
    // 切换年级时重置分页为1，且有下一页
    packagePageInfo.current = {
      ...packagePageInfo.current,
      pageIndex: 1,
      haveNextPage: true,
    };
    queryResourceListBySubjectId(selectedSubject, true);
  }, [grade]);

  // 更新单个课程包状态
  const updatePackageStatus = async () => {
    try {
      // 如果没有信息就不刷新了
      if (!globalResourceInfo.current.resourceId) {
        return;
      }
      const reqCourseId: any = globalResourceInfo.current.resourceId;
      // 使用完毕就清空，防止重新请求
      globalResourceInfo.current.resourceId = "";
      const { data } = await getTemplatePlanDetail({
        templatePlanId: reqCourseId,
        ignoreError: true,
      });
      const newList = [...globalResourceInfo.current.resourceList];
      const newResourceItem = newList.find(
        (item: IPlanSquareResListData) => item.templatePlanId === reqCourseId,
      );
      if (newResourceItem) {
        newResourceItem.planId = data.planId;
      }
      // 刷新数据
      setResourceList(newList);
    } catch (error) {
      console.warn("更新失败", error);
    }
  };

  const onInitErrorRetry = () => {
    checkAllSubjectEdition(needShowPopupTypeRef.current);
  };

  const onEditionClose = () => {
    // 当前是否有选中书本
    // const isHasBook = !!currentBook.bookId;
    // 当前是否所有学科都设置了教材版本
    const isAllSubjectHasEdition =
      allSubjectEditionRef.current.length ===
      Object.keys(userSubjectEditionRef.current).length;
    // 是不是原先就想打开 教材版本切换 弹窗
    const isStartEdition = needShowPopupTypeRef.current === PopupType.EDITION;
    // 判断是不是 所有学科 都选择了 教材版本
    // 如果已经都选了，则直接关闭，或者返回上一个弹窗
    // 反之，如果当前有选中书本，则直接关闭，如果没有选中书本，则直接退出页面
    if (isAllSubjectHasEdition) {
      setPopupType(
        isStartEdition ? PopupType.CLOSE : needShowPopupTypeRef.current,
      );
    } else {
      closeWebview();
    }
  };

  const onEditionConfirm = async (userSelected: IUserSubjectEditionMap) => {
    const isDown = await onSaveEdition(userSelected);
    if (!isDown) {
      return;
    }
    querySubjectList();
    setPopupType(PopupType.CLOSE);
  };

  // 返回后重新刷新包状态
  const handleRefreshConfig = (isShow: boolean) =>
    isShow && updatePackageStatus();

  // 页面的可见/不可见监控，变更后查询最新的奖励状态
  useVisibilitychange({
    handleVisibilitychange: handleRefreshConfig,
  });

  return (
    <div className={styles["plan-square-content-container"]}>
      {/* 学科列表swiper */}
      {!!subjectList?.length && (
        <SubjectTabSwiper
          topHeight={topHeight}
          onSwiper={(swiper: any) => (swiperRef.current = swiper)}
          data={subjectList}
          active={selectedSubject}
          handleClick={handleSubjectItemClick}
        />
      )}
      {/* 如果学科列表或者资源列表网络错误，那就显示重新加载的错误提示 */}
      {(subjectNetWorkError || resourceListNetWork) && !loading && (
        <div
          style={{
            height: subjectNetWorkError
              ? `calc(100vh - 12.8vw)`
              : `calc(100vh - 26.8vw)`,
          }}
        >
          <NetworkErrorStatus
            buttonOption={{
              handleClick: () => {
                if (subjectNetWorkError) {
                  querySubjectList();
                } else {
                  queryResourceListBySubjectId(selectedSubject, false);
                }
              },
            }}
          />
        </div>
      )}
      {/* 学科列表存在，不是两种网络错误，资源列表是空，展示无数据空态 */}
      {!subjectNetWorkError &&
        !resourceListNetWork &&
        !!subjectList?.length &&
        !resourceList?.length &&
        !isChangeSubject &&
        !loading && (
          <div style={{ height: emptyHeight || `calc(100vh - 40vw)` }}>
            <EmptyResult />
          </div>
        )}
      {/* 接口正常但是学科信息为空的话显示推荐的界面 */}
      {!subjectNetWorkError && !loading && !subjectList?.length && (
        <div className={styles["page-empty-result-container"]}>
          <EmptyResult />
        </div>
      )}
      {/* 学科切换loading态 */}
      {isChangeSubject && (
        <div className={styles["changing-subject-box"]}>
          <span>加载中</span>
          <DotLoading color="currentColor" />
        </div>
      )}
      {/* 资源列表 */}
      {!!resourceList?.length && (
        <div
          className={styles["plan-square-list-box"]}
          id="plan-square-list-box"
        >
          <ResourceItem
            data={resourceList}
            selectedSubjectName={selectedSubjectName}
            handleOperateClick={(item: IPlanSquareResListData) => {
              if (item.planId) {
                onDeletePlan(item);
              } else {
                handleAddButtonClick(item);
              }
            }}
            globalResourceRef={globalResourceInfo}
          />

          {!subjectNetWorkError && !loading && subjectList?.length === 0 && (
            <div style={{ height: `calc(100vh - 31vw)` }}>
              <EmptyResult />
            </div>
          )}

          <InfiniteScroll
            loadMore={handleLoadMore}
            hasMore={packagePageInfo.current.haveNextPage}
          >
            {!!resourceList?.length && (
              <InfiniteScrollBottom
                hasMore={packagePageInfo.current.haveNextPage}
              />
            )}
          </InfiniteScroll>
        </div>
      )}
      {loading && <Spin />}
      <EditionPopup
        open={popupType === PopupType.EDITION}
        list={allSubjectEditionRef.current}
        userSelected={userSubjectEditionRef.current}
        isError={editionError}
        onRetry={onInitErrorRetry}
        onClose={onEditionClose}
        onConfirm={onEditionConfirm}
      />
      <SelectPlanTimePopup
        visible={selectPlanTimeVisible}
        onClose={() => setSelectPlanTimeVisible(false)}
        confirmLoading={operateLoading}
        onConfirm={async (val) => {
          setOperateLoading(true);
          const { startTime, restDays } = val || {};
          const addPlanParams: any = {
            startTime,
            restDayList: restDays,
            templatePlanId: currentResourceInfo.current?.templatePlanId,
          };
          try {
            const { data: newPlanId } = await planAdd(addPlanParams);
            updateResourcePlanId(
              currentResourceInfo.current?.templatePlanId,
              newPlanId,
            );
            Toast.show("创建计划成功");
            setSelectPlanTimeVisible(false);
            clickPv(
              "ewt_h5_base_plan_desk_plan_square_detail_day_plan_popup_click",
              {
                sourceTypeName: "计划广场",
              },
            );
            setOperateLoading(false);
          } catch (error) {
            console.error("创建计划失败", error);
            if (+error?.code === ErrorCodeEnum.TEMPLATE_IN_PLAN) {
              Toast.clear();
              Toast.show({
                content: "计划已创建，页面即将刷新",
                duration: 0,
                maskClickable: false,
              });
              setTimeout(() => {
                window.location.reload();
              }, 3000);
            }
            setOperateLoading(false);
          }
          return;
        }}
      />
    </div>
  );
};

export default React.memo(PlanSquareContent);
