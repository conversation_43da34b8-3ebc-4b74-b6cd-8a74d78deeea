import * as React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { cls } from "@/utils/tool";
import { ISubjectItem } from "@/service/common";

import Style from "./style.module.scss";

/**
 * 学科列表swiper
 * @param onSwiper swiper初始化完毕回调函数，会返回swiper对象
 * @param data 学科列表
 * @param active 需要激活的tab
 */
export interface ISubjectTabSwiper {
  onSwiper: (swiper: any) => void;
  data: ISubjectItem[];
  active?: number;
  handleClick?: (item: ISubjectItem, index: number) => void;
  topHeight?: number;
}

export const SubjectTabSwiper: React.FC<ISubjectTabSwiper> = (props) => {
  const { onSwiper, data, active, handleClick, topHeight } = props;

  const items = () =>
    data?.map((item: ISubjectItem, index: number) => (
      <SwiperSlide key={`${item.subjectId}_${index + 1}`}>
        <div
          className={cls([
            Style["subject-name-box"],
            item.subjectId === active && Style["active"],
            index === 0 && Style["first-child"],
          ])}
          onClick={() => handleClick?.(item, index)}
        >
          {item.subjectName}
        </div>
      </SwiperSlide>
    ));

  return (
    <Swiper
      slidesPerView={"auto"}
      centeredSlides={false}
      pagination={false}
      onSwiper={onSwiper}
      className={Style["plan-square-content-swiper"]}
      style={{
        top: `calc(12.8vw + ${topHeight || 0}px - 1px)`,
      }}
    >
      {items()}
    </Swiper>
  );
};
