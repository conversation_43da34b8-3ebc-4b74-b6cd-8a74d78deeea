.subject-change-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.resource-item {
  position: relative;
  z-index: 1;
  margin-bottom: 10px;
  min-height: 175px;

  & > img {
    width: 100%;
    height: 175px;
    border-radius: 8px;
    display: block;
    background: #DDD;
  }

  .default-bg-box {
    width: 100%;
    height: 175px;
    background: #DDD;
    border-radius: 8px;
  }

  .add-to-plan-button {
    position: absolute;
    right: 8px;
    top: 8px;
    padding: 8px 12px;
    border: 1px solid #021E66;
    background: rgba(255, 255, 255, .85);

    & > div {
      color: #021E66;
      font-size: 12px;
      line-height: 12px;

      i {
        font-size: 12px;
        margin-right: 4px;
      }
    }

    :global {
      .svg-icon {
        margin-right: 4px;
      }
    }

    &.added {
      background-color: rgba(0, 0, 0, .45);
      border: 1px solid #fff;

      & > div {
        color: #fff;
      }
    }

    &.package-finished-button {
      background-color: rgba(0, 0, 0, .25);
      border: none;

      & > div {
        color: #fff;
      }
    }
  }
}

.resource-info-box {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, .6);
  border-radius: 0 0 8px 8px;
  color: rgba(255, 255, 255, 0.85);
  min-height: 80px;

  .base-info-container{
    padding: 10px 12px 12px;

    .base-info-box {
      display: flex;
      align-items: baseline;

      .grey-text {
        color: rgba(255, 255, 255, .85);
      }

      .plan-week-count-tag {
        background-color: #FFD52F;
        border-radius: 2px;
        font-size: 12px;
        font-weight: normal;
        color: rgba(0, 0, 0, .85);
        padding: 2px 4px;
        margin-right: 12px;
      }

      .stage-tag {
        padding: 2px 4px;
        background-color: rgba(255, 255, 255, .25);
        border-radius: 2px;
        font-size: 12px;
        font-weight: normal;
        color: #FFF;
        margin-right: 12px;
      }

      .learn-time-tag {
        margin-right: 12px;
      }

      .learn-subject-text {
        max-width: 122px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .resource-title {
      max-width: 318px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #FFF;
      font-size: 15px;
      font-weight: bold;
      margin-top: 12px;
    }

    .resource-desc {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .iconfont {
        margin-left: 10px;
      }
    }
  }
}
