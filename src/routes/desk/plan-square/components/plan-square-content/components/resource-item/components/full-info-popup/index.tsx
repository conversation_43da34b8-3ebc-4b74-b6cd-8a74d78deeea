import * as React from "react";
import { parseDeskPlanTime } from "@/utils/tool";
import { IPlanSquareResListData } from "@/service/desk/plan-square";
import { Popup } from "@/components";
import { LearnModelEnum } from "@/service/desk/plan-resource";

import Style from "./index.module.scss";
import { ISubjectItem } from "@/service/common";

/**
 * @param item 当前资源
 * @param onClose 关闭函数
 */
export interface IFullInfoPopup {
  visible: boolean;
  item: IPlanSquareResListData;
  onClose: () => void;
}

export const FullInfoPopup: React.FC<IFullInfoPopup> = (props) => {
  const { item, onClose, visible } = props;
  return (
    <Popup
      visible={visible}
      title="完整介绍"
      destroyOnClose
      onClose={onClose}
      bodyClassName={Style["full-info-popup-body"]}
    >
      <div className={Style["body-content"]}>
        <p className={Style["title"]}>{item?.name}</p>
        <p className={Style["recommend-reason"]}>{item?.desc}</p>
        <div className={Style["plan-info"]}>
          <span>
            {item?.weekCount}
            {item?.learnMode === LearnModelEnum.WEEK ? "周" : "天"}
            计划
          </span>
          <span>{item?.stage}</span>
          <span>{parseDeskPlanTime(item?.learnTime)}</span>
          <span>
            {item?.subjectList
              .map((subject: ISubjectItem) => subject.subjectName)
              .join("、")}
          </span>
        </div>
      </div>
    </Popup>
  );
};
