import * as React from "react";
import { Ellipsis } from "antd-mobile";
import {
  cls,
  createURLByType,
  EJumpType,
  openUrlInWebView,
  parseDeskPlanTime,
} from "@/utils/tool";

import Style from "./style.module.scss";
import { ResourceOperateButton } from "./components/resource-operate-button";
import { useLocation } from "react-router-dom";
import { IPlanSquareResListData } from "@/service/desk/plan-square";
import { LearnModelEnum } from "@/service/desk/plan-resource";
import { ISubjectItem } from "@/service/common";
import { IconSvg } from "@/components";
import { FullInfoPopup } from "./components/full-info-popup";
/**
 * 课包列表
 * @param data 课包列表
 */
export interface IResourceItem {
  data: IPlanSquareResListData[];
  handleOperateClick?: (item: IPlanSquareResListData) => void;
  selectedSubjectName: string;
  globalResourceRef: any;
}

export const ResourceItem: React.FC<IResourceItem> = (props) => {
  const { data, handleOperateClick, selectedSubjectName, globalResourceRef } =
    props;
  const location = useLocation();
  const [fullInfoPopupVisible, setFullInfoPopupVisible] = React.useState(false);
  const [popupData, setPopupData] =
    React.useState<IPlanSquareResListData | null>(null);

  const jumpToPlanDetail = (item: IPlanSquareResListData) => {
    globalResourceRef.current.resourceId = item.templatePlanId;
    // 新开webview去查看详情
    openUrlInWebView(
      createURLByType({
        path: "/desk/plan-detail",
        type: EJumpType.outside,
        originSearch: location.search,
        addQueryObject: {
          templatePlanId: item.templatePlanId,
        },
      }),
    );
  };

  return (
    <React.Fragment>
      {data.map((item: IPlanSquareResListData, index: number) => (
        <div
          className={Style["resource-item"]}
          key={`${item.templatePlanId}_${index}`}
        >
          {/* 背景大图，如果没有的话就显示默认的渐变背景色 */}
          {!!item.backgroundImg ? (
            <img
              src={item.backgroundImg}
              alt=""
              onClick={() => jumpToPlanDetail(item)}
            />
          ) : (
            <div
              className={Style["default-bg-box"]}
              onClick={() => jumpToPlanDetail(item)}
            />
          )}
          {/* 右上角的操作按钮 */}
          <ResourceOperateButton
            item={item}
            selectedSubjectName={selectedSubjectName}
            handleOperateClick={handleOperateClick}
          />

          <div
            className={Style["resource-info-box"]}
            onClick={() => jumpToPlanDetail(item)}
          >
            <div className={Style["base-info-container"]}>
              {/* 第一行基础信息，包括计划周数、年级或阶段、学习时长、学科列表 */}
              <div className={Style["base-info-box"]}>
                {/* 计划周数 */}
                <span className={Style["plan-week-count-tag"]}>
                  {item.weekCount}
                  {item.learnMode === LearnModelEnum.WEEK ? "周" : "天"}
                  计划
                </span>
                {/* 年级/阶段，按照需求暂时隐藏 */}
                {/* {!!item.stage && (
                  <span className={Style["stage-tag"]}>{item.stage}</span>
                )} */}

                {!!item.learnTime && (
                  <span
                    className={cls([
                      Style["learn-time-tag"],
                      Style["grey-text"],
                    ])}
                  >
                    {parseDeskPlanTime(item.learnTime)}
                  </span>
                )}

                {!!item.subjectList?.length && (
                  <span
                    className={cls([
                      Style["learn-subject-text"],
                      Style["grey-text"],
                    ])}
                  >
                    {item.subjectList
                      .map((subject: ISubjectItem) => subject.subjectName)
                      .join("、")}
                  </span>
                )}
              </div>
              {/* 课程名称 */}
              <div className={Style["resource-title"]}>{item.name}</div>
              {/* 描述 */}
              {!!item.desc && (
                <div className={Style["resource-desc"]}>
                  <Ellipsis
                    content={item.desc}
                    rows={1}
                    direction="end"
                    className={Style["grey-text"]}
                  />
                  <IconSvg
                    name="icon-jinru"
                    onClick={(e: any) => {
                      e.stopPropagation();
                      setPopupData(item);
                      setFullInfoPopupVisible(true);
                    }}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      ))}
      <FullInfoPopup
        item={popupData}
        onClose={() => {
          setPopupData(null);
          setFullInfoPopupVisible(false);
        }}
        visible={fullInfoPopupVisible}
      />
    </React.Fragment>
  );
};
