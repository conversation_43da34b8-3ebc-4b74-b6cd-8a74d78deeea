import * as React from "react";
import { clickPv, cls } from "@/utils/tool";
import { IPlanSquareResListData } from "@/service/desk/plan-square";
import Style from "../../style.module.scss";
import { Button, EButtonType, IconSvg } from "@/components";

/**
 * 右上角的操作按钮
 * @param item 当前资源
 * @param selectedSubjectName 选择的学科名称
 * @param handleOperateClick 操作按钮点击事件
 */
export interface IResourceOperateButton {
  item: IPlanSquareResListData;
  selectedSubjectName: string;
  handleOperateClick: (item: IPlanSquareResListData) => void;
}

export const ResourceOperateButton: React.FC<IResourceOperateButton> = (
  props,
) => {
  const { item, selectedSubjectName, handleOperateClick } = props;
  // 是否已加入计划，按照后端接口的约定，计划id大于0就是加入了
  const isAdded = !!item.planId;

  return (
    <Button
      type={EButtonType.grey}
      text={isAdded ? "删除创建" : "创建计划"}
      className={cls([Style["add-to-plan-button"], isAdded && Style["added"]])}
      icon={<IconSvg name={isAdded ? "icon-jianjihua" : "icon-jiajihua"} />}
      onClick={(event: any) => {
        event?.stopPropagation();
        clickPv("ewt_h5_base_plan_desk_plan_square_create_plan_button_click", {
          status: isAdded ? "移出计划" : "加入计划",
        });
        // 这里可以上报qt，可上报选择的学科名称，模版计划id，模版计划名称
        // 最后调用业务的回调
        handleOperateClick(item);
      }}
    />
  );
};
