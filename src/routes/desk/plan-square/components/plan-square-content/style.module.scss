.plan-square-content-container {

  .plan-square-list-box {
    background-color: #EFF1F7;
    padding: 15px 12px;
    position: relative;
    margin-top: 12.8vw;
  }

  .changing-subject-box{
    text-align: center;
    width: 100%;
    height: calc(100vh - 31vw);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .page-empty-result-container {
    height: calc(100vh - 12.8vw);
    background-color: #F3F4F8;
  }

  .empty-result {
    img {
      width: 160px;
      height: 160px;
    }
  }
}

.fixed-loading {
  position: fixed;
  width: 100%;
  height: 100%;
}

.big-type {
  :global {
    .adm-error-block-image {
      img {
        width: 160px;
        height: 160px;
      }
    }
  }
}
