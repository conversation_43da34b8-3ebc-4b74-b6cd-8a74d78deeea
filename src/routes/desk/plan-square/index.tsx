import React from "react";
import { IconSvg, Layout, ScrollToTop } from "@/components";
import type { ILayout } from "@/components";
import PlanSquareContent from "./components/plan-square-content";
import { useLocation, useNavigate } from "react-router-dom";
import mstJsBridge from "mst-js-bridge";
import Style from "./style.module.scss";
import { Picker } from "antd-mobile";
import PageBackIcon from "@/components/page-back-icon";

export const Component: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [topHeight, setTopHeight] = React.useState(0);
  /** 阶段选择弹窗 */
  const [stageVisible, setStageVisible] = React.useState(false);
  /** 阶段选择 */
  const [selectStage, setSelectStage] = React.useState("");
  /** 阶段选择名称 */
  const [selectStageName, setSelectStageName] = React.useState("全部年级");
  /** 年级列表 */
  const [columns, setColumns] = React.useState([
    {
      label: "全部年级",
      value: "",
    },
  ]);
  const [selectPlanTimeVisible, setSelectPlanTimeVisible] =
    React.useState<boolean>(false);

  const asyncColumns = async (list: any[]) => {
    // 如果非数组或空数组，则不进行操作
    if (!list?.length) {
      return;
    }
    const newColumns = Array.from(list).map((item: any) => ({
      label: `${item.gradeName}`,
      value: `${item.gradeId === null ? "" : item.gradeId}`,
    }));
    setColumns(newColumns);
  };

  return (
    <Layout
      showHeader
      // 给安全区染色
      topSafeAreaProps={
        { className: Style["top-safe-area"] } as ILayout["topSafeAreaProps"]
      }
      setChildrenContainerHeight={({ top, bottom }) => {
        setTopHeight(top || 0);
        return `calc(100vh - ${top || 0}px - ${bottom || 0}px)`;
      }}
      headerProps={{
        children: (
          <div className={Style["header-container"]}>
            <div className={Style["left-title"]}>计划广场</div>
            <div
              className={Style["right-grade"]}
              onClick={() => setStageVisible(true)}
            >
              {selectStageName}
              <IconSvg
                name="icon-a-jiaocaiqiehuan2x"
                className={Style["grade-icon"]}
              />
            </div>
          </div>
        ),
        className: Style["header"],
        back: <PageBackIcon />,
        onBack: () => {
          // 如果是跳转过来的，就退回去，否则就关闭 webview
          const isFrom = location.search.indexOf("from") !== -1;
          if (isFrom) {
            navigate(-1);
          } else {
            mstJsBridge.closeWebview();
          }
        },
      }}
    >
      <div
        style={{
          overflow: "auto",
          maxHeight: `calc(100% - 12.8vw)`,
          height: `calc(100vh - 12.8vw)`,
          backgroundColor: "#EFF1F7",
        }}
        className="desk-plan-square-page"
        id="desk-plan-square-page"
      >
        <PlanSquareContent
          topHeight={topHeight}
          grade={selectStage}
          onConditionReady={asyncColumns}
          scrollElementId="desk-plan-square-page"
        />
        <ScrollToTop scrollElementClass="desk-plan-square-page" />
      </div>

      <Picker
        columns={[columns]}
        visible={stageVisible}
        onClose={() => setStageVisible(false)}
        value={[`${selectStage}`]}
        onConfirm={(v: string[]) => {
          const currentGrade = columns.find((item) => item.value === v?.[0]);
          setSelectStage(currentGrade?.value);
          setSelectStageName(currentGrade?.label);
          setStageVisible(false);
        }}
      />
    </Layout>
  );
};
