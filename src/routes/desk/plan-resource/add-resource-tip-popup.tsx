import React from "react";
import { CenterPopup } from "antd-mobile";
import Styles from "./index.modules.scss";

interface AddResourceTipPopupProps {
  visible: boolean;
  title: string;
  content: string;
  onClose: () => void;
}

const AddResourceTipPopup: React.FC<AddResourceTipPopupProps> = ({
  visible,
  title,
  content,
  onClose,
}) => {
  return (
    <CenterPopup
      visible={visible}
      style={{
        "--border-radius": "6vw",
      }}
    >
      <div className={Styles["custom-center-popup"]}>
        <div className={Styles["popup-title"]}>
          {title}
          <span className={Styles["close-icon"]} onClick={onClose}>
            x
          </span>
        </div>
        <div className={Styles["popup-content"]}>{content}</div>
        <div className={Styles["popup-button"]} onClick={onClose}>
          我知道了
        </div>
      </div>
    </CenterPopup>
  );
};

export default AddResourceTipPopup;
