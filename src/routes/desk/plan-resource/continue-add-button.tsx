import React from "react";
import Styles from "./index.modules.scss";
import { AddSceneEnum } from "./context";

interface ContinueAddButtonProps {
  onAddResource: (params?: { addScene?: AddSceneEnum }) => void;
}

const ContinueAddButton: React.FC<ContinueAddButtonProps> = ({
  onAddResource,
}) => {
  return (
    <div
      className={Styles["fixed-container"]}
      onClick={() => onAddResource({ addScene: AddSceneEnum.ContinueAdd })}
    >
      <span className={Styles["fixed-container-icon"]}>+</span>
      <span className={Styles["fixed-container-text"]}>继续添加</span>
    </div>
  );
};

export default ContinueAddButton;
