import React from "react";
import { ConfirmModal, IconSvg } from "@/components";
import { Popover, Toast } from "antd-mobile";
import { postPlanRemove } from "@/service/desk/plan";
import { closeWebview } from "@/utils/bridge-utils";
import { MoreMenuActionEnum, MoreMenuActions } from "./context";

import Styles from "./index.modules.scss";
import { clickPv } from "@/utils/tool";

interface TopBarRightMenuProps {
  planId: string;
}

const TopBarRightMenu: React.FC<TopBarRightMenuProps> = ({ planId }) => {
  return (
    <Popover.Menu
      className={Styles["more-menu-popover"]}
      actions={MoreMenuActions}
      placement="bottom-start"
      onAction={(node) => {
        if (node.key === MoreMenuActionEnum.DeletePlan) {
          clickPv("ewt_h5_base_plan_desk_plan_resource_delete_plan_click");
          let confirmModalObj = null;
          confirmModalObj = ConfirmModal({
            title: "提示",
            content: "删除后将无法恢复，确定删除该计划吗？",
            onConfirm: async () => {
              try {
                clickPv(
                  "ewt_h5_base_plan_desk_plan_resource_delete_plan_confirm_click",
                );
                await postPlanRemove({
                  planId,
                });
                Toast.show("删除成功");
                closeWebview();
              } catch (error) {
                console.error("删除计划失败", error);
              } finally {
                confirmModalObj?.close();
                confirmModalObj = null;
              }
            },
            onCancel: () => {
              confirmModalObj?.close();
              confirmModalObj = null;
            },
          });
        }
      }}
      trigger="click"
    >
      <div className={Styles["more-menu-text"]}>
        <IconSvg name="icon-student-gengduo" />
      </div>
    </Popover.Menu>
  );
};

export default TopBarRightMenu;
