import { ITimeListItem } from "@/components/time-list";
import { IWeekOrDayList, LearnModelEnum } from "@/service/desk/plan-resource";
import { getUserInfo } from "@/utils/tool";

// 每页加载数量
export const PAGE_SIZE = 50;
// 周、日类型的最大资源上线
export const MAX_RESOURCE_COUNT = 50;
// 无周期类型的最大资源上线
export const MAX_RESOURCE_COUNT_NO_CYCLE = 100;
// 所有任务类型的最大资源上线
export const MAX_RESOURCE_COUNT_ALL_TASK = 400;
// 是否是第一次添加资源
export const DESK_FIRST_ADD_RESOURCE_KEY = "desk_first_add_resource";
export enum AddSceneEnum {
  Add = "add",
  ContinueAdd = "continueAdd",
}

// 将周、日列表转换为时间列表
export const transformToWeekOrDayList = (
  list: IWeekOrDayList[],
  learnModel: LearnModelEnum,
) => {
  const newWeekOrDayList: ITimeListItem[] = [];
  list?.forEach((item) => {
    const finishCount = item.finishCount || 0;
    const taskCount = item.taskCount || 0;
    newWeekOrDayList.push({
      ...item,
      mode: learnModel,
      title: `${finishCount}/${taskCount}`,
      startTime: item.startTime,
      endTime: item.endTime,
      isDone: finishCount === taskCount,
      isCurrent: item.currentWeekOrToday,
    });
  });
  return newWeekOrDayList;
};

// 不再提醒的配置key
export const NO_REMIND_ME_CONFIG_KEY = "no_remind_me_config";
// 本地缓存中写入或删除不再提醒的配置
export const operateNoRemindMeConfig = (checked: boolean) => {
  try {
    const userId = getUserInfo();
    if (!userId) {
      return;
    }
    const cacheKey = `${NO_REMIND_ME_CONFIG_KEY}_${userId}`;
    if (checked) {
      localStorage.setItem(cacheKey, "true");
    } else {
      localStorage.removeItem(cacheKey);
    }
  } catch (error) {
    console.error("操作不再提醒的配置失败", error);
  }
};
// 获取本地缓存中的不再提醒的配置
export const getNoRemindMeConfig = () => {
  try {
    const userId = getUserInfo();
    if (!userId) {
      return false;
    }
    const cacheKey = `${NO_REMIND_ME_CONFIG_KEY}_${userId}`;
    const value = localStorage.getItem(cacheKey);
    return Boolean(value);
  } catch (error) {
    console.error("获取不再提醒的配置失败", error);
    return false;
  }
};

export enum MoreMenuActionEnum {
  DeletePlan = "deletePlan",
}

// topBar 右侧更多菜单的配置
export const MoreMenuActions = [
  {
    key: MoreMenuActionEnum.DeletePlan,
    text: "删除该计划",
  },
];
