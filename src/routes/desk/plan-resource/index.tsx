import React, {
  useEffect,
  useState,
  useRef,
  Fragment,
  useCallback,
} from "react";
import {
  Layout,
  Popup,
  ScrollToTop,
  Spin,
  StudyRecommendPopup,
} from "@/components";
import { useLocation } from "react-router-dom";
import {
  clickPv,
  cls,
  debounce,
  expPv,
  getChannel,
  getUrlParam,
  getUserInfo,
  openRoute,
  separatelyReport,
} from "@/utils/tool";
import Styles from "./index.modules.scss";
import MissionProgress from "@/components/mission-progress";
import TimeList, { ITimeListItem } from "@/components/time-list";
import mstJsBridge from "mst-js-bridge";
// 引入iconfont
import "@/assets/font/iconfont/iconfont.css";
import MissionItem from "@/components/mission-item";
import {
  getDeskPlanInfoProcess,
  IPlanInfoProcessRes,
  getDeskPlanResourceList,
  LearnModelEnum,
  postRemoveResourceByRecordId,
  LearnModelTitleMap,
  planSourceMap,
  FromTypeEnum,
} from "@/service/desk/plan-resource";
import { IResourceItem } from "@/routes/week-study/apis";
import EmptyStatus, { NetworkErrorStatus } from "@/components/empty-status";
import { AllSettledEnum } from "@/typing.d";
import ProgressModal from "@/components/progress-modal";
import PlanEmpty from "@/assets/common/plan-empty.png";
import { Toast } from "antd-mobile";
import { useVisibilitychange } from "@/hooks";
import StudyRecommendPopupImg from "@/assets/common/eClass-guidance-desk.png";
import { web } from "@/utils/hosts";
import PageBackIcon from "@/components/page-back-icon";
import FeedbackPopup, { useFeedBack } from "../components/feedback-popup";
import {
  PAGE_SIZE,
  MAX_RESOURCE_COUNT,
  MAX_RESOURCE_COUNT_NO_CYCLE,
  MAX_RESOURCE_COUNT_ALL_TASK,
  DESK_FIRST_ADD_RESOURCE_KEY,
  AddSceneEnum,
  transformToWeekOrDayList,
  operateNoRemindMeConfig,
} from "./context";

import ContinueAddButton from "./continue-add-button";
import AddResourceTipPopup from "./add-resource-tip-popup";
import TopBarRightMenu from "./top-bar-right-menu";
import { closeWebview } from "@/utils/bridge-utils";
import SafeLogger from "@/utils/safe-logger";
import { useLottiePopup } from "./hooks/useLottiePopup";
import LottiePopup from "../components/positive-feedback-lottie";

export const Component: React.FC = () => {
  const location = useLocation();
  const qtUploadRef = useRef(true);
  // 页面基础信息 - app版本
  const pageBaseInfo = useRef({
    version: "",
  });
  // 存储点击的资源id信息，用于更新
  const globalResourceInfo = useRef({
    learnModel: LearnModelEnum.NO_CYCLE,
    resourceList: [],
    resourceId: "",
    currentDay: null,
    planTimeList: [],
    taskTotalCount: 0,
    completeTaskCount: 0,
    planOverviewProgress: null,
    pageNum: 1,
  });
  const safeTopRef = useRef(0);
  // 计划id
  const planId = getUrlParam("planId", location.search);
  // 开始时间，指定到某个周期
  const startTimeParam: any = getUrlParam("startTime", location.search);
  // 结束时间，指定到某个周期
  const endTimeParam: any = getUrlParam("endTime", location.search);
  // 计划标题, 通过接口获取
  const [planTitle, setPlanTitle] = useState("");
  // 计划模型，通过接口获取,默认是无周期
  const [planModel, setPlanModel] = useState<LearnModelEnum>(
    LearnModelEnum.NO_CYCLE,
  );

  // 是否展示反馈
  const [diagnosticFeedbackVisible, setDiagnosticFeedbackVisible] =
    useState<boolean>(false);

  const hasMore = useRef(true);
  // 加载中状态
  const [loading, setLoading] = useState(true);
  const [planProgress, setPlanProgress] = useState<number>(0);
  const [isPageError, setIsPageError] = useState(false);
  /** 无推荐内容时推荐e讲堂的信息展示 */
  const [studyVisible, setStudyVisible] = useState(false);
  // 计划时间列表
  const [planTimeList, setPlanTimeList] = useState<ITimeListItem[]>([]);
  const [currentWeekOrDay, setCurrentWeekOrDay] = useState<ITimeListItem>();
  const [serviceTime, setServiceTime] = useState<number>(0);
  const currentWeekOrDayStartTime = useRef<number>(0);
  // 计划完成进度, 默认全是0
  const [planOverviewProgress, setPlanOverviewProgress] =
    useState<IPlanInfoProcessRes>({
      taskCount: 0,
      completeTaskCount: 0,
      planName: planTitle,
      learnModel: LearnModelEnum.NO_CYCLE, // 默认无周期
      weekOrDayList: [],
      allowResourceEdit: true, // 默认可以编辑，具体按照接口的返回为准
      currentTime: "",
      planSource: 0,
      firstSubjectId: 1,
    });

  // 资源列表相关状态
  const [planResourceList, setPlanResourceList] = useState<IResourceItem[]>([]);
  const [resourceLoading, setResourceLoading] = useState(false);
  const [pageNum, setPageNum] = useState(1);
  // 请求时序控制，防止展示数据和筛选条件不符合
  const requestFlag = React.useRef<number>(0);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  // 渠道类型，需要从App获取，如果是校园版的情况会有特殊处理
  // 默认值是空字符串，如果获取失败也是当做普通版本使用
  const channelRef = useRef("");
  const [progressModalOpen, setProgressModalOpen] = useState(false);
  const [addResourceVisible, setAddResourceVisible] = useState(false);
  const [addResourceTipContent, setAddResourceTipContent] = useState({
    title: "",
    content: "",
  });

  /** 反馈相关 */
  const {
    visible,
    toggle,
    loading: { updateSummaryOptionsLoading },
    run: { summaryInfoAsyncRun, updateSummaryOptionsAsyncRun },
    state: { summaryInfo },
  } = useFeedBack();

  const fetchSummaryInfo = async () => {
    try {
      await summaryInfoAsyncRun({
        ignoreError: true,
      });
    } catch (error) {
      SafeLogger.baseLogger.error("get-summary-info", {
        error,
      });
    }
  };
  // lottie弹窗hook
  const {
    showLottieList,
    checkLottiePopup,
    isPlaying,
    currentIndex,
    handleSkip,
    handleComplete,
  } = useLottiePopup({
    planId,
  });

  // 加载资源列表
  const loadResourceList = async (
    isLoadMore = false,
    currentDay?: ITimeListItem,
    isRefresh = false,
  ) => {
    const newCurrentDay = currentDay || currentWeekOrDay;
    // 如果点击的是当前日期，就不再刷新请求了
    if (
      !isRefresh &&
      newCurrentDay?.startTime === currentWeekOrDay?.startTime
    ) {
      setLoading(false);
      setResourceLoading(false);
      return;
    }
    // 如果正在加载，或者没有更多，或者没有当前周或天，或没有计划id，那么直接返回
    if (resourceLoading || !hasMore.current || !newCurrentDay || !planId) {
      setLoading(false);
      setResourceLoading(false);
      return;
    }
    requestFlag.current++;
    const nowReqFlag = requestFlag.current;
    try {
      setResourceLoading(true);
      const pageIndex = isLoadMore ? pageNum + 1 : 1;
      const { data } = await getDeskPlanResourceList({
        planId,
        // 第一次加载第一页，滚动加载时加载下一页
        pageIndex: pageIndex,
        pageSize: PAGE_SIZE,
        startTime: newCurrentDay?.startTime,
        endTime: newCurrentDay?.endTime,
        channel: channelRef.current,
      });
      if (nowReqFlag !== requestFlag.current) {
        return;
      }
      const { queryResultExt } = data;
      // 使用服务端返回的haveNextPage来判断是否还有更多
      hasMore.current = queryResultExt?.haveNextPage;
      const dataRes = queryResultExt?.data || [];
      if (dataRes.length) {
        const newList = isLoadMore
          ? [...planResourceList, ...dataRes]
          : dataRes;
        setPlanResourceList(newList as IResourceItem[]);
        // 存储最新的资源列表
        globalResourceInfo.current.resourceList = newList;
        if (isLoadMore) {
          setPageNum(pageNum + 1);
          globalResourceInfo.current.pageNum = pageNum + 1;
        }
      } else {
        // 如果是滑动的自动加载没数据，那么就保持原本的界面不动，否则就是手动切换，就需要清空数据
        if (!isLoadMore) {
          setPageNum(1);
          globalResourceInfo.current.pageNum = 1;
          setPlanResourceList([]);
          globalResourceInfo.current.resourceList = [];
        }
      }
    } catch (error) {
      console.error(error);
      if (!isLoadMore) {
        setPageNum(1);
        globalResourceInfo.current.pageNum = 1;
        setPlanResourceList([]);
        globalResourceInfo.current.resourceList = [];
      }
    } finally {
      setResourceLoading(false);
      setLoading(false);
    }
  };

  const setPageError = () => {
    setIsPageError(true);
    setLoading(false);
  };

  // 初始化数据
  const initData = async (isRefresh = false) => {
    try {
      // 批量获取渠道和计划信息
      Promise.allSettled([
        getChannel(),
        getDeskPlanInfoProcess({
          planId,
          ignoreError: true,
        }),
      ])
        .then(([channelRes, planInfoRes]) => {
          if (channelRes.status === AllSettledEnum.success) {
            channelRef.current = channelRes.value as string;
          }
          if (planInfoRes.status === AllSettledEnum.success) {
            const { data } = planInfoRes.value as unknown as any;
            if (data) {
              const {
                taskCount,
                completeTaskCount,
                weekOrDayList,
                learnModel,
                planName,
                currentTime,
                planSource,
                diagnosticFeedbackShow,
              } = data;
              // 存储当前的任务总数
              globalResourceInfo.current.taskTotalCount = taskCount;
              // 存储当前的完成任务总数
              globalResourceInfo.current.completeTaskCount = completeTaskCount;
              // 存储当前的学习模式
              globalResourceInfo.current.learnModel =
                learnModel || LearnModelEnum.NO_CYCLE;
              setDiagnosticFeedbackVisible(diagnosticFeedbackShow);
              if (diagnosticFeedbackShow) {
                fetchSummaryInfo();
              }
              if (currentTime) {
                // 保存服务器时间，后续做对比使用
                setServiceTime(Number(currentTime));
              }
              setPlanModel(learnModel || LearnModelEnum.NO_CYCLE);
              // 页面初始化时执行qt上报的初始化
              if (qtUploadRef.current) {
                qtUploadRef.current = false;
                separatelyReport("ewt_h5_base_plan_desk_plan_resource_view", {
                  plantype: planSourceMap[planSource] || planSource,
                });
              }
              setPlanTitle(planName);
              // 提前计算进度，如果完成数或者总数任意一个为0，则进度为0，否则计算进度
              const newProgress =
                !completeTaskCount || !taskCount
                  ? 0
                  : Math.round((completeTaskCount / taskCount) * 100);
              // 计算计划完成进度
              setPlanProgress(newProgress);
              // 只要接口有返回计划详情，就正常渲染，展示空态即可
              setIsPageError(false);
              setPlanOverviewProgress(data);
              globalResourceInfo.current.planOverviewProgress = data;
              // 转换数据
              const newWeekOrDayList = transformToWeekOrDayList(
                weekOrDayList,
                learnModel,
              );
              // 当前周或天
              let currentWeekOrDay;
              if (newWeekOrDayList.length) {
                // 如果地址栏指定了且存在就使用该周期，否则就判断数据内是否有当前周期项
                const current = newWeekOrDayList.find((item) => {
                  if (
                    (startTimeParam && endTimeParam) ||
                    (!!+startTimeParam && !!+endTimeParam)
                  ) {
                    return (
                      startTimeParam === item.startTime &&
                      endTimeParam === item.endTime
                    );
                  }
                  return item.isCurrent;
                });
                // 如果没有指定周期也没有当前周期，那么就取第一个
                currentWeekOrDay = current || newWeekOrDayList[0];
                // 保存抓换后的计划时间列表
                setPlanTimeList(newWeekOrDayList);
                globalResourceInfo.current.planTimeList = newWeekOrDayList;
                setCurrentWeekOrDay(currentWeekOrDay);
                globalResourceInfo.current.currentDay = currentWeekOrDay;
                currentWeekOrDayStartTime.current = currentWeekOrDay?.startTime
                  ? Number(currentWeekOrDay.startTime)
                  : 0;
                // 开始加载资源列表
                loadResourceList(false, currentWeekOrDay, isRefresh);
              } else if (learnModel === LearnModelEnum.NO_CYCLE) {
                const newCurrentDay = {
                  taskCount,
                  completeTaskCount,
                  // 如果是无周期就默认是当前，可操作移出
                  currentWeekOrToday: true,
                } as unknown as ITimeListItem;
                setCurrentWeekOrDay(newCurrentDay);
                globalResourceInfo.current.currentDay = newCurrentDay;
                // 无周期计划，直接加载资源列表
                loadResourceList(false, newCurrentDay, true);
              } else {
                setLoading(false);
              }
            } else {
              setPageError();
            }
          } else {
            setPageError();
          }
        })
        .catch((error) => {
          console.error("获取渠道和计划信息失败", error);
          setPageError();
        });
    } catch (error) {
      console.error("获取渠道和计划信息失败", error);
      setPageError();
    }
  };

  // 监听滚动加载
  const handleScroll = () => {
    if (!scrollContainerRef.current) return;

    const { scrollTop, scrollHeight, clientHeight } =
      scrollContainerRef.current;
    if (
      scrollHeight - scrollTop - clientHeight < 50 &&
      !resourceLoading &&
      hasMore.current
    ) {
      loadResourceList(true, currentWeekOrDay, true);
    }
  };

  // 阻断行错误时异常页面的刷新按钮，一秒钟点一次即可
  const debounceLoadResourceList = useCallback(
    debounce(() => {
      initData(true);
    }, 1000),
    [],
  );

  // 获取app版本
  const getAppVersion = async () => {
    try {
      // 这里不用通用的方法，以为是需要异步即可，不用同步锁
      const { data } = await mstJsBridge.getAppVersion();
      if (data?.version) {
        pageBaseInfo.current.version = data.version;
      }
    } catch (error) {
      // 不是核心链路，失败了就从h5领取，因此通过arms收集即可
      console.warn("获取app版本号失败", error);
    }
  };

  const goToAPPStudy = async () => {
    try {
      // 新版本app使用新路由，告知计划相关信息
      openRoute({
        domain: "main_page",
        action: "open_main_page_tab",
        params: {
          type: 1,
          subjectId: planOverviewProgress?.firstSubjectId || 1,
          deskPlanId: planId,
          planName: planTitle,
          startTime: currentWeekOrDay?.startTime
            ? Number(currentWeekOrDay.startTime)
            : null,
          endTime: currentWeekOrDay?.endTime
            ? Number(currentWeekOrDay.endTime)
            : null,
          h5Url: `${window.location.protocol}${web}/ewtcustomerh5/desk/plan-resource?showTopBar=false`,
        },
      });
    } catch (error) {
      console.error("跳转e讲堂失败", error);
    }
  };

  // 添加资源
  const onAddResource = async (params?: { addScene?: AddSceneEnum }) => {
    const { addScene = AddSceneEnum.Add } = params || {};
    try {
      // 这里的埋点需要区分的是来自周末智学还是自主创建，但目前只有自主创建的场景，因此固定传值
      // 后续周末智学融合进来，且后端计划打通且新增字段时才可区分上报
      clickPv("ewt_h5_base_plan_desk_plan_resource_add_resource_button_click", {
        plantype: "自主创建",
      });
      // 如果当前任务总数已经达到上限，那么就提示用户不能添加，最大400条
      if (planOverviewProgress?.taskCount >= MAX_RESOURCE_COUNT_ALL_TASK) {
        setAddResourceTipContent({
          title: "计划容量已达上限",
          content: `一个计划最多添加${MAX_RESOURCE_COUNT_ALL_TASK}条任务哦～`,
        });
        setAddResourceVisible(true);
        return;
      }
      // 跳转e讲堂
      if (
        planOverviewProgress?.learnModel !== LearnModelEnum.NO_CYCLE &&
        currentWeekOrDay?.taskCount >= MAX_RESOURCE_COUNT
      ) {
        const titleText =
          planOverviewProgress?.learnModel === LearnModelEnum.WEEK
            ? "当周"
            : "";
        const contentText =
          planOverviewProgress?.learnModel === LearnModelEnum.WEEK
            ? "一周"
            : "一天";
        setAddResourceVisible(true);
        setAddResourceTipContent({
          title: `${titleText}容量已达上限`,
          content: `${contentText}最多添加${MAX_RESOURCE_COUNT}条任务哦～`,
        });
      } else if (
        planOverviewProgress?.learnModel === LearnModelEnum.NO_CYCLE &&
        currentWeekOrDay?.taskCount >= MAX_RESOURCE_COUNT_NO_CYCLE
      ) {
        setAddResourceVisible(true);
        setAddResourceTipContent({
          title: "计划容量已达上限",
          content: `一个计划最多添加${MAX_RESOURCE_COUNT_NO_CYCLE}条任务哦～`,
        });
      } else {
        // oToAPPStudy();
        checkIsFirstAddResource(params);
      }
    } catch (error) {
      console.error("添加资源失败", error);
    }
  };

  const checkIsFirstAddResource = (params?: { addScene?: AddSceneEnum }) => {
    try {
      const { addScene } = params || {};
      if (addScene === AddSceneEnum.ContinueAdd) {
        goToAPPStudy();
      } else {
        const userId = getUserInfo();
        if (userId) {
          const cacheKey = `${DESK_FIRST_ADD_RESOURCE_KEY}-${userId}`;
          const isFirstAddResource = localStorage.getItem(cacheKey);
          // 如果已经展示过了，直接进行业务流程
          if (isFirstAddResource) {
            goToAPPStudy();
          } else {
            // 存储第一次点击添加资源的标识，防止重复展示
            localStorage.setItem(cacheKey, "true");
            setStudyVisible(true);
          }
          return true;
        } else {
          goToAPPStudy();
        }
      }
    } catch (error) {
      goToAPPStudy();
    }
  };

  const onDeleteResource = async (
    recordId: string,
    resourceItem: IResourceItem,
  ) => {
    try {
      await postRemoveResourceByRecordId({
        recordId,
      });
      Toast.show("移出成功");
      const newList = planResourceList.filter(
        (item) => item.recordId !== recordId,
      );
      setPlanResourceList(newList);
      // 当前已完成任务的总数量
      const currentCompleteTaskCount = planOverviewProgress.completeTaskCount;
      // 更新计划完成进度
      setPlanOverviewProgress({
        ...planOverviewProgress,
        taskCount: planOverviewProgress.taskCount - 1,
        completeTaskCount:
          // 如果任务已完成且总完成数量不为0，那么完成数量-1，否则不变，防止出现负数
          resourceItem.finishStatus && currentCompleteTaskCount
            ? currentCompleteTaskCount - 1
            : currentCompleteTaskCount,
      });

      const newTimeList: any = [...planTimeList];
      newTimeList.map((item) => {
        if (item.startTime === currentWeekOrDay?.startTime) {
          item.taskCount = item.taskCount - 1;
          item.finishCount =
            resourceItem.finishStatus && item.finishCount
              ? item.finishCount - 1
              : item.finishCount;
        }
      });
      // 转换数据
      const newWeekOrDayList = transformToWeekOrDayList(newTimeList, planModel);
      setPlanTimeList(newWeekOrDayList);
      globalResourceInfo.current.planTimeList = newWeekOrDayList;
    } catch (error) {
      console.error("删除资源失败", error);
    }
  };

  useEffect(() => {
    setLoading(true);
    checkLottiePopup();
    initData();
    // 加载app版本
    getAppVersion();
  }, []);

  const updateResourceListForParams = async ({
    pageIndex,
    startTime,
    endTime,
  }: {
    pageIndex: number;
    startTime?: number;
    endTime?: number;
  }) => {
    try {
      const { data } = await getDeskPlanResourceList({
        planId,
        pageIndex,
        pageSize: PAGE_SIZE,
        startTime,
        endTime,
        channel: channelRef.current,
        ignoreError: true,
        fromType: FromTypeEnum.visibilityChange,
      });
      return data;
    } catch (error) {
      console.error("更新资源列表失败", error);
    }
  };

  // 更新资源状态
  const updateResourceStatus = async () => {
    // 如果没有点击某个资源，就不更新数据了
    if (!globalResourceInfo.current.resourceId) {
      return;
    }
    // 使用完后就清掉
    globalResourceInfo.current.resourceId = "";
    try {
      // 周计划、日计划类型，直接请求当前页信息来更新进度、列表资源状态
      if (globalResourceInfo.current.learnModel !== LearnModelEnum.NO_CYCLE) {
        const tempCurrentDay = globalResourceInfo.current.currentDay;
        const newData = await updateResourceListForParams({
          pageIndex: 1,
          startTime: tempCurrentDay?.startTime,
          endTime: tempCurrentDay?.endTime,
        });
        if (newData) {
          const { queryResultExt } = newData;
          const dataRes = queryResultExt?.data || [];
          if (dataRes.length) {
            // 存储最新的资源列表
            globalResourceInfo.current.resourceList = dataRes;
            setPlanResourceList(dataRes as unknown as IResourceItem[]);
          }
        }
      } else {
        // 如果是无周期计划，判断当前页数是否大于1，如果大于1就直接请求100条数据，否则就请求当前页数的数据
        const onlyOnePage = globalResourceInfo.current.pageNum === 1;
        const requests = [
          updateResourceListForParams({
            pageIndex: 1,
          }),
        ];

        if (!onlyOnePage) {
          requests.push(
            updateResourceListForParams({
              pageIndex: 2,
            }),
          );
        }
        const results = await Promise.allSettled(requests);
        let resList: any[] = [];
        // 返回后刷新的动作后端有可能会进行降级，此时分页请求就需要判断成功数量来更新
        // 假设两次中有一次失败了。那不更新了
        let successCount = 0;
        // 处理所有y页数的数据
        results.forEach((result) => {
          if (result.status === AllSettledEnum.success && result.value) {
            successCount++;
            const { queryResultExt } = result.value;
            resList = [...resList, ...(queryResultExt?.data || [])];
          }
        });
        if (successCount === requests.length) {
          setPlanResourceList(resList);
        }
      }
    } catch (error) {
      console.error("更新资源状态失败", error);
    }

    // 更新总进度和周期列表
    try {
      const { data } = await getDeskPlanInfoProcess({
        planId,
        ignoreError: true,
      });
      const {
        taskCount,
        completeTaskCount,
        weekOrDayList,
        learnModel,
        currentTime,
      } = data;
      // 存储当前的任务总数
      globalResourceInfo.current.taskTotalCount = taskCount;
      // 存储当前的完成任务总数
      globalResourceInfo.current.completeTaskCount = completeTaskCount;
      if (currentTime) {
        // 保存服务器时间，后续做对比使用
        setServiceTime(Number(currentTime));
      }
      // 提前计算进度，如果完成数或者总数任意一个为0，则进度为0，否则计算进度
      const newProgress =
        !completeTaskCount || !taskCount
          ? 0
          : Math.round((completeTaskCount / taskCount) * 100);
      // 计算计划完成进度
      setPlanProgress(newProgress);
      setPlanOverviewProgress(data);
      globalResourceInfo.current.planOverviewProgress = data;
      if (weekOrDayList.length) {
        // 转换数据
        const newWeekOrDayList = transformToWeekOrDayList(
          weekOrDayList,
          learnModel,
        );
        globalResourceInfo.current.planTimeList = newWeekOrDayList;
        // 保存抓换后的计划时间列表
        setPlanTimeList(newWeekOrDayList);
      } else if (learnModel === LearnModelEnum.NO_CYCLE) {
        const newCurrentDay = {
          taskCount,
          completeTaskCount,
          // 如果是无周期就默认是当前，可操作移出
          currentWeekOrToday: true,
        } as unknown as ITimeListItem;
        setCurrentWeekOrDay(newCurrentDay);
        globalResourceInfo.current.currentDay = newCurrentDay;
      }
    } catch (error) {
      console.error("更新资源状态失败", error);
    }
  };

  // 返回后重新刷新包状态
  const handleRefreshConfig = (isShow: boolean) => {
    if (isShow) {
      checkLottiePopup();
      updateResourceStatus();
    }
  };

  // 页面的可见/不可见监控，变更后查询最新的奖励状态
  useVisibilitychange({
    handleVisibilitychange: handleRefreshConfig,
  });

  const checkIsDisableDelete = () => {
    // 如果是广场的计划、或者计划已截止了，就直接禁用，不允许编辑了，已后端为准
    if (!planOverviewProgress?.allowResourceEdit) {
      return true;
    }
    // 如果不是无周期、已截止、广场计划，那就和服务器时间做判断，小于的都是历史的
    return (
      currentWeekOrDay?.endTime &&
      Number(currentWeekOrDay.endTime) < serviceTime
    );
  };

  // 是否禁用移出功能，历史周期是不能移出的，但前提是先判断后端的是否可编辑字段
  const isDisableDelete =
    !planOverviewProgress?.allowResourceEdit ||
    (planModel === LearnModelEnum.NO_CYCLE
      ? false
      : currentWeekOrDay?.startTime &&
        Number(currentWeekOrDay.startTime) < currentWeekOrDayStartTime.current);

  return (
    <Layout
      showHeader
      setChildrenContainerHeight={({ top, bottom }) => {
        safeTopRef.current = top || 0;
        return `calc(100vh - ${top || 0}px - ${bottom || 0}px)`;
      }}
      className={Styles["plan-resource-layout"]}
      headerProps={{
        children: planTitle || "",
        className: Styles["header"],
        back: <PageBackIcon />,
        onBack: () => {
          try {
            // 直接关闭，从哪里来回到哪里去
            closeWebview();
          } catch (error) {
            console.error("关闭webview失败", error);
          }
        },
        right: <TopBarRightMenu planId={planId} />,
      }}
    >
      <div
        className={cls([Styles["content"], "desk-plan-resource-content"])}
        style={{
          height: `calc(100vh - 12.8vw - ${safeTopRef.current}px)`,
        }}
        ref={scrollContainerRef}
        onScroll={handleScroll}
      >
        {/* 网络异常或者其他阻断性问题时友好提示，可以重新加载 */}
        {!loading && isPageError && (
          <div className={Styles["error-status-container"]}>
            <NetworkErrorStatus
              buttonOption={{
                handleClick: () => {
                  // 刷新时就开始loading
                  setLoading(true);
                  debounceLoadResourceList();
                },
              }}
            />
          </div>
        )}
        {!isPageError && (
          <Fragment>
            <div className={Styles["top-content"]}>
              {/* 任务进度 */}
              <div className={Styles["task-progress"]}>
                <MissionProgress
                  percent={planProgress}
                  showBottom
                  total={planOverviewProgress.taskCount}
                  done={planOverviewProgress.completeTaskCount}
                  showRefresh
                  onClickFeedback={() => {
                    toggle();
                    clickPv(
                      "ewt_h5_base_plan_desk_plan_resource_feedback_click",
                    );
                    expPv(
                      "ewt_h5_base_plan_desk_plan_resource_feedback_popup_expo",
                    );
                  }}
                  onQuestionClick={() => {
                    setProgressModalOpen(true);
                  }}
                  showFeedback={diagnosticFeedbackVisible}
                  onRefresh={() => {
                    // 刷新时就开始loading
                    setLoading(true);
                    hasMore.current = true;
                    debounceLoadResourceList();
                  }}
                />
              </div>
              {/* 时间列表 */}
              <div className={Styles["time-list"]}>
                {currentWeekOrDay &&
                  !!planTimeList?.length &&
                  LearnModelEnum.NO_CYCLE !== planModel && (
                    <TimeList
                      list={planTimeList}
                      currentItem={currentWeekOrDay}
                      currentTimeTitle={LearnModelTitleMap[planModel]}
                      onChange={(item) => {
                        // 切换日期时默认是有数据的，去查一次接口
                        hasMore.current = true;
                        // 刷新时就开始loading
                        setLoading(true);
                        setCurrentWeekOrDay(item);
                        globalResourceInfo.current.currentDay = item;
                        // 开始加载资源列表
                        loadResourceList(false, item);
                      }}
                      showDoneIcon={false}
                    />
                  )}
              </div>
            </div>

            <div className={Styles["plan-list-container"]}>
              {planResourceList?.map((item, index) => (
                <MissionItem
                  key={item.recordId}
                  resourceItem={{
                    ...item,
                    subjectIcon: item?.subjectId,
                  }}
                  onToAppCallback={(id: string) => {
                    globalResourceInfo.current.resourceId = id;
                  }}
                  disableDelete={checkIsDisableDelete()}
                  currentPlan={{ planId } as any}
                  onDelete={() => onDeleteResource(item.recordId, item)}
                  toPractice={(aiRecommendPaper, reqData) => {
                    // ai推题创建试卷
                    // createPaper(aiRecommendPaper, {
                    //   ...reqData,
                    //   planId,
                    // });
                  }}
                  lessonRouteParams={{
                    planId,
                    subjectId: 1,
                    planName: planTitle,
                    startTime: currentWeekOrDay?.startTime
                      ? Number(currentWeekOrDay.startTime)
                      : null,
                    endTime: currentWeekOrDay?.endTime
                      ? Number(currentWeekOrDay.endTime)
                      : null,
                  }}
                />
              ))}
              {resourceLoading && (
                <div className={Styles["loading"]}>加载中...</div>
              )}
              {!hasMore.current && !!planResourceList?.length && (
                <div className={Styles["no-more"]}>没有更多了</div>
              )}
              {!planResourceList?.length && !loading && !isPageError && (
                <div className={Styles["plan-resource-empty-container"]}>
                  {!checkIsDisableDelete() ? (
                    <EmptyStatus
                      image={PlanEmpty}
                      text="不积硅步无以至千里，快来构建你的学习蓝图吧"
                      className={Styles["empty-status-box"]}
                      buttonOption={{
                        className: Styles["empty-status-button"],
                        text: (
                          <>
                            <span className={Styles["button-add-icon"]}>+</span>
                            <span>前往E讲堂添加</span>
                          </>
                        ),
                        handleClick: () =>
                          onAddResource({ addScene: AddSceneEnum.Add }),
                      }}
                    >
                      <div
                        className={Styles["to-study-tip"]}
                        onClick={() => setStudyVisible(true)}
                      >
                        <span>如何通过E讲堂添加？</span>
                      </div>
                    </EmptyStatus>
                  ) : (
                    <EmptyStatus
                      image={PlanEmpty}
                      className={Styles["empty-status-box"]}
                    />
                  )}
                </div>
              )}
            </div>

            {/* 新增：引导添加更多内容底抽 */}
            <Popup
              title="如何通过E讲堂添加？"
              visible={studyVisible}
              onClose={() => setStudyVisible(false)}
            >
              <StudyRecommendPopup
                buttonText="前往E讲堂添加"
                noPadding={true}
                showTip={false}
                showButton={true}
                imgSrc={StudyRecommendPopupImg}
                onlyOnOk={true}
                buttonClassName={Styles["study-recommend-popup-button"]}
                onOk={() => {
                  setStudyVisible(false);
                  goToAPPStudy();
                }}
              />
            </Popup>
          </Fragment>
        )}
      </div>
      {/* 任务进度提示：资源加进来就以后进度的提示说明弹窗 */}
      <ProgressModal
        open={progressModalOpen}
        onClose={() => setProgressModalOpen((pre) => !pre)}
        onOk={() => {
          // operateNoRemindMeConfig(isNoRemindMe);
          setProgressModalOpen(false);
        }}
        showNoRemindModal={false}
      />
      {/* 全局加载中 */}
      {loading && <Spin />}
      {/* 返回顶部 */}
      <ScrollToTop scrollElementClass="desk-plan-resource-content" />
      {/* 固定悬浮容器，只有列表不为空，且当前周或天是可操作的 */}
      {!loading && !!planResourceList?.length && !isDisableDelete && (
        <ContinueAddButton onAddResource={onAddResource} />
      )}
      <FeedbackPopup
        visible={visible}
        questions={summaryInfo}
        submitLoading={updateSummaryOptionsLoading}
        onClose={() => toggle()}
        onConfirm={async (v) => {
          clickPv("ewt_h5_base_plan_desk_plan_resource_feedback_popup_click");
          await updateSummaryOptionsAsyncRun({
            planId,
            options: v,
          });
          Toast.show({
            content: "提交成功，感谢你的反馈",
            maskStyle: {
              zIndex: 1050,
            },
            position: "bottom",
          });
          toggle();
        }}
      />
      {/* 添加资源时上限提示 */}
      <AddResourceTipPopup
        visible={addResourceVisible}
        title={addResourceTipContent.title}
        content={addResourceTipContent.content}
        onClose={() => setAddResourceVisible(false)}
      />
      {/* 添加lottie弹窗组件 */}
      {!loading && !!showLottieList?.length && (
        <LottiePopup
          onClose={() => {}}
          lottieList={showLottieList}
          isPlaying={isPlaying}
          currentIndex={currentIndex}
          onSkip={handleSkip}
          onComplete={handleComplete}
        />
      )}
    </Layout>
  );
};
