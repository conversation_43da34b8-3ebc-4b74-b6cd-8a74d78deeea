import { useState } from "react";
import {
  getDeskPlanPositiveFeedback,
  PositiveFeedbackEnum,
} from "@/service/desk/plan-resource";
import { expPv } from "@/utils/tool";

interface UseLottiePopupProps {
  planId: string;
}

export interface ILottieConfig {
  animationDataUrl: string;
  type: PositiveFeedbackEnum;
  name: string;
}

// lottie文件的前缀地址
const lottiePrefix = `${process.env.PUBLIC_PATH}plan-resource-lottie`;
// lottie动画文件map
const lottieConfig: Record<PositiveFeedbackEnum, ILottieConfig> = {
  [PositiveFeedbackEnum.today]: {
    animationDataUrl: `${lottiePrefix}/today/data.json`,
    type: PositiveFeedbackEnum.today,
    name: "今天",
  },
  [PositiveFeedbackEnum.future]: {
    animationDataUrl: `${lottiePrefix}/future/data.json`,
    type: PositiveFeedbackEnum.future,
    name: "未来",
  },
  [PositiveFeedbackEnum.history]: {
    animationDataUrl: `${lottiePrefix}/history/data.json`,
    type: PositiveFeedbackEnum.history,
    name: "过去",
  },
};

export const useLottiePopup = ({ planId }: UseLottiePopupProps) => {
  // 最终需要展示的lottie配置
  const [showLottieList, setShowLottieList] = useState<ILottieConfig[]>([]);
  // 当前播放的动画索引
  const [currentIndex, setCurrentIndex] = useState(0);
  // 是否正在播放
  const [isPlaying, setIsPlaying] = useState(false);

  // 查询是否需要显示lottie弹窗
  const checkLottiePopup = async () => {
    try {
      const { data } = await getDeskPlanPositiveFeedback({
        planId,
        ignoreError: true,
      });

      if (data?.length) {
        const lottieResult = [];
        data.forEach((item) => {
          const lottieConfigItem = lottieConfig[item.popUpType];
          if (lottieConfigItem) {
            lottieResult.push(lottieConfigItem);
          }
        });
        setShowLottieList(lottieResult);
        setIsPlaying(true);
        setCurrentIndex(0);
        // 有数据即代表药暴漏，因此曝光上报
        expPv("ewt_h5_base_plan_desk_plan_resource_positive_feedback_expo");
      }
    } catch (error) {
      console.error("查询lottie弹窗状态失败", error);
    }
  };

  // 跳过当前动画
  const handleSkip = () => {
    if (currentIndex < showLottieList.length - 1) {
      // 播放下一个动画
      setCurrentIndex((prev) => prev + 1);
    } else {
      // 所有动画播放完毕
      setIsPlaying(false);
      setCurrentIndex(0);
      setShowLottieList([]);
    }
  };

  // 处理动画完成
  const handleComplete = () => {
    if (currentIndex < showLottieList.length - 1) {
      // 播放下一个动画
      setCurrentIndex((prev) => prev + 1);
    } else {
      // 所有动画播放完毕
      setIsPlaying(false);
      setCurrentIndex(0);
      setShowLottieList([]);
    }
  };

  return {
    showLottieList,
    checkLottiePopup,
    isPlaying,
    currentIndex,
    handleSkip,
    handleComplete,
  };
};
