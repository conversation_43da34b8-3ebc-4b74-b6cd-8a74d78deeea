.more-menu-popover {
  :global {
    .adm-popover-arrow {
      z-index: 100;
    }
    .adm-popover-menu-list {
      min-width: 100px;
    }
    .adm-popover-inner {
      box-shadow: 0 2px 14px 0 rgba(0, 0, 0, .25);
    }

    .adm-popover-menu-item {
      padding: 0;
    }

    .adm-popover-menu-item-text {
      padding: 0;
      padding: 12px 15px;
      text-align: center;
      flex: none;
      width: 100%;
    }
  }
}
.plan-resource-layout {
  background-image: url("@/assets/image/plan-resource/common_header_bg.png");
  background-size: 100% 120px;
  background-repeat: no-repeat;
  height: 100vh;

  .more-menu-text {
    font-weight: bold;
    font-size: 20px;
    color: rgba(0, 0, 0, .65);
  }

  .header {
    background: transparent;

    :global {
      .adm-nav-bar-left {
        flex: none;
        width: 12px;
        margin-right: 8px;
      }

      .adm-nav-bar-title {
        text-align: left;
        padding-left: 0;
      }

      .adm-nav-bar-right {
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
    }
  }

  .content {
    height: calc(100vh - 12.8vw);
    overflow-x: hidden;
    overflow-y: auto;
    background-color: #F3F4F8;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;

    // 上方的内容容器，包括进度条、计划周期列表
    .top-content {
      background-color: #fff;
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 8px;
    }

    .task-progress {
      padding: 10px 12px 0 6px;
    }

    .time-list {
      margin-top: 14px;
    }

    // 单个周期内的资源进度列表容器
    .plan-list-container {
      padding: 8px 8px 75px;

      .loading,
      .no-more {
        text-align: center;
        color: #999;
        font-size: 14px;
        padding: 16px 0;
      }
    }
  }
}

//阻断行错误时显示的容器
.error-status-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
}

// 计划资源空态容器
.plan-resource-empty-container {
  width: 100% ;
  height: 50vh;
  display: flex;
  justify-content: center;
  align-items: center;

  .empty-status-box {
    width: 100%;
    font-size: 12px;
    color: #999;

    :global {
      .adm-error-block-description {
        div {
          color: #999!important;
          font-size: 12px!important;
        }
      }

      .adm-error-block-image {
        img {
          width: 160px;
          height: 160px;
        }
      }
    }
  }

  .empty-status-button {
    padding: 4px 12px!important;

    & > div {
      font-size: 14px!important;
      font-weight: bold;
    }

    .button-add-icon {
      margin-right: 6px;
      font-weight: bold;
      font-size: 14px;
    }
  }

  .to-study-tip {
    margin-top: 16px;
    font-size: 12px;
    color: #2F86FF;
    cursor: pointer;
  }
}

// 固定悬浮容器
.fixed-container {
  position: fixed;
  right: 0;
  bottom: 56px;
  width: 87px;
  height: 30px;
  z-index: 100;
  background-color: #2F86FF;
  border-top-left-radius: 100px;
  border-bottom-left-radius: 100px;
  border: 1px solid #021E66;
  border-right: none;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  cursor: pointer;

  .fixed-container-icon {
    font-size: 16px;
    font-weight: bold;
    margin-right: 4px;
  }

  .fixed-container-text {
    font-size: 14px;
    font-weight: bold;
  }
}

.custom-center-popup {
  width: 343px;
  text-align: center;
  height: 176px;
  border-radius: 36px;

  .popup-title {
    text-align: center;
    font-size: 18px;
    color: #333333;
    font-weight: bold;
    padding-top: 24px;
    position: relative;

    .close-icon {
      position: absolute;
      right: 20px;
      top: 20px;
      font-size: 20px;
      color: #A7ACB9;
    }
  }

  .popup-content {
    padding-top: 20px;
    font-size: 14px;
    color: #666;
    text-align: center;
    line-height: 22px;
  }

  .popup-button {
    cursor: pointer;
    width: 311px;
    height: 44px;
    background-color: #2F86FF;
    border: 1px solid #021E66;
    box-shadow: 0 4px 0 0 #021e6626;
    border-radius: 30px;
    text-align: center;
    line-height: 44px;
    margin: 22px auto 0;
    color: #fff;
    font-size: 16px;
    font-weight: bold;
  }
}

.study-recommend-popup-button {
  background-color: #2F86FF;
  min-width: 164px;
  border: 1px solid #021E66;
  box-shadow: 0 4px 0 0 #021e6626;
  border-radius: 20px;
  padding: 7px 16px;
}