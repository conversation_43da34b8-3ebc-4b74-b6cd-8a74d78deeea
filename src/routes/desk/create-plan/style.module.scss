.create-plan-layout {
  .header {
    :global {
      .adm-nav-bar-left {
        flex: 0 1 auto;
        .adm-nav-bar-back {
          margin-right: 0;
        }
      }
      .adm-nav-bar-title {
        text-align: left;
      }
    }
  }
  .content {
    overflow: auto;
    display: flex;
    height: calc(100% - 12.8vw - 72px);
    background-color: #cc926d;
    .form-block {
      position: relative;
      flex: 1;
      padding: 12px 16px;
      margin: 19px 12px;
      background: #fff;
      border: 1px solid #000000;
      border-radius: 8px;
      .form-item {
        position: relative;
        z-index: 2;
        margin-bottom: 32px;
        &.pass{
          .date-picker-c{
            font-weight: bold;
            font-size: 14px;
            color: #333333;
            &.selected{
              background: #F3F4F8;
            }
          }
          .form-item-label-right{
            color: #00B81E;
            visibility:visible !important;
          }
        }
        .date-picker-c {
          display: flex;
          align-items: center;
          padding-left: 12px;
          height: 40px;
          background: #FFFFFF;
          border: 1px solid #021E66;
          border-radius: 8px;
          font-weight: 400;
          font-size: 14px;
          color: #999999;
        }
        .radio-block{
          display: flex;
          justify-content: space-between;
          flex-direction: column;
          .radio-w{
            position: relative;
            height: 88px;
            background: #FFFFFF;
            border: 1px solid #021E66;
            border-radius: 8px;
            display: flex;
            margin-bottom: 12px;
            .radio-b{
              padding: 12px 16px;
              .label{
                font-weight: bold;
                font-size: 16px;
                color: #999999;
              }
              .desc{
                margin-top: 8px;
                font-weight: 400;
                white-space: pre-wrap;
                line-height: 16px;
                font-size: 12px;
                color: #999999;
              }
            }
            .radio-c{
              display: flex;
              align-items: center;
              justify-content: center;
              color: #fff;
              content: '';
              position: absolute;
              right: 8px;
              top: 8px;
              width: 16px;
              height: 16px;
              border-radius: 50%;
              border: 1px solid #021E66;
              :global{
                .svg-icon{
                  transform: scale(0.7);
                }
              }
            }
            &.checked{
              background-color: #F3F4F8;
              .label{
                color: #333333;
              }
              .radio-c{
                background-color: #52C41A;
              }
            }
          }
        }
        :global {
          .adm-text-area-count {
            padding: 0;
            font-weight: bold;
            font-size: 12px;
            color: #999999;
          }
          .adm-slider-thumb-container{
            width: 20px;
            height: 20px;
          }
          .adm-slider-thumb{
            width: 20px;
            height: 20px;
            .svg-icon{
              width: 20px;
              height: 20px;
            }
          }
          .adm-popover-inner {
            border-radius: 20px !important;
            overflow: hidden;
          }
          .adm-popover-arrow{
            background-image: url("@/assets/common/desk/arrow.svg");
            background-size: contain;
            background-repeat: no-repeat;
            bottom: calc(var(--arrow-size) * -1 + 1px) !important;
          }
          .adm-popover-arrow-icon {
            display: none;
          }
          .adm-popover-inner-content {
            height: 27px;
            box-sizing: border-box;
            display: flex;
            border-radius: 20px;
            align-items: center;
            background: #fff1b7;
            border: 1px solid #ffd633;
            font-weight: bold;
            font-size: 14px;
            color: #333333;
            text-align: center;
          }
          .adm-text-area-element {
            line-height: 25px;
            font-weight: 500;
            font-size: 18px;
            color: #333333;
            min-height: 60px;
          }
          .adm-text-area-element::placeholder {
            line-height: 20px;
            font-weight: 400;
            font-size: 14px;
            color: #999999;
          }
        }
        .form-item-label-inner{
          margin-bottom: 12px;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .form-item-label-right {
            font-weight: 400;
            font-size: 12px;
            visibility: hidden;
            text-decoration: underline;
          }
        }
        .form-item-label {
          position: relative;
          display: flex;
          align-items: center;
          height: 22px;
          // margin-left: 12px;
          line-height: 22px;
          font-weight: bold;
          font-size: 16px;
          color: #333333;
          &::before{
            display: block;
            content: "";
            width: 4px;
            left: 0;
            height: 10px;
            background: #2F86FF;
            border-radius: 8px;
            border-radius: 2px;
            margin-right: 8px;
          }
          .form-item-label-help {
            margin-left: 16px;
            font-weight: 400;
            font-size: 12px;
            color: #999999;
          }
        }
      }
      .plan-theme{
        overflow: hidden;
        text-align: center;
        width: 100%;
        position: absolute;
        bottom: 12px;
        left: 0;
        .plan-info{
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 400;
          font-size: 14px;
          color: #999990;
          .name{
            font-weight: bold;
            color: #333333;
          }
        }
        .plan-guide{
          color: #2F86FF;
          margin-top: 8px;

          :global{
            .svg-icon{
              font-size: 16px;
            }
          }
        }
      }
    }
  }
  .footer {
    min-height: 72px;
    padding: 12px 16px;
    &.safe{
      padding-bottom: calc(constant(safe-area-inset-bottom) + 12px);
      padding-bottom: calc(env(safe-area-inset-bottom) + 12px);
    }
    > div {
      border: 1px solid #021e66;
      box-shadow: 0 4px 0 0 #021e6626;
    }
  }
}

.date-picker-popup{
  :global{
    .adm-popup-body {
      pointer-events: unset!important;
    }
  }
}