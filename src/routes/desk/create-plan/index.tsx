import React, { useState } from "react";
import { useRequest } from "ahooks";
import { CalendarPicker, Toast } from "antd-mobile";
import dayjs from "dayjs";
import debounce from "lodash/debounce";
import { useLocation, useNavigate } from "react-router-dom";

import { Layout, Button, IconSvg } from "@/components";
import * as planApi from "@/service/desk/plan";
import BackBlackImg from "@/assets/common/back_black.png";
import Styles from "./style.module.scss";
import { closeWebview, getIsInMSTApp } from "@/utils/bridge-utils";
import { clickPv, createURLByType, EJumpType } from "@/utils/tool";

export const Component: React.FC = () => {
  const locationForReactRouter = useLocation();
  const [visible, setVisible] = useState(false);
  const [endTime, setEndTime] = useState<number>();
  const [learnMode, setLearnMode] = useState<number>();
  const navigate = useNavigate();

  const {
    run: generatePlanNameRun,
    loading: generatePlanNameLoading,
    data: { data: planTheme } = {},
  } = useRequest(planApi.generatePlanName, {
    manual: true,
  });

  const { run: planAddRun, loading: planAddLoading } = useRequest(
    planApi.planAdd,
    {
      manual: true,
      onSuccess(res) {
        if (!res?.success) {
          Toast.show(res?.msg || "创建失败了");
          return;
        }
        Toast.show("计划创建成功");
        // 新增逻辑：如果是App跳转过来的，直接关闭webview，否则跳转计划详情页
        const isFromApp = location.search.indexOf("fromSource=App") !== -1;
        if (isFromApp) {
          closeWebview();
          return;
        }
        setTimeout(() => {
          navigate(
            createURLByType({
              type: EJumpType.inside,
              originSearch: locationForReactRouter.search,
              path: "/desk/plan-resource",
              addQueryObject: {
                planId: res.data,
              },
            }),
            { replace: true },
          );
        }, 500);
      },
    },
  );

  const learnModeOptions = [
    {
      label: "自由冲刺",
      desc: "在【截止日期】之前完成学习即可\n节奏你自己把控，适合P人",
      value: 3,
    },
    {
      label: "阶段攻克",
      desc: "在【截止日期】之前【按周】分解任务\n依次完成，适合J人",
      value: 1,
    },
  ];

  const onSubmit = () => {
    if (planAddLoading) {
      return;
    }
    if (!endTime || !learnMode) {
      Toast.show("请完成以上选项");
      return;
    }
    if (!planTheme) {
      Toast.show("计划名称未生成");
      return;
    }
    clickPv("ewt_h5_base_plan_desk_create_plan_create_button_click", {
      plantype: learnMode === 1 ? "周计划" : "无周期计划",
    });
    planAddRun({
      planTheme,
      endTime,
      learnMode,
    });
  };

  const onBack = () => {
    if (getIsInMSTApp()) {
      closeWebview();
    } else {
      history.back();
    }
  };

  const generatePlanNameRunDebounce = debounce(generatePlanNameRun, 500);

  return (
    <Layout
      showHeader
      setChildrenContainerHeight={({ top, bottom }) => {
        return `calc(100vh - ${top || 0}px - ${bottom || 0}px)`;
      }}
      className={Styles["create-plan-layout"]}
      headerProps={{
        children: "自主创建计划",
        className: Styles.header,
        backIconUrl: BackBlackImg,
        onBack,
      }}
    >
      <div className={Styles["content"]}>
        <div className={Styles["form-block"]}>
          <div
            className={`${Styles["form-item"]} ${endTime ? Styles["pass"] : ""}`}
          >
            <div className={Styles["form-item-label-inner"]}>
              <div className={Styles["form-item-label"]}>
                截止日期
                <span className={Styles["form-item-label-help"]}>
                  点击并选择截止日期
                </span>
              </div>
              <div className={Styles["form-item-label-right"]}>
                <IconSvg name="icon-yijiajihua" />
              </div>
            </div>
            <div
              onClick={() => setVisible(true)}
              className={`${Styles["date-picker-c"]} ${endTime ? Styles["selected"] : ""}`}
            >
              {endTime
                ? dayjs(+endTime).format("YYYY年MM月DD日")
                : "点击选择日期"}
            </div>
            <CalendarPicker
              selectionMode="single"
              popupClassName={Styles["date-picker-popup"]}
              defaultValue={endTime ? dayjs(endTime).toDate() : undefined}
              visible={visible}
              min={dayjs().toDate()}
              max={dayjs().add(5, "month").toDate()}
              title="请选择日期"
              onMaskClick={() => setVisible(false)}
              onClose={() => {
                setVisible(false);
              }}
              onConfirm={(val) => {
                const valSt = dayjs(val)
                  .hour(23)
                  .minute(59)
                  .second(59)
                  .valueOf();
                setEndTime(valSt);
                if (learnMode && valSt) {
                  generatePlanNameRun({
                    endTime: valSt,
                    learnMode,
                  });
                }
              }}
            />
          </div>
          <div
            className={`${Styles["form-item"]} ${learnMode ? Styles["pass"] : ""}`}
          >
            <div className={Styles["form-item-label-inner"]}>
              <div className={Styles["form-item-label"]}>
                学习模式
                <span className={Styles["form-item-label-help"]}>
                  点击下方选项，选择学习模式
                </span>
              </div>
              <div className={Styles["form-item-label-right"]}>
                <IconSvg name="icon-yijiajihua" />
              </div>
            </div>
            <div className={Styles["radio-block"]}>
              {learnModeOptions?.map((v) => (
                <div
                  key={v.value}
                  className={`${Styles["radio-w"]} ${learnMode === v.value ? Styles["checked"] : ""}`}
                  onClick={() => {
                    setLearnMode(v.value);
                    if (endTime && v.value) {
                      generatePlanNameRunDebounce({
                        endTime,
                        learnMode: v.value,
                      });
                    }
                  }}
                >
                  <div className={Styles["radio-b"]}>
                    <div className={Styles["label"]}>{v.label}</div>
                    <div className={Styles["desc"]}>{v.desc}</div>
                  </div>
                  <div className={Styles["radio-c"]}>
                    <IconSvg name="icon-yijiajihua" />
                  </div>
                </div>
              ))}
            </div>
          </div>
          {planTheme && (
            <div className={Styles["plan-theme"]}>
              <div className={Styles["plan-info"]}>
                计划名称：<span className={Styles["name"]}>{planTheme}</span>
              </div>
              <div className={Styles["plan-guide"]}>
                <IconSvg name="icon-xiajiantou1" />
              </div>
            </div>
          )}
        </div>
      </div>
      <div className={`${Styles["footer"]} ${Styles["safe"]}`}>
        <Button
          loading={planAddLoading}
          text="立即创建计划"
          onClick={onSubmit}
        />
      </div>
    </Layout>
  );
};
