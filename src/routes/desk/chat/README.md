# 对话式引导

## 概述

支持单选和多选模式，并可以基于用户选择生成个性化推荐。**系统现在支持嵌套的多选节点。**

## 核心功能

1. **对话式交互**：以聊天方式引导用户完成流程
2. **多选支持**：可以在一个节点中选择多个选项，**包括嵌套的多选场景**
3. **分支处理**：支持基于选择的分支处理，**能够处理多层嵌套分支**
4. **个性化推荐**：基于用户在整个（包括嵌套）对话流程中的选择生成个性化学习计划

## 多选分支处理详解

### 基本工作原理

当用户在多选节点（**可能是嵌套结构中的一个节点**）选择多个答案并确认后，系统会按照特定顺序处理每个选择对应的分支流程。
**对于嵌套的多选节点，系统会优先完成最内层多选节点的所有分支。当内层的所有分支都完整执行完毕后，系统才会返回到其上一层（父级）多选节点，继续处理该父级节点的下一个未完成的分支。**
这个过程会一直持续，直到所有层级的所有选定分支都执行完毕。最终，系统会收集在整个嵌套流程中所有分支的计划触发器，并生成综合推荐。

### 两种执行模式

系统支持两种多选分支执行顺序（**此规则适用于任何层级的多选节点**）：

1. **用户选择顺序（默认）**：按照用户点击选项的顺序执行分支
2. **配置顺序**：按照在配置中指定的`execution_order`值执行分支

### 分支结束判断

系统使用以下规则判断一个分支（**无论其嵌套层级如何**）是否结束：

1. **触发计划**：如果选择触发了`plan_trigger`，表示分支执行到终点。
2. **结束节点**：系统自动识别结束节点（例如，节点没有后续答案，或节点本身被标记为`is_branch_end`）。当一个节点被认为是结束节点时，当前分支路径结束。
3. **手动标记**：通过在节点中设置`is_branch_end: true`手动标记一个节点为分支结束点。

**当一个分支结束时，系统会尝试导航到当前多选层级的下一个分支。如果当前多选层级的所有分支都已处理完毕，系统会尝试返回到上一层（父级）多选节点，并从其下一个未处理的分支继续。如果所有层级都已完成，则整个多选流程结束。**

### 配置方法

#### 1. 配置多选节点

```javascript
{
  "node_id": "node_exam_skill_topic", // 假设这是一个外层多选
  "question_text": "最近几次考试中，哪个题型最影响得分？",
  "answer_type": "multi_select",     // 设置为多选模式
  "execute_by_config_order": true,   // 启用配置顺序执行（可选）
  "answers": [
    {
      "answer_id": "ans_skill_topic_A_listening",
      "answer_text": "听力理解",
      "next_node_id": "node_listening_issue_type", // 这个可能是一个内层多选节点
      "plan_trigger": null,
      "execution_order": 0           // 执行顺序值（数值越小越先执行）
    },
    {
      "answer_id": "ans_skill_topic_B_reading",
      "answer_text": "阅读理解",
      "next_node_id": "node_reading_issue_type", // 另一个潜在的内层多选节点
      "plan_trigger": null,
      "execution_order": 1
    },
    // 更多选项...
  ]
}

// 示例：一个可能的内层多选节点 (node_listening_issue_type)
{
  "node_id": "node_listening_issue_type",
  "question_text": "听力具体是哪方面的问题？",
  "answer_type": "multi_select",
  "answers": [
    {
      "answer_id": "ans_listening_A_cannot_understand",
      "answer_text": "听不懂内容",
      "next_node_id": "node_listening_advice_A", // 指向具体建议或更深层节点
      "plan_trigger": "听力基础知识"
    },
    {
      "answer_id": "ans_listening_B_too_fast",
      "answer_text": "语速太快跟不上",
      "next_node_id": "node_listening_advice_B",
      "plan_trigger": "听力速度训练"
    }
    // ...
  ]
}
```

#### 2. 配置分支结束标记

有三种方式可以标记分支结束（**适用于任何分支，包括嵌套分支内的叶子节点**）：

1. **触发计划**：带有`plan_trigger`的答案会自动结束当前分支路径（例如，上述`ans_listening_A_cannot_understand`）。

```javascript
{
  "answer_id": "ans_listening_A_cannot_understand",
  "answer_text": "听不懂内容",
  "next_node_id": null, // 或者指向一个总结性非交互节点
  "plan_trigger": "听力基础知识"  // 触发计划并结束分支
}
```

2. **手动标记**：在节点中设置`is_branch_end`属性

```javascript
{
  "node_id": "node_listening_advice_A_completed", // 假设这是听力分支的一个结束点
  "is_branch_end": true,            // 手动标记为分支结束点
  "question_text": "关于"听不懂内容"的建议已提供。",
  // ...
}
```

3. **无后续路径**：如果一个答案的 `next_node_id` 为 `null` 或指向一个不存在的节点，或者一个节点本身没有任何答案，该路径也会被视为结束。

### 计划触发器收集

系统会收集在**整个多选流程（包括所有嵌套层级的所有已执行分支）中所有被触发的计划**。在所有相关的分支（无论层级）都执行完毕后，这些收集到的计划触发器会一次性汇总并应用，以确保用户可以获得基于其所有选择的全面推荐结果。

## 使用场景

### 场景一：技能评估

用户可以选择多个需要提升的技能（如听力、阅读）。针对"听力"，系统可能会进一步询问具体问题（如"听不懂内容"还是"语速太快"，这本身可能是个多选形成嵌套）。系统会依次完整评估每个选定技能（包括其内嵌的细分问题）的所有分支，最终提供综合的学习计划。

### 场景二：兴趣探索

用户可以选择多个感兴趣的主题。对于某个主题，系统可能会引导用户选择该主题下的子分类（又一层多选）。系统会对每个选定主题及其子分类的所有分支进行深入探索，最终提供全面的内容推荐。

## 最佳实践

1. **明确分支执行顺序**：对于有依赖关系的分支（在任何层级），使用`execute_by_config_order`和`execution_order`控制执行顺序。
2. **合理设置结束点**：确保每个分支路径（**包括嵌套分支内的路径**）都有明确的结束标志，避免处理流程卡住或意外跳出。
3. **计划触发器命名**：使用清晰且唯一的计划触发器名称，确保不会有重复推荐，并且能够准确反映其对应的建议。
4. **测试多选组合**：测试不同的多选组合，**特别是包含嵌套多选的复杂场景**，确保所有分支都能按预期正确处理和回溯。
5. **考虑嵌套深度**：虽然系统支持嵌套，但设计过于深层的嵌套可能会让用户感到困惑，应保持引导流程的简洁明了。

## 开发指南

### 添加新分支（或嵌套分支）

1.  在`dialogData.ts`（或其他数据源）中添加新节点。
2.  确保为多选选项设置正确的`next_node_id`。如果`next_node_id`指向另一个多选节点，即构成了嵌套。
3.  如果需要为某个多选层级指定执行顺序，在该多选节点的配置中添加`execute_by_config_order: true`及对应答案的`execution_order`属性。
4.  为分支的结束点（可能是嵌套流程的叶子节点）设置`plan_trigger`，或在其父节点上设置`is_branch_end: true`，或者确保其没有有效的`next_node_id`。
5.  仔细规划嵌套逻辑，确保内层分支完成后能正确返回外层继续执行。
