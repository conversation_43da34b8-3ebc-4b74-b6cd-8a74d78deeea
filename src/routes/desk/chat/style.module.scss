.chat-page-wrapper {
  position: relative;
  background-color: #d3e6ff;
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;

  &::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 56%;
    z-index: 1;
    /* 控制渐变高度 */
    background-image: linear-gradient(
      180deg,
      rgba(47, 134, 255, 0.59) 1%,
      rgba(47, 134, 255, 0) 100%
    );
  }

  .nav-bar {
    position: fixed;
    display: flex;
    top: 0;
    left: 0;
    height: 44px;
    width: 25%;
    padding: 0 16px;
    display: flex;
    z-index: 10;
    align-items: center;

    .nav-bar-left {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      transform: rotate(180deg);
      border-radius: 50%;
      width: 32px;
      height: 32px;
      background: rgba(255, 255, 255, 0.25);
      border: 1px solid #ffffff;
    }
  }

  .page-container {
    padding: 60px 16px 24px;
    height: 100vh;
    position: relative;
    z-index: 9;
    .error-content {
      position: relative;
      z-index: 9;
      height: 100%;
      display: flex;
      align-items: center;
      .btn {
        height: 32px;
        > div {
          font-size: 14px;
          font-weight: 400;
        }
      }
    }
  }

  .dialog-interface {
    display: flex;
    flex-direction: column;
    margin: 0 auto;
  }

  .dialog-interface-main {
    flex: 1;
    display: flex;
    padding-bottom: 60px;
    flex-direction: column;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    .dialog-item {
      display: flex;
      flex-direction: column;
      margin-bottom: 22px;
    }
  }

  .dialog-restart-wrapper {
    width: 100%;
    margin-top: 12px;
    .dialog-restart {
      display: block;
      margin: 0 auto;
      border: none;
      width: 96px;
      height: 32px;
      color: rgba(0, 0, 0, 0.65);
      background: rgba(255, 255, 255, 0.45);
      border: 1px solid #ffffff;
      border-radius: 16px;
    }
  }
  .dialog-retry-wrapper {
    width: 100%;
    margin-top: 8px;
    display: flex;
    justify-content: flex-end;
    .dialog-retry {
      width: 56px;
      height: 28px;
      color: rgba(0, 0, 0, 0.65);
      background: rgba(255, 255, 255, 0.45);
      border: 1px solid #ffffff;
      border-radius: 16px;
    }
  }
  .dialog-switch-book-wrapper {
    width: 100%;
    margin-top: 8px;
    .dialog-switch {
      width: 104px;
      height: 28px;
      color: rgba(0, 0, 0, 0.65);
      background: rgba(255, 255, 255, 0.45);
      border: 1px solid #ffffff;
      border-radius: 16px;
      margin-bottom: 8px;
    }
    .dialog-switch-tips {
      font-weight: 400;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.45);
      line-height: 20px;
    }
  }
}
.dialog-scroll-bottom {
  position: fixed;
  display: flex;
  opacity: 0;
  z-index: 20;
  transition: .5s;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #fff;
  width: 96px;
  padding: 6px 0 1px;
  cursor: pointer;
  background: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  border-radius: 16px;
  bottom: 24px;
  left: 50%;
  overflow: hidden;
  line-height: 0.8;
  transform: translateX(-50%);
  &.show{
    opacity: 1;
  }
  :global{
    .svg-icon{
      display: block;
      font-size: 10px;
      transform: rotate(90deg);
    }
  }
}

.fade-up {
  opacity: 0;
  transform: translateY(20px);
  transition:
    opacity 0.5s ease-out,
    transform 0.5s ease-out;
}

.fade-up.active {
  opacity: 1;
  transform: translateY(0);
}
