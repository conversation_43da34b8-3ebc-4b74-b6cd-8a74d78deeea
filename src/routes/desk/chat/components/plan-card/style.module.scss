.plan-card {
  cursor: pointer;
  padding: 12px;
  border-radius: 16px;
  background-image: linear-gradient(0deg, #e3efff 2%, #ffffff 100%);
  border: 1px solid rgba(0, 0, 0, 0.25);
  border-radius: 12px;
  margin-top: 12px;
}

.plan-card-top {
  display: flex;
  align-items: center;
  height: 16px;
  justify-content: space-between;
  color: rgba(0, 0, 0, 0.45);
  font-weight: 400;
  font-size: 12px;
  .info {
    display: flex;
    align-items: center;
    div{
      margin-right: 12px;
    }
  }
  .week-count {
    padding: 0 3px;
    line-height: 16px;
    background: #ffd52f;
    color: rgba(0, 0, 0, 0.85);
    border-radius: 2px;
  }
}

.plan-card-title {
  margin: 10px 0 8px;
  font-weight: bold;
  font-size: 16px;
  color: #000000;
  line-height: 22px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 44px;
}

.plan-card-description {
  width: 100%;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 32px;
  line-height: 16px;
  font-weight: 400;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}