import React from "react";
import { IconSvg } from "@/components";
import Style from "./style.module.scss";
import { parseDeskPlanTime } from "@/utils/tool";

interface IPlanCardProps {
  plan: any;
  onClick: (v) => void;
}

// 学习模式，1 周计划, 2 日计划, 3 无周期计划
export enum ELearnModeType {
  week = 1,
  day = 2,
  none = 3,
}

const PlanCard: React.FC<IPlanCardProps> = ({ plan = {}, onClick }) => {
  return (
    <div
      className={Style["plan-card"]}
      onClick={(e) => {
        e.stopPropagation();
        onClick(plan);
      }}
    >
      <div className={Style["plan-card-top"]}>
        <div className={Style["info"]}>
          <div className={Style["week-count"]}>
            {plan.weekCount}
            {plan.learnMode === ELearnModeType.week ? "周" : "天"}计划
          </div>
          <div className={Style["time"]}>
            {parseDeskPlanTime(plan.learnTime)}
          </div>
          <div className={Style["subject"]}>{plan.subjectName}</div>
        </div>
        <div className={Style["arrow"]}>
          <IconSvg name="icon-a-jinru2x" />
        </div>
      </div>
      <h4 className={Style["plan-card-title"]}>{plan.name}</h4>
      <p className={Style["plan-card-description"]}>{plan.desc}</p>
    </div>
  );
};

export default PlanCard;
