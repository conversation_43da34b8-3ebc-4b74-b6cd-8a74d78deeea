import React, { forwardRef } from "react";
import DefaultTextbookP from "@/assets/chat/default_textbook.png";
import Style from "./style.module.scss";
import PlanCard from "../plan-card";
import { useDialog } from "../../context/useDialog";
import LottieCom from "@/components/lottie-box";
import { Toast } from "antd-mobile";
import { DialogNode, EAnswerType, EBizType, ENodeType } from "../../types";

import LottieJson from "../../lottie/loading.json";
export interface IDialogNode {
  node: DialogNode;
  type?: "question" | "answer";
  onChange?: (v, o) => void;
  renderExtra?: (record: any) => JSX.Element;
  onPlanClick?: (v) => void;
}

const DNode = (props: IDialogNode, ref) => {
  const { node, type = "question", onPlanClick } = props;

  const {
    handleSelection,
    handleMultiSelect,
    selectedAnswers,
    confirmSelections,
  } = useDialog();

  // 检查选项是否被选中
  const isSelected = (answerId) => {
    return selectedAnswers?.includes(answerId);
  };

  const isAnswer = type === "answer";

  const isMultiSelect = node.answerType === EAnswerType.MULTIPLE_CHOICE;

  // 处理选项点击
  const handleOptionClick = async (answerId, answer) => {
    /** 非互斥 */
    if (!answer.mutuallyExclusive && !selectedAnswers.includes(answerId)) {
      if (node.maxSelectNum && selectedAnswers.length >= node.maxSelectNum) {
        Toast.show(`最多选择 ${node.maxSelectNum} 项~`);
        return;
      }
    }
    if (isMultiSelect) {
      handleMultiSelect(answerId);
    } else {
      handleSelection(answerId);
    }
  };

  const TextBookHasAutoSelect =
    node.bizType === EBizType.TEXTBOOK && node.options?.find((v) => v.selected);

  const rendBookAutoCard = () => {
    if (TextBookHasAutoSelect) {
      return (
        <div className={Style["text-card"]}>
          <img
            src={TextBookHasAutoSelect.imageUrl || DefaultTextbookP}
            alt={TextBookHasAutoSelect.text}
          />
          <div className={Style["title"]}>{TextBookHasAutoSelect.text}</div>
        </div>
      );
    }
  };

  if ([ENodeType.LOADING, ENodeType.ERROR].includes(node.nodeType)) {
    const isLoading = node.nodeType === ENodeType.LOADING;
    const isError = node.nodeType === ENodeType.ERROR;
    return (
      <div
        className={`${Style["dialog-node-inner"]} ${isAnswer ? Style.answer : ""}  ${isLoading ? Style.loading : ""}`}
      >
        <div className={Style["dialog-node-l"]} />
        <div className={`${Style["dialog-node-r"]}`}>
          {/* 显示问题文本 */}
          <div
            className={`${Style["dialog-node-question"]} ${node.bizType === EBizType.PLAN ? Style["plan"] : ""}`}
          >
            {isLoading && (
              <div className={Style.loader}>
                <LottieCom dataJson={LottieJson} />
              </div>
            )}
            {node.questionText}
            {isError && (
              <span className={Style.retry} onClick={node.retry}>
                点击重试
              </span>
            )}
          </div>
        </div>
      </div>
    );
  }

  /** 历史记录无无可选操作 */
  if (node.nodeType === ENodeType.TEXT) {
    return (
      <div
        className={`${Style["dialog-node-inner"]} ${isAnswer ? Style.answer : ""}`}
      >
        {/* 回答 */}
        <div className={Style["dialog-node-l"]} />
        <div className={`${Style["dialog-node-r"]}`}>
          {/* 显示问题文本 */}
          <div
            className={`${Style["dialog-node-question"]} ${node.bizType === EBizType.PLAN ? Style["plan"] : ""}`}
          >
            {node.questionDescription && (
              <div className={Style["dialog-node-question-desc"]}>
                {node.questionDescription}
              </div>
            )}
            {TextBookHasAutoSelect
              ? "根据你之前的设置，已为你选择以下教材版本"
              : isAnswer
                ? node.answerText
                : node.questionText}
            {/* 教材自动选择的卡片 */}
            {TextBookHasAutoSelect && rendBookAutoCard()}
            {/* 计划卡片 */}
            {node.bizType === EBizType.PLAN && (
              <div className={Style["learning-plan-container"]}>
                {node.planData?.map((v, i) => (
                  <PlanCard
                    plan={v}
                    key={i}
                    onClick={() => {
                      onPlanClick(v);
                    }}
                  />
                ))}
              </div>
            )}
          </div>
          {node.bizType === EBizType.PLAN && (
            <div className={Style["learning-plan-tips"]}>
              提示：生成的计划不会一直存在，如果没有创建并退出的话，计划将会消失，记得去创建哦～
            </div>
          )}
          {props.renderExtra?.(props)}
        </div>
      </div>
    );
  }
  /** 当前节点存在选择 */
  return (
    <div
      ref={ref}
      className={`${Style["dialog-node-inner"]} ${isAnswer ? Style.answer : ""}`}
    >
      {/* 回答 */}
      <div className={Style["dialog-node-l"]} />
      <div className={`${Style["dialog-node-r"]}`}>
        {/* 问题节点 */}
        <div
          className={`${Style["dialog-node-question"]} ${node.bizType === EBizType.PLAN ? Style["plan"] : ""}`}
        >
          {node.questionDescription && (
            <div className={Style["dialog-node-question-desc"]}>
              {node.questionDescription}
            </div>
          )}
          {node.questionText}
        </div>
        {/* 学科选项列表 */}
        {node.bizType === EBizType.SUBJECT && (
          <div
            className={`${Style["dialog-node-answers"]} ${Style["subject"]}`}
          >
            {node.options.map((answer) => (
              <button
                key={answer.optionId}
                className={`${Style["dialog-node-answer"]} ${isSelected(answer.optionId) ? Style["dialog-node-answer-selected"] : ""}`}
                onClick={() => handleOptionClick(answer.optionId, answer)}
              >
                {answer.text}
              </button>
            ))}
          </div>
        )}

        {/* 教材选项列表 */}
        {node.bizType === EBizType.TEXTBOOK && !TextBookHasAutoSelect && (
          <div
            className={`${Style["dialog-node-answers"]} ${Style["text-book"]}`}
          >
            {node.options.map((answer) => (
              <button
                key={answer.optionId}
                className={Style["dialog-node-answer-wrapper"]}
                onClick={() => handleOptionClick(answer.optionId, answer)}
              >
                <img
                  src={answer.imageUrl || DefaultTextbookP}
                  alt={answer.text}
                />
                <div
                  className={`${Style["dialog-node-answer"]} ${isSelected(answer.optionId) ? Style["dialog-node-answer-selected"] : ""}`}
                >
                  <span>{answer.text}</span>
                </div>
              </button>
            ))}
          </div>
        )}

        {/* 单选 & 多选项列表 */}
        {node.bizType !== EBizType.SUBJECT &&
          node.bizType !== EBizType.TEXTBOOK && (
            <>
              <div className={Style["dialog-node-answers"]}>
                {node.options.map((answer) => (
                  <button
                    key={answer.optionId}
                    className={`${Style["dialog-node-answer"]} ${isSelected(answer.optionId) ? Style["dialog-node-answer-selected"] : ""}`}
                    onClick={() => handleOptionClick(answer.optionId, answer)}
                  >
                    {answer.text}
                  </button>
                ))}
              </div>
              {/* 确认按钮 - 仅在多选模式下显示 */}
              {isMultiSelect && (
                <button
                  className={Style["dialog-node-confirm"]}
                  onClick={confirmSelections}
                  disabled={selectedAnswers.length === 0}
                >
                  确认
                </button>
              )}
            </>
          )}
      </div>
    </div>
  );
};

export default forwardRef<HTMLDivElement, IDialogNode>(DNode);
