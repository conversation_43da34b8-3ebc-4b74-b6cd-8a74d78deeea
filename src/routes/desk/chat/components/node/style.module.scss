@keyframes slideUpFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dialog-node-inner {
  position: relative;
  display: flex;
  margin-bottom: 22px;
  &:last-child{
    margin-bottom: 0;
  }
  // animation: slideUpFadeIn 0.5s ease-out forwards;

  .dialog-node-l {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    flex-shrink: 0;
    width: 44px;
    height: 44px;
    background-color: #9dd0fa;
    background-image: url("@/assets/chat/tree.png");
    background-size: cover;
    border-radius: 50%;
    background-repeat: no-repeat;
  }

  .dialog-node-r {
    max-width: 265px;
    .dialog-node-question {
      max-width: 265px;
      width: fit-content;
      min-height: 44px;
      background-image: linear-gradient(0deg, #e3efff 2%, #ffffff 100%);
      border: 1px solid #ffffff;
      box-shadow: 0 1px 4px 0 rgba(0, 102, 255, 0.15);
      border-radius: 2px 20px 20px 20px;
      padding: 10px 16px;
      font-weight: bold;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.85);
      background-color: #fff;
      .dialog-node-question-desc {
        padding: 8px 10px;
        font-weight: normal;
        background: rgba(136, 186, 255, 0.15);
        font-size: 12px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 18px;
        border-radius: 12px;
        margin-bottom: 10px;
      }
      &.plan {
        padding: 10px;
      }
      .retry{
        color: #2F86FF;
        cursor: pointer;
      }
      .text-card{
        margin-top: 6px;
        display: flex;
        width: 100%;
        height: 64px;
        background: rgba(136, 186, 255, 0.15);
        border-radius: 12px;
        align-items: center;
        padding: 8px 10px;
        >img{
          display: block;
          width: 77px;
          height: 48px;
          object-fit: cover;
          margin-right: 12px;
        }
      }
    }
  }
  &.answer {
    justify-content: flex-end;
    .dialog-node-l {
      background-image: url("@/assets/chat/mine.png");
      order: 1;
      margin-left: 10px;
      margin-right: 0;
      box-shadow: 0 1px 4px 0 rgba(0, 102, 255, 0.15);
    }
    .dialog-node-r {
      .dialog-node-question {
        background-image: linear-gradient(269deg, #35b6ff 0%, #1676ff 100%);
        border-radius: 20px 2px 20px 20px;
        border: none;
        color: #fff;
        .dialog-node-question-desc {
          color: #fff;
        }
      }
    }
  }
  &.loading {
    .dialog-node-question {
      color: rgba(0, 0, 0, 0.25);
      display: flex;
      align-items: center;
      .loader {
        width: 28px;
        height: 28px;
        margin-right: 8px;
      }
    }
  }
}

.dialog-node-answers {
  display: flex;
  flex-direction: column;
  margin-top: 12px;
  // animation: slideUpFadeIn 0.5s ease-out forwards;
  &.subject {
    flex-direction: row;
    flex-wrap: wrap;
    .dialog-node-answer {
      margin-bottom: 12px;
      margin-right: 12px;
      width: calc(33.333% - 12px);
      &:nth-child(3){
        margin-right: 0;
      }
    }
  }
  &.text-book {
    flex-direction: row;
    flex-wrap: wrap;
    .dialog-node-answer-wrapper {
      display: flex;
      flex-direction: column;
      position: relative;
      justify-content: flex-end;
      min-width: 106px;
      min-height: 94px;
      position: relative;
      background: transparent;
      border: none;
      cursor: pointer;
      transition: all 0.2s;
      margin-right: 12px;
      margin-bottom: 12px;
      &:nth-child(2){
        margin-right: 0;
      }
      > img {
        position: absolute;
        display: block;
        width: 80px;
        height: 48px;
        top: 0;
        left: 50%;
        z-index: 10;
        transform: translateX(-50%);
      }
    }
    .dialog-node-answer {
      width: 110px;
      height: 74px;
      font-weight: 500;
      padding: 28px 12px 0;
      font-size: 14px;
      color: #2a333a;
      line-height: 14px;
      align-items: center;
      span {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        max-height: 28px;
      }
    }
  }
}

.dialog-node-answer {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.45);
  border: 1px solid #ffffff;
  border-radius: 16px;
  padding: 5px 16px;
  font-size: 14px;
  margin-bottom: 12px;
  color: rgba(0, 0, 0, 0.85);
  cursor: pointer;
  transition: all 0.2s;
  &:last-child{
    margin-bottom: 0;
  }
  &.dialog-node-answer-selected {
    border: 1px solid #2f86ff;
    color: #2f86ff;

    &::before {
      position: absolute;
      content: "";
      display: block;
      right: -1px;
      bottom: -1px;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      height: 13px;
      width: 16px;
      background-image: url("@/assets/chat/selected.png");
    }
  }
}

.dialog-node-confirm {
  position: relative;
  display: block;
  width: 120px;
  height: 40px;
  background-image: linear-gradient(269deg, #35b6ff 0%, #1676ff 100%);
  border-radius: 20px;
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
  margin: 20px auto 0;
  border: none;

  &:hover {
    opacity: 0.9;
  }

  &:disabled {
    width: 120px;
    height: 40px;
    background: rgba(0, 55, 129, 0.15);
    border-radius: 20px;
    cursor: not-allowed;
  }
}

.dialog-node-action {
  background-image: linear-gradient(270deg, #0387ff 0%, #02aaff 100%);
  background-color: transparent;
  color: white;
  border: none;
  border-radius: 17px;
  padding: 14px 20px;
  font-size: 16px;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: all 0.2s;
  margin-top: 16px;
  width: 100%;

  &:hover {
    opacity: 0.9;
  }
}

.learning-plan-tips {
  font-weight: 400;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65);
  margin-top: 12px;
}
