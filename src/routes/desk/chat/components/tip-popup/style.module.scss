.tip-body-wrapper {
  .tip-body {
    padding: 0 20px 20px;

    .description {
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 22px;
    }

    :global {
      .adm-checkbox {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 20px;
      }
      .adm-checkbox-content {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 22px;
      }
      .adm-checkbox-icon {
        width: 16px;
        height: 16px;
        border: 1px solid rgba(0, 0, 0, 0.25);
        border-radius: 2px;
      }
      .adm-checkbox.adm-checkbox-checked .adm-checkbox-icon {
        background-color: rgba(47, 134, 255, 1) !important;
      }
    }
  }

  .footer {
    min-height: 72px;
    padding: 16px;
    display: flex;

    &.safe {
      padding-bottom: calc(env(safe-area-inset-bottom) + 16px);
      padding-bottom: calc(constant(safe-area-inset-bottom) + 16px);
    }
    .grey {
      background-color: #fff;
      margin-right: 16px;
      > div {
        color: rgba(2, 30, 102, 1);
      }
    }
    > div {
      flex: 1;
      border: 1px solid #021e66;
      box-shadow: 0 4px 0 0 rgba(2, 30, 102, 0.15);
    }
  }
}

.tip-popup-title {
  color: #333333 !important;
}
