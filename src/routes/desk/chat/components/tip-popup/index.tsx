import React, {
  useState,
  useRef,
  forwardRef,
  useImperativeHandle,
} from "react";

import { PopupProps, Checkbox } from "antd-mobile";
import { Button, EButtonType } from "@/components";

import Popup from "@/components/popup";
import Styles from "./style.module.scss";

import { setLocalStorage } from "@/utils/tool";

export enum ETipType {
  enter = 1,
  back = 2,
}

interface IBackTipPopupProps extends PopupProps {
  reset: () => void;
  checkKey: string;
}

const BackTipPopup = (props: IBackTipPopupProps, ref) => {
  const { reset, checkKey } = props;
  const [visible, setVisible] = useState(false);
  const [type, setType] = useState<ETipType>();
  const [checked, setChecked] = useState<boolean>(false);

  const onClose = () => setVisible(false);
  const hasPlanNodeRef = useRef<boolean>(false);

  useImperativeHandle(
    ref,
    () => ({
      showPopup: (type: ETipType) => {
        if (type === ETipType.back) {
          setChecked(false);
        }
        setType(type);
        setVisible(true);
      },
    }),
    [checked, visible, hasPlanNodeRef],
  );

  const TITLE_MAP = {
    [ETipType.enter]: {
      description:
        "你有一个未完成的对话，你可以继续完成这个对话，或者清除这个对话并重新开始～",
      actions: [
        {
          label: "重新开始",
          className: Styles.grey,
          type: EButtonType.grey,
          onClick: () => {
            reset();
            onClose();
          },
        },
        {
          label: "继续对话",
          type: EButtonType.Blue,
          onClick: () => {
            onClose();
          },
        },
      ],
    },
    [ETipType.back]: {
      description:
        "生成的计划不会一直存在，如果没有创建并退出的话，计划将会消失，记得去创建哦～",
      actions: [
        {
          label: "确认离开",
          className: Styles.grey,
          type: EButtonType.grey,
          onClick: () => {
            if (checked) {
              setLocalStorage(checkKey, checked);
            }
            if (mstJsBridge.isInMstApp()) {
              mstJsBridge.closeWebview();
            } else {
              history.back();
            }
            onClose();
          },
        },
        {
          label: "继续查看",
          type: EButtonType.Blue,
          onClick: () => {
            if (checked) {
              setLocalStorage(checkKey, checked);
            }
            onClose();
          },
        },
      ],
    },
  };

  return (
    <Popup
      title="提示"
      visible={visible}
      onClose={onClose}
      titleClassName={Styles["tip-popup-title"]}
    >
      {type && (
        <div className={Styles["tip-body-wrapper"]}>
          <div className={Styles["tip-body"]}>
            <span className={Styles.description}>
              {TITLE_MAP[type].description}
            </span>
            {type === ETipType.back && (
              <Checkbox
                checked={checked}
                onChange={(e) => {
                  setChecked(e);
                }}
              >
                不再提示
              </Checkbox>
            )}
          </div>
          <div className={`${Styles["footer"]} ${Styles["safe"]}`}>
            {TITLE_MAP[type].actions?.map((v, i) => (
              <Button
                key={i}
                className={v.className}
                text={v.label}
                onClick={v.onClick}
                type={v.type}
              />
            ))}
          </div>
        </div>
      )}
    </Popup>
  );
};

BackTipPopup.displayName = "BackTipPopup";

export default forwardRef<HTMLDivElement, IBackTipPopupProps>(BackTipPopup);
