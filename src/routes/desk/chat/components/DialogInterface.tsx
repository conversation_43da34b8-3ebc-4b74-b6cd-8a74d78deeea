import React, { useRef, useEffect } from "react";

import Node from "./node";
import { useDialog } from "../context/useDialog";
import { SCROLL_TO_BOTTOM_EVENT } from "../context/useDialog";
import Style from "../style.module.scss";
import { EBizType, ENodeType, Options } from "../types";
import { openWebView, setLocalStorage, clickPv } from "@/utils/tool";
import { postShowDetail } from "../iframe-utils";
interface IDialogInterfaceProps {
  onReloadScrollBottom: () => void;
  onScrollBottom: () => void;
}

export const LOCAL_KEY = "chat-current-plan-description";

const DialogInterface: React.FC<IDialogInterfaceProps> = (
  props: IDialogInterfaceProps,
) => {
  const { onReloadScrollBottom, onScrollBottom } = props;
  const {
    history,
    currentNode,
    isIframeMode,
    restartDialog,
    reSelectDialog,
    switchTextBook,
  } = useDialog();
  const mainNodeRef = useRef(null);

  const currentNodeRef = useRef(null);

  useEffect(() => {
    onScrollBottom();
    const currentNodeDom = currentNodeRef.current;
    // const lastHistoryNode = historyNodeRef?.current?.lastElementChild;
    // if (lastHistoryNode && history?.length > 1) {
    //   lastHistoryNode?.classList?.add?.(Style["fade-up"]);
    //   lastHistoryNode.style = `transition: none`;
    //   if (lastHistoryNode?.classList?.contains(Style["active"])) {
    //     lastHistoryNode?.classList?.remove?.(Style["active"]);
    //   }
    //   setTimeout(() => {
    //     lastHistoryNode.style = ``;
    //     lastHistoryNode?.classList?.add?.(Style["active"]);
    //   }, 100);
    // }
    if (currentNodeRef.current) {
      currentNodeDom.classList.add(Style["fade-up"]);
      currentNodeDom.style = `transition: none`;
      if (currentNodeDom.classList.contains(Style["active"])) {
        currentNodeDom.classList.remove(Style["active"]);
      }
      setTimeout(() => {
        currentNodeDom.style = ``;
        currentNodeDom.classList.add(Style["active"]);
      }, 200);
    }
  }, [history, currentNode]);

  useEffect(() => {
    const handleScrollToBottom = () => {
      onScrollBottom();
    };

    document.addEventListener(SCROLL_TO_BOTTOM_EVENT, handleScrollToBottom);

    return () => {
      document.removeEventListener(
        SCROLL_TO_BOTTOM_EVENT,
        handleScrollToBottom,
      );
    };
  }, []);

  /** uniKey */
  const historyList = history?.map((v, i) => ({
    ...v,
    uniKey: `${v.nodeId}-${i}`,
  }));

  const getRetry = (v) => {
    const [lastHistoryNode] = historyList
      .filter((v) => !(v.answer as Options)?.selected)
      ?.slice(-1);
    if (currentNode?.nodeType === ENodeType.LOADING) {
      return false;
    }
    if (lastHistoryNode?.uniKey === v.uniKey) {
      return true;
    }
  };

  return (
    <div className={Style["dialog-interface"]}>
      <div className={Style["dialog-interface-main"]} ref={mainNodeRef}>
        {(historyList || []).map((v, i) => (
          <div className={Style["dialog-item"]} key={i}>
            <Node
              node={{
                ...v,
                nodeType: ENodeType.TEXT,
              }}
              renderExtra={() => {
                if (
                  v.bizType === EBizType.PLAN &&
                  i === historyList.length - 1 &&
                  !currentNode
                ) {
                  return (
                    <div className={Style["dialog-restart-wrapper"]}>
                      <button
                        className={Style["dialog-restart"]}
                        onClick={() => {
                          restartDialog();
                          clickPv(
                            "ewt_h5_base_plan_desk_chat_answer_restart_click",
                          );
                        }}
                      >
                        继续聊聊
                      </button>
                    </div>
                  );
                }
                const TextBookHasAutoSelect =
                  v.bizType === EBizType.TEXTBOOK &&
                  v.options?.find((v) => v.selected);
                if (
                  TextBookHasAutoSelect &&
                  v.options.length > 1 &&
                  i === historyList.length - 1
                ) {
                  return (
                    <div className={Style["dialog-switch-book-wrapper"]}>
                      <button
                        className={Style["dialog-switch"]}
                        onClick={() => {
                          switchTextBook(v, i);
                          onReloadScrollBottom();
                        }}
                      >
                        切换其他教材
                      </button>
                      <div className={Style["dialog-switch-tips"]}>
                        切换后其他页面的教材版本也将同步更改
                      </div>
                    </div>
                  );
                }
                return null;
              }}
              onPlanClick={(plan) => {
                setLocalStorage(LOCAL_KEY, plan.desc);
                clickPv("ewt_h5_base_plan_desk_chat_plan_click", {
                  subject: plan.subjectName,
                });
                if (isIframeMode) {
                  postShowDetail(plan.diagnosticTemplateId, plan.desc);
                } else {
                  const url = `${location.origin}/ewtcustomerh5/desk/plan-detail?diagnosticTemplatePlanId=${plan.diagnosticTemplateId}&showTopBar=false`;
                  openWebView(url);
                }
              }}
            />
            {v.answerText && (
              <Node
                node={{
                  answerText: v.answerText,
                  nodeType: ENodeType.TEXT,
                }}
                type="answer"
                renderExtra={() => {
                  const showRetry = getRetry(v);
                  if (showRetry) {
                    return (
                      <div className={Style["dialog-retry-wrapper"]}>
                        <button
                          className={Style["dialog-retry"]}
                          onClick={() => {
                            reSelectDialog(v, i);
                            onReloadScrollBottom?.();
                            clickPv(
                              "ewt_h5_base_plan_desk_chat_answer_reselect_click",
                              {
                                questionText: v.questionText,
                                answerText: v.answerText,
                              },
                            );
                          }}
                        >
                          重选
                        </button>
                      </div>
                    );
                  }
                  return null;
                }}
              />
            )}
          </div>
        ))}
        {currentNode && <Node ref={currentNodeRef} node={currentNode} />}
      </div>
    </div>
  );
};

export default DialogInterface;
