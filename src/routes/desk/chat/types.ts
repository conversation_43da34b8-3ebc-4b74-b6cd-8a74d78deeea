/**
 * 对话系统类型定义
 */

export enum EAnswerType {
  AUTO_COMPLETE = 0,
  SINGLE_CHOICE = 1,
  MULTIPLE_CHOICE = 2,
}

export enum EBizType {
  SUBJECT = 1,
  TEXTBOOK = 2,
  PLAN = 3,
  NODE = 0,
}

export enum ENodeType {
  SYSTEM = 1,
  TEXT = 2,
  LOADING = 3,
  ERROR = 4,
}
export interface Options {
  executionOrder?: number;
  /** 答案id */
  optionId?: string;
  /** 图片地址 */
  imageUrl?: string;
  /** 答案文本 */
  text?: string;
  /** 答案对应的下一个节点id */
  nextNodeId?: string;
  /** 是否是结束节点， 如果是 nextNodeId为null */
  branchEnd?: boolean;
  /** 触发的类型 1 书桌推荐计划 */
  triggerType?: number;
  /** 触发的类型的内容id，这里是计划id */
  triggerContentId?: string;
  /** 是否选中，目前只在教材版本这里有 其他为null */
  selected?: boolean;
  /** 互斥选项，如果互斥，选择这个，其他选项不能选中 */
  mutuallyExclusive?: boolean;
}

export interface INode {
  /** 节点的唯一标识符 */
  nodeId?: string;
  /** 呈现给用户的问题文本 */
  questionText?: string;
  /** 问题的描述 */
  questionDescription?: string;
  /** 期望的答案类型，  0 自动完成 1 单选；2 多选 */
  answerType?: EAnswerType;
  /** 答案列表 */
  options?: Options[];
  /** 若为true，则多选答案将导致设定分支探索，false默认按用户选择顺序 */
  executeByConfigOrder?: boolean;
  /** 默认下一个节点id */
  nextNodeId?: string;
  /** 1 学科 2 教材  3计划卡片 0 节点 */
  bizType?: EBizType;
  /** 节点 父id， 0表明是根节点 */
  pid?: string;
  /** 最多选项数量 */
  maxSelectNum?: number;
  /** 回答内容 */
  answerText?: string;
}

// 定义对话节点的类型
export interface DialogNode extends INode {
  defaultNextNodeId?: string | null; // 新增：用于 auto_complete 类型的默认下一节点
  default_plan_trigger?: string | null; // 新增：用于 auto_complete 类型的默认计划触发器
  execute_by_config_order?: boolean;
  is_branch_end?: boolean;
  multiSelected?: any[];
  /** 节点类型 1 为系统节点 2 为纯文本节点 3 loading节点 4 异常节点 */
  nodeType?: ENodeType;
  answerId?: string;
  planData?: any[]; // 仅当 type 为 'learning_plan' 时使用
  retry?: (p: any) => void; // 错误状态下重试
  answer?: Options | Options[] | null; // 对于 learning_plan 类型，可以为 null
}

// 定义多选分支的类型
export interface SelectionBranch {
  answerId?: string;
  nextNodeId?: string;
  triggerContentId?: string | null;
}
