import React, {
  createContext,
  useState,
  useContext,
  useEffect,
  useRef,
  useCallback,
} from "react";
import dayjs from "dayjs";

import { postSynthesisPlan } from "@/service/chat";
import {
  DialogNode,
  Options,
  SelectionBranch,
  ENodeType,
  EAnswerType,
  EBizType,
} from "../types";
import {
  getLocalStorageToValue,
  setLocalStorage,
  findLastIndex,
  findLast,
  expPv,
} from "@/utils/tool";

import deepClone from "lodash/cloneDeep";
import Logger from "@/utils/safe-logger";
import { checkNeedLogin } from "../iframe-utils";

const SafeLogger = new Logger("desk-chat");

// 定义滚动事件的名称
export const SCROLL_TO_BOTTOM_EVENT = "scrollToBottom";

/**
 * @interface DialogContextType
 * @description 对话上下文的状态和操作方法定义
 * @property {DialogNode | null} currentNode - 当前显示的对话节点
 * @property {DialogNode[]} history - 用户的对话历史记录
 * @property {string[]} learningPlan - 收集到的学习计划触发器列表
 * @property {string[]} selectedAnswers - 在多选模式下，当前用户勾选的答案ID列表
 * @property {SelectionBranch[][]} multiSelectBranchStack - 管理嵌套多选分支的栈，每个元素是当前层级的待处理分支数组
 * @property {number[]} currentBranchIndices - 对应 multiSelectBranchStack 中每个层级当前已处理到的分支索引的栈
 * @property {string[]} pendingPlanTriggers - 在对话流程结束前，临时收集的所有计划触发器
 * @property {(answerId: string) => void} handleSelection - 处理单选答案选择的函数
 * @property {(answerId: string) => void} handleMultiSelect - 处理多选答案勾选/取消勾选的函数
 * @property {() => void} confirmSelections - 用户在多选模式下确认选择后调用的函数
 * @property {() => void} resetDialog - 重置整个对话状态到初始状态的函数
 */
interface DialogContextType {
  currentNode: DialogNode | null;
  history: DialogNode[];
  selectedAnswers: string[];
  multiSelectBranchStack: SelectionBranch[][];
  currentBranchIndices: number[];
  pendingPlanTriggers: string[];
  isIframeMode: boolean;
  setCurrentNode: (node: DialogNode | null) => void;
  handleSelection: (answerId: string) => void;
  handleMultiSelect: (answerId: string) => void;
  confirmSelections: () => void;
  restartDialog: () => void;
  resetDialog: () => void;
  reSelectDialog: (node, i) => void;
  switchTextBook: (node, i) => void;
}

// 创建 context 并提供默认值
export const DialogContext = createContext<DialogContextType>({
  currentNode: null,
  setCurrentNode: (v) => {},
  history: [],
  selectedAnswers: [],
  multiSelectBranchStack: [],
  currentBranchIndices: [],
  pendingPlanTriggers: [],
  isIframeMode: false,
  handleSelection: () => {},
  handleMultiSelect: () => {},
  confirmSelections: () => {},
  restartDialog: () => {},
  resetDialog: () => {},
  reSelectDialog: (node, i) => {},
  switchTextBook: (node, i) => {},
});

/**
 * @hook useDialog
 * @description 自定义 Hook，用于方便地访问对话上下文。
 * @returns {DialogContextType} 对话上下文对象
 */
export const useDialog = () => useContext(DialogContext);

interface IDialogProviderProps {
  nodes: DialogNode[];
  children?: React.ReactNode;
  run: any;
  historyKey: string;
  isIframeMode: boolean;
}

/**
 * @component DialogProvider
 * @description 对话状态管理和逻辑处理的核心组件。
 *              使用 React Context API 向其子组件提供对话状态和操作方法。
 * @param {object} props - 组件属性
 * @param {React.ReactNode} props.children - 子组件
 */
export const useDialogProvider = (props: IDialogProviderProps) => {
  const { nodes, run, historyKey, isIframeMode } = props;
  const { questionNodeRun, updateUserEditionRun, getStartNodeRun } = run || {};
  const nodesRef = useRef(nodes);
  const historyRef = useRef([]);
  const [currentNode, setCurrentNode] = useState<DialogNode | null>(null);
  const [history, setHistory] = useState<DialogNode[]>([]);
  const [planLoading, setPlanLoading] = useState<boolean>(false);

  const [selectedAnswers, setSelectedAnswers] = useState<string[]>([]);
  const [multiSelectBranchStack, setMultiSelectBranchStack] = useState<
    SelectionBranch[][]
  >([]);
  const [currentBranchIndices, setCurrentBranchIndices] = useState<number[]>(
    [],
  );
  /** 重选的缓存栈 */
  const [reselectBranchStack, setReselectBranchStack] = useState<
    SelectionBranch[][]
  >([]);
  const [reCurrentBranchIndices, setReCurrentBranchIndices] = useState<
    number[]
  >([]);
  const [pendingPlanTriggers, setPendingPlanTriggers] = useState<string[]>([]);
  const [shouldFinalizePlans, setShouldFinalizePlans] = useState(false);

  // Refs 用于在回调函数中保存状态的当前值，避免因闭包问题导致回调函数重触发
  const currentNodeRef = useRef<DialogNode | null>(currentNode);
  // initialize
  const isInitializeRef = useRef<boolean>(false);

  useEffect(() => {
    currentNodeRef.current = currentNode;
  }, [currentNode]);

  useEffect(() => {
    nodesRef.current = nodes;
  }, [nodes]);
  const multiSelectBranchStackRef = useRef<SelectionBranch[][]>(
    multiSelectBranchStack,
  );
  useEffect(() => {
    multiSelectBranchStackRef.current = multiSelectBranchStack;
  }, [multiSelectBranchStack]);

  const currentBranchIndicesRef = useRef<number[]>(currentBranchIndices);
  useEffect(() => {
    currentBranchIndicesRef.current = currentBranchIndices;
  }, [currentBranchIndices]);

  const reCurrentBranchIndicesRef = useRef<number[]>(reCurrentBranchIndices);
  useEffect(() => {
    reCurrentBranchIndicesRef.current = reCurrentBranchIndices;
  }, [reCurrentBranchIndices]);

  const reselectBranchStackRef =
    useRef<SelectionBranch[][]>(reselectBranchStack);
  useEffect(() => {
    reselectBranchStackRef.current = reselectBranchStack;
  }, [reselectBranchStack]);

  const pendingPlanTriggersRef = useRef(pendingPlanTriggers);
  useEffect(() => {
    pendingPlanTriggersRef.current = pendingPlanTriggers;
  }, [pendingPlanTriggers]);

  useEffect(() => {
    historyRef.current = history;
  }, [history]);

  /**
   * @function findNodeById
   * @description 根据节点ID从对话数据中查找节点。
   * @param {string} nodeId - 要查找的节点ID。
   * @returns {DialogNode | null} 找到的节点对象，如果未找到则返回 null。
   */
  const findNodeById = useCallback(
    (nodeId: string): DialogNode | null => {
      return nodesRef.current.find((node) => node.nodeId === nodeId) || null;
    },
    [nodesRef],
  );

  /**
   * @function handlePlanTrigger
   * @description 处理并收集学习计划触发器。
   *              将触发器添加至 pendingPlanTriggers 列表，确保不重复添加。
   * @param {string | null} trigger - 计划触发器的标识符。
   */
  const handlePlanTrigger = useCallback((trigger: string | null) => {
    if (!trigger) return;
    setPendingPlanTriggers((prev) => {
      if (!prev.includes(trigger)) {
        return [...prev, trigger];
      }
      return prev;
    });
    setHistoryStateAndLocal(historyRef.current, {
      pendingPlanTriggers: [...pendingPlanTriggersRef.current, trigger],
      onlyLocal: true,
    });
  }, []);

  const proceedToNextBranchOrFinalize = useCallback((type?: string) => {
    if (multiSelectBranchStackRef.current.length === 0) {
      setShouldFinalizePlans(true);
      return;
    }

    let newStack = [...multiSelectBranchStackRef.current];
    let newIndices = [...currentBranchIndicesRef.current];

    while (newStack.length > 0) {
      const currentLevelBranches = newStack[newStack.length - 1];
      const currentLevelProcessedIndex = newIndices[newIndices.length - 1];
      const nextBranchIndexToTry =
        type === "init"
          ? currentLevelProcessedIndex
          : currentLevelProcessedIndex + 1;

      if (nextBranchIndexToTry < currentLevelBranches.length) {
        newIndices[newIndices.length - 1] = nextBranchIndexToTry;
        setHistoryStateAndLocal(historyRef.current, {
          multiSelectBranchStack: newStack,
          currentBranchIndices: newIndices,
          onlyLocal: true,
        });
        setMultiSelectBranchStack(newStack);
        setCurrentBranchIndices(newIndices);

        const nextBranch = currentLevelBranches[nextBranchIndexToTry];
        // navigateToNodeAndHandleBranchTrigger 在下方定义，并通过自身的 useCallback 保证其稳定性。
        // 此处依赖 navigateToNodeAndHandleBranchTrigger 是经过 memoized 的。
        // 为明确处理ESLint关于循环依赖的潜在警告，如果出现问题，可能需要通过ref传递或获取此函数。
        // 目前直接调用，假设 navigateToNodeAndHandleBranchTrigger 已被 memoized。
        navigateToNodeAndHandleBranchTrigger(
          nextBranch.nextNodeId,
          nextBranch.triggerContentId,
        );
        return;
      } else {
        const popReselectBranchStack = newStack.pop();
        const popReCurrentBranchIndices = newIndices.pop();
        setHistoryStateAndLocal(historyRef.current, {
          multiSelectBranchStack: newStack,
          currentBranchIndices: newIndices,
          reselectBranchStack: [popReselectBranchStack],
          reCurrentBranchIndices: [popReCurrentBranchIndices],
          onlyLocal: true,
        });
        setReselectBranchStack([popReselectBranchStack]);
        setReCurrentBranchIndices([popReCurrentBranchIndices]);
      }
    }
    setMultiSelectBranchStack([]);
    setCurrentBranchIndices([]);
    setShouldFinalizePlans(true);
  }, []);

  /**
   * @function setHistoryStateAndLocal
   * @description 设置历史记录状态并同步到本地存储。
   * @param {DialogNode[]} historyList
   */

  const setHistoryStateAndLocal = (historyList: DialogNode[], options?) => {
    const { onlyLocal } = options || {};
    const localCache = getLocalStorageToValue(historyKey) || {};
    const hasPlanNode = historyList?.find((v) => v.bizType === EBizType.PLAN);
    if (!onlyLocal) {
      setHistory(historyList);
    }
    /** 存历史对话 */
    if (!hasPlanNode) {
      !isIframeMode &&
        setLocalStorage(historyKey, {
          history: historyList,
          multiSelectBranchStack:
            options?.multiSelectBranchStack ||
            localCache.multiSelectBranchStack,
          currentBranchIndices:
            options?.currentBranchIndices || localCache.currentBranchIndices,
          pendingPlanTriggers:
            options?.pendingPlanTriggers || localCache.pendingPlanTriggers,
          reselectBranchStack:
            options?.reselectBranchStack || localCache.reselectBranchStack,
          reCurrentBranchIndices:
            options?.reCurrentBranchIndices ||
            localCache.reCurrentBranchIndices,
          expire: dayjs().add(30, "day").valueOf(),
        });
    } else {
      const planIndex = findLastIndex(
        historyList,
        (v) => v.bizType === EBizType.PLAN,
      );
      const sliceHistory = historyList.slice(planIndex + 1);
      !isIframeMode &&
        setLocalStorage(historyKey, {
          history: sliceHistory,
          multiSelectBranchStack:
            options?.multiSelectBranchStack ||
            localCache.multiSelectBranchStack,
          currentBranchIndices:
            options?.currentBranchIndices || localCache.currentBranchIndices,
          pendingPlanTriggers:
            options?.pendingPlanTriggers || localCache.pendingPlanTriggers,
          reselectBranchStack:
            options?.reselectBranchStack || localCache.reselectBranchStack,
          reCurrentBranchIndices:
            options?.reCurrentBranchIndices ||
            localCache.reCurrentBranchIndices,
          expire: dayjs().add(30, "day").valueOf(),
        });
    }
  };

  /**
   * @function navigateToNode
   * @description 导航到指定的对话节点。如果节点ID无效或为空，则尝试处理下一分支或结束对话。
   * @param {string | null | undefined} nodeId - 目标节点的ID。
   */
  const navigateToNode = useCallback(
    (nodeId: string | null | undefined) => {
      if (!nodeId) {
        proceedToNextBranchOrFinalize();
        return;
      }
      const node = findNodeById(nodeId);
      if (node) {
        setCurrentNode(node);
      } else {
        console.warn(`无效的 next_node_id '${nodeId}'。尝试继续或结束流程。`);
        proceedToNextBranchOrFinalize();
      }
    },
    [findNodeById, proceedToNextBranchOrFinalize],
  );

  /**
   * @function navigateToNodeAndHandleBranchTrigger
   * @description 导航到指定节点，并在导航前处理该分支可能带有的计划触发器。
   * @param {string | null | undefined} nodeId - 目标节点的ID。
   * @param {string | null} [branchTrigger] - (可选) 该分支的计划触发器。
   */
  const navigateToNodeAndHandleBranchTrigger = useCallback(
    (nodeId: string | null | undefined, branchTrigger?: string | null) => {
      if (branchTrigger) {
        handlePlanTrigger(branchTrigger);
      }
      navigateToNode(nodeId);
    },
    [handlePlanTrigger, navigateToNode],
  );

  /**
   * @function processDialogTransition
   * @description 根据用户选择的答案，处理对话的转换逻辑。
   *              如果答案指向下一个节点，则导航到该节点。
   *              如果当前路径结束（无下一节点或当前节点是分支末端），则尝试进入多选栈的下一分支或结束对话。
   * @param {Options} selectedAnswer - 用户选择的答案对象。
   */
  const processDialogTransition = useCallback(
    (selectedAnswer: Options) => {
      const hasNextNodeId = !!selectedAnswer.nextNodeId;
      if (!hasNextNodeId) {
        proceedToNextBranchOrFinalize();
      } else {
        navigateToNode(selectedAnswer.nextNodeId);
      }
    },
    [navigateToNode, proceedToNextBranchOrFinalize],
  ); // currentNodeRef is a stable ref object

  /**
   * @function finalizeDialogFlow
   * @description 最终确定对话流程。将所有待处理的计划触发器合并到最终的学习计划中，
   *              并清空多选分支栈、索引、待处理触发器等临时状态。
   */
  const finalizeDialogFlow = useCallback(async () => {
    const finalizedLearningPlan = [
      ...new Set([...pendingPlanTriggersRef.current]),
    ];

    if (finalizedLearningPlan.length > 0) {
      if (planLoading) return;
      setCurrentNode({
        questionText: "正在思考中...",
        nodeType: ENodeType.LOADING,
      });
      setPlanLoading(true);
      try {
        const { data, success } = await postSynthesisPlan({
          templatePlanList: finalizedLearningPlan,
        }).catch((e) => checkNeedLogin(e, isIframeMode));
        setPlanLoading(false);
        if (success) {
          const bookName =
            findLast(history, (v) => v.bizType === EBizType.TEXTBOOK)?.answer
              ?.text || "";
          const subjectName =
            findLast(history, (v) => v.bizType === EBizType.SUBJECT)?.answer
              ?.text || "";

          const learningPlanHistoryItem: DialogNode = {
            questionDescription: `根据你的选择，我们以${subjectName}${bookName}作为教材版本，为你生成了一份专属学习计划～帮助你梳理知识内容，构建知识体系～`,
            nodeId: "SYSTEM_LEARNING_PLAN",
            questionText: "点击卡片，去创建你的专属学习计划",
            answer: null,
            bizType: EBizType.PLAN,
            answerId: `lp_${Date.now()}`,
            planData: [
              {
                ...data,
                desc: `这份量身定制的${subjectName}学习计划，将基于${bookName}教材体系，为你打造专属的知识成长地图。我们将从你的实际学习需求出发，系统梳理${subjectName}的核心内容，帮你建立清晰的${subjectName}知识框架，让${subjectName}学习变得更有逻辑、更高效。`,
              },
            ],
          };
          setTimeout(() => {
            setCurrentNode(null);
            setHistoryStateAndLocal([...history, learningPlanHistoryItem], {
              multiSelectBranchStack: [],
              currentBranchIndices: [],
              pendingPlanTriggers: [],
              reselectBranchStack: [],
              reCurrentBranchIndices: [],
            });
            expPv("ewt_h5_base_plan_desk_chat_plan_expo", {
              subject: data.subjectName,
            });
            setPendingPlanTriggers([]); // 清空待处理的触发器
          }, 3000);
          setMultiSelectBranchStack([]);
          setCurrentBranchIndices([]);
          setShouldFinalizePlans(false);
        } else {
          SafeLogger.error("post-synthesisPlan-error", {
            reason: "合成计划生成错误",
            data,
          });
          setCurrentNode({
            questionText: "系统开小差了，",
            nodeType: ENodeType.ERROR,
            retry: finalizeDialogFlow,
          });
          setMultiSelectBranchStack([]);
          setCurrentBranchIndices([]);
          setShouldFinalizePlans(false);
        }
      } catch (error) {
        setPlanLoading(false);
        SafeLogger.error("post-synthesisPlan-error", {
          reason: "合成计划接口异常错误",
          error,
          message: error?.message,
        });
        setCurrentNode({
          questionText: "系统开小差了，",
          nodeType: ENodeType.ERROR,
          retry: finalizeDialogFlow,
        });
        setMultiSelectBranchStack([]);
        setCurrentBranchIndices([]);
        setShouldFinalizePlans(false);
      }
    } else {
      setCurrentNode(null);
      setPendingPlanTriggers([]); // 清空待处理的触发器
      setMultiSelectBranchStack([]);
      setCurrentBranchIndices([]);
      setShouldFinalizePlans(false);
    }
  }, [
    history,
    setCurrentNode,
    setHistoryStateAndLocal,
    setPendingPlanTriggers,
    setMultiSelectBranchStack,
    setCurrentBranchIndices,
    setSelectedAnswers,
    setShouldFinalizePlans,
  ]);

  const restartDialog = useCallback(async () => {
    setCurrentNode({
      questionText: "加载中...",
      nodeType: ENodeType.LOADING,
    });
    try {
      const res = await getStartNodeRun();
      const { success } = res || {};
      const nextDialogStartNode = nodes?.find(
        (v) => v.bizType === EBizType.SUBJECT,
      );
      if (nextDialogStartNode) {
        setCurrentNode(nextDialogStartNode);
        setShouldFinalizePlans(false);
      } else {
        console.warn(
          "无法找到ID为 'end_subject_entry' 的节点，对话将在此结束。",
        );
        setCurrentNode(null);
        setShouldFinalizePlans(false);
      }
      if (!success) {
        setCurrentNode({
          questionText: "系统开小差了，",
          nodeType: ENodeType.ERROR,
          retry: restartDialog,
        });
        SafeLogger.error("get-start-node", {
          reason: "获取开始节点错误",
          res,
        });
        throw new Error("获取开始节点异常");
      }
    } catch (error) {
      setCurrentNode({
        questionText: "系统开小差了，",
        nodeType: ENodeType.ERROR,
        retry: restartDialog,
      });
      SafeLogger.error("get-start-node", {
        reason: "获取开始节点接口异常",
        error,
        message: error?.message,
      });
      throw new Error("获取开始节点异常");
    }
  }, [setCurrentNode, nodes]);
  // --- 生命周期 Effect --- //

  /**
   * Effect: 初始化对话，加载 dialogData 中的第一个节点。
   */

  const fetchQuestionNodeRun = async (nodeId, onError) => {
    setCurrentNode({
      questionText: "加载中...",
      nodeType: ENodeType.LOADING,
    });
    try {
      const res = await questionNodeRun({
        nodeId,
      });
      const { success, data } = res || {};
      if (!success) {
        setCurrentNode({
          questionText: "系统开小差了，",
          nodeType: ENodeType.ERROR,
          retry: onError,
        });
        isInitializeRef.current = true;
        SafeLogger.error("question-node", {
          reason: "获取教材版本节点异常",
          res,
        });
        throw new Error("获取教材版本节点");
      }
      return data;
    } catch (error) {
      isInitializeRef.current = true;
      setCurrentNode({
        questionText: "系统开小差了，",
        nodeType: ENodeType.ERROR,
        retry: onError,
      });
      SafeLogger.error("question-node", {
        reason: "获取教材版本接口异常",
        error,
        message: error?.message,
      });
      throw new Error("获取教材版本节点异常");
    }
  };

  const historyInit = async () => {
    const localCache = getLocalStorageToValue(historyKey) || {};
    const textBookNodeId = localCache.history?.find(
      (v) => v.bizType === EBizType.TEXTBOOK,
    )?.answer?.nextNodeId;
    setHistory(localCache.history);
    setPendingPlanTriggers(localCache.pendingPlanTriggers || []);
    setMultiSelectBranchStack(localCache.multiSelectBranchStack || []);
    setCurrentBranchIndices(localCache.currentBranchIndices || []);
    setReselectBranchStack(localCache.reselectBranchStack || []);
    setReCurrentBranchIndices(localCache.reCurrentBranchIndices || []);

    const lastNode = localCache.history[localCache.history.length - 1];
    if (nodesRef.current.length > 0) {
      isInitializeRef.current = true;
      if (textBookNodeId) {
        await fetchQuestionNodeRun(textBookNodeId, historyInit);
      }
      setTimeout(() => {
        if (localCache.multiSelectBranchStack?.length > 0) {
          try {
            proceedToNextBranchOrFinalize("init");
          } catch (error) {
            SafeLogger.error("storage-cache-error", {
              reason: "缓存初始化异常",
              error,
              localCache,
              message: error?.message,
              stack: error?.stack,
            });
            setCurrentNode({
              questionText: "系统开小差了，",
              nodeType: ENodeType.ERROR,
              retry: () => {
                window.localStorage.removeItem(historyKey);
                window.location.reload();
              },
            });
          }
          return;
        }
        navigateToNode(lastNode?.answer?.nextNodeId);
      }, 500);
    }
  };
  useEffect(() => {
    if (isInitializeRef.current) return;
    const localCache = getLocalStorageToValue(historyKey) || {};
    const diffD = dayjs(localCache.expire).diff(dayjs(), "day");
    /** 存在缓存且缓存未过期 */
    if (localCache?.history?.length > 0 && diffD) {
      try {
        historyInit();
      } catch (error) {
        SafeLogger.error("storage-cache-error", {
          reason: "缓存初始化异常",
          localCache,
          error,
          message: error?.message,
          stack: error?.stack,
        });
        setCurrentNode({
          questionText: "系统开小差了，",
          nodeType: ENodeType.ERROR,
          retry: () => {
            window.localStorage.removeItem(historyKey);
            window.location.reload();
          },
        });
      }
      return;
    }
    if (nodesRef.current.length > 0) {
      const initialNode = nodesRef.current?.find(
        (v) => v.bizType === EBizType.SUBJECT,
      );
      if (initialNode) {
        isInitializeRef.current = true;
        setCurrentNode(initialNode as DialogNode);
      }
    }
  }, [nodesRef.current, isInitializeRef.current]);

  /**
   * Effect: 当 `shouldFinalizePlans` 为 true 时，最终确定对话流程。
   *         这是一个集中调用 `finalizeDialogFlow` 的点。
   */
  useEffect(() => {
    if (shouldFinalizePlans) {
      finalizeDialogFlow();
    }
  }, [shouldFinalizePlans, finalizeDialogFlow]);

  /**
   * Effect: 自动处理 'auto_complete' 类型的节点。
   *         如果当前节点是 'auto_complete' 且有有效答案，则在短暂延迟后模拟选择。
   */
  useEffect(() => {
    autoSelectBookText();
  }, [currentNode, setHistory, navigateToNodeAndHandleBranchTrigger]); // 更新依赖数组，移除 processDialogTransition

  const autoSelectBookText = async (curr?: DialogNode) => {
    /** 教材版本已选自动导航到下一步 */
    if (currentNode?.bizType === EBizType.TEXTBOOK) {
      const autoSelectNode = currentNode.options?.find((v) => v.selected);
      if (autoSelectNode) {
        const currentN = deepClone(curr || currentNodeRef.current);
        await fetchQuestionNodeRun(autoSelectNode.nextNodeId, () =>
          autoSelectBookText(currentN),
        );
        setTimeout(() => {
          setHistoryStateAndLocal([
            ...history,
            {
              ...currentN,
              nodeId: currentN!.nodeId,
              question: currentN!.questionText,
              answer: autoSelectNode,
              answerId: autoSelectNode.optionId,
            },
          ]);
          document.dispatchEvent(new Event(SCROLL_TO_BOTTOM_EVENT));
          navigateToNodeAndHandleBranchTrigger(
            autoSelectNode.nextNodeId,
            autoSelectNode.triggerContentId,
          );
        }, 500);
      }
    }
  };

  /**
   * @function handleSelection
   * @description 处理用户单选一个答案的逻辑。
   *              更新历史记录，派发滚动事件，处理计划触发器，并转换到下一个对话状态。
   * @param {string} answerId - 用户选择的答案的ID。
   */
  const handleSelection = useCallback(
    async (answerId: string, current?: DialogNode) => {
      const currentN = current || currentNodeRef.current;
      if (!currentN) return;

      const selectedAnswer = currentN.options.find(
        (ans) => ans.optionId === answerId,
      );
      if (!selectedAnswer) {
        console.warn(
          `在当前节点 '${currentN.nodeId}' 中未找到选定的 answerId '${answerId}'。`,
        );
        return;
      }

      if (currentNode?.bizType === EBizType.TEXTBOOK) {
        const autoSelectNode = currentNode.options?.find((v) => v.selected);
        if (autoSelectNode) {
          return;
        }
      }

      /** 教材版本 触发获取节点和更新教材版本 */
      if (currentN.bizType === EBizType.TEXTBOOK) {
        await fetchQuestionNodeRun(selectedAnswer.nextNodeId, () =>
          handleSelection(answerId, currentN),
        );
        try {
          await updateUserEditionRun({
            optionId: answerId,
          });
        } catch (error) {}
      }

      document.dispatchEvent(new Event(SCROLL_TO_BOTTOM_EVENT));

      if (selectedAnswer.triggerContentId) {
        handlePlanTrigger(selectedAnswer.triggerContentId);
      }
      processDialogTransition(selectedAnswer);
      setHistoryStateAndLocal([
        ...historyRef.current,
        {
          ...currentN,
          nodeId: currentN!.nodeId,
          question: currentN!.questionText,
          answerText: selectedAnswer.text,
          answer: selectedAnswer,
          answerId: selectedAnswer.optionId,
        },
      ]);
    },
    [
      handlePlanTrigger,
      processDialogTransition,
      setHistory,
      setHistoryStateAndLocal,
    ],
  );

  /**
   * @function handleMutuallyExclusiveSelection
   * @description (内部纯函数) 处理多选答案中的互斥逻辑。
   *              如果选择了一个互斥项，则取消其他所有项。
   *              如果选择了一个非互斥项，且当前已选中有互斥项，则取消互斥项并选中该非互斥项。
   * @param {DialogNode} currentNodeForSelection - 当前进行选择操作的节点。
   * @param {string} toggledAnswerId - 用户本次点击切换的答案ID。
   * @param {string[]} currentSelections - 在本次切换前已选中的答案ID列表。
   * @returns {string[]} 更新后的选中答案ID列表。
   */
  const handleMutuallyExclusiveSelection = useCallback(
    (
      currentNodeForSelection: DialogNode,
      toggledAnswerId: string,
      currentSelections: string[],
    ): string[] => {
      const toggledAnswer = currentNodeForSelection.options.find(
        (ans) => ans.optionId === toggledAnswerId,
      );
      if (!toggledAnswer) return currentSelections;

      const isToggledAnswerME = toggledAnswer.mutuallyExclusive === true;

      if (isToggledAnswerME) {
        // 如果切换的答案是互斥的：
        // - 如果它已经被选中，则取消选中它（有效地清除了互斥选项的选择）。
        // - 如果它未被选中，则选中它（它将成为唯一的选择）。
        return currentSelections.includes(toggledAnswerId)
          ? []
          : [toggledAnswerId];
      } else {
        // 如果切换的答案不是互斥的：
        // 检查当前是否有任何已选中的答案是互斥的。
        const anMEIsSelected = currentSelections.some((id) => {
          if (id === toggledAnswerId) return false; // 此处不检查切换的答案本身是否互斥
          const ans = currentNodeForSelection.options.find(
            (a) => a.optionId === id,
          );
          return ans?.mutuallyExclusive === true;
        });

        if (anMEIsSelected) {
          // 如果之前选中了一个互斥答案，而用户点击了一个非互斥答案，
          // 则取消选中互斥答案，并选中新的非互斥答案。
          return [toggledAnswerId];
        } else {
          // 如果没有选中互斥答案，则对非互斥答案执行标准的切换操作。
          return currentSelections.includes(toggledAnswerId)
            ? currentSelections.filter((id) => id !== toggledAnswerId)
            : [...currentSelections, toggledAnswerId];
        }
      }
    },
    [],
  );

  /**
   * @function handleMultiSelect
   * @description 处理用户在多选模式下勾选或取消勾选一个答案。
   *              使用 `handleMutuallyExclusiveSelection` 来处理互斥逻辑。
   * @param {string} answerId - 用户勾选/取消勾选的答案ID。
   */
  const handleMultiSelect = useCallback(
    (answerId: string) => {
      if (
        !currentNodeRef.current ||
        currentNodeRef.current.answerType !== EAnswerType.MULTIPLE_CHOICE
      )
        return;

      setSelectedAnswers((prev) => {
        // 此处 currentNodeRef.current 保证是一个 multi_select 节点
        return handleMutuallyExclusiveSelection(
          currentNodeRef.current!,
          answerId,
          prev,
        );
      });
    },
    [handleMutuallyExclusiveSelection],
  ); // setSelectedAnswers 是稳定的, currentNodeRef 是稳定的 ref

  /**
   * @function confirmSelections
   * @description 处理用户在多选模式下点击"确认"按钮的逻辑。
   *              收集所有选中的答案，处理它们的计划触发器，更新历史记录，
   *              然后根据配置（执行顺序）创建并排序分支，将这些分支压入多选分支栈，
   *              并导航到新分支层级的第一个分支。
   */
  const confirmSelections = useCallback(() => {
    if (!currentNodeRef.current || selectedAnswers.length === 0) return;

    const nodeForConfirm = currentNodeRef.current; // 捕获当前节点

    if (selectedAnswers.length > nodeForConfirm.maxSelectNum) {
      return;
    }

    // 1. 获取所有选中的答案对象
    const selectedAnswerObjects = nodeForConfirm.options.filter((ans) =>
      selectedAnswers.includes(ans.optionId),
    );

    const cSelectedAnswer = selectedAnswerObjects?.map((v) => v.optionId);

    // 2. 在创建分支之前处理来自选中答案的 triggerContentIds
    selectedAnswerObjects.forEach((ansObj) => {
      if (ansObj.triggerContentId) {
        handlePlanTrigger(ansObj.triggerContentId);
      }
    });

    // 3. 为历史记录构建合并的答案文本
    const combinedAnswerText = selectedAnswerObjects
      .map((ans) => ans.text)
      .join("、 ");

    // 5. 触发滚动到底部
    document.dispatchEvent(new Event(SCROLL_TO_BOTTOM_EVENT));

    // 6. 创建带有执行顺序信息的分支
    const createBranchesWithOrder = (): (DialogNode & {
      executionOrder: number;
    })[] => {
      const nodeForConfirm = currentNodeRef.current; // 捕获当前节点
      return cSelectedAnswer
        .map((answerId) => {
          const answer = nodeForConfirm.options.find(
            (ans) => ans.optionId === answerId,
          );
          if (!answer || !answer.nextNodeId) return null; // 确保分支有下一个节点

          return {
            ...answer,
            answerId: answerId,
            executionOrder: answer.executionOrder || 0,
          };
        })
        .filter((branch) => branch !== null) as (DialogNode & {
        executionOrder: number;
      })[];
    };

    let branchesWithOrder = createBranchesWithOrder();

    // // 7. 根据执行模式对分支进行排序
    // const shouldExecuteByConfigOrder = !!nodeForConfirm.executeByConfigOrder;
    // if (shouldExecuteByConfigOrder && branchesWithOrder.length > 0) {
    //   branchesWithOrder.sort((a, b) => a.executionOrder - b.executionOrder);
    //   console.log("多选：按配置顺序执行分支。");
    // } else {
    //   console.log("多选：按用户选择顺序执行分支（如果无配置顺序则为默认）。");
    // }

    const finalBranches: SelectionBranch[] = branchesWithOrder.map(
      ({ executionOrder, ...rest }) => rest,
    );

    // 8. 将新的分支层级推入栈并导航
    if (finalBranches.length > 0) {
      setMultiSelectBranchStack((prevStack) => [...prevStack, finalBranches]);
      setCurrentBranchIndices((prevIndices) => [...prevIndices, 0]); // 从新层级的第一个分支开始
      setSelectedAnswers([]); // 为下一个节点清除当前选择

      const firstBranchOfNewLevel = finalBranches[0];
      navigateToNodeAndHandleBranchTrigger(
        firstBranchOfNewLevel.nextNodeId,
        firstBranchOfNewLevel.triggerContentId,
      );
      // 4. 添加到历史记录
      setHistoryStateAndLocal(
        [
          ...historyRef.current,
          {
            ...nodeForConfirm,
            nodeId: nodeForConfirm.nodeId,
            question: nodeForConfirm.questionText,
            answerText: combinedAnswerText,
            answer: selectedAnswerObjects,
            answerId: cSelectedAnswer.join(","), // TODO 答案文本渲染形式
            multiSelected: [...cSelectedAnswer], // 存储选中答案ID的副本
          },
        ],
        {
          multiSelectBranchStack: [...multiSelectBranchStack, finalBranches],
          currentBranchIndices: [...currentBranchIndices, 0],
        },
      );
    } else {
      // 如果没有从选项中形成有效的分支（例如，选中的答案没有 next_node_id），
      // 则将其视为当前路径/多选流程的结束。
      // 4. 添加到历史记录
      setSelectedAnswers([]); // 清除选择
      proceedToNextBranchOrFinalize();
      setHistoryStateAndLocal([
        ...historyRef.current,
        {
          ...nodeForConfirm,
          nodeId: nodeForConfirm.nodeId,
          question: nodeForConfirm.questionText,
          answerText: combinedAnswerText,
          answer: selectedAnswerObjects,
          answerId: cSelectedAnswer.join(","), // TODO 答案文本渲染形式
          multiSelected: [...cSelectedAnswer], // 存储选中答案ID的副本
        },
      ]);
    }
  }, [
    selectedAnswers,
    handlePlanTrigger,
    navigateToNodeAndHandleBranchTrigger,
    proceedToNextBranchOrFinalize,
    setHistory /*, 其他 setters 是稳定的, currentNodeRef 是稳定的 */,
    setHistoryStateAndLocal,
  ]);

  /**
   * @function resetDialog
   * @description 只支持最后一个节点重选
   */
  const reSelectDialog = useCallback(
    (node: DialogNode, i: number) => {
      setSelectedAnswers([]); // 清除选择
      const cCurrentBranchIndices = [...currentBranchIndices];
      const cMultiSelectBranchStack = [...multiSelectBranchStack];
      let cPendingPlanTriggers = [...pendingPlanTriggers];
      const cReSelectBranchStack = [...reselectBranchStack];
      const cReCurrentBranchIndices = [...reCurrentBranchIndices];
      // 撤回记录的planId
      if (node.answerType === EAnswerType.SINGLE_CHOICE) {
        /** 存在执行栈索引 */
        if (currentBranchIndices.length > 0) {
          const currentLevelProcessedIndex =
            cCurrentBranchIndices[cCurrentBranchIndices.length - 1];
          const preBranchIndexToTry = Math.max(
            currentLevelProcessedIndex - 1,
            0,
          );
          cCurrentBranchIndices[cCurrentBranchIndices.length - 1] =
            preBranchIndexToTry;
          /** 存在回退栈 找回历史栈 */
          if (
            preBranchIndexToTry === 0 &&
            cReSelectBranchStack.length > 0 &&
            cReCurrentBranchIndices.length > 0
          ) {
            const lastMultiSelectBranchStack = cReSelectBranchStack.pop();
            const lastCurrentBranchIndices = cReCurrentBranchIndices.pop();
            cMultiSelectBranchStack.push(lastMultiSelectBranchStack);
            cCurrentBranchIndices.push(lastCurrentBranchIndices);
            setReselectBranchStack(cReSelectBranchStack);
            setReCurrentBranchIndices(cReCurrentBranchIndices);
            setMultiSelectBranchStack(cMultiSelectBranchStack);
            setCurrentBranchIndices(cCurrentBranchIndices);
          } else {
            setCurrentBranchIndices(cCurrentBranchIndices);
          }
        }
        const backPlanId = node.options?.find(
          (v) => v.optionId === node.answerId,
        )?.triggerContentId;
        const currentPendingPlanTriggers = pendingPlanTriggers?.filter(
          (v) => v !== backPlanId,
        );
        cPendingPlanTriggers = currentPendingPlanTriggers;
        setPendingPlanTriggers(currentPendingPlanTriggers);
      }
      if (node.answerType === EAnswerType.MULTIPLE_CHOICE) {
        // 多选存且下个节点有数据，出执行多选栈
        const AnsHasNextNode = (node.answer as Options[])?.some(
          (v) => v.nextNodeId,
        );
        /** 有下节点，当前多选栈出栈 */
        if (AnsHasNextNode) {
          cMultiSelectBranchStack.pop();
          cCurrentBranchIndices.pop();
          setMultiSelectBranchStack(cMultiSelectBranchStack);
          setCurrentBranchIndices(cCurrentBranchIndices);
        } else {
          /** 存在执行栈 */
          /** 存在执行栈索引 */
          if (currentBranchIndices.length > 0) {
            const currentLevelProcessedIndex =
              cCurrentBranchIndices[cCurrentBranchIndices.length - 1];
            const preBranchIndexToTry = Math.max(
              currentLevelProcessedIndex - 1,
              0,
            );
            cCurrentBranchIndices[cCurrentBranchIndices.length - 1] =
              preBranchIndexToTry;
            /** 存在回退栈 找回历史栈 */
            if (
              preBranchIndexToTry === 0 &&
              cReSelectBranchStack.length > 0 &&
              cReCurrentBranchIndices.length > 0
            ) {
              const lastMultiSelectBranchStack = cReSelectBranchStack.pop();
              const lastCurrentBranchIndices = cReCurrentBranchIndices.pop();
              cMultiSelectBranchStack.push(lastMultiSelectBranchStack);
              cCurrentBranchIndices.push(lastCurrentBranchIndices);
              setReselectBranchStack(cReSelectBranchStack);
              setReCurrentBranchIndices(cReCurrentBranchIndices);
              setMultiSelectBranchStack(cMultiSelectBranchStack);
              setCurrentBranchIndices(cCurrentBranchIndices);
            } else {
              setCurrentBranchIndices(cCurrentBranchIndices);
            }
          }
        }
        const backPlanIds = node.options
          ?.filter((v) => node.multiSelected.includes(v.optionId))
          ?.reduce((p, c) => {
            return [...p, c.triggerContentId];
          }, []);
        const currentPendingPlanTriggers = pendingPlanTriggers?.filter(
          (v) => !backPlanIds.includes(v),
        );
        cPendingPlanTriggers = currentPendingPlanTriggers;
      }
      setPendingPlanTriggers(cPendingPlanTriggers);
      setHistoryStateAndLocal(history.slice(0, i), {
        multiSelectBranchStack: cMultiSelectBranchStack,
        currentBranchIndices: cCurrentBranchIndices,
        pendingPlanTriggers: cPendingPlanTriggers,
        reselectBranchStack: cReSelectBranchStack,
        reCurrentBranchIndices: cReCurrentBranchIndices,
      });
      setCurrentNode(node);
    },
    [setHistory, setHistoryStateAndLocal, history, setCurrentNode],
  );

  const switchTextBook = (node, i) => {
    const nNode = {
      ...node,
      options: node.options?.map((v) => ({
        ...v,
        selected: false,
      })),
    };
    reSelectDialog(nNode, i);
  };

  /**
   * @function resetDialog
   * @description 重置整个对话状态到其初始值。
   *              用于重新开始对话或清除当前所有进度。
   */
  const resetDialog = useCallback(() => {
    const initialNode = nodesRef.current?.find(
      (v) => v.bizType === EBizType.SUBJECT,
    );
    window.localStorage.removeItem(historyKey);
    setCurrentNode(initialNode);
    setHistory([]);
    setSelectedAnswers([]);
    setMultiSelectBranchStack([]);
    setCurrentBranchIndices([]);
    setPendingPlanTriggers([]);
    setShouldFinalizePlans(false);
  }, []);

  return {
    currentNode,
    setCurrentNode,
    history,
    historyRef,
    selectedAnswers,
    multiSelectBranchStack,
    currentBranchIndices,
    pendingPlanTriggers,
    handleSelection,
    handleMultiSelect,
    confirmSelections,
    restartDialog,
    resetDialog,
    reSelectDialog,
    switchTextBook,
  };
};
