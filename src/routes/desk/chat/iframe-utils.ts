import { ErrorCodeEnum } from "@/utils/constant";

// 通知父页面
export const postMessage = (type: string, value?: any) => {
  try {
    window.parent?.postMessage(
      {
        type,
        value,
      },
      "*",
    );
  } catch (e) {
    console.error("PostMessage 失败", e);
  }
};

// 通知父页面，当前页面已准备好
export const postReady = () => {
  postMessage("chatReady");
};

// 通知父页面，显示 合成计划详情
export const postShowDetail = (id: string, desc: string) => {
  postMessage("showPlanDetail", {
    id,
    desc,
  });
};

// 通知父页面，跳转到登录页
export const postToLogin = () => {
  postMessage("toLogin");
};

// 检查是否需要跳转到登录页
export const checkNeedLogin = (e: any, isIframeMode: boolean) => {
  if (isIframeMode && +e?.code === ErrorCodeEnum.NOT_LOGIN) {
    postToLogin();
  }
  throw e;
};
