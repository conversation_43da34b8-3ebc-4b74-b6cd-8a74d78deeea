import React, { useEffect, useState, useRef, useCallback } from "react";
import { useRequest } from "ahooks";
import { useSearchParams } from "react-router-dom";
import dayjs from "dayjs";

import { IconSvg, Spin } from "@/components";
import { useDialogProvider, DialogContext } from "./context/useDialog";
import DialogInterface from "./components/DialogInterface";
import TipPopup, { ETipType } from "./components/tip-popup";
import { EBizType } from "./types";
import { ErrorBoundary } from "react-error-boundary";

import {
  getStartNode,
  getNextQuestionAll,
  updateUserEdition,
} from "@/service/chat";
import { px2RealPx } from "@/utils/vw-utils";
import Empty from "@/components/empty-status";
import errorPNG from "@/assets/common/error.png";
import { getSafeAreaTop } from "@/utils/bridge-utils";
import { getLocalStorageToValue, getUid, separatelyReport } from "@/utils/tool";
import { postReady, checkNeedLogin } from "./iframe-utils";

import Style from "./style.module.scss";

const sourceTypeToName = {
  1: "书桌",
  2: "其他",
  3: "PC",
};

const OFFSET_TOP = px2RealPx(60);
export const Component: React.FC = () => {
  const [searchParams] = useSearchParams();
  const HISTORY_STORAGE_KEY = `chat_history_${getUid()}`;
  const CHECKED_STORAGE_KEY = `chat_checked_${getUid()}`;
  const [safeTop, setSafeTop] = useState(0);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const popupRef = useRef<any>(null);
  const [showBottom, setShowBottom] = useState(false);
  const source = searchParams.get("source") || "1";
  const isIframeMode = searchParams.get("iframeMode") === "1";

  const {
    runAsync: getStartNodeRun,
    loading: startLoading,
    data: { data: startNode = [] } = {},
  } = useRequest(
    () =>
      getStartNode({
        ignoreError: true,
      }).catch((e) => checkNeedLogin(e, isIframeMode)),
    {
      // manual: true,
    },
  );

  const { runAsync: questionNodeRun, data: { data: questionNode = [] } = {} } =
    useRequest(
      (p) =>
        getNextQuestionAll({
          ...p,
          ignoreError: true,
        }).catch((e) => checkNeedLogin(e, isIframeMode)),
      {
        manual: true,
      },
    );

  const { runAsync: updateUserEditionRun } = useRequest(
    (p) =>
      updateUserEdition({
        ...p,
        ignoreError: true,
      }).catch((e) => checkNeedLogin(e, isIframeMode)),
    {
      manual: true,
    },
  );

  const initial = async () => {
    const safeTop = await getSafeAreaTop();
    setSafeTop(safeTop);
  };

  const onBack = () => {
    if (mstJsBridge.isInMstApp()) {
      if (checkShowPopup()) {
        mstJsBridge.closeWebview();
      }
    } else {
      window.history.back();
    }
  };

  const nodes = [...(startNode || []), ...(questionNode || [])];

  const {
    currentNode,
    setCurrentNode,
    history,
    historyRef,
    selectedAnswers,
    multiSelectBranchStack,
    currentBranchIndices,
    pendingPlanTriggers,
    handleSelection,
    handleMultiSelect,
    confirmSelections,
    restartDialog,
    resetDialog,
    reSelectDialog,
    switchTextBook,
  } = useDialogProvider({
    nodes,
    run: {
      questionNodeRun,
      updateUserEditionRun,
      getStartNodeRun,
    },
    historyKey: HISTORY_STORAGE_KEY,
    isIframeMode,
  });

  useEffect(() => {
    separatelyReport("ewt_h5_base_plan_desk_chat_view", {
      sourceTypeName: sourceTypeToName[source],
    });
    initial();
  }, []);

  const checkShowPopup = useCallback(() => {
    const hasPlanNode = historyRef.current?.find(
      (v) => v.bizType === EBizType.PLAN,
    );
    const cacheChecked = getLocalStorageToValue(CHECKED_STORAGE_KEY);
    if (hasPlanNode && !cacheChecked) {
      popupRef.current?.showPopup(ETipType.back);
      return false;
    }
    return true;
  }, []);

  /** 初始话弹出提示 */
  useEffect(() => {
    if (!popupRef.current) return;
    const localCache = getLocalStorageToValue(HISTORY_STORAGE_KEY) || {};
    const diffD = dayjs(localCache.expire).diff(dayjs(), "day");
    if (localCache && localCache?.history?.length > 0 && diffD) {
      popupRef.current?.showPopup(ETipType.enter);
    }
  }, []);

  useEffect(() => {
    if (isIframeMode) {
      postReady();
    }
    /** 注册返回拦截 */
    window.ewt_goBack = () => checkShowPopup();
    return () => {
      window.ewt_goBack = () => true;
    };
  }, []);

  const isScrollAtBottom = (el: HTMLElement, offset = 0): boolean => {
    return el.scrollTop + el.clientHeight + offset >= el.scrollHeight;
  };
  /** 监听滚动事件出来滚动底部 */
  const onScroll = () => {
    const el = wrapperRef.current;
    if (!el) return;

    const offset = px2RealPx(400); // 提前计算好偏移
    const shouldShow = !isScrollAtBottom(el, offset);

    // 避免重复 setState（引发 re-render）
    setShowBottom((prev) => {
      return prev !== shouldShow ? shouldShow : prev;
    });
  };

  const scrollBottom = () => {
    const el = wrapperRef.current;
    if (!el) return;
    el.scrollTo({
      top: el.scrollHeight,
      behavior: "smooth", // 平滑滚动
    });
  };

  return (
    <div
      className={Style["chat-page-wrapper"]}
      onScroll={onScroll}
      ref={wrapperRef}
    >
      <div className={Style["nav-bar"]} style={{ top: safeTop }}>
        {!isIframeMode && (
          <div className={Style["nav-bar-left"]} onClick={onBack}>
            <IconSvg name="icon-a-jinru2x" />
          </div>
        )}
      </div>
      <ErrorBoundary
        fallback={
          <div className={Style["error-content"]}>
            <Empty
              image={errorPNG}
              text="啊哦！系统开小差了"
              buttonOption={{
                className: Style["btn"],
                text: "重试",
                handleClick: () => {
                  window.localStorage.removeItem(HISTORY_STORAGE_KEY);
                  window.location.reload();
                },
              }}
            />
          </div>
        }
      >
        <div
          className={Style["page-container"]}
          style={{ paddingTop: safeTop + OFFSET_TOP }}
        >
          {startLoading && nodes?.length <= 0 && <Spin />}
          {nodes?.length > 0 && (
            <DialogContext.Provider
              value={{
                currentNode,
                setCurrentNode,
                history,
                selectedAnswers,
                multiSelectBranchStack,
                currentBranchIndices,
                pendingPlanTriggers,
                isIframeMode,
                handleSelection,
                handleMultiSelect,
                confirmSelections,
                restartDialog,
                resetDialog,
                reSelectDialog,
                switchTextBook,
              }}
            >
              <DialogInterface
                onReloadScrollBottom={() => {
                  onScroll();
                }}
                onScrollBottom={scrollBottom}
              />
            </DialogContext.Provider>
          )}
          {!startLoading && nodes?.length <= 0 && (
            <div className={Style["error-content"]}>
              <Empty
                image={errorPNG}
                text="啊哦！系统开小差了"
                buttonOption={{
                  className: Style["btn"],
                  text: "重试",
                  handleClick: () => {
                    window.localStorage.removeItem(HISTORY_STORAGE_KEY);
                    window.location.reload();
                  },
                }}
              />
            </div>
          )}
        </div>
      </ErrorBoundary>

      <div
        className={`${Style["dialog-scroll-bottom"]} ${
          showBottom ? Style["show"] : ""
        }`}
        onClick={scrollBottom}
      >
        返回底部 <IconSvg name="icon-a-jinru2x" />
      </div>
      <TipPopup
        ref={popupRef}
        checkKey={CHECKED_STORAGE_KEY}
        reset={resetDialog}
      />
    </div>
  );
};
