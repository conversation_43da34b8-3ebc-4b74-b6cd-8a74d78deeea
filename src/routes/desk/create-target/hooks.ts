import { ICreateTargetBaseDataRes } from "@/service/desk/create-target";
import { ITreeDataItem } from "@/types/common";

/**
 * 将扁平化的数据转换为三级级联格式
 * @param data 扁平化的数据数组
 * @returns 三级级联格式的数据
 */
export const convertToTreeData = (
  data: ICreateTargetBaseDataRes[],
): ITreeDataItem[] => {
  // 使用数组来存储结果，保持原始顺序
  const result: ITreeDataItem[] = [];
  // 学科ID作为key，存储学科数据，方便存取
  const subjectMap = new Map<number, ITreeDataItem>();
  if (!data?.length) {
    return result;
  }

  data.forEach((item) => {
    // 处理第一级（学科）
    if (!subjectMap.has(item.subjectId)) {
      const subject = {
        value: item.subjectId,
        label: item.subjectName,
        children: [],
      };
      subjectMap.set(item.subjectId, subject);
      result.push(subject);
    }

    const subject = subjectMap.get(item.subjectId)!;

    // 处理第二级（动作）
    let action = subject.children?.find(
      (child) => child.value === item.actionId,
    );
    if (!action) {
      action = {
        value: item.actionId,
        label: item.actionName,
        subjectId: item.subjectId,
        subjectName: item.subjectName,
        children: [],
      };
      subject.children?.push(action);
    }

    // 处理第三级（数量）
    const amount = {
      value: item.amountId,
      label: `${item.amountName}${item.unit || ""}`,
      subjectId: item.subjectId,
      subjectName: item.subjectName,
      actionId: item.actionId,
      actionName: item.actionName,
      unit: item.unit || "",
    };

    action.children?.push(amount);
  });

  return result;
};
