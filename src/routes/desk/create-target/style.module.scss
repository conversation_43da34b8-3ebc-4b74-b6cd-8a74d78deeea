.create-target-layout {
  .header {
    :global {
      .adm-nav-bar-left {
        flex: 0 1 auto;
        .adm-nav-bar-back {
          margin-right: 0;
        }
      }
      .adm-nav-bar-title {
        text-align: left;
        padding: 0;
      }
    }
  }
  .content {
    overflow-x: hidden;
    overflow-y: auto;
    height: calc(100vh - 12.8vw - 72px);
    background-color: #cc926d;
    background-image: url("@/assets/image/target-info/bg.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    :global {
      .swiper-initialized {
        padding-right: 16px;
      }
    }
    .pr-17 {
      padding-right: 17px;
    }
    .form-block {
      width: 350px;
      position: relative;
      padding: 12px 0 12px 17px;
      margin: 19px 12px;
      background: #fff;
      border: 1px solid #000000;
      border-radius: 8px;
      .form-item {
        max-width: 350px;
        position: relative;
        z-index: 2;
        margin-bottom: 24px;
        .date-picker-c {
          display: flex;
          align-items: center;
          padding-left: 12px;
          height: 40px;
          background: #ffffff;
          border: 1px solid #021e66;
          border-radius: 8px;
          font-weight: 400;
          font-size: 14px;
          color: #999999;

          &.has-end-time {
            color: #333;
          }
        }

        .form-item-label-inner {
          margin-bottom: 12px;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
        .form-item-label {
          position: relative;
          display: flex;
          align-items: center;
          height: 22px;
          line-height: 22px;
          font-weight: 600;
          font-size: 16px;
          color: #333333;
          &::before {
            display: block;
            content: "";
            width: 4px;
            left: 0;
            height: 10px;
            background: #2f86ff;
            border-radius: 8px;
            border-radius: 2px;
            margin-right: 8px;
          }
          .form-item-label-help {
            margin-left: 16px;
            font-weight: 400;
            font-size: 12px;
            color: #999999;
          }
        }
        .form-item-sub-label {
          font-weight: bold;
          font-size: 14px;
          color: #2a333a;
          line-height: 16px;
        }

        .form-item-sub-label-margin {
          padding-top: 12px;
          padding-bottom: 12px;
        }

        .form-item-sub-label-title {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          padding: 0 20px;
          max-width: 100%;
        }

        .form-item-action-swiper-slider {
          :global {
            .swiper-wrapper {
              flex-direction: column !important;
            }
            .swiper-slide {
              width: 100% !important;
              margin-bottom: 8px;

              &>div {
                width: 100% !important;
              }

              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }

      .form-item:last-child {
        margin-bottom: 0;
      }
    }
  }
  .footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    width: 100%;
    min-height: 72px;
    padding: 16px 15px;
    background-color: #fff;
    padding-bottom: calc(env(safe-area-inset-bottom) + 16px);
    padding-bottom: calc(constant(safe-area-inset-bottom) + 16px);
    display: flex;
    justify-content: center;
    align-items: center;
    > div {
      border: 1px solid #021e66;
      box-shadow: 0 4px 0 0 #021e6626;
    }
  }
}
