import React, { useEffect, useState } from "react";
import dayjs from "dayjs";
import { Layout } from "@/components";
import { convertToTreeData } from "./hooks";
import { closeWebview } from "@/utils/bridge-utils";
import { CalendarPicker, Toast } from "antd-mobile";
import CancelButton from "../components/cancel-button";
import PrimaryButton from "../components/primary-button";
import BackBlackImg from "@/assets/common/back_black.png";
import { clickPv, cls, createURLByType, EJumpType } from "@/utils/tool";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";

import {
  getDeskCreateTargetBaseData,
  getDeskTargetDetail,
  postDeskCreateTarget,
  postDeskEditTarget,
  TDeskTargetDetailRes,
} from "@/service/desk/create-target";
import SmallTabSwiper, {
  ISmallTabSwiperItem,
} from "./components/small-tab-swiper";

import Styles from "./style.module.scss";
import TargetAction from "./components/target-action";
import { NetworkErrorStatus } from "@/components/empty-status";
import PageLoading from "@/components/page-loading";

export const Component: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [searchParams, _] = useSearchParams();
  // 编辑状态时的目标id，不是强依赖，会以接口返回的为准
  const targetIdParam = searchParams.get("targetId");
  // 选择截止日期的弹窗是否显示
  const [visible, setVisible] = useState(false);
  // 截止日期
  const [endTime, setEndTime] = useState<number>();
  // 学科信息
  const [subjectInfo, setSubjectInfo] = useState({
    currentValue: -1,
    list: [],
  });
  // 目标任务信息
  const [actionInfo, setActionInfo] = useState<any>({
    currentValue: "",
    list: [],
  });
  // 目标值信息，包含对应的单位
  const [amountInfo, setAmountInfo] = useState<any>({
    currentValue: "",
    unit: "",
    list: [],
  });
  // 目标详情，编辑状态时会以接口返回的为准
  const [targetDetail, setTargetDetail] = useState<TDeskTargetDetailRes>();
  // 页面是否在加载中
  const [pageLoading, setPageLoading] = useState(true);
  // 页面是否加载失败,关键接口报错时页面整体
  const [pageError, setPageError] = useState(false);
  const [requestLoading, setRequestLoading] = useState(false);

  // 获取目标详情
  const getTargetDetail = async (treeData: any) => {
    try {
      // 如果没有目标id，那么就不继续后续流程了，当做新建目标处理
      // 如果是有未完结的目标又强行新建，那接口也会拦截的
      if (!targetIdParam) {
        return;
      }
      const { data } = await getDeskTargetDetail({
        targetId: targetIdParam,
        ignoreError: true,
      });
      if (data?.targetId && !data?.completeStatus) {
        setTargetDetail(data);
        const subject = treeData.find((v: any) => v.value === data.subjectId);
        const curActionList = subject.children || [];
        const curAmountList = curActionList.find(
          (v: any) => v.value === data.actionId,
        );
        setSubjectInfo((prev) => ({
          ...prev,
          currentValue: data?.subjectId,
        }));
        setActionInfo((prev) => ({
          ...prev,
          list: curActionList,
          currentValue: data.actionId,
        }));
        setAmountInfo((prev) => ({
          ...prev,
          list: curAmountList.children,
          currentValue: data.amountId,
          unit: data.unit,
        }));
        setEndTime(+data.endTime);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setPageLoading(false);
    }
  };

  // 初始化数据
  const initData = async () => {
    try {
      // 获取学科、目标任务、目标值数据，接口是以打平的数据格式返回的
      const { data } = await getDeskCreateTargetBaseData();
      // 因此这里需要将数据组装成树形结构，方便切换学科时处理数据
      const treeData = convertToTreeData(data);
      // 获取第一个学科，默认选中第一个学科，下同
      const firstSubject = treeData[0];
      // 获取第一个学科的第一个目标任务
      const actions = firstSubject.children;
      // 获取第一个目标任务的第一个目标值
      const amounts = actions[0].children;
      // 设置学科信息
      setSubjectInfo({
        currentValue: firstSubject.value as number,
        // 直接使用树形结构的列表，因为遍历时也只取第一层节点
        list: treeData,
      });
      // 设置目标任务信息
      setActionInfo({
        currentValue: actions[0].value,
        list: actions,
      });
      // 设置目标值信息
      setAmountInfo({
        currentValue: amounts[0].value,
        list: amounts,
        unit: amounts[0].unit,
      });
      setPageError(false);
      getTargetDetail(treeData);
    } catch (error) {
      console.error(error);
      setPageLoading(false);
      setPageError(true);
    }
  };

  // 目标任务切换
  const onActionChange = (val: ISmallTabSwiperItem) => {
    setActionInfo({ ...actionInfo, currentValue: val.value });
    const amounts = val.children;
    setAmountInfo({
      currentValue: amounts[0].value,
      list: amounts,
      unit: amounts[0].unit,
    });
  };

  // 目标值切换
  const onAmountChange = (val: ISmallTabSwiperItem) => {
    setAmountInfo({
      ...amountInfo,
      currentValue: val.value,
      unit: val.unit,
    });
  };

  // 学科切换
  const onSubjectChange = (val: ISmallTabSwiperItem) => {
    setSubjectInfo({
      ...subjectInfo,
      currentValue: val.value as number,
    });
    const actions = val.children;
    setActionInfo({
      currentValue: actions[0].value,
      list: actions,
    });
    const amounts = actions[0].children;
    setAmountInfo({
      currentValue: amounts[0].value,
      list: amounts,
      unit: amounts[0].unit,
    });
  };

  const onSubmit = async () => {
    if (!endTime) {
      Toast.show("请选择截止日期");
      return;
    }
    // 是否编辑模式：如果targetDetail有值，则表示是编辑模式
    // 一个人只能有一个目标，因此只要有值必然是编辑
    const isEdit = !!targetDetail;
    if (requestLoading) {
      Toast.show(`目标${isEdit ? "修改" : "设置"}中`);
      return;
    }
    setRequestLoading(true);
    try {
      clickPv("ewt_h5_base_plan_desk_create_target_confirm_button_click", {
        type: isEdit ? "修改目标" : "新建目标",
      });
      const apiParams = {
        endTime,
        subjectId: subjectInfo.currentValue,
        actionId: actionInfo.currentValue,
        amountId: amountInfo.currentValue,
        unit: amountInfo.unit,
        // 编辑模式才传递targetId
        ...(isEdit ? { targetId: targetDetail.targetId } : {}),
      };
      const api = isEdit ? postDeskEditTarget : postDeskCreateTarget;
      // 创建、编辑成功后都会返回目标id
      const { data } = await api(apiParams);
      if (data) {
        const tipMsg = isEdit ? "目标修改成功" : "目标设置成功";
        Toast.show(tipMsg);
        const targetInfoPage = createURLByType({
          path: "/desk/target-info",
          type: EJumpType.inside,
          originSearch: location.search,
          // 先移出targetId，原因是编辑目标时候地址栏是有该参数的
          removeQueryKeys: ["targetId"],
          // 跳转目标信息页时，增加最新的targetId参数
          addQueryObject: {
            targetId: data,
          },
        });
        // 跳转目标信息页,replace形式
        navigate(targetInfoPage, { replace: true });
      }
    } catch (error) {
      console.error(error);
    } finally {
      setRequestLoading(false);
    }
  };

  const onCloseWindow = () => {
    try {
      // 如果是跳转过来的，就退回去，否则就关闭 webview
      const isFrom = location.search.indexOf("from") !== -1;
      if (isFrom) {
        navigate(-1);
      } else {
        closeWebview();
      }
    } catch (error) {
      console.error("关闭webview失败", error);
    }
  };

  // 页面渲染
  useEffect(() => {
    initData();
  }, []);

  return (
    <Layout
      showHeader
      // 给安全区染色
      setChildrenContainerHeight={({ top }) => {
        return `calc(100vh - ${top || 0}px)`;
      }}
      className={Styles["create-target-layout"]}
      headerProps={{
        children: "请确认我的目标",
        className: Styles.header,
        backIconUrl: BackBlackImg,
        onBack: onCloseWindow,
      }}
    >
      {pageLoading && <PageLoading visible={pageLoading} />}
      {/* 加载完毕且不是页面错误时展示 */}
      {!pageLoading && !pageError && (
        <>
          <div className={Styles["content"]}>
            <div className={Styles["form-block"]}>
              {/* 截止日期 */}
              <div className={cls([Styles["form-item"], Styles["pr-17"]])}>
                <div className={Styles["form-item-label-inner"]}>
                  <div className={Styles["form-item-label"]}>
                    截止日期
                    <span className={Styles["form-item-label-help"]}>
                      点击并选择截止日期
                    </span>
                  </div>
                </div>
                <div
                  onClick={() => setVisible(true)}
                  className={cls([
                    Styles["date-picker-c"],
                    endTime && Styles["has-end-time"],
                  ])}
                >
                  {endTime
                    ? dayjs(+endTime).format("YYYY-MM-DD")
                    : "点击并选择截止日期"}
                </div>
                <CalendarPicker
                  selectionMode="single"
                  popupClassName={Styles["date-picker-popup"]}
                  defaultValue={endTime ? dayjs(+endTime).toDate() : undefined}
                  visible={visible}
                  min={dayjs().toDate()}
                  max={dayjs().add(5, "month").toDate()}
                  title="请选择日期"
                  onMaskClick={() => setVisible(false)}
                  onClose={() => {
                    setVisible(false);
                  }}
                  onConfirm={(val) => {
                    const valSt = dayjs(val)
                      .hour(23)
                      .minute(59)
                      .second(59)
                      .valueOf();
                    setEndTime(valSt);
                  }}
                />
              </div>
              {/* 我的目标 */}
              <div className={Styles["form-item"]}>
                <div className={Styles["form-item-label-inner"]}>
                  <div className={Styles["form-item-label"]}>
                    我的目标
                    <span className={Styles["form-item-label-help"]}>
                      点击下方选项，选择我的目标
                    </span>
                  </div>
                </div>
                <div className={Styles["form-item-sub-label"]}>目标学科</div>
                <SmallTabSwiper
                  list={subjectInfo.list}
                  currentValue={subjectInfo.currentValue}
                  onChange={onSubjectChange}
                  className={Styles["form-item-sub-label-margin"]}
                />
                <div className={Styles["form-item-sub-label"]}>目标任务</div>
                <TargetAction
                  list={actionInfo.list}
                  currentValue={actionInfo.currentValue}
                  onChange={onActionChange}
                />
                <div className={Styles["form-item-sub-label"]}>目标值</div>
                <SmallTabSwiper
                  autoSliderNum={4}
                  list={amountInfo.list}
                  currentValue={amountInfo.currentValue}
                  onChange={onAmountChange}
                  className={Styles["form-item-sub-label-margin"]}
                />
              </div>
            </div>
          </div>
          <div className={Styles["footer"]}>
            <CancelButton text="取消" onClick={onCloseWindow} />
            <PrimaryButton text="确定" onClick={onSubmit} />
          </div>
        </>
      )}

      {/* 加载完毕且是页面错误时展示 */}
      {!pageLoading && pageError && (
        <div
          style={{
            height: `calc(100vh - 12.8vw)`,
          }}
        >
          <NetworkErrorStatus
            buttonOption={{
              handleClick: () => {
                initData();
              },
            }}
          />
        </div>
      )}
    </Layout>
  );
};
