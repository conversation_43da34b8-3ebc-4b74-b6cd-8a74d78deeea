import React from "react";
import beSelectedIcon from "~/assets/image/self-learning-for-subject/be-choosed.png";
import styles from "./index.module.scss";
import { cls } from "@/utils/tool";

export interface ISmallTabSwiperItem {
  label: string;
  value: string | number;
  /** 其他自定义数据，点击时候会传递 */
  [key: string]: any;
}

export interface ISmallTabSwiperProps {
  /** 列表数据 */
  list: ISmallTabSwiperItem[];
  /** 当前选中的项 */
  currentValue: string | number;
  /** 点击事件 */
  onChange: (item: ISmallTabSwiperItem) => void;
  /** 自定义样式类名 */
  className?: string;
  /** 标题样式类名 */
  titleClassName?: string;
}

const TargetAction: React.FC<ISmallTabSwiperProps> = ({
  list,
  currentValue,
  onChange,
  className,
  titleClassName,
}) => {
  const onItemClick = (index: number, item: ISmallTabSwiperItem) => {
    onChange?.(item);
  };

  return (
    <div className={cls([styles.container, className])}>
      {list.map((item, index) => (
        <div
          key={index}
          className={cls([
            styles.item,
            item.value === currentValue && styles.active,
          ])}
          onClick={() => onItemClick(index, item)}
        >
          <div className={cls([styles.item_title, titleClassName])}>
            {item.label}
          </div>
          {item.value === currentValue && (
            <img
              src={beSelectedIcon}
              alt=""
              className={styles.item_active_icon}
            />
          )}
        </div>
      ))}
    </div>
  );
};

export default TargetAction;
