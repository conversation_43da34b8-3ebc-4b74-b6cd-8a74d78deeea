import React, { useEffect, useRef } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import beSelectedIcon from "~/assets/image/self-learning-for-subject/be-choosed.png";
import styles from "./index.module.scss";
import { cls } from "@/utils/tool";

export interface ISmallTabSwiperItem {
  label: string;
  value: string | number;
  /** 其他自定义数据，点击时候会传递 */
  [key: string]: any;
}

export interface ISmallTabSwiperProps {
  /** 列表数据 */
  list: ISmallTabSwiperItem[];
  /** 当前选中的项 */
  currentValue: string | number;
  /** 点击事件 */
  onChange: (item: ISmallTabSwiperItem) => void;
  /** 自定义样式类名 */
  className?: string;
  /** 是否自动滑动 */
  autoSlide?: boolean;
  /** 自动滑动时间间隔 */
  autoSlideInterval?: number;
  /** 间距 */
  spaceBetween?: number;
  /** 超过多少个自开启动滑动 */
  autoSliderNum?: number;
  /** 标题样式类名 */
  titleClassName?: string;
}

const SmallTabSwiper: React.FC<ISmallTabSwiperProps> = ({
  list,
  currentValue,
  onChange,
  className,
  autoSlide = true,
  autoSlideInterval = 100,
  spaceBetween = 4,
  autoSliderNum = 3,
  titleClassName,
}) => {
  const swiperRef = useRef<any>();
  const count = list.length;

  const onItemClick = (index: number, item: ISmallTabSwiperItem) => {
    if (autoSlide && count > autoSliderNum) {
      swiperRef.current.slideTo(index);
    }
    onChange?.(item);
  };

  useEffect(() => {
    setTimeout(() => {
      if (swiperRef.current && autoSlide) {
        const index = list.findIndex((item) => item.value === currentValue);
        if (index > -1 && count > autoSliderNum) {
          swiperRef.current.slideTo(index);
        }
      }
    }, autoSlideInterval);
  }, [currentValue]);

  return (
    <div className={cls([styles.container, className])}>
      <Swiper
        onSwiper={(swr) => {
          swiperRef.current = swr;
        }}
        slidesPerView="auto"
        // centeredSlides
        centeredSlidesBounds
        // threshold={10}
        spaceBetween={spaceBetween}
      >
        {list.map((item, index) => (
          <SwiperSlide key={index} virtualIndex={index}>
            <div
              key={index}
              className={cls([
                styles.item,
                item.value === currentValue && styles.active,
              ])}
              onClick={() => onItemClick(index, item)}
            >
              <div className={cls([styles.item_title, titleClassName])}>
                {item.label}
              </div>
              {item.value === currentValue && (
                <img
                  src={beSelectedIcon}
                  alt=""
                  className={styles.item_active_icon}
                />
              )}
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};

export default SmallTabSwiper;
