.container {
  width: 100%;
  height: 100%;
  background: #fff;
  box-sizing: border-box;

  :global {
    .swiper {
      width: 100%;
      height: 100%;
    }

    .swiper-slide {
      width: auto;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.item {
  min-width: 70px;
  height: 37px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  position: relative;
  padding: 0 20px;
  cursor: pointer;
  border-radius: 20px;
  border: 1.5px solid transparent;
  background-color: #F3F4F8;

  &_title {
    font-size: 14px;
    color: #333;
    text-align: center;
    font-weight: bold;
  }

  &.active {
    border: 1.5px solid #2E86FF;
    background-color: #fff;

    .item_title {
      color: #2E86FF;
    }
  }

  .item_active_icon {
    width: 18.6px;
    height: 16px;
    position: absolute;
    right: -1.5px;
    bottom: -1.5px;
  }
}