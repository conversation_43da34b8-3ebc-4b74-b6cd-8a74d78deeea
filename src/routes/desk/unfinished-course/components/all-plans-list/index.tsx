/** 全部计划列表 */
import * as React from "react";
import { useLocation } from "react-router-dom";
import { EJumpType, createURLByType, openUrlInWebView } from "@/utils/tool";
import { List, RowLoading } from "@/components";
import {
  IPostAllPlanReq,
  IPackageItem,
  postAllPlan as postAllPlanApi,
} from "@/service/home";
import { PackageItem } from "@/components";
import { useSubjectList } from "@/hooks";
import SafeLogger from "@/utils/safe-logger";
import { EPackageListStatus, EPackageStatus } from "../../common";
import { Filter } from "./filter";
import { ListEmpty } from "./list-empty";

import Style from "./style.module.scss";
import { openRoute } from "@/utils/tool";

export const AllPlanList: React.FC<object> = () => {
  const location = useLocation();
  function toOtherPage(path: string, addQueryObject?: Record<string, any>) {
    openUrlInWebView(
      createURLByType({
        path,
        type: EJumpType.outside,
        originSearch: location.search,
        addQueryObject: {
          ...(addQueryObject || {}),
        },
      }),
    );
  }

  /** 获取科目列表 */
  const { loading: getSubjectsLoading, subjectList } = useSubjectList();
  // 课程包状态、所属科目的筛选值
  const [filter, setFilter] = React.useState<IPostAllPlanReq>({
    status: EPackageStatus.doing,
  });

  /** 筛选变更 */
  function handleFilterChange(subject) {
    setFilter((pre) => ({
      ...pre,
      subjectInfo: {
        categoryCode: subject?.parentCode,
        subjectId: subject?.code,
      },
    }));
  }
  /** 获取到学科列表后，设置筛选的默认值 */
  React.useEffect(() => {
    if (subjectList) {
      /** 防止空态闪现 */
      setLoading(true);
      handleFilterChange(subjectList?.[0]);
    }
  }, [subjectList]);
  const pageRef = React.useRef<IPostAllPlanReq>({
    pageIndex: 1,
    pageSize: 10,
  });

  /** 是否还有更多 */
  const [hasMore, setHasMore] = React.useState<boolean>(true);
  /** 列表状态 */
  const [listStatus, setListStatus] = React.useState<EPackageListStatus>(
    EPackageListStatus.empty,
  );
  const [loading, setLoading] = React.useState<boolean>();
  /** 存储全部计划 */
  const [data, setData] = React.useState<IPackageItem[]>();
  // 请求时序控制，防止展示数据和筛选条件不符合
  const requestFlag = React.useRef<number>(0);
  async function postAllPlan(init = false) {
    const nowReqFlag = ++requestFlag.current;
    try {
      /** 重置列表状态 */
      // init && setListStatus(null);
      setLoading(init);
      pageRef.current.pageIndex = init ? 1 : pageRef.current.pageIndex + 1;
      const { data } = await postAllPlanApi({
        ...pageRef.current,
        ...filter,
        // 非 200，不抛出异常
        ignoreError: true,
      } as IPostAllPlanReq);
      if (nowReqFlag === requestFlag.current) {
        const packages = data.listPage.data || [];
        // 已经加载完毕，没有下一页了
        setHasMore(data.listPage.haveNextPage);
        /** 用户还没有加入过课程 */
        if (!data.allCourseCount) {
          setData([]);
          setListStatus(EPackageListStatus.empty);
        } else if (
          // 如果没有选中全部，并且返回的数据为空，那么展示暂无数据的空态
          !data.listPage.total &&
          (filter.subjectInfo?.subjectId || filter.subjectInfo.categoryCode)
        ) {
          setData([]);
          setListStatus(EPackageListStatus.noData);
        } else if (
          /** 用户已经学完进行中的计划 */
          !data.listPage.total &&
          filter.status === EPackageStatus.doing
        ) {
          setData([]);
          setListStatus(EPackageListStatus.doingEmpty);
        } else if (
          // 用户还没有已经完成的课程
          !data.listPage.total &&
          filter.status === EPackageStatus.done
        ) {
          setData([]);
          setListStatus(EPackageListStatus.doneEmpty);
        } else {
          setListStatus(EPackageListStatus.haveData);
          setData((pre) => (init ? packages : [...(pre || []), ...packages]));
        }
      }
    } catch (error) {
      if (nowReqFlag === requestFlag.current) {
        // 清空数据
        init && setData([]);
        setListStatus(EPackageListStatus.error);
        setHasMore(false);
      }
      console.error(error);
      SafeLogger.baseLogger.error("get-allPlans-failed", { error });
    } finally {
      if (nowReqFlag === requestFlag.current) {
        setLoading(false);
      }
    }
  }
  /** 初始进来 先获取一次，因为学科列表通过接口获取，等拿到之后再获取计划 */
  React.useEffect(() => {
    if (filter.subjectInfo) {
      postAllPlan(true);
    }
  }, [filter]);

  function handlePackageClick(packageDetail: IPackageItem) {
    openRoute({
      domain: "course",
      action: "open_detail",
      params: {
        id: packageDetail.courseId,
      },
    });
  }

  return (
    <>
      {/* 还没添加过计划、获取科目列表报错 这两种情况，不展示 filter 列表 */}
      {listStatus !== EPackageListStatus.empty && !getSubjectsLoading ? (
        <Filter value={filter} onChange={setFilter} subjects={subjectList} />
      ) : null}
      {loading || getSubjectsLoading ? (
        <RowLoading />
      ) : data?.length ? (
        <List
          className={Style["all-plans-list"]}
          scrollClassName={Style["all-plans-list-scroll"]}
          getKey={(item) => item?.courseId}
          renderItem={(item) => (
            <PackageItem
              handleClick={handlePackageClick}
              item={item}
              hiddenProgress={true}
            />
          )}
          data={data}
          hasMore={hasMore}
          loadMore={postAllPlan}
        >
          {(loadMore: boolean) => {
            if (loadMore) {
              return <RowLoading />;
            }
            return null;
          }}
        </List>
      ) : (
        <ListEmpty listStatus={listStatus} nav={toOtherPage} />
      )}
    </>
  );
};
