/** 头部筛选区 */
import * as React from "react";
import { IPostAllPlanReq, ISubjectItem } from "@/service/home";
import { SubjectList } from "@/components";
import type { ISubjectList } from "@/components";

import Style from "./style.module.scss";

interface IFilter {
  subjects?: ISubjectList["subjectList"];
  value: IPostAllPlanReq;
  onChange: React.Dispatch<React.SetStateAction<IPostAllPlanReq>>;
}

export const Filter: React.FC<IFilter> = (props) => {
  const { value, subjects, onChange } = props;

  return (
    <div className={Style["query"]}>
      {!!subjects?.length && (
        <SubjectList
          className={Style["subjects"]}
          itemClassName={Style["subject-item"]}
          subjectList={subjects}
          value={
            {
              code: value?.subjectInfo?.subjectId,
              parentCode: value?.subjectInfo?.categoryCode,
            } as ISubjectItem
          }
          onChange={(item) => {
            onChange((pre) => ({
              ...pre,
              subjectInfo: {
                categoryCode: item.parentCode,
                subjectId: item.code,
              },
            }));
          }}
        />
      )}
    </div>
  );
};
