/** 列表空态 */

import * as React from "react";

import noContentPNG from "@/assets/common/no-content.png";
import addPNG from "@/assets/common/add.png";
import errorPNG from "@/assets/common/error.png";
import Empty from "@/components/empty-status";
import { EPackageListStatus } from "../../common";

import Style from "./style.module.scss";

interface IListEmpty {
  nav: (path: string) => void;
  listStatus: EPackageListStatus;
}

export const ListEmpty: React.FC<IListEmpty> = (props) => {
  const { listStatus, nav } = props;

  return (
    <div className={Style["empty"]}>
      <Empty
        image={
          listStatus === EPackageListStatus.error ? errorPNG : noContentPNG
        }
        text="暂无内容"
        className={
          listStatus === EPackageListStatus.error ? "empty-error-img" : null
        }
        buttonOption={{
          showIcon: listStatus !== EPackageListStatus.error,
          icon: addPNG,
          className:
            listStatus === EPackageListStatus.error ? Style["error-btn"] : null,
          text: "",
        }}
      />
    </div>
  );
};
