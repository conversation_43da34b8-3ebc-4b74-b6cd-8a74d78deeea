import * as React from "react";
import { Layout } from "@/components";
import type { ILayout, ILayoutRef } from "@/components";
import { clickPv, openUrlInWebView } from "@/utils/tool";
import { Context } from "./context";
import { AllPlanList } from "./components/all-plans-list";
import { homeScrollContainer } from "./common";
import { closeWebview } from "@/utils/bridge-utils";
import PageBackIcon from "@/components/page-back-icon";

import Style from "./style.module.scss";

export const Component: React.FC = () => {
  // layoutRef，设置页面级的 loading
  const layoutRef = React.useRef<ILayoutRef>();
  // 组件共享的状态
  const contextValue = React.useMemo(() => {
    return {};
  }, []);

  return (
    <Layout
      showHeader
      // 给安全区染色
      topSafeAreaProps={
        { className: Style["top-safe-area"] } as ILayout["topSafeAreaProps"]
      }
      setChildrenContainerHeight={({ top, bottom }) => {
        return `calc(100vh - ${top || 0}px - ${bottom || 0}px)`;
      }}
      bgClassName={Style["have-image-bg"]}
      bgChildren={<div className={Style["bg-children"]} />}
      headerProps={{
        children: "旧版计划内容",
        className: Style["header"],
        back: <PageBackIcon />,
        onBack: () => closeWebview(),
      }}
      ref={layoutRef}
    >
      <Context.Provider value={contextValue}>
        <div
          style={{
            overflow: "auto",
            maxHeight: `calc(100% - 12.8vw)`,
            height: `calc(100% - 12.8vw)`,
            display: "flex",
            flexDirection: "column",
          }}
          className={homeScrollContainer}
        >
          <div className={Style["offline-notice"]}>
            「旧版自习计划」于2025-09-29下线，功能将全面升级至新版自习计划。请及时将旧版自习计划中的“未完成课程”添加至新版自习计划。
            <span
              className={Style["offline-notice-link"]}
              onClick={() => {
                clickPv(
                  "ewt_h5_base_plan_desk_unfinished_course_upgrade_notice_button_click",
                );
                openUrlInWebView(
                  "https://web.ewt360.com/themeTemplateClient/index.html?id=1970107569695055872&showTopBar=false",
                  "产品升级公告",
                );
              }}
            >
              查看添加方法及升级公告&gt;
            </span>
          </div>
          <AllPlanList />
        </div>
      </Context.Provider>
    </Layout>
  );
};
