/** 统一兜底页面 */

import React, { useEffect, useState } from "react";
import { useRouteError } from "react-router";
import SafeLogger from "@/utils/safe-logger";
import ErrorInfo from "@/components/error-info";
import { closeWebview, getIsInMSTApp } from "@/utils/bridge-utils";

import styles from "./style.module.scss";

export const Error: React.FC = () => {
  const error = useRouteError() as any;
  const [isInApp, setIsInApp] = useState(false);

  useEffect(() => {
    setIsInApp(getIsInMSTApp());
  }, []);

  useEffect(() => {
    if (error && error.name !== "ChunkLoadError") {
      SafeLogger.baseLogger.error("render-error", {
        error,
        message: error.message,
        stack: error.stack,
      });
    }
    console.log("发生错误", error);
  }, [error]);
  return (
    <div className={styles.container}>
      <ErrorInfo
        onRetry={() => {
          window.location.reload();
        }}
        type="light"
        onConfirm={isInApp ? closeWebview : undefined}
      />
    </div>
  );
};
