/** 无权限页面 */

import * as React from "react";

import Empty from "@/components/empty-status";
import backBlackPNG from "@/assets/common/back_black.png";
import { Layout } from "@/components";
import mstJsBridge from "mst-js-bridge";

import Style from "./style.module.scss";

export const NoAuth: React.FC = () => {
  return (
    <Layout
      showHeader
      setChildrenContainerHeight={({ top, bottom }) => {
        return `calc(100vh - ${(top || 0) + (bottom || 0)}px)`;
      }}
      headerProps={{
        backIconUrl: backBlackPNG,
        children: "自习计划",
        className: Style["header"],
        onBack: () => {
          // 直接关闭 webview
          mstJsBridge.closeWebview();
        },
      }}
    >
      <div
        style={{
          overflow: "auto",
          maxHeight: `calc(100% - 12.8vw)`,
          height: `calc(100% - 12.8vw)`,
          backgroundColor: "#EEF1F6",
        }}
      >
        <Empty text="暂不支持使用该功能哦~" />
      </div>
    </Layout>
  );
};
