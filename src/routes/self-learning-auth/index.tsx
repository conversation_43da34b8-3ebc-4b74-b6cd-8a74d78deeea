/** 自主学习统一鉴权 - 路由守卫 */

import * as React from "react";
import { DotLoading } from "antd-mobile";
import { Outlet, Navigate, useLocation } from "react-router-dom";

import { EJumpType, createURLByType } from "@/utils/tool";
import { useAuth } from "@/hooks";

import Style from "./style.module.scss";

const Auth: React.FC = () => {
  const location = useLocation();
  const { loading, hasAuth } = useAuth();
  // 鉴权中，给个loading展示
  if (loading) {
    return (
      <div className={Style["dot-loading-container"]}>
        <DotLoading />
      </div>
    );
  }
  if (!hasAuth) {
    const url = createURLByType({
      path: "/self-learning/no-auth",
      type: EJumpType.inside,
      originSearch: location.search,
    });
    return <Navigate to={url} replace={true} />;
  }
  return <Outlet />;
};

export default Auth;
