import React, { useEffect, useState } from "react";

import rightArrowPNG from "@/assets/mbti/right-arrow.png";
import { getMajorListByType, IMajorInfo } from "@/service/question";
import { IconSvg } from "@/components/icon-svg";
import { Button } from "@/components/button";
import { cls, openUrlInWebView, openRoute, expPv, clickPv } from "@/utils/tool";

import { mbtiProfiles, professionalDetailPath } from "./common";

import Style from "./style.module.scss";

export interface IMbtiResult {
  type: keyof typeof mbtiProfiles;
  onReset: () => void;
}

const commonInfo = [
  {
    key: "description",
    name: "性格解析",
    backgroundColor: "#F4F5FF",
    borderLeftColor: "#C9CDFE",
  },
  {
    key: "strengths",
    name: "优势特征",
    borderLeftColor: "#B7DFFF",
    backgroundColor: "#EEF7FF",
  },
  {
    key: "growthAdvice",
    name: "成长建议",
    borderLeftColor: "#C9CDFE",
    backgroundColor: "#F4F5FF",
  },
];

const MBTIResult: React.FC<IMbtiResult> = (props) => {
  const { type, onReset } = props;

  const result = mbtiProfiles[type];

  const [majors, setMajors] = useState<IMajorInfo>();
  async function getMajors(type) {
    try {
      const { data } = await getMajorListByType({ type });
      setMajors(data);
    } catch (error) {
      console.error(error);
    }
  }

  useEffect(() => {
    if (!type) {
      return;
    }
    getMajors(type);
  }, [type]);

  useEffect(() => {
    expPv("ewt_h5_base_operation_career_personality_result_expo");
  }, []);

  function toDetailTest() {
    clickPv(
      "ewt_h5_base_operation_career_personality_professional_personality_assessment_click",
    );
    openRoute({
      domain: "flutter",
      action: "open_router",
      params: {
        templateType: "MBTI",
        answerTaskId: majors.answerTaskId,
        router: "/career_plann/evaluation_self_cognition",
      },
    });
  }
  return (
    <div className={Style["mbti-result"]}>
      <div className={Style["mbti-result-top"]}>
        <div className={Style["mbti-result-top-left"]}>
          <div className={Style["mbti-result-top-left-title"]}>你的MBTI是:</div>
          <div className={Style["mbti-result-top-left-result"]}>
            {result.name}
          </div>
          <div>
            请根据与你人格类型匹配的专业推荐，点击了解专业详情，也许就找到自己未来的专业方向。
          </div>
        </div>
        <div className={Style["mbti-result-top-right"]}>
          <img src={result.logo} />
        </div>
      </div>
      <div className={Style["mbti-result-middle"]}>
        {commonInfo.map(({ key, backgroundColor, name, borderLeftColor }) => {
          return (
            <div
              key={key}
              style={{
                backgroundColor,
                borderLeftColor,
              }}
              className={Style["mbti-result-middle-item"]}
            >
              <div className={Style["mbti-result-middle-item-title"]}>
                {name}
              </div>
              <div className={Style["mbti-result-middle-item-desc"]}>
                {result[key]}
              </div>
            </div>
          );
        })}
        <div
          className={cls([
            Style["mbti-result-middle-item"],
            Style["mbti-result-middle-major-item"],
          ])}
        >
          <div className={Style["mbti-result-middle-item-title"]}>专业推荐</div>
          <div className={Style["mbti-result-middle-item-major-container"]}>
            {(majors?.recommendList || []).map((item) => {
              return (
                <div
                  key={item.id}
                  className={
                    Style["mbti-result-middle-item-major-container-item"]
                  }
                  onClick={() => {
                    clickPv(
                      "ewt_h5_base_operation_career_personality_major_recommendation_click",
                    );
                    openUrlInWebView(
                      `${window.location.origin}${professionalDetailPath}?majorId=${item.id}&showTopBar=false`,
                    );
                  }}
                >
                  <div
                    className={
                      Style["mbti-result-middle-item-major-container-item-name"]
                    }
                  >
                    {item.name}
                  </div>
                  <div
                    className={
                      Style[
                        "mbti-result-middle-item-major-container-item-img-container"
                      ]
                    }
                  >
                    <img src={rightArrowPNG} />
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
      <div
        className={Style["mbti-result-footer"]}
        onClick={() => {
          clickPv("ewt_h5_base_operation_career_personality_restart_btn_click");
          onReset();
        }}
      >
        <Button className={Style["mbti-result-footer-btn"]} text="重新测试" />
      </div>
      {!!majors?.answerTaskId && (
        <div className={Style["mbti-result-reTest"]} onClick={toDetailTest}>
          不确定是否准确？
          <span>
            试试专业版职业人格类型测评
            <IconSvg
              className={Style["mbti-result-reTest-icon-arrow"]}
              name="icon-jinru"
            />
          </span>
        </div>
      )}
    </div>
  );
};

export default MBTIResult;
