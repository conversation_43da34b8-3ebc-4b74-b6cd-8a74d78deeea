/** 选项消息 */

import React, { useEffect, useState } from "react";

import { useScrollToView } from "@/hooks";
import { clickPv, cls } from "@/utils/tool";

import { Question } from "./components/dialog/type";

import Style from "./style.module.scss";

export interface IOption {
  initValue?: string;
  isLast?: boolean;
  optionClassName?: string;
  options: Question["options"];
  onSelect: (v: string, cb?: () => void) => void;
}

const Options: React.FC<IOption> = (props) => {
  const { options, isLast, initValue, optionClassName, onSelect } = props;
  // 选项的值
  const [value, setValue] = useState(initValue);
  const { containerRef, scrollToView } = useScrollToView();
  useEffect(() => {
    isLast && scrollToView();
  }, [isLast]);

  return (
    <div ref={containerRef} className={cls([Style["options-container"]])}>
      {(options || []).map((option) => {
        return (
          <div
            onClick={() => {
              clickPv("ewt_h5_base_operation_career_personality_options_click");
              // 防止重复作答
              if (value) {
                return;
              }
              onSelect(option.answer, () => {
                setValue(option.answer);
              });
            }}
            className={Style["option-container"]}
            key={option.answer}
          >
            <img src={option.icon} />
            <div
              className={cls([
                Style["option"],
                value && value !== option.answer && Style["option-disabled"],
                value && value === option.answer && Style["option-selected"],
                optionClassName,
              ])}
            >
              {option.desc}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default Options;
