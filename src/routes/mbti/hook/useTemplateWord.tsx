/** 获取模板消息 */

import React from "react";

import { Button, EButtonType } from "@/components/button";
import treeAvatar from "@/assets/mbti/tree-header.png";
import mineAvatar from "@/assets/mbti//mine.png";

import { TAnswer } from "@/service/question";

import MBTIResult from "../mbti-result";
import { EDialogLayout, EDialogType } from "../components/dialog/type";
import { getMBTI, introduction } from "../common";
import Options, { IOption } from "../options";

import Style from "../style.module.scss";

interface IUseStartWord {
  onStart: () => void;
}

const useTemplateWord = (props: IUseStartWord) => {
  const { onStart } = props;
  // 获取前言
  const getStartWord = () => {
    return {
      type: EDialogType.text,
      word: introduction,
      avatar: treeAvatar,
      layout: EDialogLayout.left,
      renderWord: (word) => (
        <>
          {word}
          <Button
            text="点击开始"
            className={Style["start-btn"]}
            type={EButtonType.Blue}
            onClick={() => {
              onStart();
            }}
          />
        </>
      ),
    };
  };
  // 获取测试结果消息类型
  function getMBTIResult(
    questionAnswers: Array<Omit<TAnswer, "paperId">>,
    resetProgress,
  ) {
    return {
      avatar: treeAvatar,
      renderWord: () => {
        return (
          <MBTIResult type={getMBTI(questionAnswers)} onReset={resetProgress} />
        );
      },
    };
  }

  // 获取右布局类型的消息
  function getRightTextWord(currentAnswer: string) {
    return {
      layout: EDialogLayout.right,
      word: currentAnswer,
      avatar: mineAvatar,
    };
  }
  // 获取左布局类型的消息
  function getLeftTextWord(word: string) {
    return {
      type: EDialogType.text,
      word,
      avatar: treeAvatar,
    };
  }
  // 获取选项消息
  function getOptionsWord(props: IOption) {
    return {
      render: (isLast, key) => <Options key={key} isLast={isLast} {...props} />,
    };
  }
  return {
    getStartWord,
    getMBTIResult,
    getRightTextWord,
    getLeftTextWord,
    getOptionsWord,
  };
};

export { useTemplateWord };
