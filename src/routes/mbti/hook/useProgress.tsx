/** 获取、记录、更新答题进度 */

import React, { useEffect, useState, useRef } from "react";

import {
  saveAnswer as saveAnswerApi,
  getAnswers,
  EPaperID,
  clearRecord,
} from "@/service/question";
import treeAvatar from "@/assets/mbti/tree-header.png";

import { TAnswer } from "@/service/question";
import { clickPv } from "@/utils/tool";

import { EDialogType, IDialogItem } from "../components/dialog/type";
import { questions } from "../common";
import { useTemplateWord } from "./useTemplateWord";

import Style from "../style.module.scss";

/**
 * 对话进行的推进依赖下列状态的变更
 * 包含用户刚进来消息记录的初始化、答题流程消息的渲染
 */
type TCurrentQuestionConfig = {
  // 当前答到第几道了
  currentIndex: number;
  // 当前这道题的答案是什么 - 用户选择的答案
  currentAnswer?: string;
  // 是否要展示当前用户回答
  showUserAnswer?: boolean;
  // 当前这道题是否已经答完
  currentHasFinished?: boolean;
  // 是否要包含之前所有的题目 - 初始化时需要
  includeBefore: boolean;
  // 所有打过答的答案 - 初始化时需要
  answers?: Omit<TAnswer, "paperId">[];
};

/**
 * 1. 用户刚进来根据答题记录，计算出已经完成作答的题目，组装出 n 道完整题目和第n + 1道题的题干、选项信息
 *    如果全部答玩，就要获取到性格类型对应的专业推荐
 * 2. 如果没答过题或重新作答，点击开始答题，开始答第一题，依次往对话记录里添加题干、选项，间隔一秒
 * 3. 作答后，push 用户选项，间隔一秒展示回复，在间隔一秒出现下一个问题
 * 4. 全部答完后，根据答题记录生成性格测试的结果包含专业推荐（从接口获取）
 */
const useProgress = () => {
  const [loading, setLoading] = useState<boolean>(false);
  // 对话进度配置信息
  const [currentQuestionIndex, setCurrentQuestionIndex] =
    useState<TCurrentQuestionConfig>();
  // 答题进度，包含已经答过的题目Id和答案
  const [questionAnswers, setQuestionAnswers] =
    useState<Omit<TAnswer, "paperId">[]>();
  // 初始化的时候就一句介绍语 - 可触发开始对话
  const {
    getStartWord,
    getMBTIResult,
    getRightTextWord,
    getLeftTextWord,
    getOptionsWord,
  } = useTemplateWord({
    onStart: () => {
      clickPv("ewt_h5_base_operation_career_personality_start_btn_click");
      // 开始作答后，再次点击无效
      setCurrentQuestionIndex((pre) =>
        pre?.currentIndex
          ? pre
          : {
              currentIndex: 1,
              includeBefore: false,
            },
      );
    },
  });
  // 对话记录，每一个元素就是一条消息记录
  const [dialogList, setDialogList] = useState<IDialogItem[]>([]);
  /**
   * 根据答题进度，生成对话记录
   * @param currentQuestion 第几题
   * @param includeBefore 是否包含之前的题目
   */
  function createWords(wordConfig: TCurrentQuestionConfig) {
    const {
      currentIndex,
      includeBefore,
      currentHasFinished,
      currentAnswer,
      answers,
      showUserAnswer,
    } = wordConfig;
    // 当前作答的题目
    const currentQuestion = questions[currentIndex - 1];
    // 初始化的时候，currentIndex 代表已经答完题目数量，因为是按顺序答的，所以直接从数组里从前往后截取
    // 需要把之前答过题目的消息一次性生成, 包含前言、题干、选项、用户的回答、回答的分析，如果全部答完还要包括最终的测试结果
    if (includeBefore) {
      var initDialogList: IDialogItem[] = [getStartWord()];
      questions.slice(0, currentIndex).map((item, idx) => {
        // 当前题目对应的用户回答
        const questionAnswer = answers.find((answer) => {
          return answer.questionId === item.id;
        });
        initDialogList = [
          ...initDialogList,
          // 每道题的题干
          {
            type: EDialogType.text,
            word: item.title,
            avatar: treeAvatar,
          },
          // 每道题的选项
          getOptionsWord({
            onSelect: (v, cb) =>
              updateProgress(
                {
                  answers: v,
                  questionId: item.id,
                  paperId: EPaperID.careerMBTI,
                },
                cb,
              ),
            initValue: questionAnswer.answers,
            options: item.options,
            optionClassName:
              // 处理某些选项字数超多的情况
              (idx === 1 || idx === 2) && Style["option-plus"],
          }),
          // 用户的回答
          getRightTextWord(questionAnswer.answers),
          // 用户回答的分析
          {
            word: item.options.find(
              (option) => option.answer === questionAnswer.answers,
            ).analysisText,
            avatar: treeAvatar,
          },
        ];
      });
      setDialogList(initDialogList);
      // 如果已经全部答完，就展示测试结果
      if (currentIndex === questions.length) {
        initDialogList.push(getMBTIResult(answers, resetProgress));
      } else if (currentIndex) {
        // 如果至少答了一题并且还没答完，就继续答下一题
        setCurrentQuestionIndex((pre) => ({
          ...pre,
          currentIndex: pre.currentIndex + 1,
          // 用户还没作答,自然就没有答案
          currentAnswer: null,
          showUserAnswer: false,
          currentHasFinished: false,
          includeBefore: false,
          answers: null,
        }));
      }
      /**
       * 下面展示的是一道一道题目依次作答的流程
       * 每次答题的流程：
       * 1. 先展示题干
       * 2. 一秒后展示选项
       * 3. 用户作答后，立马显示用户选择的答案
       * 4. 一秒后展示答案分析
       * 5. 一秒后开始开始答下一题
       * 6. 全部答完题目, 显示性格检测结果
       */
    } else if (currentIndex && !currentQuestion) {
      // 第六步
      setDialogList((pre) => {
        return [...pre, getMBTIResult(questionAnswers, resetProgress)];
      });
    } else if (showUserAnswer) {
      // 3. 显示用户的答案
      setDialogList((pre) => {
        return [...pre, getRightTextWord(currentAnswer)];
      });
    } else if (currentHasFinished) {
      // 4. 展示答案分析
      setDialogList((pre) => {
        return [
          ...pre,
          // 用户回答的分析
          getLeftTextWord(
            currentQuestion.options.find(
              (option) => option.answer === currentAnswer,
            ).analysisText,
          ),
        ];
      });
    } else {
      // 1. 题目题干
      setDialogList((pre) => [...pre, getLeftTextWord(currentQuestion.title)]);
      // 2. 一秒后展示选项
      setTimeout(() => {
        setDialogList((pre) => [
          ...pre,
          getOptionsWord({
            options: currentQuestion.options,
            optionClassName:
              (currentIndex === 2 || currentIndex === 3) &&
              Style["option-plus"],
            onSelect: (v, cb) =>
              updateProgress(
                {
                  answers: v,
                  questionId: currentQuestion.id,
                  paperId: EPaperID.careerMBTI,
                },
                cb,
              ),
          }),
        ]);
      }, 1000);
    }
  }
  useEffect(() => {
    if (!currentQuestionIndex) {
      return;
    }
    createWords(currentQuestionIndex);
  }, [currentQuestionIndex]);
  // 更新答题记录
  const updateRef = useRef<boolean>(false);
  const updateProgress = async (value: TAnswer, cb) => {
    try {
      if (updateRef.current) {
        return;
      }
      updateRef.current = true;
      setLoading(true);
      const res = await saveAnswerApi(value);
      cb();
      setQuestionAnswers((pre) => [...(pre || []), value]);
      // 先展示用户的回答
      setCurrentQuestionIndex((pre) => ({
        ...pre,
        currentAnswer: value.answers,
        showUserAnswer: true,
      }));
      // 间隔一秒出现性格分析
      setTimeout(() => {
        setCurrentQuestionIndex((pre) => ({
          ...pre,
          currentAnswer: value.answers,
          showUserAnswer: false,
          currentHasFinished: true,
        }));
      }, 1000);
      // 再间隔一秒开始出现下一题
      setTimeout(() => {
        setCurrentQuestionIndex((pre) => ({
          ...pre,
          currentIndex: pre.currentIndex + 1,
          currentAnswer: null,
          showUserAnswer: false,
          currentHasFinished: false,
        }));
      }, 2000);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
      updateRef.current = false;
    }
  };
  // 获取答过题目的id、答案
  const [showErrorTip, setShowErrorTip] = useState<boolean>(false);
  const getProgress = async () => {
    try {
      setLoading(true);
      setShowErrorTip(false);
      const { data } = await getAnswers(EPaperID.careerMBTI);
      /**
       * 对获取到的答案进行合法性校验
       * 1. 答案的顺序必须是正确的，从1到n按序对应
       * 2. 最多四个答案
       * 3. 答案必须是题目选项之一
       * 如果不满足就截断处理，舍弃之后的所有答案，让用户重新答题
       */
      const newResData = (data || [])
        .filter((item) => !!item)
        .slice(0, questions.length);
      // 获取的答案列表，可能和题目顺序不一致，如果不一致那就是不合法的数据需要过滤掉
      const newData = [];
      for (let i = 0; i < newResData.length; i++) {
        if (
          newResData[i].questionId !== questions[i].id ||
          questions[i].options.every((option) => {
            return option.answer !== newResData[i].answers;
          })
        ) {
          break;
        }
        newData.push(newResData[i]);
      }
      setQuestionAnswers(newData || []);
      setCurrentQuestionIndex({
        includeBefore: true,
        currentIndex: (newData || []).length,
        answers: newData,
      });
    } catch (error) {
      setShowErrorTip(true);
      console.error(error);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    getProgress();
  }, []);

  // 重置进度
  const resetProgress = async () => {
    try {
      setLoading(true);
      const { data } = await clearRecord({ paperId: EPaperID.careerMBTI });
      if (data) {
        // 答案列表重置
        setQuestionAnswers([]);
        // 当前答题配置重置
        setCurrentQuestionIndex(null);
        // 对话记录重置
        setDialogList([getStartWord()]);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };
  return {
    showErrorTip,
    loading,
    questionAnswers,
    dialogList,
    getProgress,
    setDialogList,
    updateProgress,
    resetProgress,
  };
};

export { useProgress };
