/** 渲染每条消息 */

import React, { useEffect } from "react";

import { useScrollToView } from "@/hooks";
import { cls } from "@/utils/tool";

import { EDialogLayout, EDialogType, IDialogItem } from "./type";

import Style from "./style.module.scss";

interface IWord extends IDialogItem {
  // 是否是最后一条消息，如果是就默认滚动到可见
  isLastWord?: boolean;
}

const Word: React.FC<IWord> = (props) => {
  const {
    isLastWord,
    type,
    avatar,
    avatarClassName,
    word,
    wordClassName,
    layout,
    renderWord,
  } = props;
  const { containerRef, scrollToView } = useScrollToView();
  // 默认滚动到结果组件
  useEffect(() => {
    if (isLastWord) {
      scrollToView({
        behavior: "auto",
      });
    }
  }, [isLastWord]);
  return (
    <div
      ref={containerRef}
      className={cls([
        Style["word"],
        layout === EDialogLayout.right && Style["word-right"],
      ])}
    >
      {/* 有头像 */}
      {type !== EDialogType.centerWord && (
        <div className={cls([Style["avatar"], avatarClassName])}>
          <img src={avatar} />
        </div>
      )}
      <div className={cls([Style["desc"], wordClassName])}>
        {renderWord ? renderWord(word) : word}
      </div>
    </div>
  );
};

export default Word;
