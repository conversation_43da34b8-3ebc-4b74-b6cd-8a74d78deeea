.dialog-container {
  transform: translateY(-4px);
  padding: 20px 16px 82px;
  position: relative;
  min-height: calc(100% - 176px);
  background: #DCF0F7;
  box-shadow: inset 0 2px 0 0 #FFFFFF;
  border-radius: 8px 8px 0 0;
}

.word {
  display: flex;
  opacity: 0;
  animation: slideUpFadeIn 0.3s ease-out forwards;
  &:not(:first-child) {
    margin-top: 16px;
  }
}

@keyframes slideUpFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-animate {
  animation: slideUpFadeIn 0.3s ease-out forwards;
}

.word-right {
  flex-direction: row-reverse;
  justify-content: end;
  align-items: center;

  .avatar {
    margin-right: 0px;
    margin-left: 10px;
  }

  .desc {
    width: 60px;
    text-align: center;
    height: 44px;
    font-weight: bold;
    font-size: 14px;
    border-radius: 20px 20px 0px 20px;
    background: #FFFFFF;
  }
}

.avatar {
  margin-right: 10px;
}

.desc {
  padding: 12px;
  width: 297px;
  white-space: pre-wrap;
  background-image: linear-gradient(0deg, #F0FBFF 0%, #FFFFFF 100%);
  border: 1px solid #FFFFFF;
  box-shadow: 0 1px 2px 0 #B1D5E2, inset 0 -2px 11px 0 #C4EDFA;
  border-radius: 20px 20px 20px 0;
  font-size: 14px;
  color: #333333;
}

.avatar {
  width: 36px;
  height: 36px;

  img {
    width: 100%;
    height: 100%;
  }
}

.center-word {
  width: 297px;
  height: 44px;
  margin: 0 auto;
  background: #FFFFFF;
  border: 1px solid #FFFFFF;
  box-shadow: inset 0 -2px 11px 0 #C4EDFA;
  border-radius: 22px;
  text-align: center;
  font-weight: bold;
  font-size: 14px;
  color: #333333;
}