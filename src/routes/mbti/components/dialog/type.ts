export interface Option {
  id: string;
  /** 题目答案 */
  answer: string;
  /** 与选项相关的图标 */
  icon: string;
  /** 选项的简短描述 */
  desc: string;
  /** 针对答案的简要分析 */
  analysis: string;
  /** 提供详细解释的分析 - 文本 */
  analysisText: string;
}

export interface Question {
  /** 题目 id */
  id: string;
  /** 题目标题 */
  title: string;
  /** 题目选项 */
  options: Option[];
}

/** 对话记录的类型，不同类型不会不同的交互、样式 */
export enum EDialogType {
  /** 没有头像，水平居中的消息 */
  centerWord,
  /** 纯文字的消息 */
  text,
}

/** 对话布局 - 左|右 */
export enum EDialogLayout {
  left,
  right,
}
/** 每条对话的定义 */
export interface IDialogItem {
  id?: string | number;
  /** 对话类型 */
  type?: EDialogType;
  /** 头像的类定义 */
  avatarClassName?: string;
  /** 头像 */
  avatar?: string;
  /** 对话内容 */
  word?: string;
  /** 对话内容的类定义 */
  wordClassName?: string;
  /** 对话布局 */
  layout?: EDialogLayout;
  /** 支持自定义渲染整条消息 */
  render?: (isLastWord: boolean, key: string | number) => React.ReactNode;
  /** 支持自定义渲染消息内容 */
  renderWord?: (word) => React.ReactNode;
}
