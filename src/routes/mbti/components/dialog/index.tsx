import React from "react";

import { cls } from "@/utils/tool";

import { Question, Option, IDialogItem } from "./type";
import Word from "./word";

import Style from "./style.module.scss";

export interface IDialog {
  // 对话记录
  dialogList: IDialogItem[];
  // 答题记录
  answerRecord?: string[];
  // 题目列表
  questions?: Questions;
  wrapperClassName?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
  // 开始答题
  onStart?: () => void;
}

export type Questions = Question[];

const Dialog: React.FC<IDialog> = (props) => {
  // 对话列表
  const {
    dialogList,
    questions,
    answerRecord,
    wrapperClassName,
    children,
    ...rest
  } = props;

  return (
    <div
      className={cls([Style["dialog-container"], wrapperClassName])}
      {...rest}
    >
      {children}
      {/* 依次渲染每条消息记录 */}
      {(dialogList || []).map((dialog, idx) => {
        const key = dialog.id || idx;
        const isLastWord = idx === dialogList.length - 1;
        return dialog.render ? (
          dialog.render(isLastWord, key)
        ) : (
          <Word isLastWord={isLastWord} key={key} {...dialog} />
        );
      })}
    </div>
  );
};

export default Dialog;
