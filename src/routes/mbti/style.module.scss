.mbti-background {
  height: 175px;
  width: 375px;
  display: block;
}

.page-title {
  font-weight: bold;
  font-size: 18px;
  color: #333333;
  line-height: 20px;
}

.start-btn {
  margin-top: 12px;
  width: 273px;
  height: 34px;
  background-image: linear-gradient(270deg, #0387FF 0%, #02AAFF 100%);
  border-radius: 17px;
}

.talk-container {
  width: 375px;
  background: #DCF0F7;
  box-shadow: inset 0 2px 0 0 #FFFFFF;
  border-radius: 8px 8px 0 0;
}

@keyframes slideUpFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.options-container {
  animation: slideUpFadeIn 0.5s ease-out forwards;
  margin-top: 16px;
  .option-container {
    display: flex;
    position: relative;
    align-items: center;
    justify-content: flex-end;
    &:not(:first-child) {
      margin-top: 16px;
    }
    img {
      position: absolute;
      left: 18px;
      width: 52px;
      height: 52px;
    }
  }
  .option {
    width: 297px;
    height: 44px ;
    background: #FFFFFF;
    border: 1px solid #FFFFFF;
    box-shadow: inset 0 -2px 11px 0 #C4EDFA;
    border-radius: 22px;
    font-weight: bold;
    font-size: 14px;
    color: #333333;
    text-align: center;
    padding: 12px 20px;
  }
}

.option-plus {
  height: 64px !important;
  border-radius: 32px !important;
}

.option-disabled {
  color: #8A8A8A !important;
}

.option-selected {
  border: 1px solid #2E86FF !important;
}

.mbti-result {
  position: relative;
  &-top {
    display: flex;
    justify-content: space-between;
    &-left {
      flex: 0 0 179px;
      color: #333333;
      font-size: 12px;
      & > :not(:first-child) {
        margin-top: 4px;
      }
      &-title {
        font-weight: bold;
        font-size: 14px;
      }
      &-result {
        font-weight: bold;
        font-size: 20px;
        color: #2E86FF;
      }
    }
    &-right {
      flex: 0 0 80px;
      height: 118px;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  &-middle {
    margin-top: 13.25px;

    &-item {
      padding: 8px 12px 8px 8px;
      width: 273px;
      background: #F4F5FF;
      border-radius: 4px;
      border-left: 4px solid #B7DFFF;

      & > :not(:first-child) {
        margin-top: 4px;
      }

      &-title {
        font-weight: bold;
        font-size: 14px;
        color: #333333;
      }

      &-desc {
        font-size: 12px;
        color: #666666;
      }

      &-major-container {
        display: flex;
        flex-wrap: wrap;
        margin-top: 0 !important;
        &-item {
          display: flex;
          padding: 4px 8px;
          margin-top: 10px;
          margin-left: 10px;
          align-items: center;
          justify-content: space-between;
          // height: 26px;
          background: #FFFFFF;
          border: 1px solid #2E86FF;
          border-radius: 4px;

          &-name {
            font-weight: bold;
            font-size: 12px;
            color: #2E86FF;
          }
  
          &-img-container {
            display: flex;
            margin-left: 2px;
            img {
              width: 5px;
              height: 8px;
            }
          }
        }
      }
      &:not(:first-child) {
        margin-top: 12px;
      }
    }
    &-major-item {
      background-color: #EEF7FF;
      padding-left: 0px;
      .mbti-result-middle-item-title {
        padding-left: 12px;
      }
    } 
  }

  &-footer {
    width: 273px;
    height: 46px;
    display: flex;
    align-items: flex-end;
    &-btn {
      width: 100%;
      height: 34px;
      background-image: linear-gradient(270deg, #0387FF 0%, #02AAFF 100%);
      border-radius: 17px;
    }
  }

  &-reTest {
    bottom: -28px;
    transform: translateY(100%);
    position: absolute;
    color: #2e86ff;
    font-size: 12px;

    span {
      text-decoration: underline;
    }

    &-icon-arrow {
      font-size: 12px;
    }
  }
}