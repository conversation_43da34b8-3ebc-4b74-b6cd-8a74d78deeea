/**
 * 生涯性格测试
 */

import * as React from "react";
import mstJsBridge from "mst-js-bridge";

import backBlackPNG from "@/assets/common/back_black.png";
import topImg from "@/assets/mbti/background.jpg";
import ErrorInfo from "@/components/error-info";
import { Layout } from "@/components";

import { useProgress } from "./hook";
import Dialog from "./components/dialog";

import Style from "./style.module.scss";

export const Component: React.FC = () => {
  const { loading, dialogList, showErrorTip, getProgress } = useProgress();
  return (
    <Layout
      showHeader
      setChildrenContainerHeight={({ top, bottom }) => {
        return `calc(100vh - ${top || 0}px - ${bottom || 0}px)`;
      }}
      pageLoadingProps={{
        color: "none",
      }}
      headerProps={{
        left: <div className={Style["page-title"]}>解锁你的性格密码</div>,
        backIconUrl: backBlackPNG,
        onBack: () => {
          mstJsBridge.closeWebview();
        },
      }}
      pageLoading={loading}
    >
      <div
        style={{
          overflow: "scroll",
          maxHeight: `calc(100% - 12.8vw)`,
          height: `calc(100% - 12.8vw)`,
          background: "#DCF0F7",
        }}
      >
        <img className={Style["mbti-background"]} src={topImg} />
        {showErrorTip ? (
          <ErrorInfo
            onRetry={() => {
              getProgress();
            }}
            type="light"
            style={{
              minHeight: "calc(100% - 46.7vw)",
            }}
          />
        ) : (
          <Dialog dialogList={dialogList} />
        )}
      </div>
    </Layout>
  );
};
