import optionAImg from "@/assets/mbti/choose-a.png";
import optionBImg from "@/assets/mbti/choose-b.png";
import INTJPng from "@/assets/mbti/INTJ.png";
import ENFJPng from "@/assets/mbti/ENFJ.png";
import ENFPPng from "@/assets/mbti/ENFP.png";
import ENTJPng from "@/assets/mbti/ENTJ.png";
import ENTPPng from "@/assets/mbti/ENTP.png";
import ESFPPng from "@/assets/mbti/ESFP.png";
import ESTPPng from "@/assets/mbti/ESTP.png";
import INFJPng from "@/assets/mbti/INFJ.png";
import INFPPng from "@/assets/mbti/INFP.png";
import INTPPng from "@/assets/mbti/INTP.png";
import ISFJPng from "@/assets/mbti/ISFJ.png";
import ISFPPng from "@/assets/mbti/ISFP.png";
import ISTPPng from "@/assets/mbti/ISTP.png";
import ISTJPng from "@/assets/mbti/ISTJ.png";
import ESFJPng from "@/assets/mbti/ESFJ.png";
import ESTJPng from "@/assets/mbti/ESTJ.png";

import { Questions } from "./components/dialog";
import { TAnswer } from "@/service/question";

/** 题目、答案、性格分析 */
export const questions: Questions = [
  {
    id: "1",
    title:
      "好的，让我们开始吧～刚才我在解一道超复杂的根系营养题，如果是你遇到难题的话会...？",
    options: [
      {
        id: "1-1",
        answer: "A",
        icon: optionAImg,
        desc: "打电话找同学讨论思路",
        analysis: "E",
        analysisText:
          "哇，通过交流充电成功！你的【能量来源】维度属于外向型（E），就像光合作用需要阳光呢！",
      },
      {
        id: "1-2",
        answer: "B",
        icon: optionBImg,
        desc: "自己查资料尝试解决",
        analysis: "I",
        analysisText:
          "嗯，专注内在就能蓄能！你的【能量来源】维度属于内向型（I），就像深根吸收大地养分呢！",
      },
    ],
  },
  {
    id: "2",
    title: "今天学校组织我们去博物馆研学，有两个任务，你想试试哪个？",
    options: [
      {
        id: "2-1",
        answer: "A",
        icon: optionAImg,
        desc: "文物修复体验：跟着老师学习青铜器除锈技术，完成一件可带走的仿古工艺品",
        analysis: "S",
        analysisText: "你更关注具体细节呀！你的【认知方式】维度属于感觉型（S）",
      },
      {
        id: "2-2",
        answer: "B",
        icon: optionBImg,
        desc: '策展动线设计：明代书画展设计故事线，用AR技术让古画"活"过来',
        analysis: "N",
        analysisText: "你擅长构思可能性呢！你的【认知方式】维度属于直觉型（N）",
      },
    ],
  },
  {
    id: "3",
    title: "如果你的好友考试失利，安慰ta时，你通常会？",
    options: [
      {
        id: "3-1",
        answer: "A",
        icon: optionAImg,
        desc: "帮ta分析此次考试具体失利的原因，讨论应对方案",
        analysis: "T",
        analysisText: "理性分析超精准！你的【决策模式】维度属于思考型（T）",
      },
      {
        id: "3-2",
        answer: "B",
        icon: optionBImg,
        desc: "跟ta一起吐槽这次考试太难，或帮ta转移注意力",
        analysis: "F",
        analysisText:
          "你真是个温暖的安慰者～你的【决策模式】维度属于情感型（F）",
      },
    ],
  },
  {
    id: "4",
    title: "暑假要到了，你会如何安排？",
    options: [
      {
        id: "4-1",
        answer: "A",
        icon: optionAImg,
        desc: "列出每天详细时间表",
        analysis: "J",
        analysisText:
          "你是严谨的时间规划师！你的【适应模式】维度属于计划型（J）",
      },
      {
        id: "4-2",
        answer: "B",
        icon: optionBImg,
        desc: "设定几个关键目标即可",
        analysis: "P",
        analysisText:
          "你是灵活的目标掌控者！你的【适应模式】维度属于灵活型（P）",
      },
    ],
  },
];

// 根据用户的答题记录获取对应的性格类型
export function getMBTI(answers: Omit<TAnswer, "paperId">[]): TMbti {
  if (!answers) {
    return;
  }
  /** questions 是前端本地维护的问题列表，包括问题的选项，每种选项对应的解读、性格类型 */
  return answers.reduce((pre, now, idx) => {
    const question = questions.find((item) => item.id === now.questionId);
    if (question) {
      const option = question.options.find((op) => op.answer === now.answers);
      return pre + option.analysis;
    }
    return pre;
  }, "") as TMbti;
}

/** 对话记录 */
export const introduction = `登岛摘星梦，伸手触苍穹。\nHi，欢迎来到摘星岛呀，在这里，梦想触手可及。接下来，请跟随我的步伐，按照你的第一直觉做出选择，就能得到你的性格密码和专属专业推荐~
现在要试试吗？`;

/** 不同类型对应的分析 */
export type TMbti = keyof typeof mbtiProfiles;
export const mbtiProfiles = {
  INTJ: {
    logo: INTJPng,
    name: "INTJ战略建筑师",
    description: "富有远见的规划者，擅长将复杂目标拆解为可行步骤",
    strengths: "系统思维、决策果断、独立性强",
    growthAdvice: "在高中参与模联/商业模拟活动，学习团队协作与柔性管理",
  },
  INTP: {
    logo: INTPPng,
    name: "INTP逻辑发明家",
    description: "好奇心驱动的理论家，热衷探索知识底层逻辑",
    strengths: "概念分析、创新思维、知识整合",
    growthAdvice: "考虑在高中加入机器人社团，将抽象理论转化为实体作品",
  },
  ENTJ: {
    logo: ENTJPng,
    name: "ENTJ领袖指挥官",
    description: "目标导向的领导者，善于调动资源达成结果",
    strengths: "战略视野、执行力强、抗压能力",
    growthAdvice: "担任班长/社团负责人，培养同理心与风险意识",
  },
  ENTP: {
    logo: ENTPPng,
    name: "ENTP跨界辩论家",
    description: "思维敏捷的挑战者，擅长多角度解构问题",
    strengths: "快速学习、概念迁移、应变能力",
    growthAdvice: "参加学术主题的辩论赛，建立系统性知识框架",
  },
  INFJ: {
    logo: INFJPng,
    name: "INFJ心灵引航者",
    description: "富有洞察力的理想主义者，擅长激励深层共鸣",
    strengths: "共情能力、价值观驱动、创意表达",
    growthAdvice: "组织公益实践，平衡理想主义与现实落地",
  },
  INFP: {
    logo: INFPPng,
    name: "INFP诗意调解者",
    description: "敏感细腻的治愈者，追求精神世界的和谐",
    strengths: "隐喻思维、艺术感知、人文关怀",
    growthAdvice: "创作校园心理剧，将抽象情感具象化表达",
  },
  ENFJ: {
    logo: ENFJPng,
    name: "ENFJ成长催化师",
    description: "富有魅力的赋能者，善于激发他人潜能",
    strengths: "沟通激励、资源整合、责任感强",
    growthAdvice: "开发知识分享自媒体，避免过度付出",
  },
  ENFP: {
    logo: ENFPPng,
    name: "ENFP灵感探险家",
    description: "热情洋溢的创意者，热衷探索多元可能性",
    strengths: "联想创新、人际敏感、适应力强",
    growthAdvice: "使用番茄钟法推进跨学科项目（如AI诗歌生成）",
  },
  ISTJ: {
    logo: ISTJPng,
    name: "ISTJ务实规划师",
    description: "严谨可靠的组织者，擅长建立稳定秩序",
    strengths: "细节把控、流程管理、执行力强",
    growthAdvice: "参与实验性课题（如3D打印文物修复），培养创新思维",
  },
  ISFJ: {
    logo: ISFJPng,
    name: "ISFJ温暖守护者",
    description: "细致入微的照顾者，默默维护集体和谐",
    strengths: "信息整合、服务意识、持久耐力",
    growthAdvice: "尝试知识图谱工具，突破经验依赖模式",
  },
  ESTJ: {
    logo: ESTJPng,
    name: "ESTJ高效管理者",
    description: "目标明确的实干家，善于推进标准化流程",
    strengths: "决策效率、责任担当、资源协调",
    growthAdvice: "参与校园创业项目，学习应对不确定性",
  },
  ESFJ: {
    logo: ESFJPng,
    name: "ESFJ社交筑桥者",
    description: "温暖贴心的协调者，擅长维系人际关系",
    strengths: "同理心强、执行力高、危机调解",
    growthAdvice: "策划文化传承活动（如汉服社运营），培养批判思维",
  },
  ISTP: {
    logo: ISTPPng,
    name: "ISTP技术鉴赏家",
    description: "冷静敏锐的实践家，热衷解决即时问题",
    strengths: "工具运用、危机处理、空间思维",
    growthAdvice: "选修或自学创客课程（如智能硬件开发），补足理论深度",
  },
  ISFP: {
    logo: ISFPPng,
    name: "ISFP艺术探索家",
    description: "感性自由的创作者，善于捕捉瞬间美感",
    strengths: "审美表达、共情能力、即兴发挥",
    growthAdvice: "尝试数据可视化艺术，提升逻辑结构化能力",
  },
  ESTP: {
    logo: ESTPPng,
    name: "ESTP机会捕手",
    description: "灵活机敏的行动派，善于把握当下机遇",
    strengths: "快速反应、风险预判、实践创新",
    growthAdvice: "参加急救培训/商业挑战，转化冒险精神为可控策略",
  },
  ESFP: {
    logo: ESFPPng,
    name: "ESFP活力表演家",
    description: "热情洋溢的体验者，擅长营造积极氛围",
    strengths: "环境适应、人际联结、即时行动",
    growthAdvice: "尝试策划知识类短视频（如实验生活化），强化深度思考",
  },
};

// 专业详情地址
export const professionalDetailPath =
  "/career/careerapp/index/#/professional-details";
