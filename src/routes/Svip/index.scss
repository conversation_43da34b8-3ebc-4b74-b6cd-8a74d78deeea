.svip_page {
  padding: 0 12px 24px;
  background-color: #f3f4f8;
  .bannerWrapper {
    padding-top: 24px;
    position: relative;
    height: 120px;
    box-sizing: content-box;
    background-size: 100%;
    background-repeat: no-repeat;
    .banner {
      height: 120px;
      width: 100%;
      position: absolute;
      z-index: 0;
    }
  }
  &_user {
    position: relative;
    padding-top: 18px;
    display: flex;
    padding: 18px 16px;
    img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      margin-right: 8px;
    }
    &_nickname {
      font-size: 16px;
      font-weight: 500;
      color: #543309;
      line-height: 22px;
      text-shadow: 0px 16px 16px 0px rgba(230, 194, 137, 0.4);
    }
  }
  &_title {
    font-weight: 500;
    color: #434f59;
    font-size: 20px;
    line-height: 28px;
  }
  &_more,
  &_lesson {
    opacity: 1;
    background: #fff;
    border-radius: 12px;
    background-repeat: no-repeat;
    background-size: 56% 46px;
    background-position-x: 4px;
    padding: 12px;
    img {
      width: 100%;
    }
  }
  &_lesson {
    margin-top: 12px;
    p:nth-child(2) {
      padding-bottom: 10px;
    }
    &_img {
      &_icon {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        min-height: 188px;
        span {
          font-size: 14px;
          font-weight: 400;
          text-align: center;
          color: #a7acb9;
          line-height: 20px;
          padding-top: 8px;
        }
        img {
          width: 120px;
        }
      }
    }
    &_tab {
      display: flex;
      flex-flow: row  wrap;
      margin-bottom:16px;
      span {
        margin: 16px 16px 0 0;
        font-size: 12px;
        background-color: #f3f4f8;
        border-radius: 12px;
        padding: 4px 8px;
      }
      .active {
        background: linear-gradient(270deg, #f3d3a0, #b08748);
        color: #fff;
      }
    }
  }
  .svip-page-tab {
    display: flex;
    flex-flow: row nowrap ;
    justify-content: space-around;
    padding: 16px 0;
    .svip-page-tab-list{
      color:#434F59;
      position:relative;
      font-size: 14px;
    }
    .active {
      color:#543309;
      &:before{
        content: ' ';
        width:24px;
        height: 4px;
        background-image: linear-gradient(90deg, #F3D3A0 0%, #D4AB6B 100%);
        border-radius: 2px;
        position:absolute;
        transform: translate(-50%,-50%);
        left: 50%;
        bottom: -22%;
      }
    }
  }
  .LeftTitle{
    width: 56px;
    height: 24px;
    background-image: linear-gradient(269deg, #F3D3A0 0%, #B08748 100%);
    border-radius: 2px;
    position:relative;
    font-size:14px;
    padding-left: 8px;
    color:#fff;
    line-height:24px;
    margin-bottom:12px;
    margin-left:-10px;
    &:before {
      content: '';
      width: 0;
      height: 0;
      border-top: 12px solid transparent;
      border-right: 14px solid #fff;
      border-bottom: 12px solid transparent;
      position: absolute;
      transform: translate(0,-50%);
      right:0;
      top:50%;
    }
  }
  .svip-page-course{
    //display: flex;
    //flex-flow: row wrap;
    overflow-x: scroll;
    display: -webkit-box;
    margin-bottom:24px;
    width: 340px;
    .svip-page-course-list{
      width:159px;
      margin-right: 8px;
      //margin-top: 8px;
      .course-list-Img{
        position:relative;
        .main-Img{
          //width:100%;
          width: 159px;
          height: 88px;
          border-radius: 5px;
        }
        .watch{
          position:absolute;
          left:8px;
          top:62px;
          opacity: 0.45;
          background: #434F59;
          height: 18px;
          border-radius: 2px;
          display: flex;
          align-items: center;
          img{
            width:12px;
            margin-left:4px;
            margin-right:2px;
          }
          .title{
            font-size: 12px;
            color:#fff;
            line-height: 18px;
            padding-right: 3px;
          }
        }
      }
      .course-list-title{
        font-size: 14px;
        color: #434F59;
        padding: 8px;
        height: 48px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      .course-compere{
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
        margin-top:4px;
        img{
          width: 14px;
          vertical-align: middle;
        }
        .title{
          opacity: 0.65;
          font-size: 12px;
          color: #434F59;
          padding-left: 2px;
        }
      }
    }
  }
  .svip-page-paper{
    width: 327px;
    background: #FFFFFF;
    box-shadow: 0 0 12px 0 rgba(230,194,137,0.20);
    border-radius: 8px;
    margin: 0 auto;
    padding: 12px;
    ul{
      background: linear-gradient(
          0deg, #F3F4F8 0%, #FFFFFF 97%);
    }
    li{
      margin-top: 12px;
      overflow: hidden;
      background: #fff;
      padding: 12px;
    }
    .paper-head-list{
      display: flex;
      flex-flow: row nowrap;
      justify-content: space-between;
      .paper-title{
        width: 190px;
        font-size: 14px;
        color: #434F59;
        letter-spacing: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        line-clamp: 2;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      .paper-accuracy{
        //width: 73px;
        color:#2E86FF;
      }
    }
    .paper-head{
      padding-top: 12px;
      overflow: hidden;
      line-height: 24px;
      .paper-head-topic{
        font-size: 12px;
        color: #543309;
      }
      .paper-head-topic-num{
        padding-left: 5px;
      }
      .paper-head-people{
        font-size: 12px;
        color: #543309;
        padding-left: 20px;
      }
      .paper-head-num{
        padding-left: 5px;
      }
      .paper-btn{
        width: 82px;
        height: 24px;
        background-image: linear-gradient(269deg, #F3D3A0 0%, #B08748 100%);
        border-radius: 12px;
        float: right;
        img{
          width: 14px;
          vertical-align: middle;
          margin-left: 8px;
        }
        .paper-go{
          font-size: 12px;
          color: #FFFFFF;
          line-height: 24px;
          padding-left: 5px;
        }
      }
    }
  }
  .expireTime {
    color: #543309;
    font-weight: 400;
    font-size: 12px;
    // font-family:"PingFangSC, PingFangSC-Regular";
  }
  .svip_page_train_img {
    // margin-bottom: 14px;
  }
  .train_list {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0px 0px 12px 0px rgba(230, 194, 137, 0.2);
    margin-bottom: 10px;
    &_item {
      display: flex;
      height: 42px;
      align-items: center;
      border-bottom: 1px dotted #d0d3d5;
      padding: 0 18px;
      justify-content: space-between;
      // margin-bottom: 16px;
      span {
        font-size: 12px;
        font-weight: 400;
        text-align: left;
        color: #434f59;
        line-height: 17px;
      }
      img {
        width: 14px;
      }
      &:last-child {
        border-bottom: none;
      }
    }
  }
}
