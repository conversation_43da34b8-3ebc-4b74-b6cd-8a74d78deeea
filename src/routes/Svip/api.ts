import request from "@/utils/request";

export const getCurrentUser = () =>
  request.get("/api/usercenter/user/baseinfo");
export const getCatalogNodeList = () =>
  request.get("/api/studyprod/svip/web/subject/getCatalogNodeList");
export const getBindContentList = (params: { categoryId: number }) =>
  request.get("/api/studyprod/svip/web/subject/getBindContentList", { params });
export const getAnswerUrl = () => request.get("/api/answerprod/web/answer/url");
