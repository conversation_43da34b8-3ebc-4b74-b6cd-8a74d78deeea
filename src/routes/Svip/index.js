import React from "react";
import * as api from "./api";
import "./index.scss";
import moment from "moment";
import SvipBanner from "./assets/svipBanner.png";
import MoreImg from "./assets/svip_power.png";
import Suyang from "./assets/suyang.png";
import LessonEmpty from "./assets/lesson_empty.png";
import JobAbility from "./assets/jobAbility.png";
import LeaderShip from "./assets/leadership.png";
import Creativity from "./assets/creativity.png";
import PlayCounts from "./assets/playcounts.png";
import Right from "./assets/arrow.png";
import TopColor from "./assets/topColor.png";
import Bg1 from "./assets/bg_1.png";
import Bg2 from "./assets/bg_2.png";
import Bg3 from "./assets/bg_3.png";
import teacher from "./assets/teacher.png";
import pencil from "./assets/pencil.png";
import { openVideoPackage, openUrlInWebView } from "~/utils/tool";
import PageLoading from "@/components/page-loading";
import { cls } from "@/utils/tool";

const diffLeadershipContentList = [
  {
    name: "中学生领导力课程",
    coursePackageId: "1503",
  },
  {
    name: "领导力课程-什么是领导力？",
    coursePackageId: "13623",
  },
  {
    name: "领导力课程-自我领导力",
    coursePackageId: "13627",
  },
  {
    name: "领导力课程-践行领导力",
    coursePackageId: "13628",
  },
];

const innovationContentList = [
  {
    name: "中学生创新课程",
    coursePackageId: "1501",
  },
];

const jobContentList = [
  {
    name: "创业概论：带你“入门”创业",
    coursePackageId: "9798",
  },
  {
    name: "武大商帮和校友经济",
    coursePackageId: "9839",
  },
  {
    name: "认知升级：基于资本思维的商业模式构建",
    coursePackageId: "9907",
  },
  {
    name: "中国艺术品拍卖行业透析",
    coursePackageId: "9908",
  },
  {
    name: "从巨成发展谈创业创新",
    coursePackageId: "9909",
  },
  {
    name: "现代企业经营模式与实战",
    coursePackageId: "9910",
  },
  {
    name: "创投、创业、创新",
    coursePackageId: "9913",
  },
  {
    name: "医疗健康产业的股权投资",
    coursePackageId: "9927",
  },
  {
    name: "不忘初心 创造价值 勇争第一",
    coursePackageId: "9928",
  },
  {
    name: "创业是最大的社会公益",
    coursePackageId: "9929",
  },
  {
    name: "From 0 to 1 billion USD",
    coursePackageId: "9930",
  },
  {
    name: "创业成就别样人生",
    coursePackageId: "11242",
  },
  {
    name: "营销无极限",
    coursePackageId: "11243",
  },
  {
    name: "坚持才能让梦想照进现实",
    coursePackageId: "11244",
  },
  {
    name: "请跟师兄师姐一起去创业",
    coursePackageId: "11245",
  },
  {
    name: "积淀创业素质，备战事业生涯",
    coursePackageId: "11246",
  },
  {
    name: "投资、企业文化与创业",
    coursePackageId: "11247",
  },
  {
    name: "我的商业思考：尊重常识、坚守专业、勇于放弃、顺势有为",
    coursePackageId: "11248",
  },
  {
    name: "创业史及企业家精神",
    coursePackageId: "11249",
  },
  {
    name: "创业过程中的资源整合",
    coursePackageId: "11250",
  },
  {
    name: "快乐创业",
    coursePackageId: "11251",
  },
];

// eslint-disable-next-line react/prop-types
const LinkList = ({ list = [], img }) => (
  <div className="train_list">
    <img src={img} />
    {list.map((i) => (
      <div
        key={i.coursePackageId}
        onClick={() => openVideoPackage(i.coursePackageId)}
        className="train_list_item"
      >
        <span>{i.name}</span>
        <img src={Right} />
      </div>
    ))}
  </div>
);

class Svip extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      active: 0,
      getCurrentUserData: [],
      TabGrade: [],
      ContentList: {},
      Typename: "",
      currentUser: null,
    };
  }

  componentDidMount() {
    this.getCurrentUser();
    document.addEventListener("visibilitychange", this.visibilityListener);
  }
  visibilityListener() {
    switch (document.visibilityState) {
      case "hidden":
        // videoElement.pause();
        break;
      case "visible":
        location.reload();
        break;
    }
  }
  getCurrentUser = async () => {
    try {
      const [{ data: nodeList }, { data: currentUser }] = await Promise.all([
        api.getCatalogNodeList(),
        api.getCurrentUser(),
      ]);
      const data = nodeList.map((item, index) => {
        item.active = index === 0;
        item.index = index;
        return item;
      });
      this.setState(
        {
          getCurrentUserData: data,
          TabGrade: (data[0] && data[0].childrenList) || [],
          currentUser,
        },
        () => {
          if (data[0] && data[0].childrenList) {
            const { id } = data[0].childrenList[0];
            this.getBindContentList(id);
          }
        },
      );
    } catch (error) {
      console.log(error);
    }
  };
  changeTab(item, idx) {
    this.setState(
      {
        active: idx,
      },
      () => {
        const { id } = item;
        this.getBindContentList(id);
      },
    );
  }
  ChangePageTab(item, index) {
    const { getCurrentUserData } = this.state;
    if (item.active) return;
    let TabGrade = [];
    let Typename = "";
    const data = getCurrentUserData.map((item1, i) => {
      if (i === index) {
        TabGrade = item1.childrenList || [];
        Typename = item1.categoryName;
      }
      return Object.assign({}, item1, { active: i === index });
    });
    this.setState(
      {
        getCurrentUserData: data,
        TabGrade,
        active: 0,
        Typename,
      },
      () => {
        if (TabGrade.length > 0) {
          const { id } = TabGrade[0];
          this.getBindContentList(id);
        }
      },
    );
  }
  async getBindContentList(categoryId) {
    const { data } = await api.getBindContentList({ categoryId });
    this.setState({
      ContentList: data,
    });
  }
  LeftTitle(title) {
    return <div className={"LeftTitle"}>{title}</div>;
  }

  async linkman(item) {
    const { finish, paperId, bizCode, platform, reportId } = item;
    const {
      data: { answerUrl, reportUrl },
    } = await api.getAnswerUrl();
    let url = `${finish ? reportUrl : answerUrl}?paperId=${paperId}&bizCode=${bizCode}&platform=${platform}`;
    if (reportId) {
      url = `${url}&reportId=${reportId}`;
    }
    openUrlInWebView(url, "至尊会员专区");
  }
  render() {
    const { active, getCurrentUserData, TabGrade, ContentList, currentUser } =
      this.state;
    if (!currentUser) {
      return <PageLoading />;
    }
    const { expireTime, nickName, photoUrl } = currentUser;
    const expireDate = moment(expireTime).format("YYYY.MM.DD");
    return (
      <div className="svip_page">
        <div
          className="bannerWrapper"
          style={{ backgroundImage: `url(${TopColor})` }}
        >
          <img src={SvipBanner} className="banner" />
          <div className="svip_page_user">
            <img src={photoUrl} />
            <div>
              <p className="svip_page_user_nickname">
                {nickName ? nickName : "未填写"}
              </p>
              <p className="expireTime">{`有效期至 ${expireDate}`}</p>
            </div>
          </div>
        </div>
        <div
          className="svip_page_more"
          style={{ backgroundImage: `url(${Bg1})` }}
        >
          <p className="svip_page_title">至尊会员精选权益</p>
          <img src={MoreImg} />
        </div>
        <div
          className="svip_page_lesson"
          style={{ backgroundImage: `url(${Bg2})` }}
        >
          <p className="svip_page_title">全学科精品复习备考资源 </p>
          <p>
            九大学科全覆盖，专为学考(合格考)和高考备考打造精品课程，提供考前精品模拟卷，配套试题讲解，学练结合，高效备考
          </p>
          <div className={"svip-page-tab"}>
            {getCurrentUserData.map((item, index) => (
              <div
                key={item.id}
                onClick={() => {
                  this.ChangePageTab(item, index);
                }}
                className={cls(["svip-page-tab-list", item.active && "active"])}
              >
                {item.categoryName}
              </div>
            ))}
          </div>
          <div className="svip_page_lesson_tab">
            {TabGrade.map((i, idx) => {
              return (
                <span
                  className={idx === active ? "active" : ""}
                  key={i.id}
                  onClick={() => this.changeTab(i, idx)}
                >
                  {i.categoryName}
                </span>
              );
            })}
          </div>
          {TabGrade.length > 0 &&
            ContentList.courseList &&
            ContentList.courseList.length > 0 &&
            this.LeftTitle("课程")}
          {TabGrade.length > 0 && ContentList && ContentList.courseList && (
            <div className={"svip-page-course"}>
              {ContentList &&
                ContentList.courseList &&
                ContentList.courseList.map((item) => (
                  <div
                    key={item.id}
                    className={"svip-page-course-list"}
                    onClick={() => {
                      openVideoPackage(item.id);
                    }}
                  >
                    <div className={"course-list-Img"}>
                      <img src={item.picture} className={"main-Img"} />
                      <div className={"watch"}>
                        <img src={PlayCounts} />
                        <span className={"title"}>{item.hitCount}</span>
                      </div>
                    </div>
                    <h3 className={"course-list-title"}>
                      {item.coursePackageName}
                    </h3>
                    <p className={"course-compere"}>
                      <img src={teacher} />
                      <span className={"title"}>
                        {item.teacherList &&
                          item.teacherList.map((item1, index) => {
                            return index === item.teacherList.length - 1
                              ? item1.teacherName
                              : item1.teacherName + "、";
                          })}
                      </span>
                    </p>
                  </div>
                ))}
            </div>
          )}
          {TabGrade.length > 0 &&
            ContentList.paperList &&
            ContentList.paperList.length > 0 &&
            this.LeftTitle("试卷")}
          {TabGrade.length > 0 && ContentList && ContentList.paperList && (
            <div className={"svip-page-paper"}>
              <ul>
                {ContentList &&
                  ContentList.paperList &&
                  ContentList.paperList.map((item) => (
                    <li key={item.paperId}>
                      <div className={"paper-head-list"}>
                        <h3 className={"paper-title"}>{item.paperName}</h3>
                        {item.finish ? (
                          <div className={"paper-accuracy"}>
                            {item.correctResult
                              ? `正确率${item.rightRate}`
                              : "已答完，待批改"}
                          </div>
                        ) : item.doneCount > 0 || item.doneQuestions > 0 ? (
                          <span
                            style={{ color: "#2E86FF" }}
                          >{`答题至${item.doneCount || item.doneQuestions}/${item.totalCount || item.totalQuestions}`}</span>
                        ) : null}
                      </div>
                      <div className={"paper-head"}>
                        <span className={"paper-head-topic"}>
                          {item.totalQuestions}
                        </span>
                        <span className={"paper-head-topic-num"}>题</span>
                        <span className={"paper-head-people"}>
                          {item.donePeoples}
                        </span>
                        <span className={"paper-head-num"}>人已练</span>
                        <div
                          className={"paper-btn"}
                          onClick={() => {
                            this.linkman(item);
                          }}
                        >
                          <img src={pencil} />
                          <span className={"paper-go"}>
                            {item.finish
                              ? item.correctResult
                                ? "查看报告"
                                : "去批改"
                              : "开始练习"}
                          </span>
                        </div>
                      </div>
                    </li>
                  ))}
              </ul>
            </div>
          )}
          <div className="svip_page_lesson_img">
            {!TabGrade.length && (
              <div className="svip_page_lesson_img_icon">
                <img src={LessonEmpty} />
                <span>备考期尚未开始，我们稍后见~</span>
              </div>
            )}
          </div>
        </div>
        <div
          className="svip_page_lesson"
          style={{ backgroundImage: `url(${Bg3})` }}
        >
          <p className="svip_page_title">150+讲创新创业/领导力课程</p>
          <p>帮助高中生建立初步认知，为步入大学、走向社会做好准备</p>
          <img className="svip_page_train_img" src={Suyang} />
          <LinkList img={LeaderShip} list={diffLeadershipContentList} />
          <LinkList img={Creativity} list={innovationContentList} />
          <LinkList img={JobAbility} list={jobContentList} />
        </div>
      </div>
    );
  }
}

export const Component = Svip;
