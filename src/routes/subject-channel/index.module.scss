.container {
  background: linear-gradient(135deg, #3b4fff, #2f86ff);
  height: 100vh;
  overflow: hidden;

  .list_container {
    position: relative;
    background: linear-gradient(135deg, #3b4fff, #2f86ff);
  }

  .float {
    width: 100%;
    background: linear-gradient(135deg, #3b4fff, #3761ff);
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    padding-bottom: 8px;

    transform: translateY(-100%);
    transition: transform 0.5s ease-in-out;

    &.show {
      transform: translateY(0);
    }
  }

  .bottom_more {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    padding-bottom: calc(32px + constant(safe-area-inset-bottom));
    padding-bottom: calc(32px + env(safe-area-inset-bottom));
  }
}
