import React, {
  useEffect,
  useLayoutEffect,
  useReducer,
  useRef,
  useState,
} from "react";
import Header from "./components/header";
import { px2RealPx } from "@/utils/vw-utils";
import MenuPopup from "./components/menu-popup";
import {
  useSearchParams,
  useParams,
  useNavigate,
  useLocation,
} from "react-router-dom";
import {
  BASE_LEVEL_ID,
  BASE_LEVEL_NAME,
  ChannelTypeEnum,
  channelTypeToCategoryType,
  getFilterHeight,
  Heights,
} from "./constants";
import {
  closeWebview,
  getSafeAreaTop,
  notifyAppSubjectId,
  openCourse,
} from "@/utils/bridge-utils";
import TextHeightCalculator from "@/utils/text-height-calculator";
import List, { IListRef, IRow, ListItemTypeEnum } from "./components/list";
import TaskQueue from "@/utils/task-queue";
import {
  getCategorySubject,
  getCondition,
  getCourseList,
  getCurrentBook,
  getLastStudy,
  getLessonList,
  getOldUrl,
  getOtherBookList,
  getTreeResource,
  ICourseItem,
  ICurrentBook,
  IHardLevel,
  ILastStudy,
  ILessonItem,
  IOtherBook,
  ISubject,
  ITreeItem,
  saveLastStudy,
} from "./apis";
import { clickPv, createURLByType, expPv, sleep } from "@/utils/tool";
import DelayPageLoading from "@/components/delay-page-loading";
import { Toast } from "antd-mobile";
import { useAPILevel } from "@/hooks";
import SafeLogger from "@/utils/safe-logger";

import styles from "./index.module.scss";
import SubjectBookContext from "./context/subject-book";
import HardLevelsContext from "./context/hard-levels";
import BaseInfoContext, { ICategorySubjectMap } from "./context/base-info";
import SubjectBookManager, {
  ISubjectBookManagerRef,
} from "./components/subject-book-manager";
import LessonMore from "./components/lesson-more";

const textBookTabs = [ChannelTypeEnum.TEXTBOOK, ChannelTypeEnum.KEY_POINTS];

const SubjectChannel = () => {
  const params = useParams();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const location = useLocation();
  // 当前页面类型
  const activeChannel = params.channelType as ChannelTypeEnum;
  // 页面类型 转 后端 页面类型 type
  const categoryType = channelTypeToCategoryType[activeChannel];
  // 教材同步 与 重点复习 需要显示 教材/书本
  const showBook = textBookTabs.includes(activeChannel);
  // 维持请求顺序
  const { upLevel, getLevelKey } = useAPILevel(5);
  // 日志
  const loggerRef = useRef<SafeLogger>();
  // 一些基础高度信息
  const [heightInfo, setHeightInfo] = useState({
    // 安全区域高度
    top: 0,
    // 页头占用高度
    header: Heights.HEADER,
    // 滚动区域高度
    scroll: Heights.LIST,
  });
  // 页面类型以及其支持学科信息
  const categorySubjectRef = useRef<ICategorySubjectMap>({});
  // 当前选中学科
  const subjectRef = useRef<Partial<ISubject>>({});
  // 用户当前 选中 的 教材版本 与 书本
  const currentBookRef = useRef<ICurrentBook>({});
  // 非 教材同步、重点复习 的 书本列表
  const [otherBookList, setOtherBookList] = useState<IOtherBook[]>([]);
  // 章 高度计算
  const chapterHeightRef = useRef<TextHeightCalculator>();
  // 节 高度计算
  const sectionHeightRef = useRef<TextHeightCalculator>();
  // 包信息请求队列
  const courseQueueRef = useRef<TaskQueue<ICourseItem>>();
  // 讲列表请求队列
  const lessonsQueueRef = useRef<TaskQueue<ILessonItem[]>>();
  // 全局 Loading 状态
  const [pageLoading, setPageLoading] = useState(false);
  // 列表展示数据
  const [showRows, setShowRows] = useState<IRow[]>([]);
  // 列表组件
  const listRef = useRef<IListRef>();
  // 章 展开/折叠 情况记录
  const chapterCollapseRef = useRef<Record<string, boolean>>({});
  // 课程包 展开/折叠 情况记录
  const courseExpandRef = useRef<Record<string, boolean>>({});
  // 目录弹窗 显示与否
  const [showMenuPopup, setShowMenuPopup] = useState(false);
  // 历史课程地址
  const [oldUrl, setOldUrl] = useState("");
  // 难度 列表
  const [hardLevels, setHardLevels] = useState<IHardLevel[]>([]);
  // 当前 难度
  const [currentHardLevel, setCurrentLevel] = useState(BASE_LEVEL_ID);
  // 【全部】难度 的目录记录
  const [baseTree, setBaseTree] = useState<ITreeItem[]>([]);
  // 当前难度 的 树
  const currentTreeRef = useRef<ITreeItem[]>([]);
  // 上次学到 信息
  const lastStudyRef = useRef<ILastStudy>();
  // 教材版本切换 弹窗 与 学科切换 弹窗的管理
  const popupManagerRef = useRef<ISubjectBookManagerRef>();
  // 异常情况，需要重试的 步骤
  const errorStepRef = useRef(0);
  // 空状态，底部是否显示 更多 提示
  const [showEmptyMore, setShowEmptyMore] = useState(false);
  // 数据异常的课程包，极端情况下，目录加载时，有相关包，但是获取包信息时，无相关包
  const errorCourseRef = useRef<Record<string, boolean>>({});
  // 完全更新重置 list
  const [listKey, upListKey] = useReducer((x) => x + 1, 0);
  // 列表需要重新渲染并跳转到特定位置，防止页面抖动
  const [centerIndex, setCenterIndex] = useState(-2);
  // 为全局变更增加透明度变化，防止闪烁
  const [listOpacity, setListOpacity] = useState(0);
  // 强制刷新
  const [_, forceUpdate] = useReducer((x) => x + 1, 0);

  const showMenu =
    (activeChannel === ChannelTypeEnum.TEXTBOOK ||
      activeChannel === ChannelTypeEnum.COLLEGE_EXAM) &&
    !!baseTree.length;

  // 只有 【全部】 难度筛选时，不显示 难度筛选
  const showHardLevels = hardLevels.length > 1;
  // 筛选组件是否显示 第二行
  const showFilterLine2 = showHardLevels || (!showBook && showMenu);

  // 1. 获取 类型 以及对应 学科信息，安全区域高度
  const initTabAndSubject = async () => {
    const levelKey = upLevel(1);
    // 如果已经有缓存，则不重复请求
    if (Object.keys(categorySubjectRef.current).length > 0) {
      checkSubject();
      return;
    }
    try {
      const [res] = await Promise.all([
        getCategorySubject(),
        getSafeAreaTop().then((top) => {
          // 处理安全区域高度
          if (top) {
            setHeightInfo({
              top,
              header: Heights.HEADER + top,
              scroll: Heights.LIST - top,
            });
          }
        }),
      ]);
      if (levelKey !== getLevelKey(1)) {
        return;
      }
      categorySubjectRef.current = {};
      (res.data || []).forEach((item) => {
        const { categoryType, categoryName } = item;
        if (item.subjectList?.length) {
          categorySubjectRef.current[categoryType] = {
            categoryName,
            subjectList: item.subjectList,
          };
        }
      });
      if (!Object.keys(categorySubjectRef.current).length) {
        throw new Error("页面类型数据异常");
      }
      checkSubject();
    } catch (error) {
      loggerRef.current?.error("page-blocking", {
        reason: "category-subject-error",
        error,
      });
      console.log("获取 类型 以及对应 学科信息 失败", error);
      if (levelKey !== getLevelKey(1)) {
        return;
      }
      setPageLoading(false);
      errorStepRef.current = 1;
      pushErrorRows();
    }
  };

  // 2. 处理 学科
  const checkSubject = async (subjectId?: number) => {
    upLevel(2);
    // 该用户 不支持 本页面类型
    if (!categorySubjectRef.current[categoryType]) {
      setPageLoading(false);
      invalidEntry();
      return;
    }

    // 如果需要切换的 学科 与 当前 一致，则不做处理
    if (subjectId && subjectId === subjectRef.current.subjectId) {
      setPageLoading(false);
      return;
    }

    const subjectList = categorySubjectRef.current[categoryType].subjectList;

    const querySubjectId = +searchParams.get("subjectId") || 0;
    // 处理 学科 选中状态
    const nextSubjectId = subjectId || querySubjectId;
    let currentSubject = subjectList.find(
      (item) => item.subjectId === nextSubjectId,
    );
    // 兜底选中 第一个 学科
    if (!currentSubject) {
      currentSubject = subjectList[0];
    }

    if (currentSubject.subjectId !== querySubjectId) {
      syncSubject(currentSubject.subjectId);
    }

    subjectRef.current = currentSubject;
    // 这里让 教材切换 弹窗 能够渲染
    forceUpdate();

    if (showBook) {
      initCurrentBook();
    } else {
      initOtherBooks();
    }
  };

  // 3-a. 获取 教材同步、重点复习 的 当前 教材版本/书本
  const initCurrentBook = async () => {
    const levelKey = upLevel(3);
    const subjectId = subjectRef.current.subjectId;
    try {
      const [res, lastStudyRes] = await Promise.all([
        getCurrentBook({
          subjectId,
        }),
        getLastStudy({
          subjectId,
          categoryType,
          ignoreError: true,
        }).catch((error) => {
          loggerRef.current?.warn("last-study-error", {
            error,
            params: {
              categoryType,
              subjectId,
            },
          });
        }),
      ]);
      if (levelKey !== getLevelKey(3)) {
        return;
      }
      const current = res.data || {};

      currentBookRef.current = current;
      lastStudyRef.current = lastStudyRes && lastStudyRes.data;

      if (!current.baseEditionId) {
        setPageLoading(false);
        popupManagerRef.current.showEdition();
      } else if (!current.bookId) {
        setPageLoading(false);
        popupManagerRef.current.showBook();
      } else {
        initChapterAndLast();
      }
    } catch (error) {
      loggerRef.current?.error("page-blocking", {
        reason: "current-book-error",
        error,
        params: {
          subjectId,
          categoryType,
        },
      });
      if (levelKey !== getLevelKey(3)) {
        return;
      }
      setPageLoading(false);
      errorStepRef.current = 3;
      // 一些数据清理
      currentBookRef.current = {};
      currentTreeRef.current = [];
      setBaseTree([]);
      setHardLevels([]);
      pushErrorRows();
    }
  };

  // 3-b. 获取 非 教材同步、重点复习 的 书本 列表，并设置当前 书本
  const initOtherBooks = async () => {
    const levelKey = upLevel(3);
    const subjectId = subjectRef.current.subjectId;
    try {
      const [res, lastStudyRes] = await Promise.all([
        getOtherBookList({
          subjectId,
          categoryType,
        }),
        getLastStudy({
          subjectId,
          categoryType,
          ignoreError: true,
        }).catch((error) => {
          loggerRef.current?.warn("last-study-error", {
            error,
            params: {
              subjectId,
              categoryType,
            },
          });
        }),
      ]);
      if (levelKey !== getLevelKey(3)) {
        return;
      }
      if (!res.data?.length) {
        // 处理空数据
        setPageLoading(false);
        setHardLevels([]);
        setOtherBookList([]);
        pushEmptyRows();

        loggerRef.current?.error("page-blocking", {
          reason: "other-book-list-empty",
          params: {
            subjectId,
            categoryType,
          },
        });
        return;
      }
      const lastStudy = lastStudyRes && lastStudyRes.data;
      lastStudyRef.current = lastStudy;
      setOtherBookList(res.data);
      const book =
        res.data.find((it) => it.bookId === lastStudy?.bookId) || res.data[0];

      // 选中 上次学到 的 书，没有找到则 第一本书
      currentBookRef.current = { ...book };
      initChapterAndLast();
    } catch (error) {
      loggerRef.current?.error("page-blocking", {
        reason: "other-book-list-error",
        error,
        params: {
          subjectId,
          categoryType,
        },
      });
      if (levelKey !== getLevelKey(3)) {
        return;
      }
      setPageLoading(false);
      errorStepRef.current = 3;
      // 一些数据清理
      setHardLevels([]);
      setOtherBookList([]);
      pushErrorRows();
    }
  };

  // 4. 获取 章节（含包 ID 列表） 以及 难度信息
  const initChapterAndLast = async () => {
    const levelKey = upLevel(4);
    const bookId = currentBookRef.current.bookId;
    const subjectId = subjectRef.current.subjectId;
    try {
      const [treeRes, levelRes] = await Promise.all([
        getTreeResource({
          bookId,
          categoryType,
          hardId: BASE_LEVEL_ID,
          subjectId,
        }),
        // 素养拓展 不需要展示难度，则直接不请求
        activeChannel !== ChannelTypeEnum.LITERACY &&
          getCondition({
            bookId,
            categoryType,
            subjectId,
            ignoreError: true,
          }).catch((error) => {
            loggerRef.current?.warn("hard-level-error", {
              error,
              params: {
                bookId,
                categoryType,
                subjectId,
              },
            });
          }),
      ]);
      if (levelKey !== getLevelKey(4)) {
        return;
      }

      const tree = treeRes?.data || [];
      // 前端兜底显示 【全部】难度
      const levels =
        levelRes && levelRes.data?.hardLevels?.length > 0
          ? levelRes.data.hardLevels
          : [{ id: BASE_LEVEL_ID, name: BASE_LEVEL_NAME }];

      // 清空 包 请求列表缓存
      courseQueueRef.current.reset();
      // 清空 讲 请求列表缓存
      lessonsQueueRef.current.reset();
      currentTreeRef.current = tree;

      // 处理空数据
      if (!tree.length) {
        setPageLoading(false);
        setHardLevels([]);
        setBaseTree([]);
        pushEmptyRows();
        return;
      }

      const lastStudy = lastStudyRef.current;
      // 处理 队列 与 缓存，这里没有 考虑 列表中 包 重复的情况，不过至少上次学到的包是一定请求的
      makeCourseQueueAndCache(tree, lastStudy?.contentId || "0");

      // 等待 首屏 需要展示的元素请求结果
      await Promise.all([
        courseQueueRef.current.start(),
        lessonsQueueRef.current.start(),
      ]);

      if (levelKey !== getLevelKey(4)) {
        return;
      }

      setCurrentLevel(BASE_LEVEL_ID);
      setHardLevels(levels);
      setBaseTree(tree);
      upListKey();
      setOldUrl("");
      // 清空 章 折叠情况
      chapterCollapseRef.current = {};
      // 清空 课程 展开情况
      courseExpandRef.current = {};

      // 显示 列表
      pushToRows(
        lastStudy
          ? {
              id: lastStudy.contentId,
              type: ListItemTypeEnum.ITEM,
              parentIds: [lastStudy.chapterId, lastStudy.sectionId].filter(
                Boolean,
              ),
            }
          : undefined,
        true,
      );

      initOldLesson();
      setPageLoading(false);
    } catch (error) {
      loggerRef.current?.error("page-blocking", {
        reason: "chapter-list-error",
        error,
        params: {
          subjectId,
          bookId,
          categoryType,
          hardId: BASE_LEVEL_ID,
        },
      });
      if (levelKey !== getLevelKey(4)) {
        return;
      }
      setPageLoading(false);
      errorStepRef.current = 4;
      // 一些数据清理
      setHardLevels([]);
      setBaseTree([]);
      pushErrorRows();
    }
  };

  // 5. 获取 特定 难度下的 章节（包含 ID 列表）
  const getChapterWithCondition = async (hardId: number) => {
    const levelKey = upLevel(5);
    const bookId = currentBookRef.current.bookId;
    const categoryType = channelTypeToCategoryType[activeChannel];
    const subjectId = subjectRef.current.subjectId;
    try {
      let tree: ITreeItem[];
      if (hardId === BASE_LEVEL_ID) {
        tree = baseTree;
      } else {
        const res = await getTreeResource({
          bookId,
          categoryType,
          hardId,
          subjectId,
        });
        tree = res.data || [];
      }
      if (levelKey !== getLevelKey(5)) {
        return;
      }
      if (!tree.length) {
        currentTreeRef.current = [];
        setPageLoading(false);
        pushEmptyRows();
        return;
      }
      // 暂不处理 新增 课程包的包信息加载，这种情况应该出现的几率很小
      currentTreeRef.current = tree;
      upListKey();
      pushToRows(
        lastStudyRef.current
          ? {
              id: lastStudyRef.current.contentId,
              type: ListItemTypeEnum.ITEM,
              parentIds: [
                lastStudyRef.current.chapterId,
                lastStudyRef.current.sectionId,
              ].filter(Boolean),
            }
          : undefined,
      );
      setPageLoading(false);
    } catch (error) {
      loggerRef.current?.error("page-blocking", {
        reason: "chapter-list-error",
        error,
        params: {
          subjectId,
          bookId,
          categoryType,
          hardId,
        },
      });
      if (levelKey !== getLevelKey(5)) {
        return;
      }
      setPageLoading(false);
      currentTreeRef.current = [];
      errorStepRef.current = 5;
      pushErrorRows();
    }
  };

  // 4.1. 获取过往课程
  const initOldLesson = async () => {
    setOldUrl("");
    const bookId = currentBookRef.current.bookId;
    try {
      const levelKey = getLevelKey(4);
      // 仅 教材同步 需要获取
      if (activeChannel === ChannelTypeEnum.TEXTBOOK) {
        const res = await getOldUrl({
          bookId,
          ignoreError: true,
        });
        if (levelKey !== getLevelKey(4)) {
          return;
        }
        setOldUrl(res.data || "");
      }
    } catch (error) {
      loggerRef.current?.warn("old-lesson-error", {
        error,
        params: {
          bookId,
        },
      });
    }
  };

  // 根据 章节 信息，生成 展示列表
  const pushToRows = (centerItem?: IRow, expandFirst?: boolean) => {
    const tree = currentTreeRef.current;
    const rows: IRow[] = [];
    // 先推入 筛选条件
    rows.push({
      id: "filter",
      type: ListItemTypeEnum.FILTER,
      parentIds: [],
    });
    tree.forEach((chapter) => {
      // 推入 章
      rows.push({
        id: chapter.id,
        type: ListItemTypeEnum.CHAPTER,
        name: chapter.title,
        parentIds: [],
      });
      // 如果 章 折叠了，就不继续推入
      if (chapterCollapseRef.current[chapter.id]) {
        return;
      }
      // 推入 直接挂在 章 下面的资源
      (chapter.resourceList || []).forEach((course) => {
        if (!errorCourseRef.current[course.contentId]) {
          rows.push({
            id: course.contentId,
            type: ListItemTypeEnum.ITEM,
            parentIds: [chapter.id],
          });
        }
      });
      (chapter.children || []).forEach((section) => {
        // 推入 节
        rows.push({
          id: section.id,
          type: ListItemTypeEnum.SECTION,
          name: section.title,
          parentIds: [chapter.id],
        });
        // 推入 挂在 节 下面的资源
        (section.resourceList || []).forEach((course) => {
          if (!errorCourseRef.current[course.contentId]) {
            rows.push({
              id: course.contentId,
              type: ListItemTypeEnum.ITEM,
              parentIds: [chapter.id, section.id],
            });
          }
        });
      });
    });
    // 推入 更多
    rows.push({
      id: "more",
      type: ListItemTypeEnum.MORE,
      parentIds: [],
    });
    // 推入 底部占位
    rows.push({
      id: "bottom",
      type: ListItemTypeEnum.BOTTOM,
      parentIds: [],
    });
    setShowEmptyMore(false);
    let index = -1;
    if (centerItem && centerItem.id !== "0") {
      index = rows.findIndex((row) => {
        let isSame =
          row.type === centerItem.type && `${row.id}` === `${centerItem.id}`;
        // 如果是 课程包，还得判断是不是相同节点下的，毕竟不同节点下也可能有相同的包
        if (isSame && row.type === ListItemTypeEnum.ITEM) {
          isSame = row.parentIds.join("_") === centerItem.parentIds.join("_");
        }
        return isSame;
      });
    }
    // 如果定位元素是 包，则展开 讲列表
    if (index > -1 && centerItem.type === ListItemTypeEnum.ITEM) {
      // 包 可能重复
      courseExpandRef.current[`${centerItem.id}_${index}`] = true;
    } else if (expandFirst) {
      // 如果没有找到定位的包，且需要展开第一个包，则查找第一个包，并展开
      const firstIndex = rows.findIndex(
        (row) => row.type === ListItemTypeEnum.ITEM,
      );
      if (firstIndex > -1) {
        courseExpandRef.current[`${rows[firstIndex].id}_${firstIndex}`] = true;
      }
    }

    setShowRows(rows);
    setCenterIndex(index);
    return rows;
  };

  // 显示空状态
  const pushEmptyRows = () => {
    const rows: IRow[] = [];
    // 先推入 筛选条件
    rows.push({
      id: "filter",
      type: ListItemTypeEnum.FILTER,
      parentIds: [],
    });
    rows.push({
      id: "empty",
      type: ListItemTypeEnum.EMPTY,
      parentIds: [],
    });
    setShowEmptyMore(true);
    setShowRows(rows);
    setCenterIndex(0);
    upListKey();
  };

  // 显示错误状态
  const pushErrorRows = () => {
    const rows: IRow[] = [];
    // 先推入 筛选条件
    rows.push({
      id: "filter",
      type: ListItemTypeEnum.FILTER,
      parentIds: [],
    });
    rows.push({
      id: "error",
      type: ListItemTypeEnum.ERROR,
      parentIds: [],
    });
    setShowEmptyMore(false);
    setShowRows(rows);
    setCenterIndex(0);
    upListKey();
  };

  // 处理 包 预加载队列
  const makeCourseQueueAndCache = (
    tree: ITreeItem[],
    centerCourseId?: string,
  ) => {
    const courseIds = [];
    tree.forEach((chapter) => {
      (chapter.resourceList || []).forEach((course) => {
        courseIds.push(course.contentId);
      });
      (chapter.children || []).forEach((section) => {
        (section.resourceList || []).forEach((course) => {
          courseIds.push(course.contentId);
        });
      });
    });
    let newCourseIds = [];
    const len = courseIds.length;
    const index =
      centerCourseId && centerCourseId !== "0"
        ? courseIds.findIndex((id) => `${id}` === `${centerCourseId}`)
        : -1;
    if (index < 0) {
      newCourseIds = courseIds;
      lessonsQueueRef.current.push([courseIds[0]]);
    } else {
      newCourseIds.push(courseIds[index]);
      const max = Math.max(index + 1, len - index);
      for (let i = 1; i < max; i++) {
        if (courseIds[index + i]) {
          newCourseIds.push(courseIds[index + i]);
        }
        if (courseIds[index - i]) {
          newCourseIds.push(courseIds[index - i]);
        }
      }

      // 由于预加载未考虑 包 重复的情况，所以把 包 的 第一个 课程 也推入队列
      lessonsQueueRef.current.push([centerCourseId, courseIds[0]]);
    }
    courseQueueRef.current.push(newCourseIds);
  };

  // 同步 学科 到 链接 以及 App 原生
  const syncSubject = (subjectId: number) => {
    // 修改链接上的学科
    navigate(
      createURLByType({
        path: location.pathname,
        originSearch: location.search,
        removeQueryKeys: ["subjectId"],
        addQueryObject: {
          subjectId,
        },
      }),
      { replace: true },
    );

    // 通知 App 学科变化
    notifyAppSubjectId(subjectId);
  };

  // 批量 获取包详情 接口
  const queueCourseList = async (ids: string[]) => {
    const res = await getCourseList({ courseIds: ids, ignoreError: true });
    const map = {};
    res.data.forEach((it) => {
      map[it.id] = it;
    });
    return map;
  };

  // 包详情 信息更新
  const queueCourseUpdate = (ids: string[], emptyIds: string[]) => {
    if (emptyIds.length) {
      // 处理 后端未返回的数据
      // 感觉这种极端情况是不需要处理的，用户刷新页面就OK了，但是 后端 与 质量 想要处理，这边简单处理了，不过可能会造成 页面跳动
      emptyIds.forEach((id) => {
        errorCourseRef.current[id] = true;
      });
      pushToRows();
      loggerRef.current?.error("course-info-empty", {
        params: {
          emptyIds,
        },
      });
    } else {
      listRef.current.resetAfterIndex(0);
    }
  };

  // 获取课程讲列表接口
  const queueLessonList = async (ids: string[]) => {
    const res = await getLessonList({
      courseId: ids[0],
      // ignoreError: true,
    });
    return {
      [ids[0]]: res.data,
    };
  };

  // 课程讲 信息更新
  const queueLessonUpdate = (ids: string[]) => {
    listRef.current.resetAfterIndex(0);
  };

  // 切换 学科 或者 教材/书本
  const onChangeSubjectOrBook = (
    subject?: ISubject,
    book?: ICurrentBook,
    needRecheck?: boolean,
  ) => {
    if (subject) {
      currentBookRef.current = {};
      setPageLoading(true);
      checkSubject(subject.subjectId);
    } else if (book) {
      currentBookRef.current = book;
      if (book.bookId) {
        setPageLoading(true);
        initChapterAndLast();
      } else {
        if (needRecheck) {
          setPageLoading(true);
          initCurrentBook();
        } else {
          forceUpdate();
        }
      }
    }
  };

  const onHardLevelChange = (id: number) => {
    setCurrentLevel(id);
    courseExpandRef.current = {};
    chapterCollapseRef.current = {};
    setPageLoading(true);
    getChapterWithCondition(id);
  };

  // 【注意作用域】切换 章 展开/折叠 状态
  const onChapterCollapse = (id: string, isCollapse: boolean) => {
    chapterCollapseRef.current[id] = isCollapse;
    pushToRows();
  };

  // 【注意作用域】切换 包详情 中 课程讲列表 的展开状态
  const onCourseExpand = (id: string, isExpand: boolean, index: number) => {
    courseExpandRef.current[`${id}_${index}`] = isExpand;
    listRef.current.resetAfterIndex(index);
    lessonsQueueRef.current.push([id]);
    lessonsQueueRef.current.start();

    clickPv("ewt_h5_study_channel_textbook_course_expand_click", {
      page: categorySubjectRef.current[categoryType]?.categoryName,
      action: isExpand ? "展开" : "收起",
    });
  };

  // 【注意作用域】点击 跳转 到 播放页，并处理上次学到
  const onClickVideo = (
    parentIds: string[],
    courseId: string,
    lessonId?: string,
  ) => {
    openCourse(courseId, lessonId);

    const newLastStudyInfo = {
      baseEditionId: currentBookRef.current.baseEditionId || "0",
      bookId: currentBookRef.current.bookId,
      contentId: courseId,
      contentType: 2,
      chapterId: parentIds[0],
      sectionId: parentIds[1],
    };

    lastStudyRef.current = newLastStudyInfo;
    forceUpdate();

    // 同步信息到 后端
    saveLastStudy({
      ...newLastStudyInfo,
      subjectId: subjectRef.current.subjectId,
      categoryType,
      ignoreError: true,
    }).catch((e) => {
      console.log("保存 上次学到 失败", e);
    });

    // 因为切换了上次学到，所以默认查询一下
    lessonsQueueRef.current.push([courseId]);
    lessonsQueueRef.current.start();

    clickPv(
      lessonId
        ? "ewt_h5_study_channel_textbook_lesson_click"
        : "ewt_h5_study_channel_textbook_course_click",
      {
        page: categorySubjectRef.current[categoryType]?.categoryName,
      },
    );
  };

  // 【注意作用域】异常情况，点击重试
  const onListRetry = () => {
    if (errorStepRef.current === 3) {
      setPageLoading(true);
      showBook ? initCurrentBook() : initOtherBooks();
    } else if (errorStepRef.current === 4) {
      setPageLoading(true);
      initChapterAndLast();
    } else if (errorStepRef.current === 5) {
      setPageLoading(true);
      getChapterWithCondition(currentHardLevel);
    } else {
      window.location.reload();
    }
  };

  // 切换当前选中的 书本，当前给 非教材同步、重点复习 使用
  const onChangeCurrentBook = async (currentBook: ICurrentBook) => {
    currentBookRef.current = currentBook;
    setPageLoading(true);
    initChapterAndLast();
  };

  // 点击目录中的章/节
  const onSelectChapter = (chapterId: string, sectionId?: string) => {
    setShowMenuPopup(false);

    clickPv("ewt_h5_study_channel_textbook_menu_item_click");

    // 如果 章 折叠了，需要展开后处理
    if (chapterCollapseRef.current[chapterId]) {
      delete chapterCollapseRef.current[chapterId];
    }

    let isNeedSwitchToAll = false;
    // 非 【全部】 难度时，判断 章/节 是否在当前 难度 中
    if (currentHardLevel !== BASE_LEVEL_ID) {
      const chapter = currentTreeRef.current.find(
        (it) => `${it.id}` === `${chapterId}`,
      );
      if (!chapter) {
        isNeedSwitchToAll = true;
      } else {
        if (sectionId) {
          const section = chapter.children?.find(
            (it) => `${it.id}` === `${sectionId}`,
          );
          if (!section) {
            isNeedSwitchToAll = true;
          }
        }
      }
    }

    const centerItem = sectionId
      ? {
          id: sectionId,
          type: ListItemTypeEnum.SECTION,
          parentIds: [chapterId],
        }
      : { id: chapterId, type: ListItemTypeEnum.CHAPTER, parentIds: [] };

    if (isNeedSwitchToAll) {
      Toast.show("该章节暂无对应难度课程，已帮你切换至全部难度");
      setCurrentLevel(BASE_LEVEL_ID);
      currentTreeRef.current = baseTree;
      pushToRows(centerItem);
      return;
    }

    pushToRows(centerItem);
  };

  // 链接不合法，强制退出页面
  const invalidEntry = () => {
    Toast.show({
      content: "页面路径不合法，3 秒后自动退出",
      duration: 0,
      maskClickable: false,
    });
    setTimeout(() => {
      closeWebview();
    }, 3000);
    loggerRef.current?.error("invalid-visit");
  };

  // 点击 筛选 相关埋点
  const onClickFilter = (qtInfo: any) => {
    clickPv("ewt_h5_study_channel_textbook_filter_click", {
      ...qtInfo,
      page: categorySubjectRef.current[categoryType]?.categoryName,
    });
  };

  useLayoutEffect(() => {
    if (centerIndex > -2) {
      if (centerIndex === -1) {
        listRef.current.resetAfterIndex(0);
      } else {
        listRef.current.resetAfterIndex(0, false);
        // 先让虚拟列表计算一遍全部高度
        listRef.current._getItemStyle(showRows.length - 1);
        const item = showRows[centerIndex];
        if (item?.type === ListItemTypeEnum.ITEM) {
          // 如果是课程包，且不是第一个课程包，则需要露出顶部筛选条件
          if (
            showRows
              .slice(0, centerIndex)
              .some((it) => it.type === ListItemTypeEnum.ITEM)
          ) {
            const top = listRef.current._getItemStyle(centerIndex).top;
            listRef.current.forceShowFilterAfterNextScroll();
            listRef.current.scrollTo(
              Math.max(
                Math.floor(top - getFilterHeight(showBook, showFilterLine2)),
                0,
              ),
            );
          }
        } else {
          listRef.current.scrollToItem(centerIndex, "start");
        }
      }
      setListOpacity(1);
      setCenterIndex(-2);
    }
  }, [showRows, centerIndex]);

  useEffect(() => {
    chapterHeightRef.current = new TextHeightCalculator(
      {
        fontSize: `${px2RealPx(16)}px`,
        lineHeight: `${px2RealPx(22)}px`,
        width: `${px2RealPx(331)}px`,
        fontWeight: "bold",
      },
      19,
      px2RealPx(22),
    );
    sectionHeightRef.current = new TextHeightCalculator(
      {
        fontSize: `${px2RealPx(14)}px`,
        lineHeight: `${px2RealPx(20)}px`,
        width: `${px2RealPx(331)}px`,
        fontWeight: "bold",
      },
      22,
      px2RealPx(20),
    );
    courseQueueRef.current = new TaskQueue<ICourseItem>(
      "获取包详情",
      queueCourseList,
      queueCourseUpdate,
      10,
    );
    lessonsQueueRef.current = new TaskQueue<ILessonItem[]>(
      "获取讲列表",
      queueLessonList,
      queueLessonUpdate,
      1,
    );
    loggerRef.current = new SafeLogger("subject-channel");
    return () => {
      chapterHeightRef.current.destroy();
      sectionHeightRef.current.destroy();
      courseQueueRef.current.clearQuery();
      lessonsQueueRef.current.clearQuery();
    };
  }, []);

  useEffect(() => {
    if (!channelTypeToCategoryType[activeChannel]) {
      // 链接异常，一般不会出现，除非手动触发
      invalidEntry();
    } else {
      setPageLoading(true);
      setShowRows([]);
      setBaseTree([]);
      setListOpacity(0);
      currentBookRef.current = {};
      initTabAndSubject();
    }
    return () => {
      // 切换页面类型时，停止请求队列
      courseQueueRef.current?.clearQuery();
      lessonsQueueRef.current?.clearQuery();
    };
  }, [activeChannel]);

  const lastStudyKey = lastStudyRef.current
    ? [
        lastStudyRef.current.chapterId,
        lastStudyRef.current.sectionId,
        lastStudyRef.current.contentId,
      ]
        .filter(Boolean)
        .join("_")
    : "";

  return (
    <BaseInfoContext.Provider
      value={{
        showBook,
        showMenu,
        showHardLevels,
        showFilterLine2,
        safeTop: heightInfo.top,
        activeChannel,
        categorySubject: categorySubjectRef.current,
        lastStudyKey,
        oldUrl,
        onClickMenu: () => {
          setShowMenuPopup(true);

          clickPv("ewt_h5_study_channel_textbook_menu_click", {
            page: categorySubjectRef.current[categoryType]?.categoryName,
          });
        },
      }}
    >
      <SubjectBookContext.Provider
        value={{
          currentSubject: subjectRef.current,
          currentBook: currentBookRef.current,
          otherBookList: otherBookList,
          onClickEdition: (qtInfo) => {
            popupManagerRef.current.showEdition();
            onClickFilter(qtInfo);
          },
          onClickSubject: (qtInfo) => {
            popupManagerRef.current.showSubject();
            onClickFilter(qtInfo);
          },
          onClickFilterBook: (qtInfo) => {
            popupManagerRef.current.showBook();
            onClickFilter(qtInfo);
          },
          onChangeBook: (bookId, bookName) => {
            onChangeCurrentBook({
              bookId,
              bookName,
            });

            clickPv("ewt_h5_study_channel_college_exam_book_click", {
              book: bookName,
              page: categorySubjectRef.current[categoryType]?.categoryName,
            });
          },
        }}
      >
        <HardLevelsContext.Provider
          value={{
            hardLevels,
            currentHardLevel,
            onHardLevelChange,
          }}
        >
          <div className={styles.container}>
            <Header />
            <div style={{ height: heightInfo.header }}></div>
            <div
              className={styles.list_container}
              style={{ height: heightInfo.scroll }}
            >
              <div
                style={{
                  height: heightInfo.scroll,
                  opacity: listOpacity,
                  transition: listOpacity ? "opacity 0.3s" : "",
                }}
              >
                <List
                  key={listKey}
                  scrollHeight={heightInfo.scroll}
                  showRows={showRows}
                  showBook={showBook}
                  showFilterLine2={showFilterLine2}
                  chapterHeightRef={chapterHeightRef}
                  sectionHeightRef={sectionHeightRef}
                  ref={listRef}
                  chapterCollapseRef={chapterCollapseRef}
                  courseExpandRef={courseExpandRef}
                  courseQueueRef={courseQueueRef}
                  lessonsQueueRef={lessonsQueueRef}
                  oldUrl={activeChannel === ChannelTypeEnum.TEXTBOOK && oldUrl}
                  onChapterCollapse={onChapterCollapse}
                  onCourseExpand={onCourseExpand}
                  onClickVideo={onClickVideo}
                  onErrorRetry={onListRetry}
                  onBackTop={() => {
                    upListKey();
                    clickPv("ewt_h5_study_channel_textbook_back_top_click", {
                      page: categorySubjectRef.current[categoryType]
                        ?.categoryName,
                    });
                  }}
                  onBackTopExpo={() => {
                    expPv("ewt_h5_study_channel_textbook_back_top_expo", {
                      page: categorySubjectRef.current[categoryType]
                        ?.categoryName,
                    });
                  }}
                />
              </div>
            </div>
            <MenuPopup
              open={showMenuPopup}
              key={currentBookRef.current.bookId || "menu"}
              onCancel={() => {
                setShowMenuPopup(false);
              }}
              menus={baseTree}
              onSelect={onSelectChapter}
            />
            <SubjectBookManager
              ref={popupManagerRef}
              key={activeChannel}
              onChange={onChangeSubjectOrBook}
              logger={loggerRef.current}
            />
            {showEmptyMore && (
              <div className={styles.bottom_more}>
                <LessonMore />
              </div>
            )}
            <DelayPageLoading loading={pageLoading} />
          </div>
        </HardLevelsContext.Provider>
      </SubjectBookContext.Provider>
    </BaseInfoContext.Provider>
  );
};

export const Component = SubjectChannel;
