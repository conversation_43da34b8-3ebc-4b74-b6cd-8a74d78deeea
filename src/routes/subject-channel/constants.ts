import { SubjectEnum } from "@/utils/constant";
import { getSafeAreaBottom } from "@/utils/tool";
import { px2RealPx } from "@/utils/vw-utils";

export const basePath = "/subject-channel";

export enum ChannelTypeEnum {
  /**
   * 教材同步
   */
  TEXTBOOK = "textbook",
  /**
   * 重点复习
   */
  KEY_POINTS = "key-points",
  /**
   * 备战高考
   */
  COLLEGE_EXAM = "college-exam",
  /**
   * 备战学考
   */
  STUDY_EXAM = "study-exam",
  /**
   * 素养拓展
   */
  LITERACY = "literacy",
}

export const channelTypeToCategoryType = {
  [ChannelTypeEnum.TEXTBOOK]: 1,
  [ChannelTypeEnum.KEY_POINTS]: 2,
  [ChannelTypeEnum.COLLEGE_EXAM]: 3,
  [ChannelTypeEnum.STUDY_EXAM]: 4,
  [ChannelTypeEnum.LITERACY]: 5,
};

export const categoryTypeToChannelType = {
  1: ChannelTypeEnum.TEXTBOOK,
  2: ChannelTypeEnum.KEY_POINTS,
  3: ChannelTypeEnum.COLLEGE_EXAM,
  4: ChannelTypeEnum.STUDY_EXAM,
  5: ChannelTypeEnum.LITERACY,
};

const headerHeight = Math.floor(px2RealPx(53));
// 各种高度，预先计算好
export const Heights = {
  // 页头默认高度
  HEADER: headerHeight,
  // 列表默认高度
  LIST: window.innerHeight - headerHeight,
  // 筛选条件高度 - 教材同步与重点复习
  FILTER_TEXTBOOK: px2RealPx(74),
  // 筛选条件高度 - 其他
  FILTER_OTHER: px2RealPx(86),
  // 筛选第二行高度
  FILTER_LINE2: px2RealPx(36),
  // 课程包高度
  COURSE: px2RealPx(187),
  // 课程包展开高度
  COURSE_EXPAND: px2RealPx(249),
  // 吸顶筛选条件出现的滚动高度
  FILTER_SCROLL: px2RealPx(100),
  // 安全区底部高度
  SAFE_AREA_BOTTOM: getSafeAreaBottom(),
  // 底部更多课程组件高度
  COURSE_MORE: px2RealPx(58),
  // 底部更多课程组件高度(含过往课程)
  COURSE_MORE_OLD: px2RealPx(76),
  // 无数据组件高度
  EMPTY: px2RealPx(260),
  // 异常组件高度
  ERROR: px2RealPx(300),
};

export function getFilterHeight(showBook: boolean, showFilterLine2: boolean) {
  return (
    (showBook ? Heights.FILTER_TEXTBOOK : Heights.FILTER_OTHER) -
    (showFilterLine2 ? 0 : Heights.FILTER_LINE2)
  );
}

// 【全部】难度 的 ID
export const BASE_LEVEL_ID = -1;
// 【全部】难度 的 名称
export const BASE_LEVEL_NAME = "全部";

export const SubjectIconName = {
  [SubjectEnum.CHINESE]: "icon-yuwen",
  [SubjectEnum.MATH]: "icon-shuxue",
  [SubjectEnum.ENGLISH]: "icon-yingyu",
  [SubjectEnum.PHYSICS]: "icon-wuli",
  [SubjectEnum.CHEMISTRY]: "icon-huaxue",
  [SubjectEnum.BIOLOGY]: "icon-shengwu",
  [SubjectEnum.POLITICS]: "icon-zhengzhi",
  [SubjectEnum.HISTORY]: "icon-lishi",
  [SubjectEnum.GEOGRAPHY]: "icon-dili",
  [SubjectEnum.GENERAL]: "icon-tongyong",
  [SubjectEnum.INFORMATION]: "icon-xinxi",
};
