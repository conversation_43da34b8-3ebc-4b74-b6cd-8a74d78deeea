import { createContext, useContext } from "react";
import { ChannelTypeEnum } from "../constants";
import { ISubject } from "../apis";

export type ICategorySubjectMap = Record<
  number,
  {
    categoryName: string;
    subjectList: ISubject[];
  }
>;

export interface IBaseInfoContext {
  showBook: boolean;
  showMenu: boolean;
  showHardLevels: boolean;
  showFilterLine2: boolean;
  safeTop: number;
  activeChannel: ChannelTypeEnum;
  categorySubject: ICategorySubjectMap;
  lastStudyKey: string;
  oldUrl: string;
  onClickMenu: () => void;
}

const BaseInfoContext = createContext<IBaseInfoContext>({
  showBook: true,
  showMenu: false,
  showHardLevels: false,
  showFilterLine2: false,
  activeChannel: ChannelTypeEnum.TEXTBOOK,
  safeTop: 0,
  categorySubject: {},
  lastStudyKey: "",
  oldUrl: "",
  onClickMenu: () => {},
});

export const useBaseInfo = () => {
  const {
    safeTop,
    showMenu,
    showBook,
    showHardLevels,
    showFilterLine2,
    activeChannel,
    categorySubject,
    lastStudyKey,
    oldUrl,
    onClickMenu,
  } = useContext(BaseInfoContext);
  return {
    safeTop,
    showBook,
    showMenu,
    showHardLevels,
    showFilterLine2,
    activeChannel,
    categorySubject,
    lastStudyKey,
    oldUrl,
    onClickMenu,
  };
};

export default BaseInfoContext;
