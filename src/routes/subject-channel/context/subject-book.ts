import { createContext, useContext } from "react";
import { ICurrentBook, IOtherBook, ISubject } from "../apis";

export interface ISubjectBookContext {
  currentSubject: Partial<ISubject>;
  currentBook: ICurrentBook;
  otherBookList: IOtherBook[];
  onClickSubject: (qtInfo: {
    filter_type: string;
    filter_value: string;
  }) => void;
  onClickFilterBook: (qtInfo: {
    filter_type: string;
    filter_value: string;
  }) => void;
  onClickEdition: (qtInfo: {
    filter_type: string;
    filter_value: string;
  }) => void;
  onChangeBook: (bookId: string, bookName: string) => void;
}

const SubjectBookContext = createContext<ISubjectBookContext>({
  currentSubject: {},
  currentBook: {},
  otherBookList: [],
  onClickSubject: () => {},
  onClickFilterBook: () => {},
  onClickEdition: () => {},
  onChangeBook: () => {},
});

export const useSubjectBook = () => {
  return useContext(SubjectBookContext);
};

export default SubjectBookContext;
