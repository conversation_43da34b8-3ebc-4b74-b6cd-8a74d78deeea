import { createContext, useContext } from "react";
import { IHardLevel } from "../apis";
import { BASE_LEVEL_ID, BASE_LEVEL_NAME } from "../constants";

export interface IHardLevelsContext {
  hardLevels: IHardLevel[];
  currentHardLevel: number;
  onHardLevelChange: (id: number) => void;
}

const HardLevelsContext = createContext<IHardLevelsContext>({
  hardLevels: [{ id: BASE_LEVEL_ID, name: BASE_LEVEL_NAME }],
  currentHardLevel: BASE_LEVEL_ID,
  onHardLevelChange: () => {},
});

export const useHardLevels = () => {
  const { hardLevels, currentHardLevel, onHardLevelChange } =
    useContext(HardLevelsContext);
  return {
    hardLevels,
    currentHardLevel,
    onHardLevelChange,
  };
};

export default HardLevelsContext;
