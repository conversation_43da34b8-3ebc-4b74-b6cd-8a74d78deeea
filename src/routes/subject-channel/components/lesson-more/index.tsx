import React, { CSSProperties } from "react";

import styles from "./index.module.scss";
import { openSearch, openWebView } from "@/utils/bridge-utils";
import { clickPv, cls } from "@/utils/tool";

const LessonMore = ({
  className,
  style,
  oldUrl,
  qtEvent,
  qtMore,
}: {
  className?: string;
  style?: CSSProperties;
  oldUrl?: string;
  qtEvent?: string;
  qtMore?: any;
}) => {
  const onClickOld = () => {
    openWebView(oldUrl);

    clickPv("ewt_h5_study_channel_textbook_old_lesson_click");
  };

  const onClickSearch = () => {
    openSearch();

    if (qtEvent) {
      clickPv(qtEvent, qtMore);
    }
  };

  return (
    <div className={cls([styles.base, className])} style={style}>
      {oldUrl ? (
        <div className={styles.old_container}>
          <div className={styles.icon}></div>
          <div>没找到想要的课？</div>
          <div>
            <span onClick={onClickOld}>可以查看过往课程</span>或者
            <span onClick={onClickSearch}>试着搜索看看吧</span>
          </div>
        </div>
      ) : (
        <div className={styles.container}>
          <div className={styles.icon}></div>
          <div>
            没找到想要的课？<span onClick={onClickSearch}>试着搜索看看吧</span>
          </div>
        </div>
      )}
    </div>
  );
};
export default LessonMore;
