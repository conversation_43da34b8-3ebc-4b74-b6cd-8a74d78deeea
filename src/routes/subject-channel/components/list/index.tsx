import React, {
  CSSProperties,
  forwardRef,
  MutableRefObject,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { getFilterHeight, Heights } from "../../constants";
import { px2RealPx } from "@/utils/vw-utils";
import TextHeightCalculator from "@/utils/text-height-calculator";
import Filters from "../filters";
import { Chapter, CourseMore, Item, Placeholder, Section } from "../list-items";
import { VariableSizeList } from "react-window";
import { cls } from "@/utils/tool";

import styles from "./index.module.scss";
import TaskQueue from "@/utils/task-queue";
import { ICourseItem, ILessonItem } from "../../apis";
import EmptyInfo from "../empty-info";
import ErrorInfo from "@/components/error-info";
import ErrorPng from "@/assets/common/error-b.png";

export enum ListItemTypeEnum {
  FILTER = "filter",
  CHAPTER = "chapter",
  SECTION = "section",
  ITEM = "item",
  MORE = "more",
  BOTTOM = "bottom",
  ERROR = "error",
  EMPTY = "empty",
}

export interface IRow {
  id: string;
  type: ListItemTypeEnum;
  name?: string;
  parentIds: string[];
}

export interface IVariableSizeList {
  resetAfterIndex: (index: number, forceUpdate?: boolean) => void;
  scrollTo: (scrollOffset: number) => void;
  scrollToItem: (index: number, align?: string) => void;
  // hack 方法，让 组件 全部计算一下高度
  _getItemStyle: (index: number) => any;
}

export interface IListRef extends IVariableSizeList {
  // 强制让下次滚动显示顶部筛选条件
  forceShowFilterAfterNextScroll: () => void;
}

enum TopFilterShowStatusEnum {
  FORCE_HIDE = 0,
  HIDE = 1,
  SHOW = 2,
}

const List = forwardRef(function List(
  {
    scrollHeight,
    showRows,
    showBook,
    showFilterLine2,
    chapterHeightRef,
    sectionHeightRef,
    chapterCollapseRef,
    courseExpandRef,
    courseQueueRef,
    lessonsQueueRef,
    oldUrl,
    onChapterCollapse,
    onCourseExpand,
    onClickVideo,
    onErrorRetry,
    onBackTop,
    onBackTopExpo,
  }: {
    scrollHeight: number;
    showRows: IRow[];
    showBook: boolean;
    showFilterLine2: boolean;
    chapterHeightRef: MutableRefObject<TextHeightCalculator>;
    sectionHeightRef: MutableRefObject<TextHeightCalculator>;
    chapterCollapseRef: MutableRefObject<Record<string, boolean>>;
    courseExpandRef: MutableRefObject<Record<string, boolean>>;
    courseQueueRef: MutableRefObject<TaskQueue<ICourseItem>>;
    lessonsQueueRef: MutableRefObject<TaskQueue<ILessonItem[]>>;
    oldUrl?: string;
    onChapterCollapse: (id: string, isCollapse: boolean) => void;
    onCourseExpand: (id: string, isExpand: boolean, index: number) => void;
    onClickVideo: (
      parentIds: string[],
      courseId: string,
      lessonId?: string,
    ) => void;
    onErrorRetry: () => void;
    onBackTop: () => void;
    onBackTopExpo: () => void;
  },
  ref: React.ForwardedRef<IListRef>,
) {
  // 顶部筛选条的显示状态，当滚动接近到顶部时，需要强制隐藏
  const [topFilterShow, setTopFilterShow] = useState(
    TopFilterShowStatusEnum.FORCE_HIDE,
  );
  // 回到顶部按钮的显示状态
  const [showBackTop, setShowBackTop] = useState(false);
  // 有连续两次上滑动事件时，才出现顶部 筛选
  const isLastBackwardRef = useRef(false);
  // 是否下次滚动强制显示顶部栏
  const isNextForceShowFilterRef = useRef(false);
  // react-window 的 list
  const realListRef = useRef<IVariableSizeList>();

  const onListChapterCollapse = useCallback(
    (isExpand: boolean, chapterId: string) => {
      isLastBackwardRef.current = false;
      onChapterCollapse(chapterId, !isExpand);
    },
    [],
  );

  const onListCourseExpand = useCallback(
    (isExpand: boolean, info: ICourseItem, index: number) => {
      isLastBackwardRef.current = false;
      onCourseExpand(info.id, isExpand, index);
    },
    [],
  );

  const onCourseRetry = useCallback((courseId: string, index: number) => {
    courseQueueRef.current.retry(courseId);
    realListRef.current.resetAfterIndex(index);
  }, []);

  const onLessonRetry = useCallback((info: ICourseItem, index: number) => {
    lessonsQueueRef.current.retry(info.id);
    realListRef.current.resetAfterIndex(index);
  }, []);

  const onItemToVideo = useCallback(
    (parentIds: string[], courseId: string, lessonId?: string) => {
      onClickVideo(parentIds, courseId, lessonId);
    },
    [],
  );

  const listItemKey = useCallback((index: number, data: IRow[]) => {
    const item = data[index];
    // 由于 测试数据可能重复，所以还是将 index 作为 key
    return item ? `${item.type}_${item.id}_${index}` : "";
  }, []);

  const listItemSize = useCallback(
    (index: number) => {
      const item = showRows[index];
      if (item?.type) {
        switch (item.type) {
          case ListItemTypeEnum.FILTER:
            return getFilterHeight(showBook, showFilterLine2);
          case ListItemTypeEnum.CHAPTER:
            return (
              px2RealPx(20) +
              chapterHeightRef.current.calculateHeight(item.name)
            );
          case ListItemTypeEnum.SECTION:
            const isAfterChapter =
              showRows[index - 1]?.type === ListItemTypeEnum.CHAPTER;
            return (
              (isAfterChapter ? px2RealPx(4) : px2RealPx(20)) +
              sectionHeightRef.current.calculateHeight(item.name)
            );
          case ListItemTypeEnum.ITEM:
            return courseExpandRef.current[`${item.id}_${index}`]
              ? Heights.COURSE_EXPAND
              : Heights.COURSE;
          case ListItemTypeEnum.MORE:
            return oldUrl ? Heights.COURSE_MORE_OLD : Heights.COURSE_MORE;
          case ListItemTypeEnum.BOTTOM:
            return px2RealPx(32) + Heights.SAFE_AREA_BOTTOM;
          case ListItemTypeEnum.ERROR:
            return Heights.ERROR;
          case ListItemTypeEnum.EMPTY:
            return Heights.EMPTY;
        }
      }
      return 0;
    },
    [showRows, showBook, oldUrl, showFilterLine2],
  );

  const listItemRender = useCallback(
    ({
      index,
      style,
      data,
    }: {
      index: number;
      style: CSSProperties;
      data: IRow[];
    }) => {
      const item = data[index];
      if (item?.type) {
        switch (item.type) {
          case ListItemTypeEnum.FILTER:
            return <Filters style={style} />;
          case ListItemTypeEnum.CHAPTER:
            return (
              <Chapter
                style={style}
                name={item.name}
                expand={!chapterCollapseRef.current[item.id]}
                chapterId={item.id}
                onExpandChange={onListChapterCollapse}
              />
            );
          case ListItemTypeEnum.SECTION:
            const isAfterChapter =
              data[index - 1]?.type === ListItemTypeEnum.CHAPTER;
            return (
              <Section
                isAfterChapter={isAfterChapter}
                style={style}
                name={item.name}
              />
            );
          case ListItemTypeEnum.ITEM:
            const courseInfo = courseQueueRef.current.getStatus(item.id);
            const lessonInfo = lessonsQueueRef.current.getStatus(item.id);
            const isExpand = !!courseExpandRef.current[`${item.id}_${index}`];
            const studyKey = [...item.parentIds, item.id].join("_");
            return courseInfo.data ? (
              <Item
                style={style}
                isExpand={isExpand}
                info={courseInfo.data}
                lessons={lessonInfo.data}
                lessonError={lessonInfo.isError}
                index={index}
                onExpandChange={onListCourseExpand}
                studyKey={studyKey}
                parentIds={item.parentIds}
                onLessonRetry={onLessonRetry}
                onClickVideo={onItemToVideo}
              />
            ) : (
              <Placeholder
                style={style}
                isError={courseInfo.isError}
                isExpand={isExpand}
                courseId={item.id}
                index={index}
                onRetry={onCourseRetry}
              />
            );
          case ListItemTypeEnum.MORE:
            return <CourseMore style={style} />;
          case ListItemTypeEnum.EMPTY:
            return (
              <EmptyInfo style={style} text="暂无内容，正在加速生产中！" />
            );
          case ListItemTypeEnum.ERROR:
            return (
              <ErrorInfo
                icon={ErrorPng}
                style={style}
                desc="oops，网络异常，请刷新重试"
                retryText="重试"
                onRetry={onErrorRetry}
              />
            );
        }
      }
      return <div style={style}></div>;
    },
    [],
  );

  const listOnScroll = useCallback(
    ({ scrollOffset, scrollDirection, scrollUpdateWasRequested }) => {
      if (scrollOffset <= Heights.HEADER) {
        isLastBackwardRef.current = false;
        setTopFilterShow(TopFilterShowStatusEnum.FORCE_HIDE);
      } else if (isNextForceShowFilterRef.current) {
        setTopFilterShow(TopFilterShowStatusEnum.SHOW);
      } else if (scrollDirection === "backward" && !scrollUpdateWasRequested) {
        if (isLastBackwardRef.current) {
          setTopFilterShow(TopFilterShowStatusEnum.SHOW);
        } else {
          isLastBackwardRef.current = true;
        }
      } else {
        isLastBackwardRef.current = false;
        setTopFilterShow(TopFilterShowStatusEnum.HIDE);
      }
      isNextForceShowFilterRef.current = false;
      setShowBackTop(scrollOffset > window.innerHeight);
    },
    [],
  );

  useImperativeHandle(ref, () => {
    return {
      resetAfterIndex: (index: number, forceUpdate?: boolean) => {
        realListRef.current.resetAfterIndex(index, forceUpdate);
      },
      scrollTo: (scrollOffset: number) => {
        realListRef.current.scrollTo(scrollOffset);
      },
      scrollToItem: (index: number, align?: string) => {
        realListRef.current.scrollToItem(index, align);
      },
      _getItemStyle: (index: number) =>
        realListRef.current._getItemStyle(index),
      forceShowFilterAfterNextScroll: () => {
        isNextForceShowFilterRef.current = true;
      },
    };
  }, []);

  useEffect(() => {
    if (showBackTop) {
      onBackTopExpo();
    }
  }, [showBackTop]);

  return (
    <>
      <VariableSizeList
        height={scrollHeight}
        itemCount={showRows.length}
        itemKey={listItemKey}
        itemSize={listItemSize}
        itemData={showRows}
        width="100%"
        onScroll={listOnScroll}
        ref={realListRef}
      >
        {listItemRender}
      </VariableSizeList>
      {topFilterShow !== TopFilterShowStatusEnum.FORCE_HIDE && (
        <div
          className={cls([
            styles.float,
            topFilterShow === TopFilterShowStatusEnum.SHOW && styles.show,
          ])}
        >
          <Filters />
        </div>
      )}
      <div
        className={cls([styles.back_top, showBackTop && styles.show])}
        style={{ bottom: Heights.SAFE_AREA_BOTTOM + px2RealPx(40) }}
        onClick={onBackTop}
      ></div>
    </>
  );
});
export default List;
