.float {
  width: 100%;
  background: linear-gradient(135deg, #3b4fff, #3761ff);
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  padding-bottom: 8px;

  transform: translateY(-100%);
  transition: transform 0.4s ease-out;

  &.show {
    transform: translateY(0);
  }
}

.back_top {
  position: fixed;
  right: 18px;
  bottom: 40px;
  width: 36px;
  height: 36px;
  background: url(../../../../assets/subject-channel/back-to-top.png) center/contain no-repeat;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.4s ease-in-out;

  &.show {
    opacity: 1;
    pointer-events: auto;
  }
}
