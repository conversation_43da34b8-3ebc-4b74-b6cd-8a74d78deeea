.container {
  background: url(../../../../assets/subject-channel/header-bg.png) center / cover no-repeat, #3B4455;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  z-index: 2;

}

.main {
  position: relative;
  box-sizing: content-box;
  height: 42px;
  padding-top: 5px;
  border-bottom: 6px solid #DEAF74;
  width: 100%;
  box-shadow: 0 2px #5D5E5F;
  display: flex;
  align-items: center;
  justify-content: space-between;


  .left {
    display: flex;
    align-items: center;

    .back {
      width: 32px;
      height: 42px;
      // background: url(../../../../assets/common/back.png) center / 18px no-repeat;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: #FFFFFF;
      transform: scaleX(-1);
    }

    .title {
      font-weight: bold;
      font-size: 18px;
      color: #FFFFFF;
      line-height: 20px;
    }
  }

  .right {
    height: 42px;
    padding-right: 12px;
    display: flex;
    align-items: center;
    font-weight: bold;
    font-size: 12px;
    color: #FFFFFF;

    .word {
      max-width: 70px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      margin-right: 4px;
    }

  }

  .center {
    height: 32px;
    background: #262C37;
    border-radius: 16px;
    display: flex;
    align-items: center;
    padding: 0 2px;
    position: absolute;
    left: 50%;
    top: 10px;
    transform: translateX(-50%);

    .item {
      padding: 0 10px;
      height: 28px;
      line-height: 28px;
      border-radius: 14px;
      font-size: 14px;
      color: #D8D8D8;

      &.selected {
        background: #FFD52F;
        color: #333333;
        font-weight: bold;
      }
    }
  }
}
