import React, { useEffect, useState } from "react";
import styles from "./index.module.scss";
import { IconSvg } from "@/components";
import { cls, createURLByType } from "@/utils/tool";
import {
  basePath,
  ChannelTypeEnum,
  channelTypeToCategoryType,
} from "../../constants";
import { useLocation, useNavigate } from "react-router";
import { changeStatusBarStyle, closeWebview } from "@/utils/bridge-utils";
import { useSubjectBook } from "../../context/subject-book";
import { useBaseInfo } from "../../context/base-info";

interface ITabItem {
  key: string;
  title: string;
}

const BaseHeader = ({
  safeTop,
  activeTab,
  tabs,
  right,
  onTabClick,
  onLeftClick,
  onRightClick,
}: {
  safeTop: number;
  activeTab: string;
  tabs: ITabItem[];
  right?: string;
  onTabClick: (tab: ITabItem) => void;
  onLeftClick: () => void;
  onRightClick?: () => void;
}) => {
  const showTab = tabs.length === 2;
  const onItemClick = (tab: ITabItem) => {
    if (tab.key === activeTab) {
      return;
    }
    onTabClick(tab);
  };

  const tab = tabs.find((tab) => tab.key === activeTab);

  useEffect(() => {
    changeStatusBarStyle(1);
  }, []);
  return (
    <div className={styles.container} style={{ paddingTop: safeTop || 0 }}>
      <div className={styles.main}>
        <div className={styles.left}>
          <div className={styles.back} onClick={onLeftClick}>
            <IconSvg name="icon-a-jinru2x" />
          </div>
          {!showTab && <div className={styles.title}>{tab?.title || ""}</div>}
        </div>
        {!!right && (
          <div className={styles.right} onClick={onRightClick}>
            <div className={styles.word}>{right}</div>
            <IconSvg name="icon-a-jiaocaiqiehuan2x" />
          </div>
        )}
        {showTab && (
          <div className={styles.center}>
            {tabs.map((tab) => (
              <div
                className={cls([
                  styles.item,
                  tab.key === activeTab && styles.selected,
                ])}
                key={tab.key}
                onClick={() => onItemClick(tab)}
              >
                {tab.title}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

const Header = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [tabs, setTabs] = useState<ITabItem[]>([]);
  const { currentSubject, currentBook, onClickEdition, onClickSubject } =
    useSubjectBook();
  const { safeTop, showBook, activeChannel, categorySubject } = useBaseInfo();
  const categoryType = channelTypeToCategoryType[activeChannel];
  const right = showBook ? currentBook.editionName : currentSubject.subjectName;

  const onTabClick = (tab: ITabItem) => {
    if (tab.key === activeChannel) {
      return;
    }
    navigate(
      createURLByType({
        path: `${basePath}/${tab.key}`,
        originSearch: location.search,
      }),
      { replace: true },
    );
  };

  const onRightClick = () => {
    if (showBook) {
      onClickEdition({
        filter_type: "教材版本",
        filter_value: currentBook.editionName,
      });
    } else {
      onClickSubject({
        filter_type: "学科",
        filter_value: currentSubject.subjectName,
      });
    }
  };

  useEffect(() => {
    const currentTabs: ITabItem[] = [];
    if (categorySubject[categoryType]) {
      if (activeChannel === ChannelTypeEnum.LITERACY) {
        // 素养拓展 只显示一个 tab
        currentTabs.push({
          key: ChannelTypeEnum.LITERACY,
          title: categorySubject[categoryType].categoryName,
        });
      } else if (showBook) {
        // 教材同步 与 重点复习 需要显示这两个 tab
        const textbookType =
          channelTypeToCategoryType[ChannelTypeEnum.TEXTBOOK];
        const keyPointsType =
          channelTypeToCategoryType[ChannelTypeEnum.KEY_POINTS];
        if (
          categorySubject[textbookType] &&
          categorySubject[textbookType].subjectList.find(
            (item) => item.subjectId === currentSubject.subjectId,
          )
        ) {
          currentTabs.push({
            key: ChannelTypeEnum.TEXTBOOK,
            title: categorySubject[textbookType].categoryName,
          });
        }
        if (
          categorySubject[keyPointsType] &&
          categorySubject[keyPointsType].subjectList.find(
            (item) => item.subjectId === currentSubject.subjectId,
          )
        ) {
          currentTabs.push({
            key: ChannelTypeEnum.KEY_POINTS,
            title: categorySubject[keyPointsType].categoryName,
          });
        }
      } else {
        // 备战高考 与 备战学考 需要显示这两个 tab
        const collegeExamType =
          channelTypeToCategoryType[ChannelTypeEnum.COLLEGE_EXAM];
        const studyExamType =
          channelTypeToCategoryType[ChannelTypeEnum.STUDY_EXAM];
        if (
          categorySubject[studyExamType] &&
          categorySubject[studyExamType].subjectList.find(
            (item) => item.subjectId === currentSubject.subjectId,
          )
        ) {
          currentTabs.push({
            key: ChannelTypeEnum.STUDY_EXAM,
            title: categorySubject[studyExamType].categoryName,
          });
        }
        if (
          categorySubject[collegeExamType] &&
          categorySubject[collegeExamType].subjectList.find(
            (item) => item.subjectId === currentSubject.subjectId,
          )
        ) {
          currentTabs.push({
            key: ChannelTypeEnum.COLLEGE_EXAM,
            title: categorySubject[collegeExamType].categoryName,
          });
        }
      }
    }
    setTabs(currentTabs);
  }, [showBook, activeChannel, categorySubject, currentSubject]);

  return (
    <BaseHeader
      safeTop={safeTop}
      tabs={tabs}
      activeTab={activeChannel}
      right={right}
      onTabClick={onTabClick}
      onLeftClick={() => closeWebview()}
      onRightClick={onRightClick}
    />
  );
};

export default Header;
