.item {
  height: 36px;
  padding: 0 16px;
  background: #e2eeff;
  border: 1px solid #021e66;
  border-radius: 19px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
  color: #ffffff;
  line-height: 22px;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.26);
  white-space: nowrap;

  position: relative;
  z-index: 1;

  &::before {
    content: attr(data-text);
    position: absolute;
    -webkit-text-stroke: 2px #6e7ea7;
    z-index: -1;
  }

  &.selected_1,
  &.selected_2,
  &.selected_3,
  &.selected_4,
  &.selected_5,
  &.selected_6 {
    &::before {
      -webkit-text-stroke: 2px #021e66;
    }
  }

  &.selected_1 {
    background: url(../../../../assets/subject-channel/select-bg1.png) left
      center/ cover no-repeat;
  }
  &.selected_2 {
    background: url(../../../../assets/subject-channel/select-bg2.png) left
      center/ cover no-repeat;
  }
  &.selected_3 {
    background: url(../../../../assets/subject-channel/select-bg3.png) left
      center/ cover no-repeat;
  }
  &.selected_4 {
    background: url(../../../../assets/subject-channel/select-bg4.png) left
      center/ cover no-repeat;
  }
  &.selected_5 {
    background: url(../../../../assets/subject-channel/select-bg5.png) left
      center/ cover no-repeat;
  }
  &.selected_6 {
    background: url(../../../../assets/subject-channel/select-bg6.png) left
      center/ cover no-repeat;
  }
}

.one {
  padding: 0 12px;
  height: 36px;
}

.multi {
  height: 36px;

  :global {
    .adm-capsule-tabs-header {
      padding: 0;
      border: 0;

      .adm-scroll-mask {
        display: none;
      }

      .adm-capsule-tabs-tab-wrapper {
        padding: 0;
        padding-left: 12px;

        .adm-capsule-tabs-tab {
          padding: 0;
          background: transparent;
        }
      }

      div.adm-capsule-tabs-tab-wrapper:last-child {
        padding-right: 12px;
      }
    }
  }
}
