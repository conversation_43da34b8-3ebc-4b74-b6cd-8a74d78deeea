import React from "react";
import { cls } from "@/utils/tool";
import { CapsuleTabs } from "antd-mobile";

import styles from "./index.module.scss";

interface ITabItem {
  key: string;
  title: string;
}

const CustomTab = ({
  activeTab,
  tabs,
  onTabClick,
}: {
  activeTab: string;
  tabs: ITabItem[];
  onTabClick?: (tab: ITabItem, index: number) => void;
}) => {
  const tabCount = tabs.length;

  const renderOne = (tab: ITabItem, index: number, limitLen = 18) => {
    const bgIndex = (index % 6) + 1;
    const isActive = `${activeTab}` === `${tab.key}`;
    let word = tab.title;
    // 简单处理文字超长的情况
    if (limitLen > 0 && word.length > limitLen) {
      word = word.slice(0, limitLen) + "...";
    }
    return (
      <div
        key={tab.key}
        className={cls([
          styles.item,
          isActive && styles[`selected_${bgIndex}`],
        ])}
        data-text={word}
        onClick={() => onClick(tab, index)}
      >
        {word}
      </div>
    );
  };

  const onClick = (tab: ITabItem, index: number) => {
    if (`${activeTab}` === `${tab.key}`) {
      return;
    }
    onTabClick?.(tab, index);
  };

  // 只有一个 tab 时，需要占满一行，且字数有限，不然描边啥的可能有问题
  if (tabCount === 1) {
    return <div className={styles.one}>{renderOne(tabs[0], 0)}</div>;
  }

  return (
    <div className={styles.multi}>
      <CapsuleTabs activeKey={activeTab}>
        {tabs.map((tab, index) => (
          <CapsuleTabs.Tab key={tab.key} title={renderOne(tab, index)} />
        ))}
      </CapsuleTabs>
    </div>
  );
};
export default CustomTab;
