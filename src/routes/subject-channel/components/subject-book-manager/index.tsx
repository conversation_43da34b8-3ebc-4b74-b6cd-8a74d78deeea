import React, {
  ForwardedRef,
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { useBaseInfo } from "../../context/base-info";
import { useSubjectBook } from "../../context/subject-book";
import DelayPageLoading from "@/components/delay-page-loading";
import SubjectPopup from "../subject-popup";
import { channelTypeToCategoryType } from "../../constants";
import { ICurrentBook, ISubject } from "../../apis";
import EditionPopup, {
  IUserSubjectEditionMap,
} from "@/components/edition-popup";
import BookPopup, { IBookPopupRef, IUserSelectedBook } from "../book-popup";
import { closeWebview } from "@/utils/bridge-utils";
import SafeLogger from "@/utils/safe-logger";
import useBaseEdition from "@/components/edition-popup/base-edition-hook";

export interface ISubjectBookManagerRef {
  showEdition: () => void;
  showBook: () => void;
  showSubject: () => void;
}

export enum PopupType {
  CLOSE = "close",
  // 教材版本弹窗
  EDITION = "edition",
  // 教材弹窗
  BOOK = "book",
  // 学科弹窗
  SUBJECT = "subject",
}

const SubjectBookManager = forwardRef(function SubjectBookManager(
  {
    onChange,
    logger,
  }: {
    onChange: (
      subject?: ISubject,
      book?: ICurrentBook,
      needRecheck?: boolean,
    ) => void;
    logger?: SafeLogger;
  },
  ref: ForwardedRef<ISubjectBookManagerRef>,
) {
  const { currentSubject, currentBook } = useSubjectBook();
  const { showBook, activeChannel, categorySubject } = useBaseInfo();
  const [popupType, setPopupType] = useState(PopupType.CLOSE);
  const needShowPopupTypeRef = useRef(PopupType.BOOK);
  const bookPopupRef = useRef<IBookPopupRef>(null);
  // 需要聚焦的 学科
  const [forceSubjectId, setForceSubjectId] = useState(1);
  const categoryType = channelTypeToCategoryType[activeChannel];
  const subjectList = categorySubject[categoryType]?.subjectList || [];

  const {
    loading: editionLoading,
    isError: editionError,
    initEditions,
    allSubjectEditionRef,
    userSubjectEditionRef,
    onSaveEdition,
  } = useBaseEdition({ logger });

  // 检查用户 是否对所有 学科都设置了 教材版本
  const checkAllSubjectEdition = async (popupType: PopupType) => {
    needShowPopupTypeRef.current = popupType;
    const isReady = await initEditions();
    if (!isReady) {
      setPopupType(PopupType.EDITION);
      return;
    }
    // 当前是否所有学科都设置了教材版本
    const isAllSubjectHasEdition =
      allSubjectEditionRef.current.length ===
      Object.keys(userSubjectEditionRef.current).length;
    if (!isAllSubjectHasEdition) {
      // 如果存在有学科未选择 教材版本，则必然 弹出 教材版本 弹窗
      setPopupType(PopupType.EDITION);
    } else {
      setPopupType(needShowPopupTypeRef.current);
    }
  };

  const onClickBookEdition = (subjectId: number) => {
    needShowPopupTypeRef.current = PopupType.BOOK;
    setForceSubjectId(subjectId);
    setPopupType(PopupType.EDITION);
  };

  const onEditionClose = () => {
    // 当前是否有选中书本
    const isHasBook = !!currentBook.bookId;
    // 当前是否所有学科都设置了教材版本
    const isAllSubjectHasEdition =
      allSubjectEditionRef.current.length ===
      Object.keys(userSubjectEditionRef.current).length;
    // 是不是原先就想打开 教材版本切换 弹窗
    const isStartEdition = needShowPopupTypeRef.current === PopupType.EDITION;
    // 判断是不是 所有学科 都选择了 教材版本
    // 如果已经都选了，则直接关闭，或者返回上一个弹窗
    // 反之，如果当前有选中书本，则直接关闭，如果没有选中书本，则直接退出页面
    if (isAllSubjectHasEdition) {
      setPopupType(
        isStartEdition ? PopupType.CLOSE : needShowPopupTypeRef.current,
      );
    } else {
      isHasBook ? setPopupType(PopupType.CLOSE) : closeWebview();
    }
  };

  const onEditionConfirm = async (userSelected: IUserSubjectEditionMap) => {
    const isDown = await onSaveEdition(userSelected);
    if (!isDown) {
      return;
    }
    // 当前学科的 教材版本 是否变更
    const isEditionChange =
      userSelected[currentSubject.subjectId].editionId !==
      currentBook.baseEditionId;
    // 是不是原先就想打开 教材版本切换 弹窗
    const isStartEdition = needShowPopupTypeRef.current === PopupType.EDITION;

    // 当前学科的教材版本如果有变更，则需要更新全局状态
    // 如果原先目的就是打开 教材版本切换 弹窗，则关闭弹窗，并外部重新走一下初始化流程
    // 反之，则返回 书本 弹窗，让书本弹窗来处理 书本选中
    if (isEditionChange) {
      onChange(
        null,
        {
          ...userSelected[currentSubject.subjectId],
          baseEditionId: userSelected[currentSubject.subjectId].editionId,
        },
        isStartEdition,
      );
    }
    setPopupType(
      isStartEdition ? PopupType.CLOSE : needShowPopupTypeRef.current,
    );
  };

  const onBookClose = () => {
    // 当前是否有选中书本
    const isHasBook = !!currentBook.bookId;
    if (isHasBook) {
      setPopupType(PopupType.CLOSE);
    } else {
      closeWebview();
    }
  };

  const onBookConfirm = (userSelectedBook: IUserSelectedBook) => {
    if (currentSubject.subjectId !== userSelectedBook.subjectId) {
      // 如果学科已经变更，直接重新走初始化流程
      onChange({
        subjectId: userSelectedBook.subjectId,
        subjectName: userSelectedBook.subjectName,
      });
    } else {
      // 其他情况，只需要更新 书本 信息，重新加载列表数据即可
      onChange(null, {
        ...userSelectedBook,
      });
    }
    setPopupType(PopupType.CLOSE);
  };

  const onInitErrorRetry = () => {
    checkAllSubjectEdition(needShowPopupTypeRef.current);
  };

  useImperativeHandle(ref, () => {
    return {
      showSubject: () => {
        if (showBook) {
          bookPopupRef.current?.resetInfo(
            currentSubject as ISubject,
            currentBook,
            true,
          );
          checkAllSubjectEdition(PopupType.BOOK);
        } else {
          setPopupType(PopupType.SUBJECT);
        }
      },
      // 只有显示 教材 的场景，才能切换教材版本
      showEdition: () => {
        if (showBook) {
          setForceSubjectId(currentSubject.subjectId);
          checkAllSubjectEdition(PopupType.EDITION);
        }
      },
      // 只有显示 教材 的场景，才能切换教材
      showBook: () => {
        if (showBook) {
          bookPopupRef.current?.resetInfo(
            currentSubject as ISubject,
            currentBook,
            false,
          );
          checkAllSubjectEdition(PopupType.BOOK);
        }
      },
    };
  }, [showBook, activeChannel, categorySubject, currentSubject, currentBook]);
  if (!currentSubject.subjectId) {
    return null;
  }
  return (
    <div>
      <SubjectPopup
        open={popupType === PopupType.SUBJECT}
        subjectList={subjectList}
        currentSubject={currentSubject as ISubject}
        onClose={() => setPopupType(PopupType.CLOSE)}
        onConfirm={(subject) => {
          onChange(subject);
          setPopupType(PopupType.CLOSE);
        }}
      />
      <EditionPopup
        open={popupType === PopupType.EDITION}
        list={allSubjectEditionRef.current}
        userSelected={userSubjectEditionRef.current}
        isError={editionError}
        showSubjectId={forceSubjectId}
        onRetry={onInitErrorRetry}
        onClose={onEditionClose}
        onConfirm={onEditionConfirm}
      />
      <BookPopup
        open={popupType === PopupType.BOOK}
        subjectList={subjectList}
        currentSubject={currentSubject as ISubject}
        currentBook={currentBook}
        subjectEditionMap={userSubjectEditionRef.current}
        logger={logger}
        onChangeEdition={onClickBookEdition}
        onClose={onBookClose}
        onConfirm={onBookConfirm}
        ref={bookPopupRef}
      />
      <DelayPageLoading loading={editionLoading} />
    </div>
  );
});
export default SubjectBookManager;
