@import "~@/styles/lib.scss";

.chapter_container {
  padding-top: 20px;
  .chapter {
    padding: 0 12px 0 32px;
    font-weight: bold;
    font-size: 16px;
    color: #ffffff;
    line-height: 22px;
    position: relative;
    word-wrap: break-word;

    .icon {
      width: 12px;
      height: 12px;
      position: absolute;
      left: 12px;
      top: 5px;
    }

    &.expand {
      .icon {
        transform: rotate(90deg);
      }
    }
  }
}

.section {
  position: relative;
  padding: 20px 12px 0 32px;
  word-wrap: break-word;
  font-weight: bold;
  font-size: 14px;
  color: #ffffff;
  line-height: 20px;

  &::before {
    content: " ";
    width: 4px;
    height: 4px;
    background: #ffffff;
    border-radius: 50%;
    position: absolute;
    left: 16px;
    top: 28px;
  }

  &.after_chapter {
    padding-top: 4px;

    &::before {
      top: 12px;
    }
  }
}

.item_container {
  position: relative;
  padding: 19px 0 0 12px;
}

.placeholder {
  width: 351px;
  height: 168px;
  background: #567dff;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  flex-direction: column;

  .word {
    font-size: 14px;
    line-height: 20px;
    color: #ffffff;
  }

  .btn {
    width: 116px;
    height: 32px;
    margin-top: 12px;
    border: 1px solid #ffffff;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #ffffff;
  }

  .block_1 {
    background: #6f8afa;
    left: 16px;
    top: 16px;
    width: 137px;
    height: 78px;
  }

  .block_2 {
    background: #6f8afa;
    left: 165px;
    top: 16px;
    width: 170px;
    height: 15px;
  }

  .block_3 {
    background: #6f8afa;
    left: 165px;
    top: 35px;
    width: 129px;
    height: 15px;
  }

  .block_4 {
    background: #6f8afa;
    left: 165px;
    top: 60px;
    width: 170px;
    height: 15px;
  }

  .block_5 {
    background: #6f8afa;
    left: 165px;
    top: 79px;
    width: 129px;
    height: 15px;
  }

  .block_6 {
    background: #6f8afa;
    left: 16px;
    top: 111px;
    width: 319px;
    height: 41px;
  }

  &.expand_placeholder {
    height: 230px;

    .block_6 {
      height: 103px;
    }
  }
}

.item {
  width: 351px;
  height: 168px;
  background: #ffffff;
  border: 1px solid #021e66;
  border-radius: 4px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  .container1 {
    width: 343px;
    height: 160px;
    border: 1px solid #e6e9ed;
    border-radius: 2px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .line1 {
      height: 83px;
      width: 319px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .cover {
        width: 137px;
        height: 78px;
        border: 1px solid #021e66;
        border-radius: 2px;
        background-color: #ddd;
      }

      .infos {
        height: 83px;
        width: 170px;

        .title {
          height: 40px;
          font-weight: bold;
          line-height: 20px;
          font-size: 14px;
          color: #021e66;
          word-wrap: break-word;

          @include multi_lines_ellipsis(2);
        }

        .tags {
          margin-top: 4px;
          height: 18px;
          display: flex;
          overflow: hidden;

          .tag {
            height: 18px;
            border: 1px solid rgba(2, 30, 102, 0.85);
            border-radius: 2px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 4px;
            font-size: 12px;
            color: rgba(2, 30, 102, 0.85);
            margin-right: 4px;
            flex: 0 0 auto;
          }

          .hard_1 {
            background: #eaf5ff;
            border-color: #004fbb;
            color: #004fbb;
          }

          .hard_2 {
            background: #baf2ea;
            border-color: #158d91;
            color: #0c9497;
          }

          .hard_3 {
            background: #ffdcdc;
            border-color: #ff5858;
            color: #ff5858;
          }
        }

        .numbers {
          line-height: 17px;
          height: 17px;
          margin-top: 4px;
          display: flex;
          align-items: center;
          color: #6e7ea7;
          font-size: 12px;

          :global {
            .svg-icon {
              overflow: visible;
            }
          }

          .num {
            margin: 0 8px 0 2px;
          }
        }
      }
    }

    .line2 {
      width: 319px;
      margin-top: 15px;
      padding: 4px;
      background: #eaf5ff;
      border-radius: 2px;

      .lesson_info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 33px;

        .word {
          padding-left: 8px;
          font-weight: bold;
          font-size: 12px;
          color: #021e66;
          height: 33px;
          line-height: 33px;

          span {
            font-weight: normal;
            margin-left: 4px;
          }
        }

        .expand_icon {
          height: 33px;
          width: 36px;
          font-size: 12px;
          color: #6e7ea7;
          display: flex;
          align-items: center;
          justify-content: center;
          transform: scaleY(-1);

          &.expand {
            transform: scaleY(1);
          }
        }
      }

      .lessons {
        width: 311px;
        height: 62px;
        overflow: hidden;
        position: relative;

        .lesson {
          width: 96px;
          height: 54px;
          border: 1px solid #ffd52f;
          border-radius: 4px;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: #fff;

          .lesson_in {
            width: 92px;
            height: 50px;
            border: 1px solid #536281;
            border-radius: 2px;
            display: flex;
            justify-content: center;
            align-items: center;

            .lesson_name {
              width: 72px;
              height: 34px;
              font-weight: bold;
              font-size: 12px;
              line-height: 17px;
              color: #021e66;
              word-wrap: break-word;

              @include multi_lines_ellipsis(2);
            }
          }
        }

        .block_1 {
          left: 8px;
          width: 96px;
          height: 54px;
          background-color: #ddd;
        }

        .block_2 {
          left: 116px;
          width: 96px;
          height: 54px;
          background-color: #ddd;
        }

        .block_3 {
          left: 224px;
          width: 87px;
          height: 54px;
          background-color: #ddd;
        }

        .lesson_error {
          height: 60px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          color: rgba(2, 30, 102, 0.65);
          line-height: 20px;

          .retry {
            height: 20px;
            background: #2e86ff;
            border-radius: 10px;
            padding: 0 8px;
            color: #ffffff;
          }
        }

        :global {
          .swiper {
            height: 54px;

            .swiper-slide {
              width: auto !important;
              padding: 0 4px 0 8px;
            }
          }
        }
      }
    }
  }

  .teacher {
    position: absolute;
    font-weight: bold;
    font-size: 12px;
    color: #ffffff;
    line-height: 20px;
    height: 20px;
    right: 10px;
    top: -8px;
    white-space: nowrap;
    z-index: 1;

    &::before {
      content: " ";
      position: absolute;
      left: -12px;
      right: -7px;
      bottom: 0;
      height: 20px;
      background: #8391b4;
      border: 1px solid #021e66;
      transform: skewX(-22deg);
      z-index: -1;
    }

    .avatar {
      width: 24px;
      height: 24px;
      background: #ddd;
      border: 1px solid #021e66;
      border-radius: 50%;
      position: absolute;
      bottom: 0;
      left: -26px;
    }
  }

  .last {
    width: 73px;
    height: 26px;
    position: absolute;
    left: 80px;
    top: 3px;
    background: url(../../../../assets/subject-channel/last.png) center/cover no-repeat;
  }

  &.expand_item {
    height: 230px;

    .container1 {
      height: 222px;
    }
  }
}

.course_more {
  padding-top: 28px;
  margin: 0 auto;
}
