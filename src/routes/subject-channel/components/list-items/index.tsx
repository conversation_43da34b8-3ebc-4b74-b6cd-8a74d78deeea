import React, { CSSProperties, memo } from "react";
import { IconSvg } from "@/components";
import { cls, handleNumberToTenThousand } from "@/utils/tool";
import { Swiper, SwiperSlide } from "swiper/react";
import { areEqual } from "react-window";

import "swiper/css";

import styles from "./index.module.scss";
import LoadingBlock from "@/components/loading-block";
import LoadingImage from "@/components/loading-image";
import CourseCoverDefaultPng from "@/assets/common/course-cover-default.png";
import TeacherDefaultPng from "@/assets/common/teacher-default.png";
import { ICourseItem, ILessonItem } from "../../apis";
import LessonMore from "../lesson-more";
import { makeThumbnailImage } from "@/utils/image";
import { useBaseInfo } from "../../context/base-info";
import { channelTypeToCategoryType } from "../../constants";

export const Chapter = memo(function Chapter({
  name,
  expand,
  chapterId,
  onExpandChange,
  style,
}: {
  name: string;
  expand: boolean;
  chapterId: string;
  onExpandChange: (isExpand: boolean, chapterId: string) => void;
  style?: React.CSSProperties;
}) {
  return (
    <div className={styles.chapter_container} style={style}>
      <div
        className={cls([styles.chapter, expand && styles.expand])}
        onClick={() => onExpandChange(!expand, chapterId)}
      >
        <IconSvg className={styles.icon} name="icon-a-zhangbiaotizhankai2x" />
        {name}
      </div>
    </div>
  );
}, areEqual);

export const Section = memo(function Section({
  name,
  isAfterChapter,
  style,
}: {
  name: string;
  isAfterChapter: boolean;
  style?: React.CSSProperties;
}) {
  return (
    <div
      className={cls([styles.section, isAfterChapter && styles.after_chapter])}
      style={style}
    >
      {name}
    </div>
  );
}, areEqual);

export const Placeholder = memo(function Placeholder({
  style,
  isError,
  isExpand,
  courseId,
  index,
  onRetry,
}: {
  style?: CSSProperties;
  isError: boolean;
  isExpand: boolean;
  courseId: string;
  index: number;
  onRetry: (courseId: string, index: number) => void;
}) {
  const className = cls([
    styles.placeholder,
    isExpand && styles.expand_placeholder,
  ]);

  return (
    <div className={styles.item_container} style={style}>
      {isError ? (
        <div className={className}>
          <div className={styles.word}>啊哦～发生了未知异常，请稍后重试</div>
          <div className={styles.btn} onClick={() => onRetry(courseId, index)}>
            重新加载
          </div>
        </div>
      ) : (
        <div className={className}>
          <LoadingBlock className={styles.block_1} />
          <LoadingBlock className={styles.block_2} />
          <LoadingBlock className={styles.block_3} />
          <LoadingBlock className={styles.block_4} />
          <LoadingBlock className={styles.block_5} />
          <LoadingBlock className={styles.block_6} />
        </div>
      )}
    </div>
  );
}, areEqual);

export const Item = memo(function Item({
  style,
  lessonError,
  isExpand,
  info,
  lessons,
  studyKey,
  index,
  parentIds,
  onExpandChange,
  onLessonRetry,
  onClickVideo,
}: {
  style?: CSSProperties;
  lessonError?: boolean;
  isExpand: boolean;
  info: ICourseItem;
  lessons?: ILessonItem[];
  studyKey: string;
  index: number;
  parentIds: string[];
  onExpandChange: (isExpand: boolean, info: ICourseItem, index: number) => void;
  onLessonRetry: (info: ICourseItem, index: number) => void;
  onClickVideo: (
    parentIds: string[],
    courseId: string,
    lessonId?: string,
  ) => void;
}) {
  const { lastStudyKey } = useBaseInfo();
  const showLast = studyKey === lastStudyKey;
  const lessonCount = (lessons || []).length;
  const onExpandClick = () => {
    onExpandChange(!isExpand, info, index);
  };

  const onRetryClick = () => {
    onLessonRetry(info, index);
  };

  const showHard =
    !!info.hardLevel && info.hardLevel.id >= 1 && info.hardLevel.id <= 3;
  const gradeWord = (info.gradeList || [])
    .map((item) => item.gradeName)
    .join("/");
  let lessonCountWord = "";
  if (info.onlineLessonCount >= 0 && info.lessonCount >= 0) {
    lessonCountWord =
      info.onlineLessonCount >= info.lessonCount
        ? `共${info.onlineLessonCount}讲`
        : `更新至${info.onlineLessonCount}/${info.lessonCount}讲`;
  }
  const teacherCount = (info.teacherList || []).length;
  const currentTeacher = info.teacherList?.[0];

  let teacherName = currentTeacher?.teacherName || "";
  if (teacherName.length > 11) {
    teacherName = teacherName.slice(0, 11) + "...";
  }
  if (teacherCount > 1) {
    teacherName += " 等";
  }

  return (
    <div className={styles.item_container} style={style}>
      <div className={cls([styles.item, isExpand && styles.expand_item])}>
        <div className={styles.container1}>
          <div
            className={styles.line1}
            onClick={() => onClickVideo(parentIds, info.id)}
          >
            <LoadingImage
              src={makeThumbnailImage(info.picture, 274, 156)}
              fallback={CourseCoverDefaultPng}
              className={styles.cover}
            />
            <div className={styles.infos}>
              <div className={styles.title}>{info.courseName}</div>
              <div className={styles.tags}>
                {showHard && (
                  <div
                    className={cls([
                      styles.tag,
                      styles[`hard_${info.hardLevel.id}`],
                    ])}
                  >
                    {info.hardLevel.name}
                  </div>
                )}
                {!!gradeWord && <div className={styles.tag}>{gradeWord}</div>}
              </div>
              <div className={styles.numbers}>
                {!!info.hitCount && (
                  <>
                    <IconSvg name="icon-a-bofangliang2x" />
                    <div className={styles.num}>
                      {handleNumberToTenThousand(info.hitCount)}次
                    </div>
                  </>
                )}
                {!!info.courseScore && (
                  <>
                    <IconSvg name="icon-a-kechengpingfen2x" />
                    <div className={styles.num}>{info.courseScore}分</div>
                  </>
                )}
              </div>
            </div>
          </div>
          <div className={styles.line2}>
            <div className={styles.lesson_info} onClick={onExpandClick}>
              <div className={styles.word}>
                课程目录<span>{lessonCountWord}</span>
              </div>
              <div
                className={cls([styles.expand_icon, isExpand && styles.expand])}
              >
                <IconSvg name="icon-a-kechengjiangshouqi2x" />
              </div>
            </div>
            {isExpand && (
              <div className={styles.lessons}>
                {lessonCount > 0 ? (
                  <Swiper slidesPerView="auto" threshold={10}>
                    {lessons.map((lesson, index) => (
                      <SwiperSlide key={lesson.lessonId}>
                        <div
                          className={styles.lesson}
                          onClick={() =>
                            onClickVideo(parentIds, info.id, lesson.lessonId)
                          }
                        >
                          <div className={styles.lesson_in}>
                            <div className={styles.lesson_name}>
                              {`${index < 9 ? "0" : ""}${index + 1} ${lesson.lessonName}`}
                            </div>
                          </div>
                        </div>
                      </SwiperSlide>
                    ))}

                    <SwiperSlide key="empty"></SwiperSlide>
                  </Swiper>
                ) : lessonError ? (
                  <div className={styles.lesson_error}>
                    <div>加载失败！</div>
                    <div className={styles.retry} onClick={onRetryClick}>
                      重试
                    </div>
                  </div>
                ) : (
                  <>
                    <LoadingBlock className={styles.block_1} />
                    <LoadingBlock className={styles.block_2} />
                    <LoadingBlock className={styles.block_3} />
                  </>
                )}
              </div>
            )}
          </div>
        </div>
        {!!currentTeacher && (
          <div className={styles.teacher}>
            {teacherName}
            <LoadingImage
              src={makeThumbnailImage(currentTeacher.teacherPicture, 44, 44)}
              fallback={TeacherDefaultPng}
              className={styles.avatar}
            />
          </div>
        )}
        {showLast && <div className={styles.last}></div>}
      </div>
    </div>
  );
}, areEqual);

export const CourseMore = memo(function CourseMore({
  style,
}: {
  style?: CSSProperties;
}) {
  const { oldUrl, activeChannel, categorySubject } = useBaseInfo();
  return (
    <LessonMore
      className={styles.course_more}
      style={style}
      oldUrl={oldUrl}
      qtEvent="ewt_h5_study_channel_textbook_search_click"
      qtMore={{
        page: categorySubject[channelTypeToCategoryType[activeChannel]]
          ?.categoryName,
      }}
    />
  );
}, areEqual);
