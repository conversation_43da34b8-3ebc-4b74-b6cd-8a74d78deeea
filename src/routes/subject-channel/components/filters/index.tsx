import React, { CSSProperties, memo } from "react";
import CustomTab from "../custom-tab";
import { IconSvg } from "@/components";
import MenuPng from "@/assets/subject-channel/menu.png";

import styles from "./index.module.scss";
import { cls } from "@/utils/tool";
import { useSubjectBook } from "../../context/subject-book";
import { useHardLevels } from "../../context/hard-levels";
import { useBaseInfo } from "../../context/base-info";
import { SubjectIconName } from "../../constants";
import { areEqual } from "react-window";

const Filters = memo(function Filters({ style }: { style?: CSSProperties }) {
  const {
    currentSubject,
    currentBook,
    otherBookList,
    onClickSubject,
    onClickFilterBook,
    onChangeBook,
  } = useSubjectBook();
  const { hardLevels, currentHardLevel, onHardLevelChange } = useHardLevels();
  const { showBook, showMenu, onClickMenu, showHardLevels, showFilterLine2 } =
    useBaseInfo();
  const onLevelClick = (id: number) => {
    if (id !== currentHardLevel) {
      onHardLevelChange(id);
    }
  };

  return (
    <div className={styles.container} style={style}>
      {showBook ? (
        <div className={styles.actions}>
          <div className={styles.btns}>
            {!!currentSubject.subjectId && (
              <div
                className={styles.btn}
                onClick={() =>
                  onClickSubject({
                    filter_type: "学科",
                    filter_value: currentSubject.subjectName,
                  })
                }
              >
                {!!SubjectIconName[currentSubject.subjectId] && (
                  <div className={styles.icon}>
                    <IconSvg name={SubjectIconName[currentSubject.subjectId]} />
                  </div>
                )}
                <div className={styles.word}>{currentSubject.subjectName}</div>
                <IconSvg
                  className={styles.arrow}
                  name="icon-a-xuanxiangzhankai2x"
                />
              </div>
            )}
            {!!currentBook.bookId && (
              <div
                className={styles.btn}
                onClick={() =>
                  onClickFilterBook({
                    filter_type: "教材",
                    filter_value: currentBook.bookName,
                  })
                }
              >
                <div className={styles.word}>
                  {currentBook.bookName || "切换教材"}
                </div>
                <IconSvg
                  className={styles.arrow}
                  name="icon-a-xuanxiangzhankai2x"
                />
              </div>
            )}
          </div>
          {showMenu && (
            <div className={styles.menu} onClick={onClickMenu}>
              目录 <img src={MenuPng} /> <IconSvg name="icon-a-jinru2x" />
            </div>
          )}
        </div>
      ) : (
        <CustomTab
          activeTab={currentBook.bookId}
          tabs={otherBookList.map((book) => ({
            key: book.bookId,
            title: book.bookName,
          }))}
          onTabClick={(tab) => {
            onChangeBook(tab.key, tab.title);
          }}
        />
      )}
      {showFilterLine2 && (
        <div className={styles.levels_container}>
          {showHardLevels && (
            <div className={styles.levels}>
              {hardLevels.map((hardLevel) => (
                <div
                  key={hardLevel.id}
                  className={cls([
                    styles.level,
                    hardLevel.id === currentHardLevel && styles.selected,
                  ])}
                  onClick={() => onLevelClick(hardLevel.id)}
                >
                  {hardLevel.name}
                </div>
              ))}
            </div>
          )}

          {!showBook && showMenu && (
            <div className={styles.menu} onClick={onClickMenu}>
              目录 <img src={MenuPng} /> <IconSvg name="icon-a-jinru2x" />
            </div>
          )}
        </div>
      )}
    </div>
  );
}, areEqual);

export default Filters;
