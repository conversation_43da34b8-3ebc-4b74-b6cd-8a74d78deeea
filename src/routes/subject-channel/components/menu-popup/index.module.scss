@import "~@/styles/lib.scss";

.popup {
  // border-radius: 16px 16px 0 0;
  height: 70vh;
  @include adaptive-max((
    border-radius: 16px 16px 0 0,
  ));

  .container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .close {
    position: absolute;
    // top: 10px;
    right: 0;
    // font-size: 14px;
    // width: 46px;
    // height: 46px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #a7acb9;
    @include adaptive-max((
      top: 10px,
      font-size: 14px,
      width: 46px,
      height: 46px,
    ));
  }

  .title {
    // height: 57px;
    text-align: center;
    font-weight: bold;
    // font-size: 18px;
    color: #333333;
    // padding-top: 20px;
    flex: 0 0 auto;
    @include adaptive-max((
      height: 57px,
      font-size: 18px,
      padding-top: 20px,
    ));
  }

  .content {
    flex: 1;
    overflow-x: hidden;
    overflow-y: auto;

    .top {
      // height: 5px;
      @include adaptive-max((
        height: 5px,
      ));
    }

    .bottom {
      box-sizing: content-box;
      // height: 19px;
      padding-bottom: constant(safe-area-inset-bottom);
      padding-bottom: env(safe-area-inset-bottom);
      @include adaptive-max((
        height: 19px,
      ));
    }
  }

  .more {
    // padding-top: 41px;
    @include adaptive-max((
      padding-top: 41px,
    ));
  }
}

.item {
  position: relative;
  // padding: 0 20px 16px 44px;
  @include adaptive-max((
    padding: 0 20px 16px 44px,
  ));

  .expand_icon {
    position: absolute;
    // height: 21px;
    // width: 21px;
    display: flex;
    align-items: center;
    justify-content: center;
    // font-size: 16px;
    // left: 13.5px;
    top: 0;
    @include adaptive-max((
      height: 21px,
      width: 21px,
      font-size: 16px,
      left: 13.5px,
    ));

    :global {
      .svg-icon {
        overflow: visible;
      }
    }

    .point {
      // width: 6px;
      // height: 6px;
      background: #333333;
      // border-radius: 50%;
      @include adaptive-max((
        width: 6px,
        height: 6px,
        border-radius: 50%,
      ));
    }
  }

  .word_last {
    display: inline-block;
  }

  .right_icon {
    // font-size: 15px;
    color: #999999;
    // margin-left: -4px;
    display: inline-block;
    vertical-align: middle;
    position: relative;
    // top: -1px;
    @include adaptive-max((
      font-size: 15px,
      margin-left: -4px,
      top: -1px,
    ));
  }

  .span_right {
    // margin-right: 8px;
    @include adaptive-max((
      margin-right: 8px,
    ));
  }

  .chapter {
    // line-height: 21px;
    font-weight: bold;
    // font-size: 15px;
    color: #333333;
    word-wrap: break-word;
    @include adaptive-max((
      line-height: 21px,
      font-size: 15px,
    ));

    .word_last {
      // height: 21px;
      // line-height: 21px;
      @include adaptive-max((
        height: 21px,
        line-height: 21px,
      ));
    }
  }

  .section {
    // padding-top: 16px;
    // font-size: 14px;
    color: #333333;
    // line-height: 20px;
    word-wrap: break-word;
    @include adaptive-max((
      padding-top: 16px,
      font-size: 14px,
      line-height: 20px,
    ));

    .word_last {
      // height: 20px;
      // line-height: 20px;
      @include adaptive-max((
        height: 20px,
        line-height: 20px,
      ));
    }
  }

  &.expand {
    &::before {
      content: ' ';
      position: absolute;
      width: 0;
      // border-left: 1px dashed #EEEFF3;
      // left: 23.75px;
      // top: 10.5px;
      // bottom: -10.5px;
      @include adaptive-max((
        border-left: 1px dashed #EEEFF3,
        left: 23.75px,
        top: 10.5px,
        bottom: -10.5px,
      ));
    }

    .expand_icon {
      transform: rotate(90deg);
    }
  }

  &.item_last {
    padding-bottom: 0;

    &::before {
      bottom: 0;
    }
  }
}

.more {
  // padding-top: 41px;
  @include adaptive-max((
    padding-top: 41px,
  ));
}
