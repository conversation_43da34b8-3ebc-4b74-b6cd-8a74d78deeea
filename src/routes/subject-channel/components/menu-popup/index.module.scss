.popup {
  border-radius: 16px 16px 0 0;
  height: 70vh;

  .container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .close {
    position: absolute;
    top: 10px;
    right: 0;
    font-size: 14px;
    width: 46px;
    height: 46px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #a7acb9;
  }

  .title {
    height: 57px;
    text-align: center;
    font-weight: bold;
    font-size: 18px;
    color: #333333;
    padding-top: 20px;
    flex: 0 0 auto;
  }

  .content {
    flex: 1;
    overflow-x: hidden;
    overflow-y: auto;

    .top {
      height: 5px;
    }

    .bottom {
      box-sizing: content-box;
      height: 19px;
      padding-bottom: constant(safe-area-inset-bottom);
      padding-bottom: env(safe-area-inset-bottom);
    }
  }

  .more {
    padding-top: 41px;
  }
}

.item {
  position: relative;
  padding: 0 20px 16px 44px;

  .expand_icon {
    position: absolute;
    height: 21px;
    width: 21px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    left: 13.5px;
    top: 0;

    :global {
      .svg-icon {
        overflow: visible;
      }
    }

    .point {
      width: 6px;
      height: 6px;
      background: #333333;
      border-radius: 50%;
    }
  }

  .word_last {
    display: inline-block;
  }

  .right_icon {
    font-size: 15px;
    color: #999999;
    margin-left: -4px;
    display: inline-block;
    vertical-align: middle;
    position: relative;
    top: -1px;
  }

  .span_right {
    margin-right: 8px;
  }

  .chapter {
    line-height: 21px;
    font-weight: bold;
    font-size: 15px;
    color: #333333;
    word-wrap: break-word;

    .word_last {
      height: 21px;
      line-height: 21px;
    }
  }

  .section {
    padding-top: 16px;
    font-size: 14px;
    color: #333333;
    line-height: 20px;
    word-wrap: break-word;

    .word_last {
      height: 20px;
      line-height: 20px;
    }
  }

  &.expand {
    &::before {
      content: ' ';
      position: absolute;
      width: 0;
      border-left: 1px dashed #EEEFF3;
      left: 23.75px;
      top: 10.5px;
      bottom: -10.5px;
    }

    .expand_icon {
      transform: rotate(90deg);
    }
  }

  &.item_last {
    padding-bottom: 0;

    &::before {
      bottom: 0;
    }
  }
}

.more {
  padding-top: 41px;
}
