import React from "react";

import styles from "./index.module.scss";
import { IconSvg } from "@/components";
import { cls } from "@/utils/tool";
import LessonMore from "../lesson-more";
import { ITreeItem } from "../../apis";
import BasePopup from "@/components/base-popup";

const MenuItem = ({
  item,
  isLast,
  onSelect,
}: {
  item: ITreeItem;
  isLast: boolean;
  onSelect: (chapterId: string, sectionId?: string) => void;
}) => {
  const [isExpand, setIsExpand] = React.useState(true);
  const sectionLen = (item.children || []).length;
  const showExpand = isExpand && sectionLen > 0;

  const renderWord = (word: string) => {
    const last = (word || "").slice(-1) || "";
    const before = (word || "").slice(0, -1) || "";
    return [
      <span key={1}>{before}</span>,
      <div key={2} className={styles.word_last}>
        <span className={styles.span_right}>{last}</span>
        <IconSvg name="icon-jinru" className={styles.right_icon} />
      </div>,
    ];
  };

  return (
    <div
      className={cls([
        styles.item,
        showExpand && styles.expand,
        isLast && styles.item_last,
      ])}
    >
      <div
        className={styles.expand_icon}
        onClick={() => setIsExpand((expand) => !expand)}
      >
        {sectionLen > 0 ? (
          <IconSvg name="icon-a-mulushouqi2x" />
        ) : (
          <div className={styles.point}></div>
        )}
      </div>
      <div className={styles.chapter} onClick={() => onSelect(item.id)}>
        {renderWord(item.title)}
      </div>
      {showExpand && (
        <div>
          {(item.children || []).map((section) => (
            <div
              key={section.id}
              className={styles.section}
              onClick={() => onSelect(item.id, section.id)}
            >
              {renderWord(section.title)}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

const MenuPopup = ({
  open,
  onCancel,
  menus,
  onSelect,
}: {
  open: boolean;
  menus: ITreeItem[];
  onCancel: () => void;
  onSelect: (chapterId: string, sectionId?: string) => void;
}) => {
  const menuCount = menus.length;

  return (
    <BasePopup height="80vh" title="目录" open={open} onClose={onCancel}>
      {menus.map((menu, index) => (
        <MenuItem
          key={menu.id}
          item={menu}
          isLast={index === menuCount - 1}
          onSelect={onSelect}
        />
      ))}
      <LessonMore
        className={styles.more}
        qtEvent="ewt_h5_study_channel_textbook_menu_search_click"
      />
    </BasePopup>
  );
};

export default MenuPopup;
