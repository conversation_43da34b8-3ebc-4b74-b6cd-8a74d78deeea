import React, { CSSProperties } from "react";
import EmptyPng from "@/assets/common/empty-b.png";

import styles from "./index.module.scss";

const EmptyInfo = ({
  style,
  text,
}: {
  style?: CSSProperties;
  text?: string;
}) => {
  return (
    <div className={styles.container} style={style}>
      <img src={EmptyPng} />
      <div className={styles.word}>{text || "暂无数据"}</div>
    </div>
  );
};
export default EmptyInfo;
