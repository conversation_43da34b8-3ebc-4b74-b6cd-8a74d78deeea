import React, { useEffect, useState } from "react";
import BasePopup from "@/components/base-popup";

import styles from "./index.module.scss";
import { ISubject } from "../../apis";
import { cls } from "@/utils/tool";
import { SubjectIconName } from "../../constants";
import { IconSvg } from "@/components";

export const SubjectListComponent = ({
  subjectList,
  subject,
  onClickSubject,
}: {
  subjectList: ISubject[];
  subject: ISubject;
  onClickSubject: (subject: ISubject) => void;
}) => {
  return (
    <div className={styles.subject_container}>
      {subjectList.map((item) => (
        <div
          key={item.subjectId}
          className={cls([
            styles.subject,
            subject.subjectId === item.subjectId && styles.active,
          ])}
          onClick={() => onClickSubject(item)}
        >
          {!!SubjectIconName[item.subjectId] && (
            <div className={styles.icon}>
              <IconSvg name={SubjectIconName[item.subjectId]} />
            </div>
          )}
          <div>{item.subjectName}</div>
          <div className={styles.selected_icon}></div>
        </div>
      ))}
    </div>
  );
};

const SubjectPopup = ({
  open,
  currentSubject,
  subjectList,
  onClose,
  height,
  onConfirm,
}: {
  open: boolean;
  subjectList: ISubject[];
  height?: string;
  currentSubject: ISubject;
  onClose: () => void;
  onConfirm: (subject: ISubject) => void;
  onClickSubject?: (subject: ISubject) => void;
}) => {
  const [subject, setSubject] = useState(currentSubject);

  const onItemClick = (subject: ISubject) => {
    setSubject(subject);
  };

  const onConfirmClick = () => {
    if (subject.subjectId === currentSubject.subjectId) {
      onClose();
      return;
    }
    onConfirm(subject);
  };

  useEffect(() => {
    setSubject(currentSubject);
  }, [currentSubject]);

  return (
    <BasePopup
      height={height}
      open={open}
      title="所选学科"
      onClose={onClose}
      showConfirm
      onConfirm={onConfirmClick}
      closeOnMaskClick={true}
    >
      <SubjectListComponent
        subjectList={subjectList}
        subject={subject}
        onClickSubject={onItemClick}
      />
    </BasePopup>
  );
};

export default SubjectPopup;
