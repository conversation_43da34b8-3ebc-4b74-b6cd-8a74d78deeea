.subject_container {
  padding: 0 0 6px 16px;
  display: flex;
  flex-wrap: wrap;

  .subject {
    width: 107px;
    height: 40px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 11px;
    margin-bottom: 12px;
    font-size: 14px;
    color: #333333;
    font-weight: bold;
    position: relative;

    &::before {
      content: ' ';
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      border-radius: 20px;
      background: #f3f4f8;
      z-index: -1;
    }

    .icon {
      margin-right: 4px;
      color: #000000;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .selected_icon {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 19px;
      height: 16px;
      background: url(../../../../assets/subject-channel/subject-selected.png)
        center/contain no-repeat;
      display: none;
    }

    &.active {
      color: #2e86ff;
      pointer-events: none;

      &::before {
        background-color: #ffffff;
        border: 1.5px solid #2e86ff;
      }

      .icon {
        color: #2e86ff;
      }

      .selected_icon {
        display: block;
      }
    }
  }

  div.subject:nth-child(3n + 3) {
    margin-right: 0;
  }
}
