import React, {
  forwardRef,
  useEffect,
  useReducer,
  useRef,
  useState,
  useImperativeHandle,
  ForwardedRef,
} from "react";
import BasePopup from "@/components/base-popup";
import {
  getBookList,
  getCurrentBook,
  IBookItem,
  ICurrentBook,
  ISubject,
  saveUserBook,
} from "../../apis";
import { IUserSubjectEditionMap } from "@/components/edition-popup";
import LoadingImage from "@/components/loading-image";
import { SubjectListComponent } from "../subject-popup";
import { IconSvg } from "@/components";
import CoverPng from "@/assets/subject-channel/default-cover.png";
import DelayPageLoading from "@/components/delay-page-loading";

import styles from "./index.module.scss";
import LoadingBlock from "@/components/loading-block";
import { cls } from "@/utils/tool";
import { Toast } from "antd-mobile";
import SafeLogger from "@/utils/safe-logger";

interface IBookApiStatus<T> {
  isLoading?: boolean;
  isError?: boolean;
  data?: T;
}

export interface IUserSelectedBook {
  subjectId: number;
  subjectName: string;
  baseEditionId?: string;
  editionName?: string;
  bookId?: string;
  bookName?: string;
}

export interface IBookPopupRef {
  resetInfo: (
    subject: ISubject,
    book: ICurrentBook,
    expandSubject: boolean,
  ) => void;
}

const LoadingArr = [1, 2];

const BookPopup = forwardRef(function BookPopup(
  {
    open,
    onClose,
    onConfirm,
    subjectList,
    currentSubject,
    currentBook,
    subjectEditionMap,
    logger,
    onChangeEdition,
  }: {
    open: boolean;
    onClose: () => void;
    onConfirm: (userSelectedBook: IUserSelectedBook) => void;
    subjectList: ISubject[];
    currentSubject: ISubject;
    currentBook: ICurrentBook;
    subjectEditionMap: IUserSubjectEditionMap;
    logger?: SafeLogger;
    onChangeEdition: (subjectId: number) => void;
  },
  ref: ForwardedRef<IBookPopupRef>,
) {
  const [current, setCurrent] = useState<IUserSelectedBook>({
    ...currentSubject,
    ...currentBook,
  });
  const [_, forceUpdate] = useReducer((x) => x + 1, 0);
  const booksRef = useRef<Record<string, IBookApiStatus<IBookItem[]>>>({});
  const [loading, setLoading] = useState(false);
  // 缓存 学科 下 之前选中的书本，避免一切换学科就丢失书本选中信息
  const subjectSelectBookRef = useRef<Record<number, Record<string, string>>>(
    {},
  );
  // 是否展开 学科
  const [isSubjectExpand, setIsSubjectExpand] = useState(false);
  // 是否展开 教材
  const [isBookExpand, setIsBookExpand] = useState(true);

  // 初始化书本列表, 并确认默认选中书本信息
  const initBooksAndSelectBook = async (
    subjectId: number,
    baseEditionId: string,
  ) => {
    const bookInfo = booksRef.current[baseEditionId];
    if (bookInfo) {
      selectLastBook();
      return;
    }
    booksRef.current[baseEditionId] = {
      isLoading: true,
    };
    forceUpdate();
    try {
      const [res] = await Promise.all([
        getBookList({
          subjectId,
          baseEditionId,
        }),
        initSubjectSelectBook(subjectId),
      ]);
      booksRef.current[baseEditionId] = {
        data: res.data || [],
      };
      selectLastBook();
    } catch (error) {
      logger?.error("subject-edition-books-error", {
        error,
        params: { subjectId, baseEditionId },
      });
      booksRef.current[baseEditionId] = {
        isError: true,
      };
    }
    forceUpdate();
  };

  // 初始化 用户 在 某学科 下 之前选中的书本
  const initSubjectSelectBook = async (subjectId: number) => {
    if (subjectSelectBookRef.current[subjectId]) {
      return;
    }
    try {
      const res = await getCurrentBook({
        subjectId,
        ignoreError: true,
      });
      subjectSelectBookRef.current[subjectId] = {};
      if (res.data && res.data.bookId) {
        subjectSelectBookRef.current[subjectId][res.data.baseEditionId] =
          res.data.bookId;
      }
    } catch (error) {
      logger?.warn("book-popup-last-subject-book-error", {
        error,
        params: { subjectId },
      });
      // 失败则后续不在请求
      subjectSelectBookRef.current[subjectId] = {};
    }
  };

  // 选中 用户之前的书本 或者 第一本书
  const selectLastBook = () => {
    setCurrent((pre) => {
      if (pre.baseEditionId && booksRef.current[pre.baseEditionId]?.data) {
        // 如果 所选 的 书已经不存在了，则修正一下
        const books = booksRef.current[pre.baseEditionId].data || [];
        const lastBookId =
          pre.bookId ||
          subjectSelectBookRef.current[pre.subjectId]?.[pre.baseEditionId];
        const book =
          (!!lastBookId && books.find((it) => it.bookId === lastBookId)) ||
          books[0];
        return {
          ...pre,
          ...(book || {}),
        };
      }
      return pre;
    });
  };

  // 学科 点击，如果有学科没有教材版本，则无法点击切换
  const onSubjectClick = (subject: ISubject) => {
    const edition = subjectEditionMap[subject.subjectId];
    if (edition) {
      setCurrent({ ...subject, ...edition, baseEditionId: edition.editionId });
      initBooksAndSelectBook(subject.subjectId, edition.editionId);
    }
  };

  // 点击切换书本
  const onClickBook = (book: IBookItem) => {
    setCurrent({
      ...current,
      ...book,
    });
  };

  // 书本列表获取失败
  const onRetry = () => {
    delete booksRef.current[current.baseEditionId];
    initBooksAndSelectBook(current.subjectId, current.baseEditionId);
  };

  const onClickConfirm = async () => {
    if (!current.bookId) {
      Toast.show("未选择书本");
      return;
    }
    // 如果没有改变，则不保存
    if (current.bookId === currentBook.bookId) {
      onClose();
      return;
    }
    setLoading(true);
    try {
      await saveUserBook({
        subjectId: current.subjectId,
        baseEditionId: current.baseEditionId,
        bookId: current.bookId,
      });
      // 保存 用户 在 某学科 下 之前选中的书本
      subjectSelectBookRef.current[current.subjectId] =
        subjectSelectBookRef.current[current.subjectId] || {};
      subjectSelectBookRef.current[current.subjectId][current.baseEditionId] =
        current.bookId;
      onConfirm(current);
    } catch (error) {
      logger?.error("save-edition-books-error", {
        error,
        params: {
          subjectId: current.subjectId,
          baseEditionId: current.baseEditionId,
          bookId: current.bookId,
        },
      });
    }
    setLoading(false);
  };

  // 展开学科切换
  const onSubjectExpand = () => {
    setIsSubjectExpand(!isSubjectExpand);
  };

  // 展开教材切换
  const onBookExpand = () => {
    setIsBookExpand(!isBookExpand);
  };

  const refreshData = (nextSubject: ISubject, nextBook: ICurrentBook) => {
    setCurrent({ ...nextSubject, ...nextBook });
    nextBook.baseEditionId &&
      initBooksAndSelectBook(nextSubject.subjectId, nextBook.baseEditionId);
  };

  // 如果用户主动切换了教材版本，则重新获取书本列表
  useEffect(() => {
    const edition = subjectEditionMap[current.subjectId];
    if (edition && edition.editionId !== current.baseEditionId) {
      refreshData(
        { subjectId: current.subjectId, subjectName: current.subjectName },
        { ...edition, baseEditionId: edition.editionId },
      );
    }
  }, [subjectEditionMap]);

  useImperativeHandle(ref, () => {
    return {
      resetInfo: (
        subject: ISubject,
        book: ICurrentBook,
        expandSubject: boolean,
      ) => {
        refreshData(subject, book);
        setIsSubjectExpand(expandSubject);
        setIsBookExpand(true);
      },
    };
  });

  const booksInfo = booksRef.current[current.baseEditionId];
  let node = null;
  let bookName = "";
  if (!booksInfo || booksInfo.isLoading) {
    // 加载中
    node = (
      <div className={styles.books}>
        {LoadingArr.map((it) => (
          <div key={it} className={styles.book}>
            <LoadingBlock className={styles.block_1} />
            <LoadingBlock className={styles.block_2} />
            <LoadingBlock className={styles.block_3} />
          </div>
        ))}
      </div>
    );
  } else if (booksInfo.isError) {
    // 加载异常
    node = (
      <div className={styles.error}>
        <div className={styles.word}>啊哦～发生了未知异常，请稍后重试</div>
        <div className={styles.btn} onClick={onRetry}>
          重新加载
        </div>
      </div>
    );
  } else if ((booksInfo.data || []).length < 1) {
    // 没有书本
    node = (
      <div className={styles.error}>
        <div className={styles.word}>当前教材版本下无内容，请更换教材版本</div>
      </div>
    );
  } else {
    bookName = current.bookName;
    // 显示书本列表
    node = !!isBookExpand && (
      <div className={styles.books}>
        {booksInfo.data.map((item) => (
          <div
            key={item.bookId}
            className={cls([
              styles.book,
              item.bookId === current.bookId && styles.active,
            ])}
            onClick={() => onClickBook(item)}
          >
            <LoadingImage
              className={styles.cover}
              src={item.backgroundImg}
              fallback={CoverPng}
            />
            <div className={styles.book_name}>{item.bookName}</div>
            <div className={styles.selected_icon}></div>
          </div>
        ))}
      </div>
    );
  }
  return (
    <BasePopup
      open={open}
      height="80vh"
      onClose={onClose}
      onConfirm={onClickConfirm}
      title="课程筛选"
      showConfirm
    >
      <div className={styles.book_list_container}>
        <div
          className={cls([
            styles.title_container,
            isSubjectExpand && styles.subject_expand,
          ])}
        >
          <div>所选学科：</div>
          <div className={styles.title} onClick={onSubjectExpand}>
            <div className={styles.title_word}>
              {current.subjectName || "切换学科"}
            </div>
            <IconSvg
              name="icon-a-xuanxiangzhankai2x"
              className={styles.expand_icon}
            />
          </div>
        </div>
        {!!isSubjectExpand && (
          <SubjectListComponent
            subjectList={subjectList}
            subject={current}
            onClickSubject={onSubjectClick}
          />
        )}
        <div className={styles.title_container}>
          <div>所选教材版本：</div>
          <div
            className={styles.title}
            onClick={() => onChangeEdition(current.subjectId)}
          >
            <div className={styles.title_word}>
              {current.editionName || "切换教材版本"}
            </div>
            <IconSvg name="icon-a-jiaocaiqiehuan2x" />
          </div>
        </div>
        <div className={styles.books_container}>
          <div
            className={cls([
              styles.title_container,
              isBookExpand && styles.subject_expand,
            ])}
          >
            <div>所选教材：</div>
            {!!bookName && (
              <div className={styles.title} onClick={onBookExpand}>
                <div className={styles.title_word}>{bookName}</div>
                <IconSvg
                  name="icon-a-xuanxiangzhankai2x"
                  className={styles.expand_icon}
                />
              </div>
            )}
          </div>
          {node}
        </div>
        <DelayPageLoading loading={loading} />
      </div>
    </BasePopup>
  );
});

export default BookPopup;
