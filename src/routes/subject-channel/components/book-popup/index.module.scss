@import "~@/styles/lib.scss";

.book_list_container {
  .title_container {
    padding-left: 16px;
    font-weight: bold;
    font-size: 16px;
    color: #2a333a;
    line-height: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .title {
      display: flex;
      align-items: center;
      color: #2e86ff;

      .title_word {
        margin-right: 4px;
        max-width: 196px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .expand_icon {
        font-size: 10px;
        transform: rotate(180deg);
      }
    }
  }

  .subject_expand {
    .title {
      .expand_icon {
        transform: rotate(0deg);
      }
    }
  }

  .books_container {
    .books {
      display: flex;
      flex-wrap: wrap;
      padding-left: 15px;

      .book {
        width: 166px;
        height: 70px;
        position: relative;
        margin-right: 12px;
        margin-bottom: 12px;
        padding-top: 10px;
        padding-left: 68px;
        display: flex;
        align-items: center;

        &::before {
          content: " ";
          position: absolute;
          left: 0;
          right: 0;
          bottom: 0;
          height: 60px;
          background: #f5f7fb;
          border-radius: 8px;
          z-index: -1;
        }

        .cover {
          position: absolute;
          left: 12px;
          top: 0;
          width: 45px;
          height: 60px;
          border-radius: 2px;
          border: 1px solid #f5f7fb;
        }

        .book_name {
          font-weight: bold;
          font-size: 14px;
          color: #333333;
          line-height: 20px;
          max-width: 71px;

          @include multi_lines_ellipsis(2);
        }

        .selected_icon {
          position: absolute;
          width: 19px;
          height: 16px;
          right: 0;
          bottom: 0;
          background: url(../../../../assets/subject-channel/book-selected.png)
            center/contain no-repeat;
          display: none;
        }

        &.active {
          pointer-events: none;
          &::before {
            background: #e1efff;
            border: 1.5px solid #2e86ff;
          }
          .book_name {
            color: #558bff;
          }
          .selected_icon {
            display: block;
          }
        }

        .block_1 {
          left: 12px;
          top: 0;
          width: 45px;
          height: 60px;
          border-radius: 2px;
          background-color: #ddd;
        }

        .block_2 {
          left: 68px;
          top: 22px;
          width: 71px;
          height: 16px;
          border-radius: 2px;
          background-color: #ddd;
        }

        .block_3 {
          left: 68px;
          top: 42px;
          width: 45px;
          height: 16px;
          border-radius: 2px;
          background-color: #ddd;
        }
      }

      div.book:nth-child(2n + 2) {
        margin-right: 0;
      }
    }

    .error {
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      height: 100px;

      .word {
        font-size: 14px;
        color: #666666;
        line-height: 20px;
      }

      .btn {
        width: 116px;
        height: 32px;
        border-radius: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 12px;
        font-size: 16px;
        background-color: #2e86ff;
        color: #ffffff;
      }
    }
  }
}
