import request, { RequestResponse } from "@/utils/request";

export interface ISubject {
  /** 学科id */
  subjectId: number;
  /** 学科名称 */
  subjectName: string;
}

export interface ICategorySubject {
  /** 业务类型  1 教材同步；2 重点复习；3 备战高考；4 备战学考；5 素养拓展；100 专项提升    */
  categoryType: number;
  /** 业务名称 */
  categoryName: string;
  /** 学科列表 */
  subjectList: ISubject[];
}

// 获取 页面类型 及其 支持的学科
export const getCategorySubject = () =>
  request.get("/api/studyprod/app/category/subjectList") as RequestResponse<
    ICategorySubject[]
  >;

export interface ICurrentBook {
  /** 教材版本 ID */
  baseEditionId?: string;
  /** 教材版本 名字 */
  editionName?: string;
  /** 书本 ID */
  bookId?: string;
  /** 书本 名称 */
  bookName?: string;
}

// 获取用户选择的教材和书本信息
export const getCurrentBook = (params: {
  subjectId: number;
  ignoreError?: boolean;
}) =>
  request.get("/api/studyprod/app/category/user/editionBook", {
    params,
  }) as RequestResponse<ICurrentBook>;

export interface IOtherBook {
  /** 书本 ID */
  bookId: string;
  /** 书本 名字 */
  bookName: string;
}

// 获取 非教材同步、重点复习 获取“书本”列表
export const getOtherBookList = (params: {
  subjectId: number;
  categoryType: number;
}) =>
  request.get("/api/studyprod/app/category/other/bookList", {
    params,
  }) as RequestResponse<IOtherBook[]>;

export interface ILastStudy {
  /** 教材id */
  baseEditionId?: string;
  /** 书本id */
  bookId: string;
  /** 章节id */
  chapterId: string;
  /** 节id */
  sectionId?: string;
  /** 资源id */
  contentId: string;
  /** 资源类型  2课包 */
  contentType: number;
}
// 获取 上次学到
export const getLastStudy = (params: {
  subjectId: number;
  categoryType: number;
  ignoreError?: boolean;
}) =>
  request.get("/api/studyprod/app/category/lastLearn/get", {
    params,
  }) as RequestResponse<ILastStudy>;

// 更新 上次学到
export const saveLastStudy = (
  data: ILastStudy & {
    subjectId: number;
    categoryType: number;
    ignoreError?: boolean;
  },
) =>
  request.post(
    "/api/studyprod/app/category/lastLearn/save",
    data,
  ) as RequestResponse<boolean>;

export interface IHardLevel {
  /** 难度ID，-1 是全部 */
  id: number;
  /** 难度名称 */
  name: string;
}

export interface ICondition {
  /** 难度列表 */
  hardLevels: IHardLevel[];
}

// 获取 筛选条件，当前只有 难度信息
export const getCondition = (params: {
  bookId: string;
  categoryType: number;
  subjectId: number;
  ignoreError?: boolean;
}) =>
  request.get("/api/studyprod/app/category/condition", {
    params,
  }) as RequestResponse<ICondition>;

export interface ITreeItem {
  /** 节点id */
  id: string;
  /** 节点名称 */
  title: string;
  /** 注释 */
  children?: ITreeItem[];
  /** 节点下面的资源列表 */
  resourceList?: {
    /** 资源id */
    contentId: string;
    /** 资源类型 2 课程包； 1试卷 */
    contentType: number;
  }[];
}

// 获取 章/节（含包 ID） 信息
export const getTreeResource = (params: {
  bookId: string;
  categoryType: number;
  hardId: number;
  subjectId: number;
}) =>
  request.get("/api/studyprod/app/category/treeResource", {
    params,
  }) as RequestResponse<ITreeItem[]>;

export interface ICourseItem {
  /** 课程包id */
  id: string;
  /** 图片 */
  picture?: string;
  /** 课程包名称 */
  courseName: string;
  /** 年级列表 */
  gradeList?: {
    /** 年级ID */
    gradeId: number;
    /** 年级名字 */
    gradeName: string;
  }[];
  /** 注释 */
  hardLevel?: {
    /** id */
    id: number;
    /** 层次名称 */
    name: string;
  };
  /** 点击量 */
  hitCount?: number;
  /** 课程包评分 */
  courseScore?: string;
  /** 预计讲数 */
  lessonCount?: number;
  /** 大分类id */
  categoryCode?: number;
  /** 科目id */
  subjectId?: number;
  /** 学科名称 */
  subjectName?: string;
  /** 上线讲数 */
  onlineLessonCount?: number;
  /** 注释 */
  teacherList: {
    /** 老师id */
    teacherId: string;
    /** 老师名称 */
    teacherName: string;
    /** 老师头像 */
    teacherPicture: string;
  }[];
}

// 获取 过往课程 的链接
export const getOldUrl = (params: { bookId: string; ignoreError?: boolean }) =>
  request.get("/api/studyprod/app/subjectlist/getFormerBookUrl", {
    params,
  }) as RequestResponse<string>;

// 批量获取 课程包 信息
export const getCourseList = (data: {
  courseIds: string[];
  ignoreError?: boolean;
}) =>
  request.post(
    "/api/studyprod/app/category/courseList",
    data,
  ) as RequestResponse<ICourseItem[]>;

export interface ILessonItem {
  /** 讲id */
  lessonId: string;
  /** 讲标题 */
  lessonName: string;
}

// 获取 课程讲 列表
export const getLessonList = (params: {
  courseId: string;
  ignoreError?: boolean;
}) =>
  request.get("/api/studyprod/course/simpleLessonList", {
    params,
  }) as RequestResponse<ILessonItem[]>;

export interface IBookItem {
  /** 书本id */
  bookId: string;
  /** 书本名称 */
  bookName: string;
  /** 书本封面 */
  backgroundImg: string;
}

// 获取 教材同步、重点复习 的 书本列表
export const getBookList = (params: {
  subjectId: number;
  baseEditionId: string;
}) =>
  request.get("/api/studyprod/app/category/edition/bookList", {
    params,
  }) as RequestResponse<IBookItem[]>;

// 保存 教材同步、重点复习 的 选中书本
export const saveUserBook = (data: {
  subjectId: number;
  baseEditionId: string;
  bookId: string;
}) =>
  request.post(
    "/api/studyprod/app/category/edition/saveBook",
    data,
  ) as RequestResponse<boolean>;
