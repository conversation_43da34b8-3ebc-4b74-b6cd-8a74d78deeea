import React, { forwardRef, HTMLAttributes } from "react";
import { cls } from "@/utils/tool";

import sunPng from "@/assets/high-school-journey/sun.png";
import moonPng from "@/assets/high-school-journey/moon.png";
import startPng from "@/assets/high-school-journey/star.png";
import rainPng from "@/assets/high-school-journey/rain.png";

import styles from "./style.module.scss";

export const imageMap = {
  1: sunPng,
  2: moonPng,
  3: startPng,
  4: rainPng,
};

export interface Props extends HTMLAttributes<HTMLButtonElement> {
  id: string;
  active?: boolean;
  isOver?: boolean;
}

export const Item = forwardRef<HTMLLIElement, Props>(function Item(
  { isOver, active, style, id, ...props },
  ref,
) {
  return (
    <li
      className={cls([
        styles.Wrapper,
        active && styles.active,
        isOver && styles.isOver,
      ])}
      style={style}
      ref={ref}
    >
      <button className={styles.item} data-id={id.toString()} {...props}>
        <div
          className={styles.img}
          style={{
            backgroundImage: `url(${imageMap[id]})`,
          }}
        />
      </button>
    </li>
  );
});
