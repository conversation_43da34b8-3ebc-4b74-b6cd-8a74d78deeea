.game-zone {
  height: 186px;
  position: relative;
  background-image: url('@/assets/high-school-journey/sort-container.jpg');
  background-size: cover;
  .tip {
    margin-bottom: 10px;
    text-align: center;
  }

  .drag-drop-container {
    top: 50px;
    left: 50%;
    transform: translateX(-50%);
    position: absolute;
    display: flex;
    justify-content: center;
  }

  .drop {
    width: 68px;
    height: 68px;
    background: #EAE0C3;
    border: 0.97px solid #CCAF84;
    border-radius: 3.89px;
    margin-right: 9px;
  }
  .pages {
    display: flex;
    margin: 0;
  }

  .Wrapper {
    position: relative;
    list-style: none;
    width: 68px;
    height: 68px;
    margin-right: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 0.97px dashed #CCAF84;
    border-radius: 3.89px;
    background: #EAE0C3;
  
    .img {
      top: 0;
      left: 0;
      position: absolute;
      width: 100%;
      height: 100%;
      background-repeat: no-repeat;
      background-size: cover;
    }
  
    &.active {
      .item {
        background: #EAE0C3;
        div {
          background-image: none !important;
        }
      }
    }
  }
  
  .item {
    position: relative;
    display: block;
    width: 64px;
    border: 0;
    height: 64px;
    touch-action: none;
  }
  
  .isOver {
    border-style: solid;
    border-color: #3F2811;;
  }
  
  .overlay {
    transform: translate3d(3px, 3px, 0);
    width: 64px;
    height: 64px;
    background-size: cover;
    background-repeat: no-repeat;
  }
}

.submit-btn-container {
  width: 375px;
  height: 72px;
  background: #FFFFFF;
  position: fixed;
  bottom: 0;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  .btn {
    width: 343px;
    height: 40px;
    background: #8B572A;
    border: 1px solid #5F3B29;
    box-shadow: 0 4px 0 0 #8b572a26;
    border-radius: 20px;
    font-weight: bold;
    font-size: 16px;
    color: #FFFFFF;
    text-align: center;
  }
}

.faired-modal {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  .faired-tip-img {
    width: 375px;
    height: 235px;
  }
  .close-btn {
    text-align: center;
  }
  .close-img {
    width: 24px;
    height: 24px;
  }
}

.game-finished {
  z-index: 1;
  top: 20px;
  width: 81px;
  right: 30px;
  height: 56px;
  position: absolute;
}

.drag-lottie-container {
  width: 100%;
  height: 100%;
  z-index: 1;
  position: relative;

  .lottie-wrapper {
    width: 100%;
    height: 100%;
    position: absolute;
    top: -2px;
  }
}