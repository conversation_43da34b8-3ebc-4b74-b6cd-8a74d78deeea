import React from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

import { Item } from "./item";
import type { Props as PageProps } from "./item";

export default function SortItem({
  id,
  activeIndex,
  disabled,
  ...props
}: PageProps & { activeIndex: number; disabled?: boolean }) {
  const {
    attributes,
    listeners,
    isDragging,
    isSorting,
    over,
    active,
    setNodeRef,
    transform,
    transition,
  } = useSortable({
    id,
    disabled,
    animateLayoutChanges: () => true,
  });

  return (
    <Item
      ref={setNodeRef}
      id={id}
      active={isDragging}
      style={{
        transition,
        transform: isSorting ? undefined : CSS.Translate.toString(transform),
      }}
      isOver={over?.id === id && active?.id !== id}
      {...props}
      {...attributes}
      {...listeners}
    />
  );
}
