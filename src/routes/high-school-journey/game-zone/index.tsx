import React, { useState, useEffect } from "react";
import {
  closestCenter,
  DndContext,
  DragOverlay,
  useSensor,
  useSensors,
  TouchSensor,
  PointerSensor,
  MeasuringStrategy,
} from "@dnd-kit/core";
import type {
  DragStartEvent,
  DragEndEvent,
  MeasuringConfiguration,
} from "@dnd-kit/core";
import { arrayMove, SortableContext } from "@dnd-kit/sortable";

import { Button } from "@/components/button";
import { clickPv, cls } from "@/utils/tool";
import gameFinishedPng from "@/assets/high-school-journey/game-finished.png";

import FairedModal from "./failed-modal";
import DragLottie from "./drag-lottie";
import { imageMap } from "./item";
import SortItem from "./sort-item";
import styles from "./style.module.scss";

import { IBasicInfo, initOrder, correctOrder } from "../hook";

const measuring: MeasuringConfiguration = {
  droppable: {
    strategy: MeasuringStrategy.Always,
  },
};

interface IGameZone {
  basicInfo: IBasicInfo;
  onSubmit: () => void;
}

export default function GameZone(props: IGameZone) {
  const { basicInfo, onSubmit } = props;
  useEffect(() => {
    if (basicInfo?.isFinished) {
      setItems(correctOrder);
    } else {
      setItems(initOrder);
    }
  }, [basicInfo]);
  const [activeId, setActiveId] = useState<string>(null);
  const [items, setItems] = useState(initOrder);
  const activeIndex = activeId != null ? items.indexOf(activeId) : -1;
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 250, // 长按 250ms 才触发拖动
        tolerance: 5,
      },
    }),
  );

  function handleDragStart({ active }: DragStartEvent) {
    setActiveId(active.id as string);
    clickPv("ewt_h5_base_operation_career_sort_drag_click");
  }

  function handleDragCancel() {
    setActiveId(null);
  }

  function handleDragEnd({ over }: DragEndEvent) {
    if (over) {
      const overIndex = items.indexOf(over.id as string);

      if (activeIndex !== overIndex) {
        const newIndex = overIndex;

        setItems((items) => {
          const newArr = arrayMove(items, activeIndex, newIndex);
          return newArr;
        });
      }
    }

    setActiveId(null);
  }

  /** 提交排序 */
  const [showFailedModal, setShowFailedModal] = useState(false);
  function handleSubmit() {
    const isCorrect = items.join(",") === correctOrder.join(",");
    clickPv("ewt_h5_base_operation_career_sort_submit_click", {
      result: isCorrect ? "正确" : "错误",
    });
    if (isCorrect) {
      onSubmit();
    } else {
      setShowFailedModal(true);
    }
  }

  return (
    <DndContext
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onDragCancel={handleDragCancel}
      sensors={sensors}
      collisionDetection={closestCenter}
      measuring={measuring}
    >
      <div className={styles["game-zone"]}>
        {!!basicInfo && (
          <>
            {!!basicInfo.isFinished && (
              <img className={styles["game-finished"]} src={gameFinishedPng} />
            )}
            <SortableContext items={items}>
              <ul className={styles["drag-drop-container"]}>
                {items.map((id) => (
                  <SortItem
                    id={id}
                    key={id}
                    disabled={basicInfo.isFinished}
                    activeIndex={activeIndex}
                  />
                ))}
              </ul>
            </SortableContext>
            <DragOverlay>
              {activeId != null ? (
                <div
                  className={cls([styles.overlay])}
                  style={{
                    backgroundImage: `url(${imageMap[activeId]})`,
                  }}
                />
              ) : null}
            </DragOverlay>
            {/* 拖动演示动画 */}
            {!basicInfo.isFinished && <DragLottie />}
          </>
        )}
      </div>
      {!!basicInfo && !basicInfo.isFinished && (
        <div className={styles["submit-btn-container"]}>
          <Button
            onClick={handleSubmit}
            className={styles["btn"]}
            text="提交我的排序"
          />
          <FairedModal
            visible={showFailedModal}
            onClose={() => setShowFailedModal(false)}
          />
        </div>
      )}
    </DndContext>
  );
}
