/** 拖动演示动画 */

import React, { useState, useEffect } from "react";

import LottieBox from "@/components/lottie-box";
import dataJson from "@/assets/json/career-drag/data.json";

import styles from "./style.module.scss";

const SAVE_KEY = "ewtCustomerH5_####_high_school_journey_sort_lottieFlag";

interface DragLottieProps {}

const DragLottie: React.FC<DragLottieProps> = () => {
  const [showLottie, setShowLottie] = useState<boolean>();
  function checkIfShowLottie() {
    try {
      if (!localStorage.getItem(SAVE_KEY)) {
        localStorage.setItem(SAVE_KEY, "1");
        setShowLottie(true);
      }
    } catch (error) {
      console.error(error);
    }
  }
  useEffect(() => {
    checkIfShowLottie();
  }, []);
  return showLottie ? (
    <div className={styles["drag-lottie-container"]}>
      <LottieBox
        className={styles["lottie-wrapper"]}
        dataJson={dataJson}
        loop={false}
        assetsPath={`${process.env.PUBLIC_PATH}career-drag/images/`}
        onComplete={() => {
          setShowLottie(false);
        }}
      />
    </div>
  ) : null;
};

export default DragLottie;
