import React from "react";
import { Mask } from "antd-mobile";

import sortWrongPng from "@/assets/high-school-journey/sort-wrong.png";
import closePng from "@/assets/high-school-journey/close.png";

import Style from "./style.module.scss";

interface IFairedModalProps {
  visible: boolean;
  onClose: () => void;
}

const FairedModal: React.FC<IFairedModalProps> = ({ visible, onClose }) => {
  return (
    <Mask visible={visible} onMaskClick={onClose}>
      <div className={Style["faired-modal"]}>
        <img className={Style["faired-tip-img"]} src={sortWrongPng} />
        <div onClick={onClose} className={Style["close-btn"]}>
          <img className={Style["close-img"]} src={closePng} />
        </div>
      </div>
    </Mask>
  );
};

export default FairedModal;
