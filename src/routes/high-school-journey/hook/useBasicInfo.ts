import { useState, useEffect } from "react";

import { EPaperID, getAnswers, saveAnswer } from "@/service/question";

import { Toast } from "antd-mobile";

export interface IBasicInfo {
  isFinished?: boolean;
}

export const initOrder = ["1", "2", "3", "4"];
export const correctOrder = ["4", "2", "1", "3"];

const useBasicInfo = () => {
  const [basicInfo, setBasicInfo] = useState<IBasicInfo | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [showErrorTip, setShowErrorTip] = useState<boolean>(false);
  const getBasicInfo = async () => {
    try {
      setLoading(true);
      setShowErrorTip(false);
      const { data } = await getAnswers(EPaperID.careerSort);
      setBasicInfo({
        isFinished: !!data.length,
      });
    } catch (error) {
      console.error(error);
      setShowErrorTip(true);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    getBasicInfo();
  }, []);

  // 成功排序
  async function recordSortRes() {
    try {
      setLoading(true);
      // 只有排序正确才记录
      const { data } = await saveAnswer({
        paperId: EPaperID.careerSort,
        questionId: "1",
        answers: "4213",
      });
      if (data) {
        Toast.show({
          content: "恭喜你，修复成功！",
        });
        setBasicInfo((pre) => {
          return {
            ...pre,
            isFinished: true,
          };
        });
        return true;
      }
      return false;
    } catch (error) {
      console.error(error);
      return false;
    } finally {
      setLoading(false);
    }
  }

  return {
    basicInfo,
    loading,
    showErrorTip,
    recordSortRes,
    setBasicInfo,
    getBasicInfo,
  };
};

export { useBasicInfo };
