/**
 * 生涯大事件
 */

import * as React from "react";
import mstJsBridge from "mst-js-bridge";

import backBlackPNG from "@/assets/common/back_black.png";
import topImg from "@/assets/high-school-journey/layout-img.jpg";
import { Layout } from "@/components";
import { cls } from "@/utils/tool";

import { useBasicInfo } from "./hook";
import GameZone from "./game-zone";
import Grass from "./grass";
import PhaseIntro from "./phase-intro";
import Finished from "./finished";

import Style from "./style.module.scss";
import ErrorInfo from "@/components/error-info";

export const Component: React.FC = () => {
  const { basicInfo, loading, showErrorTip, getBasicInfo, recordSortRes } =
    useBasicInfo();
  // 记录排序结果
  const pageContainerRef = React.useRef(null);
  function handleSubmit() {
    recordSortRes();
  }
  return (
    <Layout
      showHeader
      setChildrenContainerHeight={({ top, bottom }) => {
        return `calc(100vh - ${top || 0}px - ${bottom || 0}px)`;
      }}
      headerProps={{
        left: <div className={Style["page-title"]}>修复高中生涯卷轴</div>,
        backIconUrl: backBlackPNG,
        onBack: () => {
          mstJsBridge.closeWebview();
        },
      }}
      pageLoading={loading}
    >
      <div
        style={{
          overflow: "scroll",
          maxHeight: `calc(100% - 12.8vw)`,
          height: `calc(100% - 12.8vw)`,
          position: "relative",
          backgroundColor: "#CCAF84",
        }}
        // 为了让底部装饰草不被遮挡
        className={cls([
          basicInfo && !basicInfo.isFinished && Style["bottom-padding"],
        ])}
        ref={pageContainerRef}
      >
        {showErrorTip ? (
          <ErrorInfo
            type="light"
            style={{
              height: "100%",
            }}
            onRetry={getBasicInfo}
          />
        ) : (
          <>
            {" "}
            <img className={Style["story-background"]} src={topImg} />
            {/* 拖拽区 */}
            <GameZone basicInfo={basicInfo} onSubmit={handleSubmit} />
            {/* 各阶段介绍 */}
            <PhaseIntro>
              {basicInfo && !basicInfo.isFinished ? <Grass /> : null}
            </PhaseIntro>
            {/* 完成结果展示 */}
            {!!basicInfo?.isFinished && (
              <Finished>
                <Grass />
              </Finished>
            )}
          </>
        )}
      </div>
    </Layout>
  );
};
