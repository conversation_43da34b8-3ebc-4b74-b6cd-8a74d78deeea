/**
 * 完成组件
 */

import React, { useEffect } from "react";

import successTop from "@/assets/high-school-journey/success-top.jpg";
import careerImg from "@/assets/high-school-journey/career.jpg";
import subjectImg from "@/assets/high-school-journey/subject.jpg";
import testImg from "@/assets/high-school-journey/test.jpg";
import collegeImg from "@/assets/high-school-journey/college.jpg";
import { clickPv, openRoute } from "@/utils/tool";

import style from "./style.module.scss";

interface IFinished {
  children?: React.ReactElement;
}

const Finished: React.FC<IFinished> = (props) => {
  const { children } = props;
  const containerRef = React.useRef<HTMLDivElement>(null);
  // 默认滚动到结果组件
  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollIntoView({
        behavior: "smooth",
      });
    }
  }, []);
  // 固定看对应的课程
  function playVideo() {
    clickPv("ewt_h5_base_operation_career_sort_recommend_content_click", {
      content_id: 1,
    });
    openRoute({
      domain: "media",
      action: "commonPlayer",
      params: {
        playVideoId: "101041",
        isOffLineMode: false,
      },
    });
  }
  // 跳转到专题
  function toPage(url: string, content_id: number) {
    clickPv("ewt_h5_base_operation_career_sort_recommend_content_click", {
      content_id,
    });
    openRoute({
      domain: "web",
      action: "open_webview",
      params: {
        url,
      },
    });
  }
  return (
    <div className={style["finished"]} ref={containerRef}>
      <img className={style["success-top"]} src={successTop} />
      <img className={style["career"]} src={careerImg} onClick={playVideo} />
      <img
        className={style["subject"]}
        src={subjectImg}
        onClick={() =>
          toPage(
            "https://web.ewt360.com/themeTemplateClient/index.html?id=1921865136010739713",
            2,
          )
        }
      />
      <img
        className={style["test"]}
        src={testImg}
        onClick={() =>
          toPage(
            "https://web.ewt360.com/themeTemplateClient/index.html?id=1921865486004436994",
            3,
          )
        }
      />
      <img
        className={style["college"]}
        src={collegeImg}
        onClick={() =>
          toPage(
            "https://web.ewt360.com/themeTemplateClient/index.html?id=1921865605204230146",
            4,
          )
        }
      />
      {children}
    </div>
  );
};

export default Finished;
