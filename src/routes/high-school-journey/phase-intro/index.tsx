import React from "react";

import sunTab from "@/assets/high-school-journey/sun-tab.png";
import rainTab from "@/assets/high-school-journey/rain-tab.png";
import moonTab from "@/assets/high-school-journey/moon-tab.png";
import starTab from "@/assets/high-school-journey/star-tab.png";
import sunSelected from "@/assets/high-school-journey/sun-selected.png";
import rainSelected from "@/assets/high-school-journey/rain-selected.png";
import moonSelected from "@/assets/high-school-journey/moon-selected.png";
import starSelected from "@/assets/high-school-journey/star-selected.png";
import sunMeaning from "@/assets/high-school-journey/sun-meaning.png";
import rainMeaning from "@/assets/high-school-journey/rain-meaning.png";
import moonMeaning from "@/assets/high-school-journey/moon-meaning.png";
import starMeaning from "@/assets/high-school-journey/star-meaning.png";
import { clickPv } from "@/utils/tool";

import Style from "./style.module.scss";

const phaseTabImg = {
  sun: {
    tab: sunTab,
    selected: sunSelected,
    text: sunMeaning,
  },
  moon: {
    tab: moonTab,
    selected: moonSelected,
    text: moonMeaning,
  },
  star: {
    tab: starTab,
    selected: starSelected,
    text: starMeaning,
  },
  rain: {
    tab: rainTab,
    selected: rainSelected,
    text: rainMeaning,
  },
};

type TPhaseTabKey = keyof typeof phaseTabImg;

interface IPhaseIntro {
  children?: React.ReactElement;
}

const PhaseIntro: React.FC<IPhaseIntro> = (props) => {
  const { children } = props;
  const [selected, setSelected] = React.useState<TPhaseTabKey>("sun");
  return (
    <div className={Style["phase-intro"]}>
      <div className={Style["phases"]}>
        {Object.keys(phaseTabImg).map((key: TPhaseTabKey) => {
          return (
            <img
              className={Style["phase"]}
              key={key}
              src={
                selected === key
                  ? phaseTabImg[key].selected
                  : phaseTabImg[key].tab
              }
              onClick={() => {
                setSelected(key);
                clickPv("ewt_h5_base_operation_career_sort_fragments_click");
              }}
            />
          );
        })}
      </div>
      <div className={Style["text"]}>
        <img
          key={selected}
          className={Style[selected]}
          src={phaseTabImg[selected].text}
        />
      </div>
      {children}
    </div>
  );
};

export default PhaseIntro;
