/** 90天前的完成的课程 */

import * as React from "react";
import { useLocation } from "react-router-dom";
import mstJsBridge from "mst-js-bridge";

import {
  Layout,
  RowLoading,
  SubjectList,
  List,
  PackageItem,
  CourseInvalidatePopup,
  ILayoutRef,
} from "@/components";
import {
  IPackageItem,
  IPostEarlierCompletePlanReq,
  ISubjectItem,
  postEarlierCompletePlan,
  removePackage as removePackageApi,
} from "@/service/home";
import Empty from "@/components/empty-status";
import PageLoading from "@/components/page-loading";
import backBlackPNG from "@/assets/common/back_black.png";
import noContentPNG from "@/assets/common/no-content.png";
import addPNG from "@/assets/common/add.png";
import { useSubjectList, useVisibilitychange } from "@/hooks";
import safeLogger from "@/utils/safe-logger";
import {
  EJumpType,
  clickPv,
  cls,
  createURLByType,
  openUrlInWebView,
} from "@/utils/tool";
import { EPackageStatus } from "@/routes/self-learning-home/common";

import {
  LDSP,
  ECompletedCourseListStatus,
  statusText,
  btnText,
} from "./common";

import Style from "./style.module.scss";

export const Component: React.FC = () => {
  const layoutRef = React.useRef<ILayoutRef>();
  const location = useLocation();
  /**
   * 获取科目列表
   * isError 如果为 true，会展示整个页面异常态
   */
  const { loading, subjectList, isError, getSubjectList } = useSubjectList({
    showError: true,
  });
  /** 筛选条件 */
  const [filter, setFilter] = React.useState<IPostEarlierCompletePlanReq>({});
  /** 默认选中全部 */
  React.useEffect(() => {
    if (subjectList?.length) {
      setFilter({
        subjectInfo: {
          categoryCode: subjectList[0].parentCode,
          subjectId: subjectList[0].code,
        },
      });
    }
  }, [subjectList]);
  /** 列表的状态 */
  const [listStatus, setListStatus] =
    React.useState<ECompletedCourseListStatus>();
  /** 获取已完成课程，增加时序控制 */
  const pageRef = React.useRef<IPostEarlierCompletePlanReq>({
    pageIndex: 1,
    pageSize: 10,
  });
  /** 切换筛选条件时的loading */
  const [initListLoading, setInitListLoading] = React.useState<boolean>();
  /** 列表数据 */
  const [data, setData] = React.useState<IPackageItem[]>();
  /** 是否还有下一页 */
  const [hasMore, setHasMore] = React.useState<boolean>();
  /** 时序标记 */
  const requestIndexRef = React.useRef<number>(0);
  async function getCompletedCourse(init: boolean) {
    requestIndexRef.current++;
    const flag = requestIndexRef.current;
    try {
      init && setInitListLoading(init);
      pageRef.current.pageIndex = init ? 1 : pageRef.current.pageIndex + 1;
      const { data } = await postEarlierCompletePlan({
        ...filter,
        ...pageRef.current,
        status: EPackageStatus.done,
        ignoreError: true,
      } as IPostEarlierCompletePlanReq);
      if (flag === requestIndexRef.current) {
        if (!data.listPage.total) {
          setData([]);
          if (
            filter.subjectInfo.subjectId === 0 &&
            filter.subjectInfo.categoryCode === 0
          ) {
            setListStatus(ECompletedCourseListStatus.noCompleted);
          } else {
            setListStatus(ECompletedCourseListStatus.noData);
          }
        } else {
          setData((pre) =>
            init ? data.listPage.data : [...pre, ...data.listPage.data],
          );
        }
      }
    } catch (error) {
      if (flag === requestIndexRef.current) {
        init && setData([]);
        setHasMore(false);
        setListStatus(ECompletedCourseListStatus.error);
      }
      console.error(error);
      safeLogger.baseLogger.error("get-completed-course-failed", { error });
    } finally {
      if (flag === requestIndexRef.current) {
        setInitListLoading(false);
      }
    }
  }
  React.useEffect(() => {
    if (!filter.subjectInfo) {
      return;
    }
    getCompletedCourse(true);
  }, [filter]);

  /** 跳转到 包详情是新开webview，关闭之后，要刷新列表 */
  useVisibilitychange({
    handleVisibilitychange: (isVisible) => {
      if (isVisible) {
        setFilter((pre) => ({ ...pre }));
      }
    },
  });

  /** 移除计划 */
  const [removePackagePop, setRemovePackagePop] = React.useState<boolean>();
  const removePackageRef = React.useRef<IPackageItem>();
  function changeMovePackagePopVis() {
    setRemovePackagePop((pre) => !pre);
  }

  async function removePackage() {
    try {
      layoutRef.current.setPageLoading?.(true);
      const { data } = await removePackageApi({
        courseId: removePackageRef.current.courseId,
      });
      if (data) {
        changeMovePackagePopVis();
        getCompletedCourse(true);
      }
    } catch (error) {
      console.error(error);
    } finally {
      layoutRef.current.setPageLoading?.(false);
    }
  }
  function handlePackageClick(packageDetail: IPackageItem) {
    clickPv(
      "ewt_h5_study_course_self_learning_completed_course_list_course_click",
      {
        courseId: packageDetail.courseId,
        courseName: packageDetail.title,
      },
    );
    if (packageDetail.invalidation) {
      removePackageRef.current = packageDetail;
      changeMovePackagePopVis();
    } else {
      openUrlInWebView(
        createURLByType({
          path: "/self-learning/plan-detail",
          originSearch: location.search,
          type: EJumpType.outside,
          addQueryObject: {
            courseId: packageDetail.courseId,
          },
        }),
      );
    }
  }

  return (
    <Layout
      showHeader
      ref={layoutRef}
      setChildrenContainerHeight={({ top }) => {
        return `calc(100vh - ${top || 0}px)`;
      }}
      headerProps={{
        backIconUrl: backBlackPNG,
        className: Style["header"],
        children: "更早前完成的计划",
        onBack: () => {
          // 这个页面都是通过新开webview打开的，所以返回直接关闭 webview
          mstJsBridge.closeWebview();
        },
      }}
    >
      <div
        style={{
          backgroundColor: "#EEF1F6",
          overflow: "auto",
          maxHeight: "calc(100% - 12.8vw)",
          height: "calc(100vh - 12.8vw)",
        }}
      >
        {loading ? (
          <PageLoading />
        ) : (
          <>
            {/* 学科列表 */}
            {!!subjectList?.length && (
              <SubjectList
                className={Style["subject-list"]}
                subjectList={subjectList}
                value={
                  {
                    parentCode: filter?.subjectInfo?.categoryCode,
                    code: filter?.subjectInfo?.subjectId,
                  } as ISubjectItem
                }
                onChange={(item) => {
                  setFilter((pre) => ({
                    ...pre,
                    subjectInfo: {
                      categoryCode: item.parentCode,
                      subjectId: item.code,
                    },
                  }));
                }}
              />
            )}
            {/* 提醒文案 */}
            {!isError && <div className={Style["tips"]}>{LDSP}</div>}
            {/* 初次分页请求的loading */}
            {initListLoading ? (
              <RowLoading />
            ) : data?.length ? (
              /* 课程列表 */
              <List
                className={Style["completed-course-list"]}
                scrollClassName={Style["completed-course-list-scroll"]}
                getKey={(item) => item.courseId}
                renderItem={(item) => (
                  <PackageItem handleClick={handlePackageClick} item={item} />
                )}
                data={data}
                hasMore={hasMore}
                loadMore={getCompletedCourse}
              />
            ) : (
              // 空态或者异常态
              <div
                className={cls([
                  Style["empty"],
                  isError && Style["page-error"],
                ])}
              >
                <Empty
                  image={noContentPNG}
                  text={
                    isError
                      ? statusText[ECompletedCourseListStatus.error]
                      : statusText[listStatus]
                  }
                  buttonOption={{
                    showIcon:
                      !isError &&
                      listStatus !== ECompletedCourseListStatus.error,
                    icon: addPNG,
                    className:
                      listStatus === ECompletedCourseListStatus.error || isError
                        ? Style["error-btn"]
                        : null,
                    text: isError
                      ? btnText[ECompletedCourseListStatus.error]
                      : btnText[listStatus],
                    handleClick: () => {
                      if (isError) {
                        getSubjectList();
                      } else if (
                        listStatus === ECompletedCourseListStatus.error
                      ) {
                        getCompletedCourse(true);
                      }
                    },
                  }}
                />
              </div>
            )}
          </>
        )}
      </div>
      <CourseInvalidatePopup
        onClose={changeMovePackagePopVis}
        visible={removePackagePop}
        onOk={removePackage}
      />
    </Layout>
  );
};
