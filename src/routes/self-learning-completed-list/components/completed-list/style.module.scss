.completed-list-container {
  background-color: #EFF1F7;
  min-height: calc(100vh - 12.8vw);
  padding-bottom: 50px;

  .lesson-list {
    padding: 12px;

    .left-box {
      display: flex;

      img {
        margin-right: 8px;
      }

      .lesson-info-box {
        .lesson-name {
          font-size: 15px;
          color: #323232;
          line-height: 15px;
          max-width: 270px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;

          & + div {
            font-size: 12px;
            color: #999;
            line-height: 12px;
            margin-top: 9px;

            .time {
              margin-left: 16px;
            }
          }
        }
      }
    }

    .lesson-item {
      display: flex;
      justify-content: space-between;
      background: #FFF;
      border-radius: 8px;
      padding: 8px;
      margin-bottom: 10px;

      .fire-img {
        width: 15px;
        height: 18px;
      }

      .play-img {
        width: 24px;
        height: 24px;
      }

      &.lose {
        opacity: 0.5;
      }
    }
  }
}

.week-plan-list-scroll {
  background-color: #EEF1F6;
}

// 火苗禁用的标识
.fire-img-disable {
  opacity: .5;
}

.week-plan-list {
  :global {
    .adm-list-body {
      background-color: #EEF1F6;
    }
  }
}
