import React, { useEffect, useReducer, useRef, useState } from "react";
import { Toast } from "antd-mobile";
import PageLoading, { RowLoading } from "@/components/page-loading";
import EmptyStatus from "@/components/empty-status";
import {
  clickPv,
  cls,
  createURLByType,
  EJumpType,
  openUrlInWebView,
} from "@/utils/tool";
import SafeLogger from "@/utils/safe-logger";

import type { ICompletedLessonInfo } from "@/service/self-learning/drawer";
import { getCompletedLessonList } from "@/service/self-learning/drawer";

import styles from "./style.module.scss";
import { Lesson } from "@/routes/self-learning-home/components/week-plan-list/lesson";
import { List } from "@/components";
import { useLocation } from "react-router-dom";

const logger = new SafeLogger("self-learning-completed-list");

/** 本周已完成的任务列表 */
const CompletedList: React.FC<any> = () => {
  /** 是否加载中，默认是true */
  const [loading, setLoading] = useState(true);
  /** 数据列表 */
  const [lessonList, setLessonList] = useState<ICompletedLessonInfo[]>([]);
  /** 包列表分页信息，默认在第一页且有下一页数据，后续根据接口来覆盖 */
  const packagePageInfo = useRef({
    haveNextPage: true,
    pageIndex: 1,
    pageSize: 10,
  });
  // 刷新数据信息，使ref内的list信息变更
  const [_, forceUpdate] = useReducer((x: number) => x + 1, 0);
  const location = useLocation();

  /** 根据学科的 categoryId 获取绑定的包列表 */
  const initData = async () => {
    try {
      const { data } = await getCompletedLessonList({
        ...packagePageInfo.current,
      });
      const newList = [...lessonList, ...(data?.data || [])];
      packagePageInfo.current.haveNextPage = !!data?.haveNextPage;
      setLessonList(newList);
    } catch (error) {
      // 发生错误了一定要将状态置位false，否则会无限请求
      packagePageInfo.current.haveNextPage = false;
      logger?.error("load-completed-list-error", {
        error,
        ...packagePageInfo.current,
      });
    } finally {
      loading && setLoading(false);
      forceUpdate();
    }
  };

  // 自动加载下一页
  const handleLoadMore = async () => {
    if (packagePageInfo.current.haveNextPage && !loading) {
      packagePageInfo.current = {
        ...packagePageInfo.current,
        pageIndex: packagePageInfo.current.pageIndex + 1,
      };
      await initData();
    }
  };

  useEffect(() => {
    initData();
  }, []);

  return (
    <div className={styles["completed-list-container"]}>
      {!!lessonList?.length && (
        <List
          className={styles["week-plan-list"]}
          scrollClassName={styles["week-plan-list-scroll"]}
          getKey={(item: any) =>
            `${item.courseId}-${item?.lessonInfo?.lessonId}`
          }
          renderItem={(item: any, isLast) => (
            <Lesson
              clickLessonQTKey="ewt_h5_study_course_self_learning_completed_list_lesson_click"
              isLast={isLast}
              item={{ lessonInfo: { ...item }, ...item }}
              lightFire={true}
              fireClassName={cls([
                (!item?.lessonStatus || !item?.courseStatus) &&
                  styles["fire-img-disable"],
              ])}
              disableLesson={!item?.lessonStatus || !item?.courseStatus}
              disableCourse={!item?.courseStatus}
              showPackagePop={() => {
                clickPv(
                  "ewt_h5_study_course_self_learning_completed_list_course_click",
                  { courseId: item.courseId },
                );
                if (!item?.courseStatus) {
                  Toast.show("该视频已下架");
                  return;
                }
                if (item?.courseId) {
                  // 新开webview去查看详情
                  openUrlInWebView(
                    createURLByType({
                      path: "/self-learning/plan-detail",
                      type: EJumpType.outside,
                      originSearch: location.search,
                      addQueryObject: {
                        courseId: item.courseId,
                      },
                    }),
                  );
                }
              }}
              handleCompleteLesson={() => {}}
            />
          )}
          data={lessonList || []}
          hasMore={packagePageInfo.current.haveNextPage}
          loadMore={handleLoadMore}
        >
          {(loadMore: boolean) =>
            loadMore ? <RowLoading /> : <div>已经到底了</div>
          }
        </List>
      )}

      {/* 空数据时展示空态，网络异常导致的空态同样 */}
      {!lessonList?.length && !loading && (
        <div style={{ height: "calc(90vh - 12.8vw)" }}>
          <EmptyStatus />
        </div>
      )}
      {/* 页面进入时展示用的loading */}
      <PageLoading visible={loading} warpClassName={styles["fixed-loading"]} />
    </div>
  );
};

export default React.memo(CompletedList);
