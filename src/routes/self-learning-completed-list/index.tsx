import * as React from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { Layout, ScrollToTop } from "@/components";
import type { ILayout } from "@/components";
import BackBlackImg from "@/assets/common/back_black.png";
import CompletedList from "./components/completed-list";
import mstJsBridge from "mst-js-bridge";

import Style from "./style.module.scss";
import { createURLByType, EJumpType } from "@/utils/tool";

export const Component: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();

  return (
    <Layout
      showHeader
      // 给安全区染色
      topSafeAreaProps={
        { className: Style["top-safe-area"] } as ILayout["topSafeAreaProps"]
      }
      setChildrenContainerHeight={({ top, bottom }) => {
        return `calc(100vh - ${(top || 0) + (bottom || 0)}px)`;
      }}
      headerProps={{
        children: "本周已完成的任务",
        className: Style["header"],
        backIconUrl: BackBlackImg,
        onBack: () => {
          // 如果是首页跳转过来的，就 replace 跳转回去，否则就关闭 webview
          const isFromHome = location.search.indexOf("from=home") !== -1;
          if (isFromHome) {
            navigate(
              createURLByType({
                type: EJumpType.inside,
                originSearch: location.search,
                path: "/self-learning/home",
                removeQueryKeys: ["from"],
              }),
              { replace: true },
            );
          } else {
            mstJsBridge.closeWebview();
          }
        },
      }}
    >
      <div
        style={{
          overflow: "auto",
          maxHeight: `calc(100% - 12.8vw)`,
          height: `calc(100vh - 12.8vw)`,
          backgroundColor: "#EEF1F6",
        }}
        className="self-learning-completed-list-page"
      >
        <CompletedList />
        <ScrollToTop scrollElementClass="self-learning-completed-list-page" />
      </div>
    </Layout>
  );
};
