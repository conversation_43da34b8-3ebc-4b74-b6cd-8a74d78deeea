.top-safe-area {
  background-color: transparent;
}

.header {
  background-color: transparent;

  &.holiday-header {
    :global {
      .adm-nav-bar-title,.adm-nav-bar-back {
        color: #fff;
      }
    }
  }
}

.today-check-success {
  :global {
    .adm-auto-center-content {
      z-index: 1001;
    }

    .adm-toast-main {
      max-width: 210px;
    }
  }
}

.have-image-bg {
  background-image: url("@/assets/common/bg_window.png"),linear-gradient(180deg, #FFD95F 0%, #FFBD32 99%);
  background-repeat: no-repeat;
  background-size: 46%, 100%;
}

.no-image-bg {
  background-image: linear-gradient(180deg, #FFD95F 0%, #FFBD32 99%);
}

.holiday-theme {
  background-image: url("@/assets/home/<USER>"),linear-gradient(180deg, #FF4930 0%, #FFD95F 99%)!important;
  background-size: 100%, 100%!important;
  &.no-image-bg {
    background-image: linear-gradient(180deg, #FF4930 0%, #FFD95F 99%)!important;
  }
}

// 临时内容，下期要删除的，勿动
.temp-center-popup {
  :global {
    .adm-center-popup-wrap {
      width: 260px;
      min-width: 260px;
    }
  }
}

.temp-popup-box {
  width: 260px;
  box-sizing: border-box;
  padding-top: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  text-align: center;
  .title {
    height: 16px;
    font-weight: bold;
    font-size: 16px;
    color: #333333;
    text-align: center;
  }
  .content {
    padding: 0 20px;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    text-align: center;
    margin: 14px 0 20px 0;
  }
  .footer {
    width: 100%;
    height: 44px;
    border-top: 1px #EEF1F6 solid;
    font-weight: 400;
    font-size: 16px;
    color: #2D86FE;
    text-align: center;
    line-height: 44px;
  }
}
