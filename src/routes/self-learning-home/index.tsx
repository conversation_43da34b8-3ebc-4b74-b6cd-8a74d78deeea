/**
 * 自习计划首页
 */

import * as React from "react";
import { CenterPopup, Toast } from "antd-mobile";

import SafeLogger from "@/utils/safe-logger";
import FloatIcon from "@/components/float-icon";
import { Layout, ScrollToTop } from "@/components";
import type { ILayout, ILayoutRef } from "@/components";
import BackBlackImg from "@/assets/common/back_black.png";
import BackImg from "@/assets/common/back.png";
import {
  EStudyScene,
  IFlagItem,
  IPlanDetailRes,
  updateFlag as updateFlagApi,
  updateWeekPlanCount,
  getSelfLearningOfflineConfig,
  IGetSelfLearningOfflineConfigRes,
} from "@/service/home";
import {
  DateReminder,
  IPopupRef as IDateReminderRef,
} from "./components/date-reminder";
import {
  clickPv,
  cls,
  getChannel,
  getUserInfo,
  openRoute,
  openUrlInWebView,
  versionCompare,
} from "@/utils/tool";

import { Context } from "./context";
import {
  Popups,
  BasicInfo,
  WeekAndAllList,
  EditFlagPop,
  EditWeekTargetPop,
  CheckInRemindPop,
  SetCalenderRemindPop,
  SelectWeekTargetPop,
} from "./components";
import type { IPopupRef } from "./components";
import {
  setCheckInThreshold,
  EPopupType,
  ESourceFrom,
  homeScrollContainer,
  SELF_LEARNING_FULL_SCREEN_GUID,
  sourceFrom,
} from "./common";
import {
  useCompleteCoursePop,
  useMilestonePop,
  usePlanDetail,
  useWeekCompletePop,
  useUserInfo,
  useETraveAuth,
  useCheckInPop,
  useDateReminder,
  checkHolidayUpgradeTipPop,
} from "./hooks";
import mstJsBridge from "mst-js-bridge";

import Style from "./style.module.scss";
import {
  ReceivePrizeDrawerComponent,
  IPopupRef as IReceiveRef,
} from "@/components/receive-prize-drawer";

import moment from "moment";
import {
  ReceivePrizeCommonPopup,
  IPopupRef as IReceivePopRef,
  ERewardStatus,
} from "@/components/receive-prize-common-popup";
import { getClockInPrizeConfig } from "@/service/self-learning/drawer";
import { useSearchParams } from "react-router-dom";
import OfflineTipPopup, {
  IPopupRef as IOfflineTipPopupRef,
} from "./components/offline-tip-popup";

export const Component: React.FC = () => {
  const {
    checkLogoutCount,
    saveCalenderInfo,
    checkSessionFlag,
    saveSessionFlag,
    checkCalenderSetStatus,
  } = useDateReminder();
  const [reportVis, setReportVis] = React.useState(false);
  const [refreshWeekList, setRefreshWeekList] = React.useState<boolean>();
  // E游记权限
  const { hasETraveAuth, getETraveAuth } = useETraveAuth();
  // layoutRef，设置页面级的 loading
  const layoutRef = React.useRef<ILayoutRef>();
  // 离线提示弹窗的ref
  const offlineTipRef = React.useRef<IOfflineTipPopupRef>(null);
  // setCalender 设置日历的ref
  const setCalenderRef = React.useRef<IDateReminderRef>();
  // 计划详情
  const { getUserPlanDetail, planDetail, checkPlanDetailRelatedPop } =
    usePlanDetail();
  if (planDetail?.dailyCheckInDuration) {
    setCheckInThreshold(planDetail.dailyCheckInDuration);
  }
  const receiveRef = React.useRef<IReceiveRef>();
  const receivePopRef = React.useRef<IReceivePopRef>();
  const [offlineConfig, setOfflineConfig] =
    React.useState<IGetSelfLearningOfflineConfigRes>();
  const [showOfflineTip, setShowOfflineTip] = React.useState(false);
  // app version
  const [appVersion, setAppVersion] = React.useState("");
  const [channel, setChannel] = React.useState("");
  /** 打卡的领奖信息 */
  const [receiveBaseInfo, setReceiveBaseInfo] = React.useState<any>();
  const [isHolidayReward, setIsHolidayReward] = React.useState(false);
  // 用户完成的周计划数
  const { getWeekPopInfo } = useWeekCompletePop();
  // 今日学习时长、打卡天数、里程碑相关信息
  const {
    hasCheckInWithToday,
    loading: loadTodayStudyTime,
    studyTimeAndStatus,
    showCannotCheckInReasonRef,
    checkInPrefixState,
    holidayDaysAndStatus,
    getTodayStudyTime,
    querySceneDetail,
  } = useCheckInPop({
    hasETraveAuth,
    planDetail,
  });
  const { milestoneInfo, checkMilestonePop, getMilestoneInfo } =
    useMilestonePop({
      hasETraveAuth,
    });
  // 刷新数据信息
  const [_, forceUpdate] = React.useReducer((x: number) => x + 1, 0);
  const showImgBgRef = React.useRef({
    isShow: true,
  });
  const [params, setParams] = useSearchParams();
  const showCalendar = params.get("showCalendar");
  function removeCalenderParams() {
    // 消费完后，去除
    params.delete("showCalendar");
    setParams(params, { replace: true });
  }

  // 获取自习计划下线配置
  const checkDownloadConfig = async (appVersion: string) => {
    try {
      const channel: string = (await getChannel()) as unknown as string;
      setChannel(channel);
      const { data } = await getSelfLearningOfflineConfig();
      if (data) {
        setOfflineConfig(data);
      }
      setShowOfflineTip(data?.tipSwitch);
    } catch (error) {
      console.error("获取自习计划下线配置失败", error);
    }
  };

  // 包完成
  const { getCompleteCourseInfo } = useCompleteCoursePop();
  // 用户信息，包含 flag 信息
  const { userInfo } = useUserInfo();
  const popupsRef = React.useRef<IPopupRef>();
  /** 弹窗调度 */
  React.useEffect(() => {
    getAppVersion();
    popupsRef.current.setPopupList?.([
      /** 假期的规则升级提示底抽 */
      EPopupType.holidayUpgradeTip,
      /** E游记权限 */
      EPopupType.eTraveAuth,
      /** 新手引导 */
      EPopupType.newcomerGuidance,
      /** 欢迎勋章 */
      EPopupType.welcomeMedal,
      /** 全屏的新手指引 */
      EPopupType.fullScreenGuidance,
      // 设置日历底抽
      EPopupType.calender,
      /** 打卡弹窗 */
      EPopupType.checkIn,
      /** 周达标 */
      EPopupType.weekComplete,
      /** 里程碑 */
      EPopupType.milestoneComplete,
      // 包完成
      EPopupType.courseComplete,
    ]);
  }, []);

  const handleHomeScroll = (event: Event) => {
    const top = (event.target as Element).scrollTop;
    const isShow = top < 20;
    if (isShow !== showImgBgRef.current.isShow) {
      showImgBgRef.current.isShow = isShow;
      forceUpdate();
    }
  };

  React.useEffect(() => {
    const scrollElement = document.querySelector(`.${homeScrollContainer}`);
    if (!scrollElement) {
      return;
    }
    scrollElement.addEventListener("scroll", handleHomeScroll);
    return () => {
      scrollElement?.removeEventListener("scroll", handleHomeScroll);
    };
  }, []);

  React.useEffect(() => {
    // 能获得场景信息才查询场景详情
    if (planDetail?.studyScene) {
      querySceneDetail();
    }
  }, [planDetail]);

  /** 刷新今日时长，走一次里程碑弹窗调度 */
  function refreshTodayStudyTime(showCheckRes?: boolean) {
    showCannotCheckInReasonRef.current = showCheckRes;
    popupsRef.current?.setPopupList([
      EPopupType.checkIn,
      EPopupType.milestoneComplete,
    ]);
  }

  function toAward(scene?: EStudyScene) {
    if (!hasETraveAuth) {
      Toast.show("暂无领取权限");
    } else {
      setIsHolidayReward(EStudyScene.winterHoliday === scene);
      receiveRef?.current?.setVisible(true);
    }
  }

  // 打卡的领奖、查看奖励弹窗
  const onCheckInReceiveReward = async () => {
    try {
      getMilestoneInfo();
    } catch (error) {
      console.log("获取里程碑信息出错", error);
    }

    try {
      const { data } = await getClockInPrizeConfig({
        eventCode: "900002",
      });
      // 有任务id才算成功，否则正常关闭弹框
      if (data?.taskId) {
        const { milestoneLevelConfigVOs } = data;
        const rewardStatus = checkInPrefixState?.rewardFlag
          ? ERewardStatus.view
          : ERewardStatus.receive;
        // 奖品有的话默认取第一个就行，旅程任务就只有一个奖励信息
        const prizeInfo: any = milestoneLevelConfigVOs?.[0] || {};
        // 如果在app内，且拿到了版本号，且版本号是目标版本, 且有节点信息
        if (
          mstJsBridge.isInMstApp() &&
          appVersion &&
          versionCompare(appVersion, "10.6.0") &&
          data?.nodeId
        ) {
          const { nodeId, taskId, taskRewardLevelId } = data;
          openRoute({
            domain: "user",
            action: "task_rewards",
            params: {
              // 接口如果没返回就传递一个0，原因是app内部没有对这个字段的缺省做处理，不传会导致app的crash
              nodeId: nodeId || 0,
              taskId,
              // 原因同上
              taskRewardLevelId: taskRewardLevelId || 1,
              processNum: prizeInfo?.rewardValue || 0,
              moduleName: "学生自主学习计划",
              // app参数的1代表领取奖励，2代表查看奖励，根据后端接口给值，默认是1
              rewardStatus,
            },
          });
          // 跳转app前增加后续里程碑流程
          popupsRef?.current.setPopupList([EPopupType.milestoneComplete]);
          // 如果去app领取就不继续执行h5的领取了
          return;
        }
        setReceiveBaseInfo({
          ...data,
          ...prizeInfo,
          rewardStatus,
        });
        receivePopRef?.current.setVisible(true);
      } else {
        // 弹出领奖后如果没有taskId，则继续后续的里程碑流程，其他流程等待后续触发
        popupsRef?.current.setPopupList([EPopupType.milestoneComplete]);
      }
    } catch (error) {}
  };

  // 检测全屏新手引导是否需要展示
  const checkFullScreenGuidance = () => {
    const userId = getUserInfo();
    // 如果有用户id再进行后续检测，否则直接不展示
    if (userId) {
      const cacheKey = `${SELF_LEARNING_FULL_SCREEN_GUID}-${userId}`;
      const tempCacheStr: string | null = localStorage.getItem(cacheKey);
      return !tempCacheStr;
    }
    return false;
  };

  // 组件共享的状态
  const contextValue = React.useMemo(() => {
    return {
      planDetail,
      popupsRef: popupsRef,
      refreshWeekList,
    };
  }, [planDetail, refreshWeekList]);

  /** 编辑 flag */
  const [editFlagPopVisible, setEditFlagPopVisible] = React.useState<boolean>();
  async function updateFlag(flag: IFlagItem) {
    try {
      /** 埋点 */
      clickPv("ewt_h5_study_course_self_learning_home_set_flag_click", {
        sourceFrom: sourceFrom[ESourceFrom.setFlagPop],
        flagContent: flag.flag,
        flagId: flag.id,
      });
      layoutRef.current.setPageLoading?.(true);
      const { data: result } = await updateFlagApi({
        flagContent: flag.flag,
        flagId: flag.id,
      });
      if (result) {
        setEditFlagPopVisible(false);
        // 重新获取一次计划详情，保证 flag 的值是最新的
        getUserPlanDetail();
      }
    } catch (error) {
      console.error(error);
      SafeLogger.baseLogger.error("edit-flag-failed", { error });
    } finally {
      layoutRef.current.setPageLoading?.(false);
    }
  }
  /** 选择周目标 */
  const [showSelectWeekTargetPop, setShowSelectWeekTargetPop] =
    React.useState<boolean>();
  /** 编辑周目标 */
  const [showEditWeekTargetPop, setShowEditWeekTargetPop] =
    React.useState<boolean>();
  async function updateWeekTarget(weekTarget: number) {
    try {
      /** 埋点 */
      clickPv("ewt_h5_study_course_self_learning_home_set_week_target_click", {
        sourceFrom: sourceFrom[ESourceFrom.setWeekTargetPop],
        weekPlanCount: weekTarget,
      });
      layoutRef.current.setPageLoading?.(true);
      const { data: editResult } = await updateWeekPlanCount({
        weekPlanCount: Number(weekTarget),
      });
      if (editResult) {
        Toast.show("设置成功");
        // 重新获取一次计划详情，更新周计划数
        getUserPlanDetail();
        popupsRef.current.setPopupList([EPopupType.weekComplete]);
      }
    } catch (error) {
      SafeLogger.baseLogger.error("edit-week-target-failed", { error });
    } finally {
      layoutRef.current.setPageLoading?.(false);
    }
  }

  /** 打卡提醒底抽 */
  const [checkInPopVis, setCheckInPopVis] = React.useState<boolean>();
  function changeCheckInVis() {
    setCheckInPopVis((pre) => !pre);
  }

  /** 打卡提醒底抽 */
  const [calenderRemindVis, setCalenderRemindVis] =
    React.useState<boolean>(false);
  function changeCalenderRemindVis() {
    setCalenderRemindVis((pre) => !pre);
  }

  // 检测关于日历提醒的退出次数，返回是否需要进行提示 boolean
  const checkIsRequireCalenderPop = () => {
    const { showPopup, logoutCount } = checkLogoutCount();
    const { isSetCalender, isCalenderExpired } = checkCalenderSetStatus();
    const haveSessionFlag = checkSessionFlag();
    if (haveSessionFlag) {
      return false;
    }
    // 首先判断session缓存内是否有弹出过的标识，如果有直接不再弹出，关闭窗口即可
    // 如果没弹出过就保存标识到session缓存内，并记录弹出次数，等待下次访问
    saveSessionFlag();
    if (isSetCalender && !isCalenderExpired) {
      return false;
    }
    if (showPopup) {
      saveCalenderInfo({
        logoutCount: logoutCount + 1,
        logoutTime: moment().format("YYYY-MM-DD"),
      });
      return true;
    }
    return false;
  };

  // 获取app版本
  const getAppVersion = async () => {
    try {
      // 这里不用通用的方法，以为是需要异步即可，不用同步锁
      const { data } = await mstJsBridge.getAppVersion();
      if (data?.version) {
        const appVersion = data.version;
        setAppVersion(appVersion);
        // 拿到app的版本号再去获取下线配置
        checkDownloadConfig(appVersion);
      }
    } catch (error) {
      // 不是核心链路，失败了就从h5领取，因此通过arms收集即可
      console.warn("获取app版本号失败", error);
    }
  };

  const toReportPage = () =>
    userInfo?.studyReportRouter &&
    openUrlInWebView(userInfo?.studyReportRouter);

  // 1.2的临时版本 - 等自习报告做到实时后该提示需要去掉的，直接跳转
  // 当用户打卡天数小于等于1天，且本地无缓存标识情况下，进行一次报告说明，后续不再弹出
  const handleTempTip = () => {
    /** 埋点 */
    clickPv("ewt_h5_study_course_self_learning_home_self_study_report_click");
    const SELF_LEARNING_REPORT_TEMP_TIP = "self-learning-report-temp-tip";
    const userId = getUserInfo();
    if (userId) {
      const cacheKey = `${SELF_LEARNING_REPORT_TEMP_TIP}-${userId}`;
      const cacheData = localStorage.getItem(cacheKey);
      if (!cacheData && milestoneInfo?.days <= 1) {
        setReportVis(true);
        localStorage.setItem(cacheKey, "true");
        return;
      }
    }
    toReportPage();
  };

  // 检测假期弹窗要不要弹出
  const checkHolidayPop = async () => {
    try {
      return checkPlanDetailRelatedPop((res: IPlanDetailRes) => {
        const displayHolidayUpgradePop = checkHolidayUpgradeTipPop(
          res?.studyScene === EStudyScene.winterHoliday,
        );
        return displayHolidayUpgradePop;
      });
    } catch (error) {
      console.error(error);
    }
  };

  // 检测新手引导要不要弹出，填写flag等信息
  const checkNewcomerGuidancePop = async () => {
    try {
      return checkPlanDetailRelatedPop(
        (res: IPlanDetailRes) => !res?.flagContent,
      );
    } catch (error) {
      console.error(error);
    }
  };

  const isHoliday = planDetail?.studyScene === EStudyScene.winterHoliday;

  return (
    <Layout
      showHeader
      // 给安全区染色
      topSafeAreaProps={
        { className: Style["top-safe-area"] } as ILayout["topSafeAreaProps"]
      }
      setChildrenContainerHeight={({ top, bottom }) => {
        return `calc(100vh - ${top || 0}px - ${bottom || 0}px)`;
      }}
      bgClassName={cls([
        showImgBgRef.current.isShow
          ? Style["have-image-bg"]
          : Style["no-image-bg"],
        planDetail?.studyScene === EStudyScene.winterHoliday &&
          Style["holiday-theme"],
      ])}
      headerProps={{
        children: "自习计划",
        className: cls([Style["header"], isHoliday && Style["holiday-header"]]),
        backIconUrl: isHoliday ? BackImg : BackBlackImg,
        onBack: () => {
          // 如果已经打过卡了，就无需打开提醒的底抽
          if (studyTimeAndStatus.checkInStatus || hasCheckInWithToday) {
            if (checkIsRequireCalenderPop()) {
              changeCalenderRemindVis();
            } else {
              mstJsBridge.closeWebview();
            }
          } else {
            changeCheckInVis();
          }
        },
        right: (
          <DateReminder
            isHoliday={isHoliday}
            ref={setCalenderRef}
            onClose={() => popupsRef?.current.popNext()}
          />
        ),
      }}
      ref={layoutRef}
    >
      <Context.Provider value={contextValue}>
        <div
          style={{
            overflow: "auto",
            maxHeight: `calc(100% - 12.8vw)`,
            height: `calc(100% - 12.8vw)`,
          }}
          className={homeScrollContainer}
        >
          <BasicInfo
            hasETraveAuth={hasETraveAuth}
            hasCheckIn={hasCheckInWithToday}
            actionLoading={loadTodayStudyTime}
            planDetail={planDetail}
            studyTimeAndStatus={studyTimeAndStatus}
            milestoneInfo={milestoneInfo}
            userInfo={userInfo}
            refreshTodayStudyTime={refreshTodayStudyTime}
            toSelfStudyReport={handleTempTip}
            toAward={toAward}
            setFlag={() => {
              setEditFlagPopVisible(true);
            }}
            holidayDaysAndStatus={holidayDaysAndStatus}
          />
          {/* 非校园版如果开启情况下需要展示提示 */}
          {showOfflineTip && offlineConfig && (
            <OfflineTipPopup
              ref={offlineTipRef}
              config={offlineConfig}
              appVersion={appVersion}
            />
          )}
          <WeekAndAllList
            onBatchFinishCallBack={getMilestoneInfo}
            editWeekTarget={() => {
              setShowEditWeekTargetPop(true);
            }}
            // showAddButton={!showOfflineTip}
            showAddButton={false}
          />
          {/* 编辑 flag */}
          <EditFlagPop
            title="设置我的Flag"
            flagListProps={{
              showValue: true,
              value: {
                id: planDetail?.flagId,
                flag: planDetail?.flagContent,
              },
              onSelected: updateFlag,
            }}
            visible={editFlagPopVisible}
            onClose={() => setEditFlagPopVisible(false)}
          />
          {/* 编辑周目标 */}
          <EditWeekTargetPop
            title="设置周目标数"
            visible={showEditWeekTargetPop}
            weekCompleteTargetProps={{
              planDetail,
              value: planDetail?.userPlanCount,
              onEditWeekTargetClick: () => {
                clickPv(
                  "ewt_h5_study_course_self_learning_home_set_week_target_button_click",
                );
                // 点击修改数量时关闭当前底抽，并打开选择周目标数底抽
                setShowEditWeekTargetPop(false);
                setShowSelectWeekTargetPop(true);
              },
            }}
            onClose={() => setShowEditWeekTargetPop(false)}
          />
          <SelectWeekTargetPop
            visible={showSelectWeekTargetPop}
            minValue={planDetail?.minPlanCount}
            maxValue={planDetail?.maxPlanCount}
            selectValue={planDetail?.userPlanCount}
            onClose={() => setShowSelectWeekTargetPop(false)}
            onConfirm={(weekTarget: number) => {
              clickPv(
                "ewt_h5_study_course_self_learning_home_set_week_target_ok_button_click",
                {
                  originTarget: planDetail?.userPlanCount || 0,
                  newTarget: weekTarget,
                },
              );
              updateWeekTarget(weekTarget);
            }}
          />
          {/* 打卡提醒底抽 */}
          <CheckInRemindPop
            studyDuration={studyTimeAndStatus?.studyDuration}
            visible={checkInPopVis}
            onClose={changeCheckInVis}
            onOk={changeCheckInVis}
            onCancel={() => {
              mstJsBridge.closeWebview();
            }}
          />
          <SetCalenderRemindPop
            visible={calenderRemindVis}
            onClose={changeCalenderRemindVis}
            onOk={() => {
              changeCalenderRemindVis();
              setCalenderRef?.current.setVisible(true);
            }}
            onCancel={() => {
              mstJsBridge.closeWebview();
            }}
          />
        </div>
        {/* 统一管理有优先级关系的弹窗 */}
        <Popups
          ref={popupsRef}
          planDetail={planDetail}
          hasETraveAuth={hasETraveAuth}
          toAward={toAward}
          checkETraveAuth={getETraveAuth}
          setRefreshWeekList={setRefreshWeekList}
          checkWeekPop={getWeekPopInfo}
          checkInPop={getTodayStudyTime}
          checkHolidayPop={checkHolidayPop}
          checkNewcomerGuidance={checkNewcomerGuidancePop}
          checkCompleteCoursePop={getCompleteCourseInfo}
          checkMilestonePop={checkMilestonePop}
          checkInPrefixState={checkInPrefixState}
          onCheckInReceiveReward={onCheckInReceiveReward}
          checkFullScreenGuidance={checkFullScreenGuidance}
          checkSetCalenderIsShow={() => showCalendar === "true"}
          changeCalenderCallback={() => {
            removeCalenderParams();
            setCalenderRef?.current.setVisible(true);
          }}
        />
        <FloatIcon />
        <ReceivePrizeDrawerComponent
          processNum={milestoneInfo?.days}
          ref={receiveRef}
          scene={planDetail?.studyScene}
          isHolidayReward={isHolidayReward}
          /** 关闭后，更新奖励状态 */
          closeBefore={() => {
            getMilestoneInfo();
            if (planDetail?.studyScene) {
              querySceneDetail();
            }
          }}
        />

        {/* 前端H5的领取奖励抽屉 */}
        <ReceivePrizeCommonPopup
          ref={receivePopRef}
          rewardStatus={receiveBaseInfo?.rewardStatus}
          receiveBaseInfo={receiveBaseInfo}
          // 关闭通用领奖后再去调度一次里程碑的奖励信息，目前仅在首页试用
          closeBefore={() =>
            popupsRef?.current.setPopupList([EPopupType.milestoneComplete])
          }
        />
      </Context.Provider>
      <ScrollToTop scrollElementClass={homeScrollContainer} />
      {/* 1.2版本新增的自习报告提示，该提示在下次迭代时会删除，依靠实时报告 */}
      <CenterPopup className={Style["temp-center-popup"]} visible={reportVis}>
        <div className={Style["temp-popup-box"]}>
          <div className={Style["title"]}>温馨提示</div>
          <div className={Style["content"]}>
            今天学习的内容，要等到明天才能看到对应的自习报告哦～
          </div>
          <div className={Style["footer"]} onClick={() => setReportVis(false)}>
            我知道了
          </div>
        </div>
      </CenterPopup>
    </Layout>
  );
};
