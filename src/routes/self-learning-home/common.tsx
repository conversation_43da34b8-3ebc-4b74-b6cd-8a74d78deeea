import * as React from "react";

import flagPNG from "@/assets/person/flagman.png";
import planPNG from "@/assets/person/planman.png";

/** 弹窗类型 */
export enum EPopupType {
  holidayUpgradeTip, // 假期的升级规则提示
  newcomerGuidance,
  welcomeMedal,
  calender,
  weekComplete,
  milestoneComplete,
  courseComplete,
  /** 打卡弹窗 */
  checkIn,
  /**
   * E游记权限，这个权限关系到欢迎勋章、里程碑这两个pop接口是否要调用
   * 如果没权限就不用调pop接口了，因为这两个接口，没有判断E游记权限逻辑
   * */
  eTraveAuth,
  fullScreenGuidance,
}

/** 新手引導步驟枚舉 */

export enum EStepType {
  setFlag = 1,
  addPlans,
  setWeekTarget,
}

export const stepsContent = {
  total: 2,
  [EStepType.setFlag]: {
    img: flagPNG,
    title: "立个Flag",
    content: "学习在于必胜的决心和不懈的努力",
    tip: "选择一条鼓舞自己的Flag吧",
  },
  [EStepType.addPlans]: {
    img: planPNG,
    title: "挑选自习内容",
    content: "这里是平台老师非常推荐的优质课程",
    tip: "选择你喜欢的课点击“加入计划”吧",
  },
};

export const NEXTSTEP = "下一步";
export const FINISH = "挑选完毕";

// 信息卡片
export enum EInfoCardType {
  todayStudyTime = 1,
  checkInDays,
  selfStudyReport,
}
export const INFOCARD = {
  [EInfoCardType.checkInDays]: {
    title: "累计打卡",
    unit: "天",
    noReward: "奖励详情",
    hasReward: "点击领奖",
  },
  [EInfoCardType.selfStudyReport]: {
    title: "自习报告",
    statusText: "查看",
  },
};

export enum EListType {
  weekList = 1,
  allList,
}

export const listName = {
  [EListType.weekList]: "本周计划",
  [EListType.allList]: "全部计划",
};

export enum EListStatus {
  done = 1,
  empty,
  error,
  offline,
}

export const listStatusText = {
  [EListStatus.done]: "你已学完现有计划，可继续添加内容哦～",
  [EListStatus.empty]: [
    "你的计划还没有添加内容",
    "快去添加内容开启学习之旅吧～",
  ],
  [EListStatus.error]: ["糟糕，加载遇到了点问题", "点击“重新加载”试试"],
  [EListStatus.offline]: "暂无内容，旧版已无法添加内容",
};

export const listBtnText = {
  [EListStatus.done]: "添加内容",
  [EListStatus.empty]: "添加内容",
  [EListStatus.error]: "重新加载",
};
/** 包列表状态 */
export enum EPackageListStatus {
  // 还没有加入过计划
  empty = 1,
  // 已经完成进行中的全部包
  doingEmpty,
  // 没有已完成的课程包
  doneEmpty,
  error,
  /** 暂无数据 */
  noData,
  haveData,
}

export const packageListStatusText = {
  [EPackageListStatus.doingEmpty]: "你已学完现有计划，可继续添加内容哦～",
  [EPackageListStatus.empty]: [
    "你的计划还没有添加内容",
    "快去添加内容开启学习之旅吧～",
  ],
  [EPackageListStatus.doneEmpty]: "你还没有完成的计划哦～",
  [EPackageListStatus.error]: ["糟糕，加载遇到了点问题", "点击“重新加载”试试"],
};

export const packageListBtnText = {
  [EPackageListStatus.empty]: "添加内容",
  [EPackageListStatus.doingEmpty]: "添加内容",
  [EPackageListStatus.error]: "重新加载",
};

/** 全部计划 */
export enum EPackageStatus {
  doing,
  done,
}

export const packageStatusText = {
  [EPackageStatus.doing]: "进行中",
  [EPackageStatus.done]: "已完成",
};

/** 埋点常量 */
export enum ESourceFrom {
  newcomerGuidance,
  setFlagPop,
  setWeekTargetPop,
}

export const sourceFrom = {
  [ESourceFrom.newcomerGuidance]: "新手引导",
  [ESourceFrom.setFlagPop]: "设置我的Flag底抽",
  [ESourceFrom.setWeekTargetPop]: "设置周目标数底抽",
};

export const allDonePlanScrollEndText = "查看更早前完成的计划";

/** 飞入目的地的容器 id */
export const flyTargetWrapperId = "home-week-target-img-wrapper";

/** 飞入指定的空火焰容器 id */
export const flyTargetFireId = "home-week-target-fire";

/** 首页滚动容器类名 */
export const homeScrollContainer = "home-scroll-container";

/** 打卡时间阈值 */
export let checkInThreshold = 15;

export function getCheckInThreshold() {
  return checkInThreshold;
}

export function setCheckInThreshold(value: number) {
  checkInThreshold = value;
}

/** 离开挽留文案 */
export const stillLeave = "残忍离开";
export const keepLearn = "继续学习";
export const setCalenderButton = "设置日历提醒";

export const SELF_LEARNING_FULL_SCREEN_GUID = "self-learning-full-screen-guid";
// 假期规则升级提示底抽 - 本地缓存 key
export const SELF_LEARNING_HOLIDAY_UPGRADE_POP =
  "self-learning-holiday-upgrade-pop";
