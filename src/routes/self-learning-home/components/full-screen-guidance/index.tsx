import React, { useEffect, useState } from "react";
import { clickPv, cls, createURLByType, expPv, openRoute } from "@/utils/tool";
import {
  getCheckInThreshold,
  homeScrollContainer,
  SELF_LEARNING_FULL_SCREEN_GUID,
} from "@/routes/self-learning-home/common";
import { getUserInfo } from "@/utils/tool";
import HandPng from "@/assets/common/hand.png";

import Style from "./style.module.scss";
import { useLocation, useNavigate } from "react-router-dom";
import { Mask } from "antd-mobile";

interface IFullScreenGuidance {
  onClose?: () => void;
}

export interface IPopupRef {
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
}

export const FullScreenGuidance = React.forwardRef<
  IPopupRef,
  IFullScreenGuidance
>(function Popups(props, ref) {
  const nav = useNavigate();
  const location = useLocation();
  const { onClose = () => {} } = props;
  const [visible, setVisible] = useState(false);
  const [havePlanVis, setHavePlanVis] = useState(false);
  const [noPlanVis, setNoPlanVis] = useState(false);
  const [courseInfo, setCourseInfo] = useState({
    courseId: "",
    lessonId: "",
  });
  const [noPlanBoxSize, setNoPlanBoxSize] = useState({
    width: 0,
    height: 0,
    top: 0,
    left: 0,
  });
  const [havePlanBoxSize, setHavePlanBoxSize] = useState({
    height: 0,
    top: 0,
  });
  /** 把弹窗的调度能力暴露出去 */
  React.useImperativeHandle(ref, () => {
    return {
      setVisible,
    };
  }, []);

  const handleClose = () => {
    /** 先调用外部传入的，把数据传出去 */
    onClose();
    // 再内部闭环关闭抽屉
    setVisible(false);
  };

  const changeFullScreenOverflow = (isShow: boolean) => {
    const contentScrollDom = document.querySelector(
      `.${homeScrollContainer}`,
    ) as HTMLElement;
    if (contentScrollDom) {
      contentScrollDom.style.overflow = isShow ? "auto" : "hidden";
    }
  };

  const onContentClick = () => {
    if (noPlanVis) {
      clickPv(
        "ewt_h5_study_course_self_learning_home_newcomer_guidance_week_plan_empty_status_click",
      );
      nav(
        createURLByType({
          path: "/self-learning/recommend",
          originSearch: location.search,
          addQueryObject: {
            from: "home",
          },
        }),
      );
    } else if (havePlanVis) {
      clickPv(
        "ewt_h5_study_course_self_learning_home_newcomer_guidance_week_plan_list_status_click",
      );
      if (courseInfo?.courseId) {
        openRoute({
          domain: "course",
          action: "open_detail",
          params: {
            id: `${courseInfo.courseId}`,
            lessionID: `${courseInfo.lessonId}`,
          },
        });
      }
    }
    handleClose();
  };

  // 获取容器的尺寸
  const getAddButtonSize = (dom: any) => {
    const width = dom.offsetWidth + 20;
    const height = dom.offsetHeight + 20;
    const top = dom.getBoundingClientRect().top - 10;
    const left = dom.getBoundingClientRect().left - 10;
    setNoPlanBoxSize({
      width,
      height,
      top,
      left,
    });
  };

  // 获取容器的尺寸
  const getWeekListContainerSize = (weekTargetDom: any, lessonDom: any) => {
    const lessonTop = lessonDom.getBoundingClientRect().top;
    const lessonBottom = lessonDom.getBoundingClientRect().bottom;
    const guidBoxHeight = lessonBottom - lessonTop;
    setHavePlanBoxSize({
      height: guidBoxHeight,
      top: lessonTop,
    });
  };

  const checkShowStatus = async () => {
    return window.setTimeout(() => {
      // 添加计划按钮
      const addButtonDom = document.querySelector(
        "#add-course-for-guid",
      ) as HTMLElement;
      /// 周计划列表
      const lessonDom = document.querySelector(
        "[id^=week-plan-lesson]",
      ) as HTMLElement;
      // 可滚动的内容区域，展示引导需要屏蔽滚动
      const contentScrollDom = document.querySelector(
        `.${homeScrollContainer}`,
      ) as HTMLElement;
      if ((addButtonDom || lessonDom) && contentScrollDom) {
        contentScrollDom.style.overflow = "hidden";
      }
      if (addButtonDom) {
        expPv(
          "ewt_h5_study_course_self_learning_home_newcomer_guidance_week_plan_empty_status_expo",
        );
        getAddButtonSize(addButtonDom);
        setNoPlanVis(true);
        return true;
      }
      if (lessonDom) {
        const domId = lessonDom.getAttribute("id");
        if (domId) {
          // 从id中取出课程信息
          // id的规则是1.2版本新增，week-plan-lesson-课包id-课讲id
          const [, , , courseId, lessonId] = domId.split("-");
          const weekTargetDom = document.querySelector("#week-target-box");
          if (weekTargetDom && lessonDom) {
            getWeekListContainerSize(weekTargetDom, lessonDom);
          }
          expPv(
            "ewt_h5_study_course_self_learning_home_newcomer_guidance_week_plan_list_status_expo",
          );
          setHavePlanVis(true);
          setCourseInfo({
            courseId,
            lessonId,
          });
        }
        return true;
      }
      // 如果没有找到任何一个内容，就直接关闭当前弹窗
      handleClose();
      return false;
    }, 500);
  };

  const saveLocalStorage = () => {
    const userId = getUserInfo();
    if (userId) {
      const cacheKey = `${SELF_LEARNING_FULL_SCREEN_GUID}-${userId}`;
      localStorage.setItem(cacheKey, "true");
    }
  };

  const initData = async () => {
    const result = await checkShowStatus();
    if (result) {
      // 要弹出的话就增加标识到本地
      saveLocalStorage();
    }
  };

  useEffect(() => {
    // 显示时上报状态
    if (visible) {
      initData();
    }
    // 当需要显示引导时屏蔽内容区滚动，隐藏时恢复内容区滚动
    changeFullScreenOverflow(!visible);
  }, [visible]);

  const checkInThreshold = getCheckInThreshold();

  return visible ? (
    <Mask visible={visible} className={Style["full-screen-guid-mask"]}>
      <div
        className={Style["full-screen-guidance-container"]}
        onClick={handleClose}
      >
        <div
          className={cls([
            noPlanVis && Style["guid-plan-box"],
            havePlanVis && Style["guid-plan-box"],
          ])}
          style={noPlanVis ? noPlanBoxSize : havePlanBoxSize}
          onClick={(e: any) => {
            e.stopPropagation();
            onContentClick();
          }}
        >
          {(noPlanVis || havePlanVis) && (
            <div
              className={cls([
                Style["guid-content"],
                noPlanVis && Style["no-plan-status"],
              ])}
              onClick={(e: any) => {
                e.stopPropagation();
                handleClose();
              }}
            >
              <span>
                {noPlanVis
                  ? "先去加个课程再开始自习吧～"
                  : `点此学完${checkInThreshold}分钟完成打卡即有机会获得勋章！`}
              </span>
              <img src={HandPng} alt="" />
            </div>
          )}
        </div>
      </div>
    </Mask>
  ) : null;
});
