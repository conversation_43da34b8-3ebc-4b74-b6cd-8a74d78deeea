.full-screen-guid-mask {
  background: transparent!important;
}

.full-screen-guidance-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10000;

  .guid-plan-box {
    position: fixed;
    width: 94%;
    height: 180px;
    top: calc((100vh - 180px) / 2 + 30px);
    left: 3%;
    border-radius: 16px;
    box-sizing: content-box;
    box-shadow: 0 0 0 100vh rgba(0,0,0,.6);

    .guid-content {
      position: absolute;
      bottom: -45px;
      color: #fff;
      text-align: center;
      width: 100%;
      left: 50%;
      transform: translateX(-50%);
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;

      img {
        width: 40px;
        height: 40px;
      }

      &.no-plan-status {
        top: -45px;
      }
    }
  }
}

@media(min-width: 768px) {
  .guid-content {
    top: -12vw;
  }
}
