/** 设置日历提醒底抽 */

import * as React from "react";

import NoLeavePNG from "@/assets/common/do-not-go.png";
import { Button, IPopup, Popup } from "@/components";
import { clickPv, cls, expPv } from "@/utils/tool";

import { setCalenderButton, stillLeave } from "../../common";

import Style from "./style.module.scss";

interface ISetCalenderRemindPop extends IPopup {
  onCancel: () => void;
  onOk: () => void;
}

export const SetCalenderRemindPop: React.FC<ISetCalenderRemindPop> = (
  props,
) => {
  const { visible, onCancel, onOk, onClose } = props;
  React.useEffect(() => {
    if (visible) {
      // 等待新的编码
      expPv(
        "ewt_h5_study_course_self_learning_home_no_calender_tip_popup_expo",
      );
    }
  }, [visible]);
  return (
    <Popup onClose={onClose} title="先别走！" visible={visible}>
      <div className={Style["set-calender-remind-content"]}>
        <div>我怕你把我给忘了T-T</div>
        <div>设置个日程提醒你吧</div>
        <img src={NoLeavePNG} />
      </div>
      <div className={Style["footer"]}>
        <Button
          className={cls([Style["button"], Style["cancel"]])}
          text={stillLeave}
          onClick={() => {
            clickPv(
              "ewt_h5_study_course_self_learning_home_no_calender_tip_popup_cancel_button_click",
            );
            onCancel();
          }}
        />
        <Button
          className={cls([Style["button"], Style["ok"]])}
          text={setCalenderButton}
          onClick={() => {
            clickPv(
              "ewt_h5_study_course_self_learning_home_no_calender_tip_popup_set_button_click",
            );
            onOk();
          }}
        />
      </div>
    </Popup>
  );
};
