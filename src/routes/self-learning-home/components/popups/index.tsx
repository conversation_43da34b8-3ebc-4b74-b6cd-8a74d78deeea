/** 统一管理新手引导、欢迎勋章、周达标、里程碑、包完成弹窗逻辑 */

import * as React from "react";

import {
  EStudyScene,
  ICheckInPrefixState,
  IPlanDetailRes,
} from "@/service/home";
import {
  AchieveGoalPopup,
  IPopupRef as IAchieveGoalPopupRef,
} from "@/components/achieve-goal-popup";

import { IPopupRef as IWelcomePopRef } from "../welcome-popup";
import { WelComePopup, FullScreenGuidance } from "../";
import { EPopupType } from "../../common";
import { NewComerGuidance } from "../newcomer-guidance";
import { useWelcomeModel } from "../../hooks";
import { IPopupRef as IScreenRef } from "../full-screen-guidance";
import {
  HolidayUpgradeTipPop,
  IPopupRef as IHolidayUpgradeTipRef,
} from "../holiday-upgrade-tip-pop";

interface IPopCBInfo {
  count?: number;
  completedPackageInfo?: any;
}

interface IPopups {
  hasETraveAuth: boolean;
  planDetail?: IPlanDetailRes;
  setRefreshWeekList?: React.Dispatch<React.SetStateAction<boolean>>;
  /** 假期pop */
  checkHolidayPop: () => Promise<boolean>;
  /** 新手引导是否需要弹 */
  checkNewcomerGuidance: () => Promise<boolean>;
  /** 周达标弹窗是否需要弹 */
  checkWeekPop: () => Promise<boolean>;
  /** 里程碑弹窗相关 */
  checkMilestonePop: () => Promise<boolean>;
  /** 包完成弹窗 */
  checkCompleteCoursePop: () => Promise<boolean>;
  /** 是否有e游记权限 */
  checkETraveAuth: () => Promise<boolean>;
  /** 打卡成功弹窗 */
  checkInPop: () => Promise<boolean>;
  /** 去领奖 */
  toAward: () => void;
  /** 打卡成功后的领奖弹窗前置状态 */
  checkInPrefixState?: ICheckInPrefixState;
  /** 打卡领奖弹窗的点击回调 */
  onCheckInReceiveReward?: () => void;
  /** 校验全屏指引的方法，数据在本地因此只需要正常返回boolean即可 */
  checkFullScreenGuidance: () => boolean;
  checkSetCalenderIsShow: () => boolean;
  changeCalenderCallback: () => void;
}

export interface IPopupRef {
  setPopupList: React.Dispatch<React.SetStateAction<EPopupType[]>>;
  popNext: () => void;
}

export const Popups = React.forwardRef<IPopupRef, IPopups>(
  function Popups(props, ref) {
    const {
      planDetail,
      hasETraveAuth,
      toAward,
      setRefreshWeekList,
      checkHolidayPop,
      checkNewcomerGuidance,
      checkWeekPop,
      checkMilestonePop,
      checkCompleteCoursePop,
      checkETraveAuth,
      checkInPop,
      checkInPrefixState,
      onCheckInReceiveReward,
      checkFullScreenGuidance,
      checkSetCalenderIsShow,
      changeCalenderCallback,
    } = props;
    // 欢迎勋章弹窗相关
    const { imgUrl, getWelcomeModelInfo } = useWelcomeModel({ hasETraveAuth });
    // 存储要打开的弹窗列表
    const [popupList, setPopupList] = React.useState<EPopupType[]>([]);
    // 当前被打开的弹窗
    const [nowPopup, setNowPopup] = React.useState<EPopupType>();
    // 接收 pop 接口返回的其他数据
    const [popCBInfo, setPopCBInfo] = React.useState<IPopCBInfo>();
    // 假期的提示弹窗
    const holidayUpgradeTipRef = React.useRef<IHolidayUpgradeTipRef>();
    function popCB(v: any) {
      try {
        const nowPopup = popupList[0];
        if (nowPopup === EPopupType.weekComplete) {
          setPopCBInfo({ count: v.finishCount });
        } else if (nowPopup === EPopupType.milestoneComplete) {
          setPopCBInfo({ count: v.dayCount });
        } else if (nowPopup === EPopupType.courseComplete) {
          // TODO: 字段取值可以优化
          setPopCBInfo({
            completedPackageInfo: {
              courseId: v.courseId,
              title: v.courseName,
              image: v.imgUrl,
            },
          });
        }
      } catch (error) {
        console.error(error);
      }
    }
    /** 依次调度可能要展示的弹窗 */
    function popNext() {
      setPopupList((pre) => {
        return pre.slice(1);
      });
    }
    async function schedulePopups(popups: EPopupType[]) {
      try {
        if (!popups.length) {
          return;
        }
        // 每次弹窗是否要弹，要根据后端接口返回解决决定，且接口各异
        let api;
        switch (popups[0]) {
          case EPopupType.holidayUpgradeTip:
            api = checkHolidayPop;
            break;
          case EPopupType.newcomerGuidance:
            api = checkNewcomerGuidance;
            break;
          case EPopupType.welcomeMedal:
            api = getWelcomeModelInfo;
            break;
          case EPopupType.weekComplete:
            api = checkWeekPop;
            break;
          case EPopupType.milestoneComplete:
            api = checkMilestonePop;
            break;
          case EPopupType.courseComplete:
            api = checkCompleteCoursePop;
            break;
          case EPopupType.eTraveAuth:
            api = checkETraveAuth;
            break;
          case EPopupType.checkIn:
            api = checkInPop;
            break;
          case EPopupType.fullScreenGuidance:
            api = checkFullScreenGuidance;
            break;
          case EPopupType.calender:
            api = checkSetCalenderIsShow;
            break;
          default:
            throw Error("no match");
        }
        const showPopup = await api(popCB);
        // 从数组里的第一个弹窗开始依次展示
        if (showPopup) {
          setNowPopup(popups[0]);
        } else {
          // 如果前面的弹窗不显示，就继续往后调度
          popNext();
        }
      } catch (err) {
        console.error(err);
        // 如果出现异常，直接跳到下一个要调度的弹窗
        popNext();
      }
    }

    React.useEffect(() => {
      if (!popupList?.length) {
        return;
      }
      schedulePopups(popupList);
    }, [popupList]);
    /** 把弹窗的调度能力暴露出去 */
    React.useImperativeHandle(ref, () => {
      return {
        setPopupList,
        popNext,
      };
    }, [{}]);
    /** 欢迎勋章 Pop */
    const welcomePopRef = React.useRef<IWelcomePopRef>();
    /** 新手引导 Pop */
    const screenGuidRef = React.useRef<IScreenRef>();
    /** 控制打卡、周达标、里程碑、包完成弹窗的显隐 */
    const achieveGoalPopupRef = React.useRef<IAchieveGoalPopupRef>();
    React.useEffect(() => {
      if (nowPopup === EPopupType.holidayUpgradeTip) {
        holidayUpgradeTipRef?.current.setVisible(true);
      } else if (nowPopup === EPopupType.welcomeMedal) {
        welcomePopRef.current.setVisible?.(true);
      } else if (nowPopup === EPopupType.fullScreenGuidance) {
        screenGuidRef.current.setVisible(true);
      } else if (nowPopup === EPopupType.calender) {
        changeCalenderCallback();
      } else if (
        nowPopup === EPopupType.courseComplete ||
        nowPopup === EPopupType.milestoneComplete ||
        nowPopup === EPopupType.weekComplete ||
        nowPopup === EPopupType.checkIn
      ) {
        achieveGoalPopupRef.current?.setVisible?.(true);
      }
    }, [nowPopup]);
    return (
      <div>
        {!planDetail?.flagContent ? (
          <NewComerGuidance
            visible={nowPopup === EPopupType.newcomerGuidance}
            onClose={() => {
              // 关闭之后开始调度后面的弹窗
              setNowPopup(null);
              popNext();
              // 用户可能会更改默认值，所以在新手引导关闭后，要再获取一次计划详情
              checkNewcomerGuidance();
              /** 刷新本周计划，因为可能有新加入的计划 */
              setRefreshWeekList(true);
            }}
            destroyOnClose
          />
        ) : null}
        <WelComePopup
          onClose={() => {
            popNext();
          }}
          image={imgUrl}
          ref={welcomePopRef}
        />
        <FullScreenGuidance
          ref={screenGuidRef}
          onClose={() => {
            popNext();
          }}
        />
        {nowPopup && (
          <AchieveGoalPopup
            ref={achieveGoalPopupRef}
            type={nowPopup}
            packageInfo={popCBInfo?.completedPackageInfo}
            count={popCBInfo?.count}
            checkInPrefixState={checkInPrefixState}
            onClose={() => {
              setNowPopup(null);
              // 里程碑直接去领奖，不会继续调度
              if (nowPopup === EPopupType.milestoneComplete) {
                setPopupList([]);
                toAward();
              } else if (nowPopup === EPopupType.checkIn) {
                // 打开成功后的领奖弹窗，不会继续调度
                setPopupList([]);
                onCheckInReceiveReward?.();
              } else {
                popNext();
              }
            }}
          />
        )}

        <HolidayUpgradeTipPop
          ref={holidayUpgradeTipRef}
          closeBefore={() => {
            popNext();
          }}
        />
      </div>
    );
  },
);
