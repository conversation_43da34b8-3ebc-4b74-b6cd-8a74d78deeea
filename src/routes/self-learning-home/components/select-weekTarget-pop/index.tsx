/** 周目标选择弹窗 */

import * as React from "react";
import { Picker } from "antd-mobile";

import { expPv } from "@/utils/tool";
import Style from "./style.module.scss";

interface IEditWeekTargetPop {
  visible: boolean;
  minValue: number;
  maxValue: number;
  selectValue: string | number | null;
  onClose: () => void;
  onConfirm: (value?: any) => void;
}

export const SelectWeekTargetPop: React.FC<IEditWeekTargetPop> = (props) => {
  const { minValue, maxValue, selectValue, visible, onClose, onConfirm } =
    props;
  const arrLength = maxValue - minValue + 1; // 计算需要展示的数组个数
  const columns = Array.from({ length: arrLength }).map(
    (item: any, index: number) => ({
      label: `${minValue + index}`,
      value: `${minValue + index}`,
    }),
  );

  return (
    <Picker
      columns={[columns]}
      visible={visible}
      onClose={onClose}
      value={[`${selectValue}`]}
      onConfirm={(v: any) => onConfirm(v?.[0] || "")}
    />
  );
};
