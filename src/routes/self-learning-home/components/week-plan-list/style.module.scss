.week-target {
  display: flex;
  height: 40px;
  padding-left: 12px;
  box-sizing: border-box;
  font-size: 12px;
  color: #333333;
  background-color: #fff;
  align-items: center;
  justify-content: center;
  // 吸顶
  position: sticky;
  top: 40px;
  z-index: 1;

  .week-target-desc {
    margin-right: 15px;
  }

  .img-wrapper {
    display: flex;
  }

  img {
    width: 15px;
    height: 18px;
    margin-right: 12px;
  }

  .edit {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
  }
}

.error-btn {
  font-size: 16px;
  height: 32px;
}

.empty {
  position: relative;
  width: 100vw;
  // min-height: 500px;
  height: 400px;
}

.have-completed-list {
  position: absolute;
  bottom: 24px;
  box-sizing: border-box;
  font-weight: 500;
  font-size: 12px;
  color: #2D86FE;
  left: 50%;
  transform: translateX(-50%);
}

.list-use-desc {
  margin-top: 12px;
  display: flex;
  padding: 0 12px;
  align-items: center;
  justify-content: space-between;
  font-weight: 400;
  font-size: 12px;
  color: #666666;

  img {
    margin: 0 4.33px;
    height: 20px;
    width: 20px;
  }
}

.week-plan-list-scroll {
  background-color: #EEF1F6;
}

.no-more-lessons {
  margin-top: 8px;
  height: 20px;
  min-height: 20px;
  font-weight: 400;
  font-size: 12px;
  color: #999999;
  line-height: 20px;
  padding-bottom: 40px;

  .completed-lessons {
    font-weight: 500;
    font-size: 12px;
    color: #2D86FE;
    margin-left: 4px;
  }
}

.lesson-item {
  display: flex;
  height: 96px;
  // 让3D效果生效
  perspective: 800;
  box-sizing: border-box;
  margin-top: 12px;
  padding: 0 12px 12px;

  .disable {
    opacity: .5;
  }

  &-left {
    width: 32px;
    margin-right: 8px;
    img {
      width: 100%;
      height: 32px;
    }

    &-bar {
      width: 1px;
      background-color: #fff;
      height: 56px;
      margin: 0 auto;
    }
  }

  &-right {
    height: 96px;
    width: 311px;
    box-sizing: border-box;
    border-radius: 8px;
    background-color: #fff;

    &-lesson-info {
      position: relative;
      border-radius: 8px 8px 0 0;
      padding: 8px 8px 0 12px;
      height: 56px;
      box-sizing: border-box;
      background: #FFFFFF;

      .lesson-title {
        display: flex;
        font-size: 15px;
        color: #323232;

        .text {
          flex: 1;
          width: 255px;
          font-weight: bold;
          font-size: 15px;
          color: #323232;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        img {
          margin-left: 12px;
          width: 24px;
          height: 24px;
        }
      }

      .basic-info {
        font-weight: 400;
        font-size: 12px;
        color: #999999;

        :nth-child(2) {
          margin: 0 16px;
        }
      }
    }

    &-package-info {
      display: flex;
      align-items: center;
      box-sizing: border-box;
      height: 40px;
      background: #DBE8F5;
      border: 1px solid #FFFFFF;
      border-radius: 0 0 8px 8px;
      padding: 0 8px 0 6px;

      .title {
        font-weight: 400;
        font-size: 13px;
        color: #455A72;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin: 0 4px 0 8px;
      }

      img {
        border-radius: 4px;
        width: 48px;
        height: 27px;
        background: linear-gradient(90deg, #7EB5FF, #FFDAB7);
      }

      svg {
        font-size: 16px;
      }
    }
  }
}

.week-plan-list {
  :global {
    .adm-list-body {
      background-color: #EEF1F6;
    }
  }
}

.square {
  background-color: #fff;
  width: 5.6px;
  height: 5.6px;
  bottom: -2.9px;
  left: 26px;
  position: absolute;
  transform: rotate(45deg);
}

// 火苗飞入动画
.fly-fire {
  z-index: 1;
  width: 32px;
  height: 32px;
  transform: scale(0);
  position: fixed;
 img {
  width: 100%;
  height: 100%;
 }
}

.fly-start-position {
  left: calc(var(--fire-origin-left));
  top: calc(var(--fire-origin-top));
}

.no-empty-animation {
  animation: fromSmallToLarge,fly-to-targets;
  animation-duration: 0.5s,1s;
  animation-timing-function: linear,ease-in-out;
  animation-delay:0s,0.7s;
  animation-iteration-count: 1,1;
  animation-fill-mode: forwards,forwards;
}

.have-empty-animation {
  animation: fromSmallToLarge,fly-to-targets-have-empty;
  animation-duration: 0.5s,1s;
  animation-timing-function: linear,ease-in-out;
  animation-delay:0s,0.7s;
  animation-iteration-count: 1,1;
  animation-fill-mode: forwards,forwards;
}

  // 初始从小到大动画
@keyframes fromSmallToLarge {
  to {
    transform: scale(1);
  }
}

// 飞入周目标动画 - 已经全部点亮周目标
@keyframes fly-to-targets {
  80% {
    // transform: scale(0.1);
    width: 4vw;
    height: 4.8vw;
    transform: scale(1);
    top: calc(var(--fire-target-top) - 2.4vw);
    left: calc(var(--fire-target-left) - 4vw);
  }

  100% {
    // 全部点亮的情况下，就飞入直接隐藏了
    opacity: 0;
    transform: scale(0);
    top: calc(var(--fire-target-top) - 2.4vw);
    left: calc(var(--fire-target-left) - 4vw);
  }
}

// 飞入周目标动画 - 还没有完全点亮周目标
@keyframes fly-to-targets-have-empty {
  100% {
    top: calc(var(--fire-target-top) - 4.5vw);
    left: calc(var(--fire-target-left) - 4.5vw);
    transform: scale(0);
  }
}

.fly-target-wrapper {
  position: relative;

  .fly-fire-init {
    position: absolute;
    top: 0;
    width: 15px;
    height: 18px;
    opacity: 0;
  }
  .fly-target-fire-img {
    transform-origin: center center;
    animation: fromSmallToOrigin;
    animation-duration: 0.5s;
    animation-delay: 1.5s;
    animation-timing-function: linear;
    animation-iteration-count: 1;
    animation-fill-mode: forwards;
  }

}

@keyframes fromSmallToOrigin {
  0% {
    transform: scale(0);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}


// 数字变更动画

.target-process {
  display: flex;
  flex: 0 0 auto;
  position: relative;
  align-items: baseline;
  margin-right: 12px;
  font-weight: bold;
  font-size: 14px;
  color: #666666;
  .completed-now {
    top: 0;
    position: relative;
    color: #FA8B16;
  }

  .completed-next {
    z-index: 1;
    color: #FA8B16;
    position: absolute;
    opacity: 0;
    top: 10px;
    left: 0px;
  }

  // 进位时候的偏移量
  .next-offset-left {
    transform: translateX(var(--next-number-offset-left));
  }
}

.add-disappear-animation {
  animation: up-disappear;
  animation-duration: 0.5s;
  animation-delay: 1.8s;
  animation-timing-function: linear;
  animation-iteration-count: 1;
  animation-fill-mode: forwards;
}

.add-appear-animation {
  animation: up-appear;
  animation-duration: 0.5s;
  animation-delay: 1.9s;
  animation-timing-function: linear;
  animation-iteration-count: 1;
  animation-fill-mode: forwards;
}

@keyframes up-disappear {
  from {
    opacity: 1;
    top: 0;
  }

  to {
    opacity: 0;
    top: -18px;
  }
}

@keyframes up-appear {

  to {
    opacity: 1;
    top: 0px;
  }
}

// 课程翻转动画
.course-rotate {
  perspective: 800;
  animation: course-TSO-rotate;
  animation-duration: 1s;
  animation-delay: 2.5s;
  animation-timing-function: ease-in-out;
  animation-iteration-count: 1;
  animation-fill-mode: forwards;
  transform-origin: center center
}

@keyframes course-TSO-rotate {
  0% {
    transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg);
  }
  100% {
    opacity: 0;
    transform: rotateX(180deg) rotateY(0deg) rotateZ(0deg);
  }
}

// 下一讲课程动画
.next-lesson-wrapper {
  position: absolute;
  top: -12px;
  left: 0;
}

.next-course-rotate {
  margin-left: 40px;
  opacity: 0;
  animation: next-course-TSO-rotate;
  animation-duration: 1s;
  animation-delay: 2.5s;
  animation-timing-function: ease-in-out;
  animation-iteration-count: 1;
  animation-fill-mode: forwards;
  transform-origin: center center
}

@keyframes next-course-TSO-rotate {
  0% {
    transform: rotateX(180deg) rotateY(0deg) rotateZ(0deg);
  }
  100% {
    opacity: 1;
    transform: rotateX(360deg) rotateY(0deg) rotateZ(0deg);
  }
}
