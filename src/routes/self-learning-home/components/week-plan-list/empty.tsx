/** 列表空态 */
import * as React from "react";

import Empty from "@/components/empty-status";
import noContentPNG from "@/assets/common/no-content.png";
import addPNG from "@/assets/common/add.png";
import errorPNG from "@/assets/common/error.png";
import { clickPv } from "@/utils/tool";

import {
  EListStatus,
  listBtnText,
  listName,
  listStatusText,
  EListType,
} from "../../common";

import Style from "./style.module.scss";

interface IEmpty {
  /** 是否展示添加内容按钮 */
  showAddButton?: boolean;
  listStatus: EListStatus;
  nav: (path: string) => void;
  getWeekList: (init?: boolean) => Promise<any>;
}

export const ListEmpty: React.FC<IEmpty> = (props) => {
  const { listStatus, nav, getWeekList, showAddButton = false } = props;
  return (
    <div className={Style["empty"]}>
      <Empty
        image={listStatus === EListStatus.error ? errorPNG : noContentPNG}
        text={
          showAddButton
            ? listStatusText[listStatus]
            : listStatusText[EListStatus.offline]
        }
        className={listStatus === EListStatus.error ? "empty-error-img" : null}
        buttonOption={{
          // 1.2迭代全屏的新手指引需求添加，参照full-screen-guid
          id: listStatus !== EListStatus.error ? "add-course-for-guid" : "",
          showIcon: listStatus !== EListStatus.error,
          icon: addPNG,
          className:
            listStatus === EListStatus.error ? Style["error-btn"] : null,
          // 如果下线了就不显示按钮了
          text: showAddButton ? listBtnText[listStatus] : "",
          handleClick: () => {
            if (listStatus === EListStatus.error) {
              getWeekList(true);
            } else {
              /** 埋点 */
              clickPv(
                "ewt_h5_study_course_self_learning_home_add_content_click",
                {
                  tab: listName[EListType.weekList],
                },
              );
              nav("/self-learning/recommend");
            }
          },
        }}
      >
        {EListStatus.done === listStatus ? (
          <div
            className={Style["have-completed-list"]}
            onClick={() => {
              nav("/self-learning/completed-list");
            }}
          >
            查看本周已完成的任务
          </div>
        ) : null}
      </Empty>
    </div>
  );
};
