/** 周目标 */
import * as React from "react";

import { IconSvg } from "@/components";
import { IPlanDetailRes } from "@/service/home";
import finishIcon from "@/assets/common/icon-finish-icon.png";
import unFinishIcon from "@/assets/common/icon-un-finish-fire.png";
import { cls } from "@/utils/tool";

import { ITargetProcess, TargetProcess } from "./target-process";
import { flyTargetFireId, flyTargetWrapperId } from "../../common";

import Style from "./style.module.scss";

interface IWeekTarget extends Partial<ITargetProcess> {
  haveCompleted: number;
  planDetail: IPlanDetailRes;
  editWeekTarget?: () => void;
  addCount: number;
}

export const WeekTarget: React.FC<IWeekTarget> = (props) => {
  const { planDetail, haveCompleted, showAnimation, editWeekTarget, addCount } =
    props;
  return (
    // id和新手指引的流程相关联，不要轻易删除，在1.2迭代时添加，full-screen-guid
    <div className={Style["week-target"]} id="week-target-box">
      <div className={Style["week-target-desc"]}>本周目标</div>
      {/* 火苗容器，id 在飞入动画的时候有用 */}
      <div id={flyTargetWrapperId} className={Style["img-wrapper"]}>
        <img src={finishIcon} id={flyTargetFireId} />
      </div>
      <TargetProcess
        planNumber={planDetail?.userPlanCount}
        completedNumber={haveCompleted}
        showAnimation={!!showAnimation}
        addCount={addCount}
      />
      <IconSvg
        className={Style["edit"]}
        name="icon-bianji"
        onClick={editWeekTarget}
      />
    </div>
  );
};
