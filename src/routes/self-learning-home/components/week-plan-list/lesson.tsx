/** 课程讲组件 */

import * as React from "react";
import { Toast } from "antd-mobile";

import UnFinishFirePNG from "@/assets/common/un-finish-fire.png";
import FinishPNG from "@/assets/common/fly-fire.png";
import playPNG from "@/assets/common/play.png";
import WatchedFireImg from "@/assets/common/watched-fire.png";
import type { ICourseItem } from "@/service/home";
import { IconSvg } from "@/components";
import { clickPv, cls, openRoute } from "@/utils/tool";

import Style from "./style.module.scss";
import moment from "moment";

interface ILesson {
  className?: string;
  style?: React.CSSProperties;
  /** 是否是最后一个课程讲 */
  isLast?: boolean;
  item: ICourseItem;
  /** 是否点亮火苗，不传或者false都是灰色，true是点亮 */
  lightFire?: boolean;
  watchedFire?: boolean;
  /** 火苗图片的样式 */
  fireClassName?: string;
  /** 课程讲禁用 */
  disableLesson?: boolean;
  /** 课程包禁用 */
  disableCourse?: boolean;
  /** 右侧包翻转 className */
  rotateRightLessonClassName?: string;
  hideFireLeft?: boolean;
  /** 展示包详情底抽 */
  showPackagePop?: (item: ICourseItem) => void;
  /** 手动完成课程 */
  handleCompleteLesson?: (item: ICourseItem, target?: HTMLImageElement) => void;
  /** 埋点事件名称 */
  clickLessonQTKey?: string;
  /** 点击看课的回调 */
  playVideoCB?: () => void;
}

export const Lesson: React.FC<ILesson> = (props) => {
  const {
    item,
    isLast,
    showPackagePop,
    handleCompleteLesson,
    style,
    lightFire,
    watchedFire,
    fireClassName,
    disableLesson = false,
    disableCourse = false,
    className,
    rotateRightLessonClassName,
    hideFireLeft,
    clickLessonQTKey,
    playVideoCB,
  } = props;

  function playVideo() {
    try {
      /** 埋点 */
      clickLessonQTKey &&
        clickPv(clickLessonQTKey, { lessonId: item.lessonInfo.lessonId });
      if (disableLesson) {
        Toast.show("该视频已下架");
        return;
      }
      playVideoCB && playVideoCB();
      openRoute({
        domain: "course",
        action: "open_detail",
        params: {
          id: `${item.courseId}`,
          lessionID: `${item.lessonInfo.lessonId}`,
        },
      });
    } catch (err) {
      console.error(err);
    }
  }

  return (
    <>
      <div className={cls([Style["lesson-item"], className])} style={style}>
        {/* 左边火焰区 */}
        {!hideFireLeft && (
          <div className={Style["lesson-item-left"]}>
            <img
              className={cls([fireClassName])}
              onClick={(event) => {
                handleCompleteLesson(item, event.target as HTMLImageElement);
              }}
              src={
                lightFire
                  ? FinishPNG
                  : watchedFire
                    ? WatchedFireImg
                    : UnFinishFirePNG
              }
            />
            {!isLast ? <div className={Style["lesson-item-left-bar"]} /> : null}
          </div>
        )}
        {/* 右边讲信息区 */}
        <div
          className={cls([
            Style["lesson-item-right"],
            rotateRightLessonClassName,
          ])}
          // 这里的规则是1.2迭代定的，为了拿到第一讲的信息和位置，和全屏的新手指引关联
          id={`week-plan-lesson-${item?.courseId}-${item?.lessonInfo?.lessonId}`}
        >
          {/* 讲信息 */}
          <div
            className={cls([
              Style["lesson-item-right-lesson-info"],
              disableLesson && Style["disable"],
            ])}
            onClick={playVideo}
          >
            <div className={Style["lesson-title"]}>
              <div className={Style["text"]}>
                {item?.lessonInfo?.lessonName}
              </div>
              <img src={playPNG} />
            </div>
            <div className={Style["basic-info"]}>
              <span>{item?.lessonInfo?.durationDisplay ?? "-"}</span>
              <span>{item?.subjectName}</span>
              {item?.lessonInfo?.watched ? (
                <span style={{ color: "#333" }}>看过</span>
              ) : null}
              {item?.lessonInfo?.finishTime && (
                <span>
                  {moment(item.lessonInfo.finishTime).format("YYYY年MM月DD日")}
                  完成
                </span>
              )}
            </div>
            <div className={Style["square"]} />
          </div>
          {/* 包信息 */}
          <div
            className={cls([
              Style["lesson-item-right-package-info"],
              disableCourse && Style["disable"],
            ])}
            onClick={() => showPackagePop(item)}
          >
            <img src={item?.coverImgUrl} />
            <span className={Style["title"]}>{item?.courseName}</span>
            <IconSvg name="icon-jinru" />
          </div>
        </div>
      </div>
    </>
  );
};
