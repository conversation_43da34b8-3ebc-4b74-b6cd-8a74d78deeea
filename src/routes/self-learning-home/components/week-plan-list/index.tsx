/** 本周计划列表 */

import * as React from "react";
import { To<PERSON>, Mask } from "antd-mobile";
import { useLocation, useNavigate } from "react-router-dom";

import { useVisibilitychange } from "@/hooks";
import { useFireAnimation } from "@/routes/self-learning-home/hooks";
import {
  List,
  RowLoading,
  CourseNotCompletePopup,
  SignCompleteLessonPopup,
} from "@/components";
import flyFirePNG from "@/assets/common/fly-fire.png";

import {
  getWeekList as getWeekListApi,
  getNextLesson,
  IGetWeekListReq,
  ICourseItem,
} from "@/service/home";
import { Context } from "@/routes/self-learning-home/context";
import { LayoutContext } from "@/components/layout/layout-context";
import { clickPv, cls, createURLByType } from "@/utils/tool";
import SafeLogger from "@/utils/safe-logger";

import { EListStatus, EPopupType, sourceFrom } from "../../common";
import { WeekTarget } from "./week-target";
import { Lesson } from "./lesson";
import { ListEmpty } from "./empty";
import { AddContent } from "./add-content";

import Style from "./style.module.scss";
import { ESourceFromForText } from "@/typing.d";
interface IWeekPlanList {
  /** 是否展示添加内容按钮 */
  showAddButton?: boolean;
  editWeekTarget: () => void;
  onBatchFinishCallBack: () => void;
}

export const WeekPlanList: React.FC<IWeekPlanList> = (props) => {
  const {
    editWeekTarget,
    onBatchFinishCallBack,
    showAddButton = false,
  } = props;
  const location = useLocation();
  const nav = useNavigate();
  /** 跳转到其他页面 */
  function toOtherPage(path: string, addQueryObject?: Record<string, any>) {
    nav(
      createURLByType({
        path,
        originSearch: location.search,
        addQueryObject: {
          from: "home",
          ...(addQueryObject || {}),
        },
      }),
    );
  }
  const [noFinishPopupVis, setNoFinishPopupVis] = React.useState(false);
  // 没有看课popup提示框上报qt时所需课程信息
  const [noWatchedCourse, setNoWatchedCourse] =
    React.useState<ICourseItem>(null);
  const { showPageLoading } = React.useContext(LayoutContext) || {};
  const pageRef = React.useRef<IGetWeekListReq>({
    pageIndex: 1,
    // 后端接口做了限制，每页最后10条
    pageSize: 10,
  });
  const [completeCount, setCompleteCount] = React.useState(0);
  const { planDetail, popupsRef, refreshWeekList } = React.useContext(Context);
  /** 获取本周计划列表 */
  const [data, setData] = React.useState<ICourseItem[]>();
  /** 是否还有更多 */
  const [hasMore, setHasMore] = React.useState<boolean>(true);
  /** 已经完成的周计划数 */
  const [haveCompleted, setHaveCompleted] = React.useState<number>(0);
  /** 列表状态 */
  const [listStatus, setListStatus] = React.useState<EListStatus>();
  /** 获取周计划列表 */
  const [loading, setLoading] = React.useState<boolean>();
  async function getWeekList(init = false) {
    try {
      setLoading(init);
      pageRef.current.pageIndex = init ? 1 : pageRef.current.pageIndex + 1;
      const { data } = await getWeekListApi({
        ...pageRef.current,
        ignoreError: true,
      } as IGetWeekListReq);
      const lessonList = data.courseLessonInfoList.data || [];
      setHaveCompleted(data.weekCompleteCount ?? 0);
      setCompleteCount(0);
      // 已经加载完毕，没有下一页了
      if (!data.courseLessonInfoList.haveNextPage) {
        setHasMore(false);
      }
      /** 用户已经学完周计划 */
      if (data.weekCompleteCount && !data.courseLessonInfoList.total) {
        setData([]);
        setListStatus(EListStatus.done);
        // 还未加入过计划
      } else if (!data.weekCompleteCount && !data.courseLessonInfoList.total) {
        setData([]);
        setListStatus(EListStatus.empty);
      } else {
        setData((pre) => (init ? lessonList : [...(pre || []), ...lessonList]));
      }
    } catch (error) {
      setListStatus(EListStatus.error);
      setHasMore(false);
      console.error(error);
      SafeLogger.baseLogger.error("get-weekPlanList-failed", { error });
    } finally {
      setLoading(false);
    }
  }
  /** 初始进来 先获取一次 */
  React.useEffect(() => {
    getWeekList(true);
  }, [refreshWeekList]);
  /** TODO: 优化逻辑 播放器关闭后的回调，先用ref加个标记 */
  const afterPlayVideoRef = React.useRef<boolean>(false);
  function closeVideo(isClose: boolean) {
    if (isClose && afterPlayVideoRef.current) {
      afterPlayVideoRef.current = false;
      // 如果满足自动打卡的条件，就会自动打卡
      popupsRef.current.setPopupList?.([
        EPopupType.checkIn,
        EPopupType.milestoneComplete,
      ]);
      getWeekList(true);
    }
  }
  useVisibilitychange({
    handleVisibilitychange: closeVideo,
  });

  /** 手动完成课程 */
  const [completePopVisible, setCompletePopVisible] = React.useState<boolean>();
  function changeCompletePopVisible() {
    setCompletePopVisible((pre) => !pre);
  }
  const completeLessonRef = React.useRef<ICourseItem>();

  async function handleCompleteLesson(completeNum: number[]) {
    try {
      setCompleteCount(completeNum?.length);
      setMaskLoading(true);
      /** 埋点 */
      clickPv(
        "ewt_h5_study_course_self_learning_home_complete_lesson_confirm_in_week_list_click",
        { lessonId: completeLessonRef.current.lessonInfo.lessonId },
      );
      showPageLoading?.(true);
      // 完成后，走一次周达标 -> 包完成 弹窗调度
      if (data) {
        // 获取下一讲的信息
        const { data: nextLesson } = await getNextLesson({
          courseId: completeLessonRef.current.courseId,
        }).catch((error) => {
          console.error(error);
          setMaskLoading(false);
          return { data: null };
        });
        // 关闭弹窗
        changeCompletePopVisible();
        // 动画开始
        recordInitPosition();
        // 如果还有下一讲信息，就需要播放翻转动画
        if (nextLesson?.lessonInfo?.lessonId) {
          setNextLessonInfo(nextLesson);
        }
        // TODO: 会有偏差
        // 利用定时器，等动画播放完后，在触发刷新
        setTimeout(
          () => {
            // 重新获取周计划列表，并重置动画相关字段
            getWeekList(true).finally(() => {
              // 动画蒙层关闭
              setMaskLoading(false);
              // 动画相关值重置
              setNextLessonInfo(null);
              setStyle(null);
              completeLessonRef.current = null;
            });
            // 可能触发周目标完成、课程包完成弹窗
            popupsRef.current?.setPopupList([
              EPopupType.weekComplete,
              EPopupType.courseComplete,
            ]);
            // 如果没有翻转动画，定时器缩减到2.5秒
            onBatchFinishCallBack();
          },
          nextLesson?.lessonInfo?.lessonId ? 3600 : 2500,
        );
      }
    } catch (error) {
      setMaskLoading(false);
      console.error(error);
      SafeLogger.baseLogger.error("complete-lesson-failed", { error });
    } finally {
      showPageLoading?.(false);
    }
  }
  /** 火苗动画 */
  const {
    style: animationStartStyle,
    haveCompletedWeekTargetRef,
    nextLessonInfo,
    targetRef,
    maskLoading,
    setMaskLoading,
    setStyle,
    setNextLessonInfo,
    recordInitPosition,
  } = useFireAnimation();
  return (
    <>
      <WeekTarget
        planDetail={planDetail}
        haveCompleted={haveCompleted}
        showAnimation={!!animationStartStyle}
        editWeekTarget={editWeekTarget}
        addCount={completeCount}
      />
      {data?.length ? (
        <>
          <AddContent nav={toOtherPage} showAddButton={showAddButton} />
          <List
            className={Style["week-plan-list"]}
            scrollClassName={Style["week-plan-list-scroll"]}
            getKey={(item) => `${item.courseId}-${item?.lessonInfo?.lessonId}`}
            renderItem={(item, isLast) => {
              const beCompleted =
                // 前面火苗动画的信息
                animationStartStyle &&
                // 下一讲的信息
                nextLessonInfo &&
                // 对指定完成的讲做翻转动画
                completeLessonRef.current &&
                completeLessonRef.current.courseId === item.courseId &&
                completeLessonRef.current.lessonInfo?.lessonId ===
                  item.lessonInfo.lessonId;
              return (
                <div style={beCompleted ? { position: "relative" } : null}>
                  <Lesson
                    isLast={isLast}
                    item={item}
                    watchedFire={item.lessonInfo?.watched}
                    rotateRightLessonClassName={
                      beCompleted && Style["course-rotate"]
                    }
                    clickLessonQTKey="ewt_h5_study_course_self_learning_home_lesson_in_week_list_click"
                    showPackagePop={(item) => {
                      /** 埋点 */
                      clickPv(
                        "ewt_h5_study_course_self_learning_home_course_in_week_list_click",
                        { courseId: item.courseId },
                      );
                      /** 跳转到计划详情页面 */
                      toOtherPage("/self-learning/plan-detail", {
                        courseId: item.courseId,
                      });
                    }}
                    /** 完成课程 */
                    handleCompleteLesson={(courseItem, target) => {
                      /** 埋点 */
                      clickPv(
                        "ewt_h5_study_course_self_learning_home_complete_lesson_in_week_list_click",
                        { lessonId: courseItem.lessonInfo.lessonId },
                      );
                      if (!courseItem.lessonInfo.watched) {
                        setNoWatchedCourse(courseItem);
                        setNoFinishPopupVis(true);
                        return;
                      }
                      // 存储动画起点的元素，从而获取位置信息
                      targetRef.current = target;
                      completeLessonRef.current = courseItem;
                      changeCompletePopVisible();
                    }}
                    playVideoCB={() => {
                      afterPlayVideoRef.current = true;
                    }}
                  />
                  {beCompleted && (
                    <Lesson
                      className={cls([Style["next-lesson-wrapper"]])}
                      rotateRightLessonClassName={Style["next-course-rotate"]}
                      hideFireLeft
                      item={nextLessonInfo}
                    />
                  )}
                </div>
              );
            }}
            data={data}
            hasMore={hasMore}
            loadMore={getWeekList}
          >
            {(loadMore: boolean) => {
              if (loadMore) {
                return <RowLoading />;
              }
              return (
                <div className={Style["no-more-lessons"]}>
                  已经到底了
                  <span
                    className={Style["completed-lessons"]}
                    onClick={() => toOtherPage("/self-learning/completed-list")}
                  >
                    查看本周已完成的任务
                  </span>
                </div>
              );
            }}
          </List>
        </>
      ) : loading ? (
        <RowLoading />
      ) : (
        <ListEmpty
          showAddButton={showAddButton}
          listStatus={listStatus}
          getWeekList={getWeekList}
          nav={toOtherPage}
        />
      )}

      <CourseNotCompletePopup
        visible={noFinishPopupVis}
        onCancel={() => setNoFinishPopupVis(false)}
        onOk={() => {
          setNoFinishPopupVis(false);
          clickPv(
            "ewt_h5_study_course_self_learning_no_watched_popup_ok_button_click",
            {
              sourceFrom: "自习计划首页",
              courseId: noWatchedCourse.courseId,
              lessonId: noWatchedCourse.lessonInfo.lessonId,
            },
          );
        }}
      />

      {/* 手动完成课程 */}
      <SignCompleteLessonPopup
        sourceFrom={ESourceFromForText.home}
        courseId={completeLessonRef.current?.courseId}
        lessonId={completeLessonRef.current?.lessonInfo?.lessonId}
        visible={completePopVisible}
        onCancel={changeCompletePopVisible}
        onConfirm={handleCompleteLesson}
      />
      {/* 火苗飞入动画 */}
      <div
        className={cls([
          Style["fly-fire"],
          animationStartStyle && Style["fly-start-position"],
          animationStartStyle &&
            haveCompletedWeekTargetRef.current &&
            Style["no-empty-animation"],
          animationStartStyle &&
            !haveCompletedWeekTargetRef.current &&
            Style["have-empty-animation"],
        ])}
        id="week-plan-list-completeLesson-fire-byHand"
        style={
          // 通过css变量注入位置信息
          animationStartStyle
            ? ({
                "--fire-target-top": animationStartStyle.toTop,
                "--fire-target-left": animationStartStyle.toLeft,
                "--fire-origin-left": animationStartStyle.left,
                "--fire-origin-top": animationStartStyle.top,
              } as React.CSSProperties)
            : { display: "none" }
        }
      >
        <img src={flyFirePNG} />
      </div>
      {/* 动画结束之前，不能进行操作 */}
      <Mask disableBodyScroll visible={maskLoading} style={{ opacity: 0 }} />
    </>
  );
};
