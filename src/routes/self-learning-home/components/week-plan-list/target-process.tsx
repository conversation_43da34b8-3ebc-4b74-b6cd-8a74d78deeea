/** 周目标进度 */

import { cls } from "@/utils/tool";

import * as React from "react";

import Style from "./style.module.scss";

export interface ITargetProcess {
  /** 周计划数 */
  planNumber: number;
  /** 周完成数 */
  completedNumber: number;
  className?: string;
  style?: React.CSSProperties;
  showAnimation?: boolean;
  addCount: number;
}

export const TargetProcess: React.FC<ITargetProcess> = (props) => {
  const { planNumber, completedNumber, showAnimation, className, addCount } =
    props;
  return (
    <div className={cls([Style["target-process"], className])}>
      <div
        className={cls([
          Style["completed-now"],
          showAnimation && Style["add-disappear-animation"],
        ])}
      >
        {completedNumber ?? "-"}
      </div>
      {Number.isInteger(completedNumber) && (
        <div
          className={cls([
            Style["completed-next"],
            showAnimation && Style["add-appear-animation"],
            // 数字进位时候产生的偏移量，比如从 9 到 10，防止和右边文案重合
            completedNumber.toString().length <
              (completedNumber + addCount).toString().length &&
              Style["next-offset-left"],
          ])}
          // 偏移一个数据的宽度
          style={
            {
              "--next-number-offset-left": `-${100 / (completedNumber + addCount).toString().length}%`,
            } as React.CSSProperties
          }
        >
          {completedNumber + addCount}
        </div>
      )}
      <div className={Style["plan"]}>/{planNumber ?? "-"}</div>
    </div>
  );
};
