/** 顶部添加内容模块 */
import * as React from "react";

import unFinishFirePNG from "@/assets/common/un-finish-fire.png";
import { clickPv } from "@/utils/tool";

import Style from "./style.module.scss";

interface IAddContent {
  /** 是否展示添加内容按钮 */
  showAddButton?: boolean;
  nav: (path: string) => void;
}

export const AddContent = React.memo(function AddContent(props: IAddContent) {
  const { nav, showAddButton = false } = props;
  return (
    <div className={Style["list-use-desc"]}>
      <span style={{ display: "flex", alignItems: "center" }}>
        点击下方
        <img src={unFinishFirePNG} />
        将任务标记为已完成
      </span>
      {showAddButton ? (
        <div>
          <span>内容不够？</span>
          <span
            style={{ color: "#2D86FE" }}
            onClick={() => {
              /** 埋点 */
              clickPv(
                "ewt_h5_study_course_self_learning_home_add_content_in_week_list_click",
              );
              nav("/self-learning/recommend");
            }}
          >
            添加内容
          </span>
        </div>
      ) : null}
    </div>
  );
});
