/** 新手引导 */

import * as React from "react";

import {
  Popup,
  Button,
  IconSvg,
  <PERSON>er,
  Spin,
  EButtonType,
} from "@/components";
import type { IPopup } from "@/components";
import { LayoutContext } from "@/components/layout/layout-context";
import {
  NEXTSTEP,
  FINISH,
  EStepType,
  ESourceFrom,
  sourceFrom,
} from "@/routes/self-learning-home/common";
import { clickPv, expPv } from "@/utils/tool";

import { useSteps, useStepAction } from "./hook";
import { StepTips } from "./step-tips";
import { Context } from "../../context";
import { NewcomerGuidanceContext } from "./context";

import Style from "./style.module.scss";

interface INewComerGuidance extends IPopup {}

export const NewComerGuidance: React.FC<INewComerGuidance> = (props) => {
  const { visible, onClose } = props;
  const homeContext = React.useContext(Context);
  const { safeAreaTopHeight } = React.useContext(LayoutContext) || {};
  const [nextBtnVis, changeNextBtnVis] = React.useState<boolean>(true);
  const { nowStep, steps, toNextStep } = useSteps({ visible, onClose });
  const { Comp, loading, handleNextStep } = useStepAction({
    planDetail: homeContext?.planDetail,
    nowStep,
    toNextStep,
  });

  /** 曝光埋点 */
  React.useEffect(() => {
    if (nowStep === EStepType.setFlag) {
      expPv("ewt_h5_study_course_self_learning_home_flag_list_expo", {
        sourceFrom: sourceFrom[ESourceFrom.newcomerGuidance],
      });
    }
  }, [nowStep]);

  return (
    <Popup
      hiddenHeader
      bodyClassName={Style["newcomer-guidance"]}
      bodyStyle={{ paddingTop: safeAreaTopHeight }}
      visible={visible}
      {...props}
    >
      <NewcomerGuidanceContext.Provider value={{ changeNextBtnVis }}>
        <Header
          back={<IconSvg name="icon-guanbichouti" />}
          onBack={onClose}
          className={Style["header"]}
        >
          开启自习计划
        </Header>
        <div
          style={{
            maxHeight: "calc(100% - 12.8vw - 20.27vw)",
            height: "calc(100% - 12.8vw - 20.27vw)",
            minHeight: "30vw",
            overflow: nowStep === EStepType.addPlans ? "auto" : "hidden",
          }}
        >
          {/* 步骤说明 */}
          <StepTips nowStep={nowStep} />
          {/* 每一个步骤要渲染的组件 */}
          {Comp}
        </div>
        {nextBtnVis ? (
          <div className={Style["footer"]}>
            {/* 新手引导就两步，第二步直接开始显示跳过，效果等同于结束 */}
            {!steps?.length && (
              <Button
                className={Style["cancel"]}
                type={EButtonType.grey}
                text="跳过"
                onClick={() => {
                  clickPv(
                    "ewt_h5_study_course_self_learning_home_guidance_skip_button_click",
                  );
                  handleNextStep();
                }}
              />
            )}
            <Button
              className={Style["button"]}
              text={steps?.length ? NEXTSTEP : FINISH}
              onClick={handleNextStep}
            />
          </div>
        ) : null}
      </NewcomerGuidanceContext.Provider>
      <Spin visible={loading} />
    </Popup>
  );
};
