/** 每一步引導提示 */

import * as React from "react";

import { EStepType, stepsContent } from "@/routes/self-learning-home/common";

import Style from "./style.module.scss";

interface IStepTips {
  nowStep: EStepType;
}

export const StepTips: React.FC<IStepTips> = (props) => {
  const { nowStep } = props;
  const content = stepsContent[nowStep];
  return (
    <div className={Style["step"]}>
      <div className={Style["title"]}>
        <img src={content?.img} />
        <div>{content?.title}</div>
      </div>
      <div className={Style["content"]}>{content?.content}</div>
      <div className={Style["tips"]}>{content?.tip}</div>
    </div>
  );
};
