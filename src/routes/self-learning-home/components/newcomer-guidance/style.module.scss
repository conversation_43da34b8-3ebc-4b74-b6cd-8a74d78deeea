.newcomer-guidance {
  height: 100vh;
  max-height: 100vh;
  border-radius: 0;

  .header {
    :global {
      .adm-nav-bar-title {
        color: #2A333A;
      }
      .adm-nav-bar-back {
        font-size: 22px;
      }
    }
  }

  .step {
    text-align: center;
    padding-top: 20px;
    padding-bottom: 20px;
    height: 160px;
    box-sizing: border-box;

    .title {
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 55px;
      font-weight: bold;
      font-size: 24px;
      color: #2A333A;

      img {
        margin-right: 5px;
        width: 48px;
        height: 55px;
      }
    }

    .content {
      height: 22px;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      margin-bottom: 4px;

      img {
        margin: 0 5px;
        width: 12px;
        height: 15px;
      }
    }

    .tips {
      height: 22px;
      font-weight: 400;
      font-size: 14px;
      color: #F96D05;
    }
  }

  .footer {
    display: flex;
    bottom: 0px;
    width: 100vw;
    background: #FFFFFF;
    height: 76px;
    align-items: center;
    justify-content: center;
    position: fixed;
    .cancel {
      width: 84px;
      margin-right: 15px;
    }
    .button {
      width: 243px;
    }
  }
}


.flag-list {
  width: 100vw;
  overflow: auto;
  min-height: 40px;
  height: calc(100% - 160px);
  max-height: calc(100% - 160px);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.week-target-container {
  overflow: auto;
  height: calc(100% - 160px - 36px) !important;
  max-height: calc(100% - 160px - 36px);
}
