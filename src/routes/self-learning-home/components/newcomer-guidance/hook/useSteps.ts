/** 負責調度步驟 */

import * as React from "react";

import { EStepType } from "@/routes/self-learning-home/common";

interface IUseSteps {
  onClose?: () => void;
  visible?: boolean;
}

export function useSteps(props: IUseSteps) {
  const { visible, onClose } = props;
  const [nowStep, setNowStep] = React.useState<EStepType>();
  const [steps, setSteps] = React.useState<EStepType[]>([
    EStepType.setFlag,
    EStepType.addPlans,
  ]);

  function toNextStep() {
    if (!steps?.length) {
      onClose?.();
      return;
    }
    setSteps((pre) => {
      setNowStep(pre[0]);
      return pre.slice(1);
    });
  }
  React.useEffect(() => {
    if (!visible) {
      return;
    }
    // 調度開始
    toNextStep();
  }, [visible]);
  return { nowStep, steps, toNextStep };
}
