/** 管理每一步要渲染的组件和点击下一步后的行为 */

import * as React from "react";

import {
  FlagList,
  WeekCompleteTarget,
} from "@/routes/self-learning-home/components";
import { updateWeekPlanCount, updateFlag } from "@/service/home";
import type {
  IPlanDetailRes,
  IUpdateFlagReq,
  IUpdateWeekPlanCountReq,
} from "@/service/home";
import {
  EStepType,
  ESourceFrom,
  sourceFrom,
} from "@/routes/self-learning-home/common";
import RecommendContent from "@/components/recommend-content";
import { clickPv } from "@/utils/tool";
import { ERecommendContentSourceFrom } from "@/typing.d";
import SafeLogger from "@/utils/safe-logger";

import Style from "../style.module.scss";

interface IUseStepAction {
  planDetail?: IPlanDetailRes;
  nowStep: EStepType;
  toNextStep: () => void;
}
export function useStepAction(props: IUseStepAction) {
  /** 埋点 */
  function buriedPoint(v: any) {
    /** 设置 flag */
    if (nowStep === EStepType.setFlag) {
      clickPv("ewt_h5_study_course_self_learning_home_set_flag_click", {
        sourceForm: sourceFrom[ESourceFrom.newcomerGuidance],
        flagId: v?.flagId,
        flagContent: v?.flagContent,
      });
    }
  }
  const { planDetail, nowStep, toNextStep } = props;
  const [loading, setLoading] = React.useState<boolean>(false);
  // 选中的 flag 或周目标数
  const [value, setValue] = React.useState<
    IUpdateFlagReq | IUpdateWeekPlanCountReq
  >();
  // 下一步、初始化 flag 共用，通过参数区分
  async function handleNextStep(props?: {
    data: typeof value;
    keepNowStep: boolean;
  }) {
    try {
      setLoading(true);
      buriedPoint(props?.data || value);
      let api;
      switch (nowStep) {
        case EStepType.setFlag:
          api = updateFlag;
          break;
      }
      if (api) {
        await api(props?.data || value);
      }
      if (!props?.keepNowStep) {
        toNextStep();
      }
    } catch (error) {
      console.error(error);
      SafeLogger.baseLogger.error("newcomer-guidance-error", { error });
    } finally {
      setLoading(false);
    }
  }
  // 获取每一步要渲染的组件
  const [Comp, setComp] = React.useState<React.ReactNode>(<></>);

  function getStepComp(nowStep: EStepType, value) {
    switch (nowStep) {
      case EStepType.setFlag:
        return (
          <FlagList
            value={{ id: value?.flagId }}
            className={Style["flag-list"]}
            // 模拟一次下一次，为了设置 flag 默认值，但是不真的走到下一步去
            setInitFlag={(item) => {
              setValue({
                flagContent: item.flag,
                flagId: item.id,
              });
              handleNextStep({
                keepNowStep: true,
                data: {
                  flagContent: item.flag,
                  flagId: item.id,
                },
              });
            }}
            onSelected={(item) =>
              setValue({
                flagContent: item.flag,
                flagId: item.id,
              })
            }
          />
        );
      case EStepType.addPlans:
        return (
          <RecommendContent
            sourceFrom={ERecommendContentSourceFrom.guid}
            emptyHeight="50vh"
            networkErrorHeight="60vh"
          />
        );
      default:
        throw Error("新手引导下一步出错，未匹配到相应组件");
    }
  }
  React.useEffect(() => {
    if (!nowStep) {
      return;
    }
    const comp = getStepComp(nowStep, value);
    setComp(comp);
  }, [nowStep, value, planDetail]);

  return { value, loading, Comp, handleNextStep };
}
