/** 单条 flag 组件 */

import * as React from "react";

import { IFlagItem } from "@/service/home";
import beSelectedIcon from "@/assets/common/be-choosed.png";
import { cls } from "@/utils/tool";

import Style from "./style.module.scss";

export interface IFlagItemComp {
  beSelected: boolean;
  item: IFlagItem;
  onSelected: (item: IFlagItem) => void;
}

const FlagItem = React.memo(function FlagItem(props: IFlagItemComp) {
  const { item, beSelected, onSelected } = props;
  return (
    <div
      className={cls([
        Style["flag-item"],
        beSelected && Style["beSelected-item"],
      ])}
    >
      <div onClick={() => onSelected(item)}>{item.flag}</div>
      {beSelected ? (
        <img className={Style["beSelected-icon"]} src={beSelectedIcon} />
      ) : null}
    </div>
  );
});

export default FlagItem;
