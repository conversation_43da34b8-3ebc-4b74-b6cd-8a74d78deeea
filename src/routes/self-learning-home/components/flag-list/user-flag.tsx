/** 用户已经设置的 flag */

import * as React from "react";

import { IconSvg } from "@/components";
import { cls } from "@/utils/tool";

import Style from "./style.module.scss";

interface IUserFlag {
  className?: string;
  allowEdit?: boolean;
  content: string;
  setFlag: () => void;
}
const UserFlag: React.FC<IUserFlag> = (props) => {
  const { allowEdit, content, className, setFlag } = props;
  return (
    <div className={cls([Style["user-flag"], className])}>
      <IconSvg name="icon-wodeFlag" color="#FF4D4F" />
      <div className={Style["common-word"]}>
        <div>我的Flag</div>
        <span className={Style["flag"]}>{content || "-"}</span>
      </div>
      {allowEdit ? (
        <div className={Style["edit"]} onClick={setFlag}>
          <IconSvg color="#FF4D4F" name="icon-bianji" />
        </div>
      ) : null}
    </div>
  );
};

export default UserFlag;
