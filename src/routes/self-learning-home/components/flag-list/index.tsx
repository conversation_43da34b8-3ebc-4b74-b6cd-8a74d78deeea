/** flag 列表 */

import * as React from "react";

import type { IFlagItem } from "@/service/home";
import { RowLoading } from "@/components";
import EmptyStatus from "@/components/empty-status";
import errorPNG from "@/assets/common/error.png";

import { NewcomerGuidanceContext } from "../newcomer-guidance/context";
import FlagItem from "./flag-item";
import UserFlag from "./user-flag";
import { useFlagList } from "../../hooks";

import Style from "./style.module.scss";

export interface IFlagList {
  className?: string;
  style?: Record<string, any>;
  // 是否展示已经选中的 flag
  showValue?: boolean;
  // 初始值
  value?: IFlagItem;
  // 选中后的回调
  onSelected: (item: IFlagItem) => void;
  // 是否要设置初始值，新手引导设置 flag的时候 会触发一次设置默认值 flag，当做已经看过新手引导的依据
  setInitFlag?: (item: IFlagItem) => void;
  // 更改 flag，用户首页修改
  setFlag?: () => void;
}
export const FlagList: React.FC<IFlagList> = (props) => {
  const {
    className,
    value,
    showValue,
    style = {},
    setFlag,
    onSelected,
    setInitFlag,
  } = props;

  const { flagList, loading, isError, getFlags } = useFlagList({ setInitFlag });

  /** 接口未响应前 或 获取列表失败后，需要隐藏下一步按钮 */
  const { changeNextBtnVis } = React.useContext(NewcomerGuidanceContext);
  React.useEffect(() => {
    if (changeNextBtnVis) {
      changeNextBtnVis(!loading && !isError);
    }
  }, [isError, loading]);

  return (
    <div className={className} style={style}>
      {loading ? (
        <RowLoading />
      ) : isError ? (
        <EmptyStatus
          text="糟糕，遇到了点问题，点击“重新加载”试试"
          image={errorPNG}
          className="empty-error-img"
          buttonOption={{
            text: "重新加载",
            handleClick: getFlags,
            className: Style["error-btn"],
          }}
        />
      ) : (
        <>
          {showValue ? (
            <UserFlag content={value?.flag} setFlag={setFlag} />
          ) : null}
          {(flagList || []).map((item) => (
            <FlagItem
              key={item.id}
              beSelected={value?.id === item.id}
              item={item}
              onSelected={onSelected}
            />
          ))}
        </>
      )}
    </div>
  );
};
