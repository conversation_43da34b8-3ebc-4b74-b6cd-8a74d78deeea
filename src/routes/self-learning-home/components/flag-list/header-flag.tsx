/** 用户已经设置的 flag */

import * as React from "react";

import { IconSvg } from "@/components";
import { cls } from "@/utils/tool";

import Style from "./style.module.scss";
import { Ellipsis } from "antd-mobile";

interface IUserFlag {
  className?: string;
  allowEdit?: boolean;
  content: string;
  setFlag: () => void;
}
const UserFlag: React.FC<IUserFlag> = (props) => {
  const { allowEdit, content, className, setFlag } = props;
  return (
    <div className={cls([Style["header-flag"], className])}>
      <div className={Style["common-word"]}>
        <div>
          {content
            ? content.length > 20
              ? `${content.substring(0, 20)}...`
              : content
            : "-"}
        </div>
      </div>
      <IconSvg name="icon-bianji" className={Style["edit"]} onClick={setFlag} />
    </div>
  );
};

export default UserFlag;
