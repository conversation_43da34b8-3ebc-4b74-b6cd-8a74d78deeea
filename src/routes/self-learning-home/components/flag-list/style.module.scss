.flag-item {
  text-align: center;
  width: 330px;
  height: 40px;
  background: #EEF1F6;
  border-radius: 21px;
  line-height: 40px;
  position: relative;
  margin: 0 auto;
  margin-top: 12px;
  font-size: 14px;
  color: #666666;
  box-sizing: border-box;

  div {
    margin: 0 auto;
    width: 283px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &:first-child {
    margin-top: 0;
  }

  .beSelected-icon {
    width: 19px;
    height: 16px;
    top: 10.49vw;
    right: 0px;
    transform: translateY(-100%);
    position: absolute;
  }
}

.beSelected-item {
  background: #FFFFFF;
  border: 1px solid #2D86FE;
  border-radius: 21px;
  font-weight: bold;
  font-size: 14px;
  color: #2D86FE;
}

.header-flag {
  width: 207px;
  height: 64px;
  margin-left: 4px;
  padding: 10px 12px;
  background-image: linear-gradient(90deg, #FFFFFF 0%, #DAFAFF 100%);
  box-shadow: 0 4px 2px 0 #0000001a;
  border-radius: 16px;
  position: relative;

  .common-word {
    overflow: hidden;
    font-weight: 600;
    font-size: 16px;
    color: #613400;
  }

  .edit {
    margin-left: 5px;
    color: rgba(0,0,0,.45);
    font-size: 16px;
    position: absolute;
    right: 10px;
    bottom: 10px;
  }
}

.user-flag {
  height: 32px;
  padding: 0 10px;
  display: flex;
  align-items: center;
  background: #FFF1F0;
  border-radius: 8px;
  margin-bottom: 20px;

  .common-word {
    display: flex;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    align-items: center;
    font-weight: 400;
    font-size: 12px;
    color: #333333;
    margin-left: 5px;
  }

  .flag {
    flex: 1;
    font-weight: bold;
    font-size: 12px;
    color: #FF4D4F;
    overflow: hidden;
    margin-left: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .edit {
    margin-left: 9px;
  }
}

.error-btn {
  span {
    font-weight: 400;
  }
}
