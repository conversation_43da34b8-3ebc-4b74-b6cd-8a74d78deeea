/** 周完成目标组件 */

import * as React from "react";

import { Button, IconSvg } from "@/components";
import fireIcon from "@/assets/common/icon-finish-icon.png";
import { cls } from "@/utils/tool";
import type { IPlanDetailRes } from "@/service/home";

import Style from "./style.module.scss";

export interface IWeekCompleteTarget {
  planDetail?: IPlanDetailRes;
  value?: number;
  className?: string;
  onEditWeekTargetClick: () => void;
}

export const WeekCompleteTarget: React.FC<IWeekCompleteTarget> = (props) => {
  const { planDetail, value, className, onEditWeekTargetClick } = props;

  return planDetail ? (
    <div className={cls([Style["week-complete-target"], className])}>
      <div className={Style["explain-container"]}>
        点亮1个
        <img src={fireIcon} className={Style["small-fire"]} />
        代表当周完成一个任务
      </div>
      <div className={Style["top"]}>
        <img src={fireIcon} />
        <IconSvg name="icon-guanbichouti" />
        <div className={Style["target"]}>
          {value || planDetail.defaultPlanCount || "-"}
        </div>
      </div>
      <div className={Style["bottom"]}>
        <Button text="修改数量" onClick={onEditWeekTargetClick} />
      </div>
    </div>
  ) : null;
};
