.week-complete-target {
  width: 100vw;
  height: 138px;
  box-sizing: border-box;

  .explain-container {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #666;

    .small-fire {
      width: 12px;
      height: 15px;
      margin: 0 5px;
    }
  }


  .top {
    height: 48px;
    margin-bottom: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    color: #666666;
    margin-top: 30px;

    img {
      margin-right: 16px;
      width: 40px;
      height: 48px;
    }
    .target {
      margin-left: 16px;
      font-weight: 500;
      font-size: 48px;
      color: #2A333A;
    }
  }

  .bottom {
    justify-content: center;
    display: flex;
    padding-bottom: 32px;
  }
}
