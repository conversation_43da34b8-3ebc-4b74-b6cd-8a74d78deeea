/** 周目标编辑弹窗 */

import * as React from "react";

import { IPopup, Popup } from "@/components";

import {
  IWeekCompleteTarget,
  WeekCompleteTarget,
} from "../week-complete-target";
import { sourceFrom, ESourceFrom } from "../../common";
import { expPv } from "@/utils/tool";

import Style from "./style.module.scss";

interface IEditWeekTargetPop extends IPopup {
  weekCompleteTargetProps: IWeekCompleteTarget;
}

export const EditWeekTargetPop: React.FC<IEditWeekTargetPop> = (props) => {
  const { weekCompleteTargetProps, visible, ...rest } = props;
  /** 曝光埋点 */
  React.useEffect(() => {
    if (visible) {
      expPv("ewt_h5_study_course_self_learning_home_set_week_target_expo", {
        sourceFrom: sourceFrom[ESourceFrom.setWeekTargetPop],
      });
    }
  }, [visible]);
  return (
    <Popup {...rest} visible={visible}>
      <WeekCompleteTarget
        className={Style["edit-weekTarget-pop"]}
        {...weekCompleteTargetProps}
      />
    </Popup>
  );
};
