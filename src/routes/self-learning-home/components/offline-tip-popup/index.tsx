import * as React from "react";
import Style from "./style.module.scss";
import { IGetSelfLearningOfflineConfigRes } from "@/service/home";
import { Button } from "@/components/button";
import { Popup } from "@/components";
import <PERSON>ie from "js-cookie";
import dayjs from "dayjs";
import { getUserInfo, openWebView, versionCompare } from "@/utils/tool";
import CountdownHourglassPng from "@/assets/image/countdown-hourglass.png";

interface IEditWeekTargetPop {
  /** 配置 */
  config: IGetSelfLearningOfflineConfigRes;
  /** 当前的app版本 */
  appVersion: string;
}

export interface IPopupRef {
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
}

const SelectWeekTargetPop = React.forwardRef<IPopupRef, IEditWeekTargetPop>(
  (props, ref) => {
    const { config, appVersion } = props;
    const [visible, setVisible] = React.useState(false);
    const isNewAppVersion = versionCompare(
      appVersion,
      config?.offLineVersion || "",
    );

    /** 把弹窗的调度能力暴露出去 */
    React.useImperativeHandle(ref, () => {
      return {
        setVisible,
      };
    }, []);

    const handleButtonClick = () => {
      // 跳转app后，关闭弹窗
      setVisible(false);
    };

    React.useEffect(() => {
      try {
        const userId = getUserInfo();
        const OFFLINE_COOKIE_KEY = `self-learning-offline-tip-key-${userId}`;
        const flagCache = Cookie.get(OFFLINE_COOKIE_KEY);
        if (!flagCache) {
          setVisible(true);
          // 将信息存入cookie中，1天后过期
          Cookie.set(OFFLINE_COOKIE_KEY, "true", {
            expires: 1,
          });
        }
      } catch (error) {
        console.error(error);
      }
    }, []);

    const handleJumpToGuide = () => {
      setVisible(false);
      openWebView(
        "https://web.ewt360.com/themeTemplateClient/index.html?id=1970107569695055872&showTopBar=false",
        "自习计划升级",
      );
    };

    return (
      <React.Fragment>
        <div className={Style["offline-tip-container"]}>
          <div className={Style["offline-tip-content"]}>
            <div className={Style["offline-tip-content-left"]}>
              <img src={CountdownHourglassPng} alt="" />
              <span>距旧版自习计划下线还有{config?.dayNum || 0}天</span>
            </div>
            <Button
              text="查看说明"
              className={Style["view-remark-button"]}
              onClick={() => setVisible(true)}
            />
          </div>
        </div>

        <Popup
          visible={visible}
          onClose={() => setVisible(false)}
          title="自习计划升级提醒"
        >
          <div className={Style["offline-tip-popup-content"]}>
            {isNewAppVersion ? (
              <>
                <div className={Style["text-container"]}>
                  该功能将升级至新版本App-书桌-自习计划，点击下方按钮查看详情，并学习如何将“未完成课程”加入到新版自习计划。
                </div>
                <Button
                  text="前往查看"
                  className={Style["update-app-button"]}
                  onClick={handleJumpToGuide}
                />
              </>
            ) : (
              <>
                <div className={Style["text-container"]}>
                  自习计划已全面升级，旧版自习计划将在
                  {dayjs(Number(config.offLineTimestamp)).format("YYYY.MM.DD")}
                  下线。 更新APP，体验全新的自习计划吧～
                </div>
                <Button
                  text="我知道了"
                  className={Style["update-app-button"]}
                  onClick={() => handleButtonClick()}
                />
              </>
            )}
          </div>
        </Popup>
      </React.Fragment>
    );
  },
);

SelectWeekTargetPop.displayName = "SelectWeekTargetPop";

export default SelectWeekTargetPop;
