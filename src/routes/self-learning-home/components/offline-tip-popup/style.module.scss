.offline-tip-container {
  background-color: #EEF1F6;
  padding: 12px;

  .offline-tip-content {
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    color: #222222;
    letter-spacing: 0;
    font-weight: bold;
    background-image: linear-gradient(90deg, #FFF5F2 0%, #FFF4F5 100%);
    border: 1px solid #FFFFFF;
    box-shadow: 0 1px 2px 0 #D8D8D8;
    border-radius: 8px;
    padding: 0 12px;

    .offline-tip-content-left {
      display: flex;
      align-items: center;

      img {
        width: 30px;
        margin-right: 8px;
      }
    }

    .view-remark-button {
      border: 1px solid #ffffff80;
      border-radius: 15px;
      padding: 0 10px;
      padding: 2px 12px;
      background: #FF4D4F;

      span {
        cursor: pointer;
        font-weight: normal;
        font-size: 14px;
        letter-spacing: 0;
        line-height: 22px;
        color: #fff;
      }
    }
  }
}

.offline-tip-popup-content {
  padding: 0 20px 20px;
}

.text-container {
  font-weight: normal;
  font-size: 14px;
  color: #666666;
  line-height: 22px;
}

.update-app-button {
  padding-top: 8px!important;
  padding-bottom: 8px!important;
  border-radius: 20px;
  margin-top: 36px;
  border: 1px solid #021E66;
  box-shadow: 0 4px 0 0 rgba(2, 30, 102, 0.15);

  span {
    font-weight: bold;
  }
}
