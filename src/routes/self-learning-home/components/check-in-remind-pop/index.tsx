/** 打卡提醒底抽 */

import * as React from "react";

import DoNotGoPNG from "@/assets/common/do-not-go.png";
import { Button, IPopup, Popup } from "@/components";
import { clickPv, cls, expPv } from "@/utils/tool";

import { getCheckInThreshold, keepLearn, stillLeave } from "../../common";

import Style from "./style.module.scss";

interface ICheckInRemindPop extends IPopup {
  /** 今日学习时长 */
  studyDuration: number;
  onCancel: () => void;
  onOk: () => void;
}

export const CheckInRemindPop: React.FC<ICheckInRemindPop> = (props) => {
  const { visible, studyDuration, onCancel, onOk } = props;
  const checkInThreshold = getCheckInThreshold();

  React.useEffect(() => {
    if (visible) {
      expPv("ewt_h5_study_course_self_learning_checkInRetain_popup_expo");
    }
  }, [visible]);
  return (
    <Popup onClose={onOk} title="今天的卡还没打" visible={visible}>
      <div className={Style["check-in-remind-content"]}>
        <div>不忍心放你走T-T</div>
        <div>
          今天再学
          <span style={{ color: "#FA8B16" }}>
            {Number.isInteger(studyDuration) &&
            checkInThreshold - studyDuration >= 0
              ? checkInThreshold - studyDuration
              : "-"}
            分钟
          </span>
          即可打卡喽~
        </div>
        <img src={DoNotGoPNG} />
      </div>
      <div className={Style["footer"]}>
        <Button
          className={cls([Style["button"], Style["cancel"]])}
          text={stillLeave}
          onClick={() => {
            clickPv(
              "ewt_h5_study_course_self_learning_checkInRetain_popup_btn_click",
              {
                type: stillLeave,
              },
            );
            onCancel();
          }}
        />
        <Button
          className={cls([Style["button"], Style["ok"]])}
          text={keepLearn}
          onClick={() => {
            clickPv(
              "ewt_h5_study_course_self_learning_checkInRetain_popup_btn_click",
              {
                type: keepLearn,
              },
            );
            onOk();
          }}
        />
      </div>
    </Popup>
  );
};
