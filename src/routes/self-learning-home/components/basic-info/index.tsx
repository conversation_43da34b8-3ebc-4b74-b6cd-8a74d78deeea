/** 首页的基本信息 */

import * as React from "react";
import { CenterPopup, Toast } from "antd-mobile";

import {
  ECheckInDayCount,
  EStudyScene,
  type IGetAccumulativeDaysAndRewardStatus,
  type IPlanDetailRes,
  type IStudyTimeAndCheckInStatus,
  type IUserInfoRes,
} from "@/service/home";
import { IconSvg, InfoCard } from "@/components";
import { clickPv, cls } from "@/utils/tool";
import HeaderFlag from "@/routes/self-learning-home/components/flag-list/header-flag";
import SmileIncentiveImg from "@/assets/common/smile-incentive.png";

import { CheckInInfo } from "./check-in-info";
import { getCheckInThreshold } from "../../common";

import Style from "./style.module.scss";
import {
  EReceiveReward,
  IHolidayCheckInDetail,
} from "@/service/self-learning/drawer";
import HolidayCheckInPopover from "../holiday-check-in-popover";

interface IBasicInfo {
  holidayDaysAndStatus: IHolidayCheckInDetail;
  hasETraveAuth: boolean;
  hasCheckIn?: boolean;
  actionLoading?: boolean;
  planDetail?: IPlanDetailRes;
  userInfo: IUserInfoRes;
  studyTimeAndStatus?: IStudyTimeAndCheckInStatus;
  milestoneInfo: IGetAccumulativeDaysAndRewardStatus;
  /** 刷新今日时长 */
  refreshTodayStudyTime: (showCheckRes?: boolean) => void;
  /** 去领奖 */
  toAward: (scene?: EStudyScene) => void;
  /** 查看自习报告 */
  toSelfStudyReport: () => void;
  /** 设置 flag */
  setFlag?: () => void;
}

export const BasicInfo: React.FC<IBasicInfo> = (props) => {
  const {
    hasETraveAuth,
    hasCheckIn,
    actionLoading,
    planDetail,
    milestoneInfo,
    studyTimeAndStatus,
    setFlag,
    refreshTodayStudyTime,
    toAward,
    toSelfStudyReport,
    holidayDaysAndStatus,
  } = props;
  /** 今日学习时长说明 */
  const [studyTimePop, setStudyTimePop] = React.useState<boolean>();
  // 下发的需要学满的时长，分钟
  const checkInThreshold = getCheckInThreshold();
  // 是否假期模式，假期模式需要新皮肤
  const isHoliday = planDetail?.studyScene === EStudyScene.winterHoliday;
  // 是否已打卡
  const isCheckIn = studyTimeAndStatus?.checkInStatus || hasCheckIn;
  // 假期模式的白色字体
  const holidayWhiteColor = isHoliday && Style["holiday-white"];
  // 学习时长的帮助icon
  const helpIcon = (
    <IconSvg
      className={cls([Style["explain"], holidayWhiteColor])}
      name="icon-xingzhuangjiehe"
      onClick={() => setStudyTimePop(true)}
    />
  );
  // 是否需要领奖
  const isReceiveReward =
    milestoneInfo?.rewardStatus === EReceiveReward.receiveReward;
  // 是否有e游记权限，并且可以领奖
  const hasETraveAuthAndReward = hasETraveAuth && isReceiveReward;

  return (
    <div className={Style["basic-info"]}>
      {/* header-打卡、flag容器 */}
      <div className={Style["study-status-and-flag-box"]}>
        {/* 打卡状态和学习时长 */}
        <InfoCard
          statusNoAction
          className={Style["today-study-time"]}
          infoClassName={cls([
            Style["today-study-time-info"],
            isHoliday && !isCheckIn && Style["holiday-theme"],
            isCheckIn && Style["have-check"],
          ])}
          statusClassName={cls([Style["checkIn-status"], holidayWhiteColor])}
          statusText={
            isCheckIn ? (
              <React.Fragment>
                <span style={{ marginRight: 5 }}>
                  {`今日已学${Number.isInteger(studyTimeAndStatus.studyDuration) ? studyTimeAndStatus.studyDuration : "-"}分钟`}
                </span>
                {helpIcon}
              </React.Fragment>
            ) : (
              <div
                className={cls([Style["no-check-in-box"], holidayWhiteColor])}
              >
                再学
                <span style={{ color: "#F5222D" }}>
                  {Number.isInteger(studyTimeAndStatus.studyDuration) &&
                  checkInThreshold - studyTimeAndStatus.studyDuration >= 0
                    ? checkInThreshold - studyTimeAndStatus.studyDuration
                    : "-"}
                  分钟
                </span>
                可打卡
                {helpIcon}
              </div>
            )
          }
          bottom={
            <CheckInInfo
              isHoliday={isHoliday}
              haveCheckIn={studyTimeAndStatus?.checkInStatus || hasCheckIn}
              handleCheckIn={() => {
                if (actionLoading) {
                  return;
                }
                // 如果没打卡，就去打卡
                if (!studyTimeAndStatus?.checkInStatus && !hasCheckIn) {
                  refreshTodayStudyTime(true);
                } else {
                  // 获取最新的学习时长
                  refreshTodayStudyTime(false);
                  Toast.show("今天已经打过卡啦～");
                }
              }}
              handleExplain={() => {
                setStudyTimePop(true);
              }}
            />
          }
        />
        {/* 假期模式 或 用户flag */}
        {isHoliday ? (
          <HolidayCheckInPopover
            holidayDaysAndStatus={holidayDaysAndStatus}
            handleClick={toAward}
          />
        ) : (
          <HeaderFlag
            className={Style["user-flag"]}
            allowEdit
            setFlag={setFlag}
            content={planDetail?.flagContent}
          />
        )}
      </div>
      {/* 打卡激励信息区域 */}
      {planDetail?.studyScene !== EStudyScene.winterHoliday && (
        <div
          className={cls([
            Style["receive-medal-tip-box"],
            milestoneInfo?.checkInDayCount === ECheckInDayCount.noShow &&
              Style["no-show"],
          ])}
        >
          {milestoneInfo &&
            milestoneInfo?.checkInDayCount !== ECheckInDayCount.noShow && (
              <React.Fragment>
                <img src={SmileIncentiveImg} alt="good incentive" />
                <React.Fragment>
                  {milestoneInfo.checkInDayCount ===
                  ECheckInDayCount.received ? (
                    "你真棒～已获得所有奖励"
                  ) : (
                    <>
                      再打卡
                      <span className={Style["red-text"]}>
                        {milestoneInfo.checkInDayCount}天
                      </span>
                      可获得精美勋章
                    </>
                  )}
                </React.Fragment>
              </React.Fragment>
            )}
        </div>
      )}
      {planDetail?.studyScene === EStudyScene.winterHoliday &&
        planDetail?.flagContent && (
          <React.Fragment>
            <div className={Style["holiday-theme-flag-box"]}>
              <p className={Style["flag-content"]}>{planDetail.flagContent}</p>
              <IconSvg
                name="icon-bianji"
                className={Style["edit"]}
                onClick={setFlag}
              />
            </div>
          </React.Fragment>
        )}
      <div className={Style["infos"]}>
        <div>
          累计打卡
          <span className={Style["red-text"]}>
            {milestoneInfo?.days || "0"}
          </span>
          天
        </div>
        <div className={Style["prize-and-task-count"]}>
          <div
            className={cls([
              Style["small-yellow-button"],
              hasETraveAuthAndReward && Style["red-button"],
            ])}
            onClick={() => {
              /** 领域奖励埋点 */
              if (hasETraveAuthAndReward) {
                clickPv(
                  "ewt_h5_study_course_self_learning_home_receive_award_button_click",
                );
              }
              toAward();
            }}
          >
            {hasETraveAuthAndReward ? "点击领奖" : "奖励详情"}
          </div>
          <b className={Style["line"]} />
          <div>
            完成任务
            <span className={Style["red-text"]}>
              {milestoneInfo?.completedTaskCount || "0"}
            </span>
            个
          </div>
        </div>
        <div
          className={Style["small-yellow-button"]}
          onClick={toSelfStudyReport}
        >
          自习报告
        </div>
      </div>

      {/* 规则说明模态弹窗 */}
      <CenterPopup
        className={Style["study-time-wrapper"]}
        visible={studyTimePop}
      >
        <div className={Style["study-time-desc"]}>
          <div className={Style["title"]}>打卡说明</div>
          <div className={Style["content"]}>
            当日学满{checkInThreshold}分钟即可进行打卡；
            <br />
            学习时长统计存在5分钟左右的延迟，请耐心等待；
            <br />
            当日观看90天前完成的课程包不会统计到当日学习时长
          </div>
          <div
            className={Style["footer"]}
            onClick={() => setStudyTimePop(false)}
          >
            我知道了
          </div>
        </div>
      </CenterPopup>
    </div>
  );
};
