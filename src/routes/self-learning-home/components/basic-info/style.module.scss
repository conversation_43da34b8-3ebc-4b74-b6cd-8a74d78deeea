.basic-info {
  width: 100vw;
  height: 200px;
  padding:0 10px 10px;
  box-sizing: border-box;

  .study-status-and-flag-box {
    display: flex;
    padding-top: 20px;
  }

  .receive-medal-tip-box {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
    min-height: 22px;

    &>img {
      width: 20px;
      height: 20px;
      margin-right: 4px;
    }

    &.no-show {
     height: 22px;
    }
  }

  .red-text {
    color: #F5222D;
    font-weight: bold;
  }

  .infos {
    display: flex;
    margin-top: 10px;
    align-items: center;
    justify-content: space-between;
    border-radius: 8px;
    background-color: #FFEDB5;
    margin: 9px 10px;
    padding: 7px 12px;

    .line {
      width: 0.5px;
      height: 21px;
      background-color: #FFBD32;
      border: 0;
      margin: 0 8px
    }

    .small-yellow-button {
      display: inline-block;
      color: #613400;
      font-size: 12px;
      font-weight: bold;
      padding: 2px 8px;
      background-color: #FFC53D;
      border-radius: 12px;

      &.red-button {
        background-color: #FF4D4F;
        color: #fff;
      }
    }

    .prize-and-task-count {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  .today-study-time {
    width: 128px;
    margin-left: 11px;

    &-info {
      position: relative;
      font-weight: bold;
      font-size: 20px;
      color: #613400;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      padding: 4px;
      width: 110px;
      height: 56px;
      background-image: linear-gradient(180deg, #FFE58F 0%, #FAAD14 100%);
      border: 2px solid #FFFFFF;
      box-shadow: 0 4px 0 0 #00000026;
      border-radius: 28px;


      img {
        margin-right: 8px;
      }

      &.holiday-theme {
        background: #FF4D4F;
        color: #fff;
      }

      &.have-check {
        background: none;
        background-color: #FFEFC1;
      }
    }

    &-explain {
      top: 4px;
      right: 4px;
      position: absolute;
      font-size: 13px;
      padding-right: 1px;
      color: rgba(0, 0, 0, 0.45);
    }
  }

  .checkIn-days {
    width: 128px;
    margin: 0 8px;
  }

  .no-check-in-box {
    display: flex;
    align-items: center;

    .explain {
      color: #936400;
      margin-left: 2px;
    }
  }

  .checkIn-status {
    color: #000;
    font-size: 12px;
    margin-top: 10px;
  }

  .no-reward, .to-report, .no-checkIn {
    background-color: #2D86FE;
  }

  .self-study-report {
    width: 79px;

    &-title {
      text-align: center;
    }

    &-bottom {
      text-align: center;
      font-size: 16.8px;
    }
  }
}

.holiday-white {
  color: #fff!important;
}

.study-time-wrapper {
  :global {
    .adm-center-popup-wrap {
      width: 260px;
      min-width: 260px;
    }
  }
}

.study-time-desc {
  width: 260px;
  box-sizing: border-box;
  padding-top: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  text-align: center;
  .title {
    height: 16px;
    font-weight: bold;
    font-size: 16px;
    color: #333333;
    text-align: center;
  }
  .content {
    padding: 0 20px;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    text-align: center;
    margin: 14px 0 20px 0;
  }
  .footer {
    width: 100%;
    height: 44px;
    border-top: 1px #EEF1F6 solid;
    font-weight: 400;
    font-size: 16px;
    color: #2D86FE;
    text-align: center;
    line-height: 44px;
  }
}

.user-flag {
  margin-bottom: 10px;
}

.have-checkIn {
  width: 16px;
  height: 16px;
}

.not-checkIn {
  width: 28px;
  height: 28px;
}

.checkIn-text {
  line-height: 22px;
}

.holiday-theme-flag-box {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;

  .flag-content {
    max-width: 226px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px;
    color: rgba(0,0,0,.85)
  }

  .edit {
    margin-left: 4px;
    color: rgba(0,0,0,.45);
    font-size: 16px;
  }
}
