/** 打卡信息卡片 */

import * as React from "react";

import notCheckInPNG from "@/assets/common/not-checkIn.png";
import haveCheckInPNG from "@/assets/common/have-checkIn.png";
import holidayCalenderPNG from "@/assets/home/<USER>";

import Style from "./style.module.scss";

interface ICheckInInfo {
  // 是否假期模式
  isHoliday: boolean;
  /** 今日是否已经打卡 */
  haveCheckIn?: boolean;
  handleCheckIn: () => void;
  handleExplain: () => void;
}

export const CheckInInfo: React.FC<ICheckInInfo> = (props) => {
  const {
    haveCheckIn,
    handleCheckIn,
    handleExplain,
    isHoliday = false,
  } = props;
  return (
    <>
      <img
        onClick={handleCheckIn}
        className={haveCheckIn ? Style["have-checkIn"] : Style["not-checkIn"]}
        src={
          haveCheckIn
            ? haveCheckInPNG
            : isHoliday
              ? holidayCalenderPNG
              : notCheckInPNG
        }
      />
      <div className={Style["checkIn-text"]} onClick={handleCheckIn}>
        {haveCheckIn ? "已打卡" : "打卡"}
      </div>
    </>
  );
};
