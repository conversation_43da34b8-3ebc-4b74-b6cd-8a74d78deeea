import React, { useEffect, useState } from "react";
import styles from "./style.module.scss";
import HolidayRuleTipImg from "@/assets/home/<USER>";
import { Button, Popup } from "@/components";
import { clickPv, expPv } from "@/utils/tool";

export interface IHolidayUpgradeTipPop {
  title?: string;
  closeBefore?: () => void;
}

export interface IPopupRef {
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
}

export const HolidayUpgradeTipPop = React.forwardRef<
  IPopupRef,
  IHolidayUpgradeTipPop
>(function Popups(props, ref) {
  const { title, closeBefore } = props;
  const [visible, setVisible] = useState(false);

  /** 把弹窗的调度能力暴露出去 */
  React.useImperativeHandle(ref, () => {
    return {
      setVisible,
    };
  }, []);

  const handleClose = () => {
    /** 先调用外部传入的，把数据传出去 */
    closeBefore?.();
    // 再内部闭环关闭抽屉
    setVisible(false);
  };

  useEffect(() => {
    if (visible) {
      expPv("ewt_h5_study_course_self_learning_holiday_upgrade_notice_expo");
    }
  }, [visible]);

  return (
    <Popup
      title={title || "2025年寒假全新升级"}
      visible={visible}
      onClose={() => handleClose()}
      {...props}
    >
      <div className={styles["holiday-upgrade-tip-pop-box"]}>
        <img src={HolidayRuleTipImg} alt="2025年寒假全新升级" />
        <Button
          text="我知道了，马上学习"
          className={styles["now-start-study"]}
          onClick={() => {
            clickPv(
              "ewt_h5_study_course_self_learning_holiday_upgrade_notice_close_click",
            );
            handleClose();
          }}
        />
      </div>
    </Popup>
  );
});
