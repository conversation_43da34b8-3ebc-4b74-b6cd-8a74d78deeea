import * as React from "react";
import { cls } from "@/utils/tool";
import Style from "./style.module.scss";
import {
  EReceiveReward,
  IHolidayCheckInDetail,
} from "@/service/self-learning/drawer";
import HolidayIconImg from "@/assets/home/<USER>";
import { ECheckInDayCount, EStudyScene } from "@/service/home";
import { IconSvg } from "@/components";

interface IUserFlag {
  className?: string;
  holidayDaysAndStatus: IHolidayCheckInDetail;
  handleClick: (scene?: EStudyScene) => void;
}
const HolidayCheckInPopover: React.FC<IUserFlag> = (props) => {
  const { className, holidayDaysAndStatus, handleClick } = props;
  // 是否完成了全部任务
  const needCheckInDay =
    holidayDaysAndStatus?.checkInDayCount > ECheckInDayCount.received;
  const isReceive =
    holidayDaysAndStatus?.rewardStatus === EReceiveReward.receiveReward;

  return (
    <div className={cls([Style["holiday-check-in-popover-box"], className])}>
      <div className={Style["days-info-box"]}>
        <p>假期已打卡{holidayDaysAndStatus?.days ?? "-"}天</p>
        <img src={HolidayIconImg} alt="" />
      </div>
      <div
        className={cls([
          Style["continue-status-box"],
          isReceive && Style["receive-status"],
        ])}
      >
        <span>
          {needCheckInDay
            ? `再打卡${holidayDaysAndStatus?.checkInDayCount || "-"}天可领奖`
            : "你可太棒啦～"}
        </span>
        <div onClick={() => handleClick(EStudyScene.winterHoliday)}>
          {isReceive ? "立即领奖" : "奖励详情"}
          <IconSvg
            name="icon-jinru"
            className={cls([
              Style["arrow-icon"],
              isReceive ? Style["white-arrow"] : Style["yellow-arrow"],
            ])}
          />
        </div>
      </div>
    </div>
  );
};

export default HolidayCheckInPopover;
