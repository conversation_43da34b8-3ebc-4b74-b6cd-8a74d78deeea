.query {
  background: #FFFFFF;
  position: sticky;
  top: 40px;
  z-index: 1;
  &-status {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100vw;
    margin: 0 auto;
    height: 48px;
    background: #FFFFFF;
    font-size: 14px;
    font-weight: bold;
    .tabs {
      width: 224px;
      display: flex;
    }
    .status-tab {
      width: 50%;
      height: 24px;
      border-radius: 100px 0 0 100px;
      color: #666666;
      text-align: center;
      line-height: 24px;
      background: rgba(0, 0, 0, 0.04);

      &:first-child {
        border-radius: 100px 0 0 100px;
      }

      &:last-child {
        border-radius: 0 100px 100px 0;
      }
    }
    &-selected {
      background: #2D86FE !important;
      color: #fff !important;
    }
  }
}

.empty {
  background-color: #EEF1F6;
  width: 100%;
  height: 400px
}

.all-plans-list {
  :global {
    .adm-list-body {
      background-color: #EEF1F6;
    }
  }
}

.all-plans-list-scroll {
  background-color: #EEF1F6;
  padding-bottom: 40px;
}

.subjects {
  height: 48px;
  padding: 0 16px;

  :global {
    .swiper-slide {
      margin-right: 16px;
      &:last-child {
        margin-right: 0;
      }
    }
  }

  .subject-item {
    height: 24px;
    line-height: 24px;
    padding: 0 12px;
    font-size: 12px;
    font-weight: bold;
    background: rgba(0, 0, 0, 0.04);
  }
}

.error-btn {
  span {
    font-weight: normal;
  }
}
