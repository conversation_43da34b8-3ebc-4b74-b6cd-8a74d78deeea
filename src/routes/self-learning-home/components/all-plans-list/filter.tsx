/** 头部筛选区 */
import * as React from "react";

import { IPostAllPlanReq, ISubjectItem } from "@/service/home";
import { SubjectList } from "@/components";
import type { ISubjectList } from "@/components";
import { cls } from "@/utils/tool";

import { packageStatusText, EPackageStatus, EListType } from "../../common";

import Style from "./style.module.scss";

interface IFilter {
  subjects?: ISubjectList["subjectList"];
  value: IPostAllPlanReq;
  onChange: React.Dispatch<React.SetStateAction<IPostAllPlanReq>>;
}

export const Filter: React.FC<IFilter> = (props) => {
  const { value, subjects, onChange } = props;

  return (
    <div className={Style["query"]}>
      <div
        className={Style["query-status"]}
        onClick={() =>
          onChange((pre) => {
            return {
              ...pre,
              status:
                pre.status === EPackageStatus.doing
                  ? EPackageStatus.done
                  : EPackageStatus.doing,
            };
          })
        }
      >
        <div className={Style["tabs"]}>
          <div
            className={cls([
              Style["status-tab"],
              value?.status === EPackageStatus.doing &&
                Style["query-status-selected"],
            ])}
          >
            {packageStatusText[EPackageStatus.doing]}
          </div>
          <div
            className={cls([
              Style["status-tab"],
              value?.status === EPackageStatus.done &&
                Style["query-status-selected"],
            ])}
          >
            {packageStatusText[EPackageStatus.done]}
          </div>
        </div>
      </div>
      {!!subjects?.length && (
        <SubjectList
          className={Style["subjects"]}
          itemClassName={Style["subject-item"]}
          subjectList={subjects}
          value={
            {
              code: value?.subjectInfo?.subjectId,
              parentCode: value?.subjectInfo?.categoryCode,
            } as ISubjectItem
          }
          onChange={(item) => {
            onChange((pre) => ({
              ...pre,
              subjectInfo: {
                categoryCode: item.parentCode,
                subjectId: item.code,
              },
            }));
          }}
        />
      )}
    </div>
  );
};
