/** 列表空态 */

import * as React from "react";

import { clickPv } from "@/utils/tool";
import noContentPNG from "@/assets/common/no-content.png";
import addPNG from "@/assets/common/add.png";
import errorPNG from "@/assets/common/error.png";
import Empty from "@/components/empty-status";
import {
  EListType,
  EPackageListStatus,
  listName,
  packageListBtnText,
  packageListStatusText,
  allDonePlanScrollEndText,
  EListStatus,
  listStatusText,
} from "../../common";

import Style from "./style.module.scss";

interface IListEmpty {
  /** 是否展示添加内容按钮 */
  showAddButton?: boolean;
  nav: (path: string) => void;
  listStatus: EPackageListStatus;
  postAllPlan: (init: boolean) => Promise<any>;
}

export const ListEmpty: React.FC<IListEmpty> = (props) => {
  const { listStatus, nav, postAllPlan, showAddButton = false } = props;

  return (
    <div className={Style["empty"]}>
      <Empty
        image={
          listStatus === EPackageListStatus.error ? errorPNG : noContentPNG
        }
        text={
          showAddButton
            ? packageListStatusText[listStatus]
            : listStatusText[EListStatus.offline]
        }
        className={
          listStatus === EPackageListStatus.error ? "empty-error-img" : null
        }
        buttonOption={{
          showIcon: listStatus !== EPackageListStatus.error,
          icon: addPNG,
          className:
            listStatus === EPackageListStatus.error ? Style["error-btn"] : null,
          text: showAddButton ? packageListBtnText[listStatus] : "",
          handleClick: () => {
            if (listStatus === EPackageListStatus.error) {
              postAllPlan(true);
            } else {
              /** 埋点 */
              clickPv(
                "ewt_h5_study_course_self_learning_home_add_content_click",
                {
                  tab: listName[EListType.allList],
                },
              );
              // 添加内容
              nav("/self-learning/recommend");
            }
          },
        }}
      >
        {EPackageListStatus.doneEmpty === listStatus && (
          <div
            style={{ color: "#2D86FE" }}
            onClick={() => nav("/self-learning/completed-course")}
          >
            {allDonePlanScrollEndText}
          </div>
        )}
      </Empty>
    </div>
  );
};
