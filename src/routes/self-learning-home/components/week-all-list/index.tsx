/** 本周计划列表 */

import * as React from "react";

import { EListType, listName } from "@/routes/self-learning-home/common";
import { clickPv, cls } from "@/utils/tool";

import { WeekPlanList } from "../week-plan-list";
import { AllPlanList } from "../all-plans-list";

import Style from "./style.module.scss";

interface IWeekAndAllList {
  /** 是否展示添加内容按钮 */
  showAddButton?: boolean;
  editWeekTarget: () => void;
  onBatchFinishCallBack: () => void;
}

export const WeekAndAllList: React.FC<IWeekAndAllList> = (props) => {
  const {
    editWeekTarget,
    onBatchFinishCallBack,
    showAddButton = false,
  } = props;
  /** 获取本周计划列表 */
  const [tab, setTab] = React.useState<EListType>(EListType.weekList);
  function changeTab(type: EListType) {
    /** 埋点 */
    clickPv("ewt_h5_study_course_self_learning_home_change_tab_click", {
      tab: listName[tab],
    });
    setTab(type);
  }
  return (
    <div className={Style["week-all-list"]} id="week-all-list">
      <div className={Style["header"]}>
        {/* 本周计划 tab */}
        <div
          className={cls([
            Style["tab"],
            tab === EListType.weekList && Style["selected"],
          ])}
          onClick={() => changeTab(EListType.weekList)}
        >
          {listName[EListType.weekList]}
          {/* 下方水银柱 */}
          {tab === EListType.weekList ? (
            <div className={Style["tab-bar"]}></div>
          ) : null}
        </div>
        <div style={{ color: "#EEF1F6" }}>|</div>
        {/* 全部计划 tab */}
        <div
          className={cls([
            Style["tab"],
            tab === EListType.allList && Style["selected"],
          ])}
          onClick={() => changeTab(EListType.allList)}
        >
          {listName[EListType.allList]}
          {tab === EListType.allList ? (
            <div className={Style["tab-bar"]}></div>
          ) : null}
        </div>
      </div>
      <div className={Style["list-wrapper"]}>
        {tab === EListType.weekList ? (
          <WeekPlanList
            editWeekTarget={editWeekTarget}
            onBatchFinishCallBack={onBatchFinishCallBack}
            showAddButton={showAddButton}
          />
        ) : (
          <AllPlanList showAddButton={showAddButton} />
        )}
      </div>
    </div>
  );
};
