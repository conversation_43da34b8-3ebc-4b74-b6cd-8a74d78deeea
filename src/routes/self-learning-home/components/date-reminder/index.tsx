/** 日历提醒组件 */

import * as React from "react";
import moment from "moment";
import { CalendarPickerView, Popover, Toast } from "antd-mobile";
import { EButtonType, IconSvg } from "@/components";

import { <PERSON><PERSON>, Popup } from "@/components";
import PageLoading from "@/components/page-loading";
import { clickPv, cls, convertDateFormat, debounce } from "@/utils/tool";
import { useDateReminder } from "@/routes/self-learning-home/hooks";

import Style from "./style.module.scss";

export interface IDateReminderProps {
  onClose?: () => void;
  isHoliday: boolean;
}

export interface IPopupRef {
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
}

export const DateReminder = React.forwardRef<IPopupRef, IDateReminderProps>(
  function Popups(props, ref) {
    const { onClose, isHoliday } = props;
    const {
      loading,
      dateRange,
      setDate,
      getDate,
      deleteDate,
      checkCalenderSetStatus,
      saveCalenderInfo,
    } = useDateReminder();
    /** 设置日历提醒 pop */
    const [dateReminderVis, setDateReminderVis] =
      React.useState<boolean>(false);
    function changeDateReminderVis() {
      setDateReminderVis((pre) => !pre);
    }
    /** 日历 pop */
    const [calenderVis, setCalenderVis] = React.useState<boolean>(false);
    function changeCalenderVis() {
      setCalenderVis((pre) => !pre);
    }
    const statusInfo = checkCalenderSetStatus();
    /** 未设置或者日历到期的popover */
    const [calenderExpiredVisible, setCalenderExpiredVisible] =
      React.useState<boolean>(statusInfo?.isCalenderExpired || false);
    const [displayGuidVis, setDisplayGuidVis] = React.useState(
      statusInfo?.displayGuid || false,
    );

    /** 日历范围，受控处理 */
    const [value, setValue] = React.useState<[Date, Date]>();
    /** 日历值的重置 */
    React.useEffect(() => {
      if (!calenderVis) {
        setValue(null);
      }
    }, [calenderVis]);
    /** 获取设置的日期范围 */
    React.useEffect(() => {
      if (dateReminderVis) {
        getDate();
      }
      if (!dateReminderVis) {
        onClose?.();
      }
    }, [dateReminderVis]);

    const handleCloseCalenderPopover = () => {
      clickPv(
        "ewt_h5_study_course_self_learning_home_no_set_calender_tip_popup_click",
        {
          closeType: "关闭tips",
        },
      );
      // 点击关闭的时候保存到本地标识，已出现过
      saveCalenderInfo({
        displayGuid: true,
      });
      setDisplayGuidVis(true);
    };

    /** 把弹窗的调度能力暴露出去 */
    React.useImperativeHandle(ref, () => {
      return {
        setVisible: setDateReminderVis,
      };
    }, []);

    return (
      <>
        <Popover
          className={Style["set-calender-tip-popover-box"]}
          content={
            <div className={Style["set-calender-popover"]}>
              担心忘了学习？设个提醒吧！
              <IconSvg
                name="icon-guanbi"
                onClick={handleCloseCalenderPopover}
              />
            </div>
          }
          placement="bottom-start"
          mode="dark"
          // 未设置过日历的需要显示一次提醒浮窗
          visible={!displayGuidVis}
        >
          <div
            className={cls([
              Style["data-reminder-btn"],
              isHoliday && Style["holiday-reminder-btn"],
            ])}
            onClick={() => {
              clickPv(
                "ewt_h5_study_course_self_learning_home_calendar_entrance_button_click",
              );
              // 点击设置日历按钮也会视为点击过，关门提示
              saveCalenderInfo({
                displayGuid: true,
              });
              clickPv(
                "ewt_h5_study_course_self_learning_home_no_set_calender_tip_popup_click",
                {
                  closeType: "设置日历提醒",
                },
              );
              setDisplayGuidVis(true);
              changeDateReminderVis();
            }}
          >
            设置日历提醒
            {/* 未设置或者日历到期了需要显示小红点 */}
            {calenderExpiredVisible && (
              <b
                className={cls([
                  Style["small-point"],
                  isHoliday && Style["holiday-yellow"],
                ])}
              />
            )}
          </div>
        </Popover>
        <Popup
          visible={dateReminderVis}
          title="设置日历提醒"
          onClose={changeDateReminderVis}
          destroyOnClose
        >
          <div className={Style["tip"]}>
            你可以选择起止日期来设置日历提醒，每天提醒你前来打卡学习
          </div>
          <div
            className={cls([
              Style["content"],
              dateRange ? Style["have-value"] : Style["no-value"],
            ])}
            onClick={() => {
              if (!dateRange) {
                clickPv(
                  "ewt_h5_study_course_self_learning_calendar_pop_add_button_click",
                );
                changeCalenderVis();
              }
            }}
          >
            {dateRange ? (
              <>
                <div className={Style["range-text"]}>提醒周期：</div>
                <div className={Style["range-value"]}>
                  {convertDateFormat(dateRange.start_date)}-
                  {convertDateFormat(dateRange.end_date)}
                </div>
                <div className={Style["alarm-time"]}>
                  每天上午9:00提醒 <span>（时间不可修改）</span>
                </div>
              </>
            ) : (
              <>
                <div className={Style["icon"]}>
                  <IconSvg name="icon-jiajihua" />
                </div>
                <div>新增一个提醒时间段</div>
              </>
            )}
          </div>
          {dateRange ? (
            <div className={Style["actions"]}>
              <Button
                className={Style["delete-btn"]}
                type={EButtonType.grey}
                text="删除提醒"
                onClick={debounce(() => {
                  clickPv(
                    "ewt_h5_study_course_self_learning_calendar_pop_action_button_click",
                    {
                      type: "删除提醒",
                    },
                  );
                  deleteDate(true);
                }, 300)}
              />
              <Button
                className={Style["edit-btn"]}
                text="修改周期"
                onClick={debounce(() => {
                  clickPv(
                    "ewt_h5_study_course_self_learning_calendar_pop_action_button_click",
                    {
                      type: "修改提醒",
                    },
                  );
                  changeCalenderVis();
                }, 300)}
              />
            </div>
          ) : null}
        </Popup>
        <Popup
          destroyOnClose
          visible={calenderVis}
          title="日期选择"
          onClose={changeCalenderVis}
        >
          <CalendarPickerView
            title={null}
            className={Style["calendar-picker"]}
            selectionMode="range"
            /** 最多可以选择当前日期往后推59天 */
            max={moment().add(60, "days").toDate()}
            min={moment().toDate()}
            onChange={(val) => {
              setValue(val);
            }}
            shouldDisableDate={(date) => {
              const today = moment();
              return (
                date < today.startOf("day").toDate() ||
                date > today.add(60, "days").startOf("day").toDate()
              );
            }}
          />
          <div className={Style["confirm-btn-wrapper"]}>
            <Button
              text="确认"
              className={Style["set-date-confirm-btn"]}
              onClick={debounce(async () => {
                if (value?.length) {
                  const startDate = moment(value[0]).format("YYYY-MM-DD");
                  const endDate = moment(value[1]).format("YYYY-MM-DD");
                  clickPv(
                    "ewt_h5_study_course_self_learning_calendar_pop_add_confirm_button_click",
                    {
                      startDate,
                      endDate,
                    },
                  );
                  const res = await setDate(startDate, endDate);
                  if (res) {
                    setCalenderExpiredVisible(false);
                    setValue(null);
                    changeCalenderVis();
                  }
                } else {
                  Toast.show({
                    content: "请选择一段时间区间",
                  });
                }
              }, 300)}
            />
          </div>
        </Popup>
        <PageLoading className={Style["page-loading"]} visible={loading} />
      </>
    );
  },
);
