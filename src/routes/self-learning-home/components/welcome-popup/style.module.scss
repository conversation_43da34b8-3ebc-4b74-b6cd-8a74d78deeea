.welcome-popup-box {
  width: 310px;
  height: 400px;
  border-radius: 8px;
  background-color: #2D86FE;
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 99;
  overflow: hidden;

  .content-box {
    position: relative;
    z-index: 100;
    width: 100%;
    height: 100%;

    .lottie-box {
      width: 310px;
      height: 400px;
      position: absolute;
      z-index: 101;
      left: 0;
      top: 0;
    }

    .centre-content {
      text-align: center;
      position: absolute;
      left: 0;
      top: 0;
      z-index: 102;
      width: 100%;
      height: 100%;

      .first-title {
        font-size: 32px;
        font-weight: bold;
        color: #fff;
        margin-top: 10px;
      }

      .sub-title {
        font-size: 14px;
        color: #fff;
        margin-top: 5px;
      }

      .medal-box {
        position: relative;
        z-index: 1;
        width: 200px;
        height: 200px;
        display: inline-block;
        margin-top: 11px;

        .medal-img {
          width: 200px;
          height: 200px;
        }

        .lock-bg {
          border-radius: 50%;
          background-color: rgba(0, 0, 0, .5);
          width: 25px;
          height: 25px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          position: absolute;
          bottom: 0;
          right: 0;
        }
      }

      .tip-text {
        color: #fff;
        font-size: 14px;
        margin-top: 5px;
      }

      .start-button {
        width: 278px;
        height: 44px;
        margin: 10px auto 0;
        background-color: #FFC53D;
        & > div {
          color: #fff;
          font-size: 18px;
          font-weight: bold;
        }
      }
    }
  }
}
