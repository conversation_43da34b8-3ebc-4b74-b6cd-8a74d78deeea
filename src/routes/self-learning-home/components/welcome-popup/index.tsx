import React, { useEffect, useState } from "react";
import Style from "./style.module.scss";
import { Mask } from "antd-mobile";
import { Button, EButtonType, IconSvg } from "@/components";
import { postGuidPopupCallback } from "@/service/self-learning/drawer";
import { clickPv, expPv } from "@/utils/tool";
import LottieCom from "@/components/lottie-box";
import Lottie<PERSON>son from "@/assets/json/sun-json/data";
import { getCheckInThreshold } from "../../common";

interface IWelComePopupProps {
  onClose?: () => void;
  image: string;
}

export interface IPopupRef {
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
}

export const WelComePopup = React.forwardRef<IPopupRef, IWelComePopupProps>(
  function Popups(props, ref) {
    const { onClose = () => {}, image = "" } = props;
    const [visible, setVisible] = useState(false);
    /** 把弹窗的调度能力暴露出去 */
    React.useImperativeHandle(ref, () => {
      return {
        setVisible,
      };
    }, []);

    const handleClose = () => {
      try {
        clickPv(
          "ewt_h5_study_course_self_learning_home_guidance_welcome_popup_click",
        );
      } catch (error) {
        console.log("欢迎弹窗上报点击QT出错", error);
      }
      /** 先调用外部传入的，把数据传出去 */
      onClose();
      // 再内部闭环关闭抽屉
      setVisible(false);
    };

    const uploadStatus = async () => {
      try {
        postGuidPopupCallback();
      } catch (error) {
        console.warn("新手引导弹窗上报异常", error);
      }
    };

    useEffect(() => {
      // 显示时上报状态
      if (visible) {
        uploadStatus();
        try {
          expPv(
            "ewt_h5_study_course_self_learning_home_guidance_welcome_popup_expo",
          );
        } catch (error) {
          console.warn("上报欢迎弹窗QT出错", error);
        }
      }
    }, [visible]);

    return visible ? (
      <Mask
        visible={visible}
        disableBodyScroll={true}
        onMaskClick={handleClose}
      >
        <div className={Style["welcome-popup-box"]}>
          <div className={Style["content-box"]}>
            {visible && <LottieCom dataJson={LottieJson} />}
            <div className={Style["centre-content"]}>
              <p className={Style["first-title"]}>你已开启自习计划</p>
              <p className={Style["sub-title"]}>
                每天学{getCheckInThreshold()}分钟即可打卡噢~~
              </p>
              <div className={Style["medal-box"]}>
                <img className={Style["medal-img"]} src={image} alt="勋章" />
                <div className={Style["lock-bg"]}>
                  <IconSvg name="icon-suo" />
                </div>
              </div>
              <p className={Style["tip-text"]}>今日打卡后可获得以上勋章</p>
              <Button
                onClick={handleClose}
                className={Style["start-button"]}
                text="持续打卡 攒系列勋章"
                type={EButtonType.grey}
              />
            </div>
          </div>
        </div>
      </Mask>
    ) : null;
  },
);
