/** 编辑 flag 的底抽 */

import * as React from "react";

import { IPopup, Popup } from "@/components";
import { expPv } from "@/utils/tool";

import { FlagList, IFlagList } from "../flag-list";
import { ESourceFrom, sourceFrom } from "../../common";

import Style from "./style.module.scss";

interface IEditFlagPop extends IPopup {
  flagListProps: IFlagList;
}

export const EditFlagPop: React.FC<IEditFlagPop> = (props) => {
  const { flagListProps, visible, ...rest } = props;
  /** 曝光埋点 */
  React.useEffect(() => {
    if (visible) {
      expPv("ewt_h5_study_course_self_learning_home_flag_list_expo", {
        sourceFrom: sourceFrom[ESourceFrom.setFlagPop],
      });
    }
  }, [visible]);
  return (
    <Popup {...rest} visible={visible}>
      <FlagList className={Style["flag-wrapper"]} {...flagListProps} />
    </Popup>
  );
};
