/** 获取计划详情，统一把这些信息抛出，统一维护 */

import * as React from "react";

import {
  IPlanDetailRes,
  getUserPlanDetail as getUserPlanDetailApi,
} from "@/service/home";
import SafeLogger from "@/utils/safe-logger";

interface IParams {
  callback?: (res: IPlanDetailRes) => void;
  isRefresh?: boolean;
}

export function usePlanDetail() {
  const [planDetail, setPlanDetail] = React.useState<IPlanDetailRes>();
  /**
   * 为了避免重复请求 该函数也作为是否要展示新手引导的校验函数
   * 请求在新手引导完成后，要再次调用，获取用户设置的最新值
   * @returns boolean
   */
  async function getUserPlanDetail(params?: IParams): Promise<boolean> {
    const { callback, isRefresh = true } = params || {};
    try {
      if (isRefresh) {
        const { data } = await getUserPlanDetailApi();
        setPlanDetail(data);
        callback?.(data);
        // 没有设置 flag 就认为用户是第一次进来，就要弹新手引导
        return !data.flagContent;
      }
      callback?.(planDetail);
      return !planDetail?.flagContent;
    } catch (error) {
      console.log(error);
      SafeLogger.baseLogger.error("get-plan-detail-failed", { error });
      return false;
    }
  }

  /**
   * 检测弹窗是否需要显示
   * @param callback 判断的回调函数，会将当前计划详情信息返回，函数返回boolean
   */
  async function checkPlanDetailRelatedPop(
    callback?: (res: IPlanDetailRes) => boolean,
  ): Promise<boolean> {
    try {
      if (planDetail && planDetail.flagContent) {
        return callback?.(planDetail) || false;
      } else {
        const { data } = await getUserPlanDetailApi();
        setPlanDetail(data);
        return callback?.(data) || false;
      }
    } catch (error) {
      console.log(error);
      return false;
    }
  }
  return { getUserPlanDetail, checkPlanDetailRelatedPop, planDetail };
}
