/**
 * 统一管理里程碑弹窗展示以及今日学习时长、累计打开天数等状态
 * 方便状态管理
 */

import * as React from "react";

import {
  checkMilestonePop as checkMilestonePopApi,
  getAccumulativeDaysAndRewardStatus,
} from "@/service/home";
import type {
  IGetAccumulativeDaysAndRewardStatus,
  IMilestonePopInfo,
} from "@/service/home";
import SafeLogger from "@/utils/safe-logger";

interface IUseMilestonePop {
  hasETraveAuth: boolean;
}

export function useMilestonePop(props: IUseMilestonePop) {
  const { hasETraveAuth } = props;
  /** 累计打卡天数、奖励状态 */
  const [milestoneInfo, setMilestoneInfo] =
    React.useState<IGetAccumulativeDaysAndRewardStatus>();
  async function getMilestoneInfo() {
    try {
      const { data } = await getAccumulativeDaysAndRewardStatus();
      setMilestoneInfo(data);
    } catch (error) {
      console.log(error);
      SafeLogger.baseLogger.error("get-milestoneInfo-failed", { error });
    }
  }
  async function checkMilestonePop(
    cb?: (v: IMilestonePopInfo) => void,
  ): Promise<boolean> {
    try {
      /** 获取打卡天数、奖励状态 */
      getMilestoneInfo();
      if (!hasETraveAuth) {
        return false;
      }
      const { data: milestonePopInfo } = await checkMilestonePopApi();
      if (milestonePopInfo.popup && cb) {
        cb(milestonePopInfo);
      }
      return milestonePopInfo.popup;
    } catch (err) {
      console.error(err);
      return false;
    }
  }
  return {
    milestoneInfo,
    checkMilestonePop,
    getMilestoneInfo,
  };
}
