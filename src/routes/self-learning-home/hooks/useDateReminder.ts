import * as React from "react";
import { Toast } from "antd-mobile";
import { getNativeData, getUserInfo } from "@/utils/tool";
import moment from "moment";
import mstJsBridge from "mst-js-bridge";

const KEY = "@@self-learning-home-date-reminder";

/**
 * 本地存储内的日历信息
 * @param id 本地日历的id
 * @param start_date 日历的开始时间
 * @param end_date 日历的结束时间
 * @param logoutTime 退出时间
 * @param logoutCount 退出次数，当天内是0-3,3次后不再弹出提示
 * @param displayGuid 是否展示过引导提示，默认是没有，仅出现一次
 */
export interface ICalenderStorageCache {
  id?: string;
  start_date?: string;
  end_date?: string;
  logoutTime?: string;
  logoutCount?: number;
  displayGuid?: boolean;
}

export function useDateReminder() {
  const keyRef = React.useRef<string>();
  const userId = getUserInfo();
  if (userId) {
    const key = `${KEY}-${userId}`;
    keyRef.current = key;
  }
  const [loading, setLoading] = React.useState<boolean>(false);
  const [dateRange, setDateRange] = React.useState<{
    id: string;
    start_date: string;
    end_date: string;
  } | null>();
  /**
   * 向本地存储内持久化新的日历信息
   * @param params 需要存储的日历信息
   */
  function saveCalenderInfo(params: ICalenderStorageCache = {}) {
    if (keyRef.current) {
      // 默认的缓存信息
      const defaultCacheObj = {
        displayGuid: false, // 是否展示过提示, 这个是固定的，因为生命周期内只显示一次
        id: "", // 日历id，默认为空，当设置时会存
        start_date: "", // 开始时间，默认为空，当用户存储过会有
        end_date: "", // 结束时间，默认为空，当用户存储过会有
        logoutCount: 0,
        logoutTime: "", // 点击退出按钮的时间，默认为空
      };
      const tempCacheStr: string | undefined = localStorage.getItem(
        keyRef.current,
      );
      let tempCacheData = {};
      if (tempCacheStr) {
        tempCacheData = JSON.parse(tempCacheStr);
      }
      localStorage.setItem(
        keyRef.current,
        JSON.stringify({
          ...defaultCacheObj,
          ...tempCacheData,
          ...(params || {}),
        }),
      );
    }
  }
  /**
   * 获取日历范围
   * 如果从app获取该日历不存在，就清空本地缓存
   * */
  async function getDate() {
    try {
      if (!keyRef.current) {
        return;
      }
      const res = localStorage.getItem(keyRef.current);
      if (res) {
        const dateRangeCache = JSON.parse(res);
        if (dateRangeCache?.id) {
          setLoading(true);
          const { code } = await getNativeData({
            domain: "web",
            action: "get_schedule",
            params: {
              identifier: dateRangeCache.id,
            },
          });
          /** 如果获取日历失败，就清空之前的缓存 */
          if (code !== 200) {
            setDateRange(null);
            // 如果没有获取成功就清理原有的日历id
            saveCalenderInfo({
              id: "",
              start_date: "",
              end_date: "",
            });
          } else {
            setDateRange(dateRangeCache);
          }
        }
      }
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false);
    }
  }
  /** 设置日历 */
  async function setDate(start_date, end_date): Promise<boolean> {
    try {
      setLoading(true);
      // 首次授权场景，不设置响应超时时间
      mstJsBridge.config.isReckonTime = false;
      /** 设置之前，先把之前设置的日历删除掉 */
      await deleteDate(false, true);
      const res = await getNativeData({
        domain: "web",
        action: "save_schedule",
        params: {
          title: "去完成EWT自习计划",
          notes: "快去app里学习吧 https://t.ewt360.com/ey6ne2",
          /** 每日重复提醒 */
          repeat_type: 1,
          start_date: `${start_date} 9:00:00`,
          end_date: `${start_date} 22:00:00`,
          stop_date: `${end_date}  22:00:00`,
          reminder_minutes: 10,
        },
      });
      if (res.data?.identifier) {
        const newDateRange = { id: res.data.identifier, start_date, end_date };
        saveCalenderInfo(newDateRange);
        setDateRange(newDateRange);
        Toast.show("日历设置成功");
        return true;
      } else {
        console.error("日历设置失败，拿到的信息是：", JSON.stringify(res));
        Toast.show("日历设置失败，如需授权，请允许后重试");
        return false;
      }
    } catch (err) {
      console.error(err);
      return false;
    } finally {
      mstJsBridge.config.isReckonTime = true;
      setLoading(false);
    }
  }
  /** 删除日历 */
  async function deleteDate(showToast?: boolean, keepLoading?: boolean) {
    try {
      if (dateRange?.id) {
        mstJsBridge.config.isReckonTime = false;
        setLoading(true);
        const res = await getNativeData({
          domain: "web",
          action: "cancel_schedule",
          params: {
            identifier: dateRange.id,
          },
        });
        if (res?.code === 200 && showToast) {
          // 清理掉原有的日历id
          saveCalenderInfo({
            id: "",
            start_date: "",
            end_date: "",
          });
          /** 删除后，重置提醒范围 */
          setDateRange(null);
          Toast.show("删除成功!");
        }
      }
    } catch (err) {
      Toast.show("删除失败");
      console.error(err);
    } finally {
      mstJsBridge.config.isReckonTime = true;
      !keepLoading && setLoading(false);
    }
  }
  /**
   * 计算当前日期和目标日期之间的差值，单位是天的维度
   * @param targetTime 目标值是YYYY-MM-DD的格式
   */
  function calcDiffDays(targetTime: string) {
    return moment(moment().format("YYYY-MM-DD")).diff(
      moment(targetTime),
      "days",
    );
  }
  /**
   * 检测日历设置状态
   * 判断是否设置过日历和日历是否已过期
   */
  function checkCalenderSetStatus() {
    if (!keyRef.current) {
      return;
    }
    let isSetCalender = false; // 是否设置了日历，默认为false
    let isCalenderExpired = true; // 日历是否过期了，默认为true
    const cacheDataStr: string = localStorage.getItem(keyRef.current) || "{}";
    const cacheData = JSON.parse(cacheDataStr);

    if (cacheData?.end_date) {
      // 有截止日期代表设置过日历，此时重置状态为已设置过的true
      isSetCalender = true;
      const diffDays = calcDiffDays(cacheData.end_date);
      // 拿到当前时间和存储的时间差值，当天和未来都是0~负数，过期后是1开始的正整数
      // 如果没有过期时重置状态到未过期
      if (diffDays <= 0) {
        isCalenderExpired = false;
      }
    }
    return {
      isSetCalender,
      isCalenderExpired,
      cacheData,
      displayGuid: cacheData?.displayGuid || false,
    };
  }

  /**
   * 检测退出时的提示次数
   * 如果本地的退出时间不是当天，直接默认是需要退出的，且退出次数为0
   * 如果是当天，判断提示次数是否小于3次，大于等于就不提示了
   * 其他情况默认返回需要提示
   */
  function checkLogoutCount() {
    if (userId) {
      const { cacheData } = checkCalenderSetStatus();
      // 默认的返回结果
      let returnResult = {
        showPopup: true,
        logoutCount: 0,
      };
      if (cacheData?.logoutTime) {
        const { logoutCount, logoutTime } = cacheData;
        const diffDay = calcDiffDays(logoutTime);
        if (diffDay > 0) {
          saveCalenderInfo({
            logoutTime: moment().format("YYYY-MM-DD"),
            logoutCount: 0,
          });
          return returnResult;
        }
        return {
          showPopup: logoutCount < 3,
          logoutCount,
        };
      } else {
        saveCalenderInfo({
          logoutTime: moment().format("YYYY-MM-DD"),
          logoutCount: cacheData?.logoutCount || 0,
        });
      }
      return returnResult;
    }
  }

  /**
   * 保存退出提醒到session缓存内
   * 放在这里的原因是标识不用再创建了，直接使用 keyRef.current
   */
  function saveSessionFlag() {
    if (keyRef.current) {
      sessionStorage.setItem(keyRef.current, "true");
    }
  }

  /**
   * 检测session内的标识
   */
  function checkSessionFlag() {
    if (keyRef.current) {
      return sessionStorage.getItem(keyRef.current);
    }
    return false;
  }

  return {
    loading,
    dateRange,
    setDate,
    deleteDate,
    getDate,
    checkCalenderSetStatus,
    saveCalenderInfo,
    checkLogoutCount,
    saveSessionFlag,
    checkSessionFlag,
  };
}
