/** 判断用户是否有e游记的权限，这个接口如果返回 false，就无需展示欢迎勋章、里程碑弹窗 */

import * as React from "react";

import { checkActivityAuth } from "@/service/home";

export function useETraveAuth() {
  /**
   * 这个之后可以改为 ref 更合适一些，因为我不需要它触发更新
   * 我只需用它来判断后续的某些接口是否要调用
   * */
  const [hasETraveAuth, setETraveAuth] = React.useState<boolean>();
  /** 这个会作为假弹窗调用，始终返回 false */
  async function getETraveAuth() {
    try {
      const { data } = await checkActivityAuth();
      setETraveAuth(data);
      return false;
    } catch (error) {
      console.error(error);
      return false;
    }
  }
  return { hasETraveAuth, getETraveAuth };
}
