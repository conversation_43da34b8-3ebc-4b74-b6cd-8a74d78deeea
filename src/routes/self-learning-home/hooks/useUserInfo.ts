/** 获取用户信息 */

import * as React from "react";

import { getUserInfo as getUserInfoApi } from "@/service/home";
import type { IUserInfoRes } from "@/service/home";

export function useUserInfo() {
  const [userInfo, setUserInfo] = React.useState<IUserInfoRes>();
  async function getUserInfo() {
    try {
      const { data } = await getUserInfoApi();
      setUserInfo(data);
    } catch (err) {
      console.error(err);
    }
  }
  React.useEffect(() => {
    getUserInfo();
  }, []);
  return { userInfo, getUserInfo };
}
