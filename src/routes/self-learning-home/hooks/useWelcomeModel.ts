/** 判断欢迎勋章是否需要展示、以及获取勋章图片 */

import * as React from "react";

import { getWelcomeModelInfo as getWelcomeModelInfoApi } from "@/service/home";

interface IUseWelcomeModel {
  hasETraveAuth: boolean;
}

export function useWelcomeModel(props: IUseWelcomeModel) {
  const { hasETraveAuth } = props;
  const [imgUrl, setImgUrl] = React.useState<string>();
  async function getWelcomeModelInfo(): Promise<boolean> {
    try {
      if (!hasETraveAuth) {
        console.log(hasETraveAuth, "haveETraveAuth");
        return false;
      }
      const { data } = await getWelcomeModelInfoApi();
      setImgUrl(data.imgUrl);
      return data.popup;
    } catch (err) {
      console.error(err);
      return false;
    }
  }
  return { imgUrl, getWelcomeModelInfo };
}
