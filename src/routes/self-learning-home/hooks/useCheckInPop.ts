/** 控制打卡弹窗的显隐 */

import * as React from "react";
import { Toast } from "antd-mobile";

import {
  getStudyTimeAndCheckInStatus,
  checkInWithToday,
  getCheckInPrefixState,
  EStudyScene,
} from "@/service/home";
import type {
  ICheckInPrefixState,
  IPlanDetailRes,
  IStudyTimeAndCheckInStatus,
} from "@/service/home";
import SafeLogger from "@/utils/safe-logger";

import { getCheckInThreshold } from "../common";

import Style from "@/routes/self-learning-home/components/date-reminder/style.module.scss";
import {
  getHolidayDaysAndRewardStatus,
  IHolidayCheckInDetail,
} from "@/service/self-learning/drawer";

interface IUseMilestonePop {
  hasETraveAuth: boolean;
  planDetail?: IPlanDetailRes;
}

export function useCheckInPop(props: IUseMilestonePop) {
  const { hasETraveAuth, planDetail } = props;
  /** 是否需要展示不能打卡的原因 */
  const showCannotCheckInReasonRef = React.useRef<boolean>();
  /** 今日是否已经打卡 */
  const [hasCheckInWithToday, setTodayCheckIn] = React.useState<boolean>();
  /** 刷新今日学习时长的 loading */
  const [loading, setLoading] = React.useState<boolean>(false);
  /** 今日学习时长、今日打卡状态 */
  const [studyTimeAndStatus, setStudyAndStatus] =
    React.useState<IStudyTimeAndCheckInStatus>({});
  const [checkInPrefixState, setCheckInPrefixState] =
    React.useState<ICheckInPrefixState>();
  const [holidayDaysAndStatus, setHolidayDaysAndStatus] =
    React.useState<IHolidayCheckInDetail>();

  // 查询场景详情
  const querySceneDetail = async (queryType?: string) => {
    try {
      // 假期场景需要查询假期的信息，否则不需要展示该信息，也就不需要查询了
      if (planDetail?.studyScene === EStudyScene.winterHoliday) {
        const { data } = await getHolidayDaysAndRewardStatus({
          scene: planDetail.studyScene,
          queryType,
        });
        setHolidayDaysAndStatus(data);
      }
    } catch (error) {
      console.error("假期奖励状态获取失败", error);
    }
  };

  /**
   * 获取今日学习时长，如果触发打卡条件，就自动打卡
   * @param showToast 是否展示自动打卡成功的 Toast 提示
   * @returns boolean
   */
  async function getTodayStudyTime(): Promise<boolean> {
    try {
      setLoading(true);
      /** 这个接口如果报错了，不会影响打卡天数信息的获取，所以这里 catch 住了 */
      const { data: todayStudyInfo } = await getStudyTimeAndCheckInStatus()
        .catch((error) => {
          SafeLogger.baseLogger.error("get-studyTime-failed", { error });
          return { data: {} as IStudyTimeAndCheckInStatus };
        })
        .finally(() => {
          setLoading(false);
        });

      setStudyAndStatus(todayStudyInfo);

      const checkInThreshold = getCheckInThreshold();
      if (
        todayStudyInfo.studyDuration < checkInThreshold &&
        showCannotCheckInReasonRef.current
      ) {
        Toast.show({
          maskClassName: Style["cannot-checkIn-toast"],
          content: `今日学习${checkInThreshold}分钟才能打卡哦～`,
        });
        // 消费后，重置
        showCannotCheckInReasonRef.current = false;
        return false;
      }
      // 记录是否触发过打卡动作，默认没有，用于判断是否弹出Toast
      let isCheckIn = false;
      // 如果能打卡，但是任务状态没完成，都触发一次打卡
      // 后端的打卡动作内包含打卡、触发任务完成状态双动作，但打过卡的话会自动过滤打卡的动作
      if (todayStudyInfo.canCheckInStatus && !todayStudyInfo.finishedFlag) {
        const { data: checkStatus } = await checkInWithToday().catch(
          (error) => {
            SafeLogger.baseLogger.error("checkIn-failed", { error });
            return {
              data: false,
            };
          },
        );
        // 打完卡后重新查一次接口数据
        querySceneDetail("checkIn");
      }
      /** 打卡弹窗的前置弹出 */
      const { data } = await getCheckInPrefixState().catch((error) => {
        SafeLogger.baseLogger.error("get-check-in-prefix-state-failed", {
          error,
        });
        return {
          data: {
            rewardFlag: true, // 奖励领取状态默认已领，默认值是否使用取决于任务状态
            popFlag: true, // 是否已弹出过弹窗，默认弹出过了，异常情况就不再弹了
            checkInFlag: true, // 打卡状态，默认打过卡(没打卡就在上一步自动打了)
            finishedFlag: false, // 任务完成状态，默认是未完成状态，接口异常就等待用户手动打卡
          },
        };
      });
      setCheckInPrefixState(data);
      // 如果任务完成了，并且没有弹出过，并且打卡成功状态，就返回true，需要弹出，否则不展示
      if (data.finishedFlag && !data.popFlag && data.checkInFlag) {
        setTodayCheckIn(true); // 打卡成功
        return true;
      }
      // 能打卡，一开始没打卡，本次打卡成功了，且任务没有完成时仅Toast提示
      if (
        todayStudyInfo.canCheckInStatus &&
        !todayStudyInfo.checkInStatus &&
        data.checkInFlag &&
        !data.finishedFlag
      ) {
        Toast.show("今日打卡成功");
        setTodayCheckIn(true); // 打卡成功
      }
      return false;
    } catch (err) {
      console.error(err);
      return false;
    }
  }
  return {
    studyTimeAndStatus,
    loading,
    hasCheckInWithToday,
    showCannotCheckInReasonRef,
    checkInPrefixState,
    holidayDaysAndStatus,
    getTodayStudyTime,
    querySceneDetail,
  };
}
