import { getUserInfo } from "@/utils/tool";
import { SELF_LEARNING_HOLIDAY_UPGRADE_POP } from "../common";

/** 判断是否假期时间段内，且是第一次访问，满足条件就弹出底抽提示用户，否则就不展示 */
export function checkHolidayUpgradeTipPop(isHoliday: boolean) {
  let displayHolidayUpgradePop = false;
  if (isHoliday) {
    const userId = getUserInfo();
    if (userId) {
      const cacheKey = `${userId}-${SELF_LEARNING_HOLIDAY_UPGRADE_POP}`;
      const cacheData = localStorage.getItem(cacheKey);
      if (!cacheData) {
        displayHolidayUpgradePop = true;
        // 展示过一次后就将记录写入本地
        localStorage.setItem(cacheKey, "true");
      }
    }
  }
  return displayHolidayUpgradePop;
}
