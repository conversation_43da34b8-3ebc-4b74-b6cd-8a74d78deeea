/** 管理周计划弹窗是否显示，已经用户到目前为止已经完成的周计划数 */

import * as React from "react";

import {
  IWeekPopInfo,
  getWeekPopInfo as getWeekPopInfoApi,
} from "@/service/home";

export function useWeekCompletePop() {
  /** 接口其他的返回值通过 cb 吐出，其他pop接口采用同样的方式 */
  async function getWeekPopInfo(
    cb?: (v: IWeekPopInfo) => void,
  ): Promise<boolean> {
    try {
      const { data } = await getWeekPopInfoApi();
      if (data.popup && cb) {
        cb(data);
      }
      return data.popup;
    } catch (err) {
      console.error(err);
      return false;
    }
  }
  return { getWeekPopInfo };
}
