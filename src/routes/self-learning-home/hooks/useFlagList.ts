/** 获取 flag 列表 */
import * as React from "react";

import { getFlagList } from "@/service/home";
import type { IFlagItem } from "@/service/home";
import SafeLogger from "@/utils/safe-logger";

interface IUseFlagList {
  setInitFlag?: (item: IFlagItem) => void;
}

export function useFlagList(props?: IUseFlagList) {
  const { setInitFlag } = props || {};
  const [loading, setLoading] = React.useState<boolean>();
  const [flagList, setFlagList] = React.useState<IFlagItem[]>();
  /** 异常会有异常的 UI 展示 */
  const [isError, setIsError] = React.useState<boolean>();
  async function getFlags() {
    try {
      setIsError(false);
      setLoading(true);
      const { data } = await getFlagList();
      // 设置默认的 flag
      setInitFlag && setInitFlag(data[0]);
      setFlagList(data);
    } catch (error) {
      setIsError(true);
      console.error(error);
      SafeLogger.baseLogger.error("get-flag-list-failed", { error });
    } finally {
      setLoading(false);
    }
  }
  React.useEffect(() => {
    getFlags();
  }, []);
  return { isError, loading, flagList, getFlags };
}
