/** 管理包完成弹窗是否展示 */

import * as React from "react";

import { getCompleteCourseTaskPopInfo } from "@/service/home";
import type { ICompleteCourseTaskPopRes } from "@/service/home";

export function useCompleteCoursePop() {
  async function getCompleteCourseInfo(
    cb?: (v: ICompleteCourseTaskPopRes) => void,
  ) {
    try {
      const { data: info } = await getCompleteCourseTaskPopInfo();
      if (info.popup && cb) {
        cb(info);
      }
      return info.popup;
    } catch (err) {
      console.error(err);
      return false;
    }
  }
  return { getCompleteCourseInfo };
}
