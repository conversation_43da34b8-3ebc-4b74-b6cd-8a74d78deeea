/** 火苗动画 */

import * as React from "react";

import { ICourseItem } from "@/service/home";

import { flyTargetFireId, flyTargetWrapperId } from "../common";

export function useFireAnimation() {
  /** 给动画加个保护罩 */
  const [maskLoading, setMaskLoading] = React.useState<boolean>(false);
  const [classNames, setClassNames] = React.useState<string>();
  // 动画开始时，用户是否已经完成周目标标识，完成和没完成会有两种不同的动画
  const haveCompletedWeekTargetRef = React.useRef<boolean>();
  // 火苗飞入的位置信息
  const [style, setStyle] = React.useState<{
    top: string;
    left: string;
    toTop: string;
    toLeft: string;
  }>();
  // 是否需要翻转手动完成的课程，如果下一讲没了，就不用翻转了
  const [nextLessonInfo, setNextLessonInfo] = React.useState<ICourseItem>();
  // 控制动画开始
  const targetRef = React.useRef<HTMLImageElement>();
  function recordInitPosition() {
    try {
      const target = targetRef.current;
      haveCompletedWeekTargetRef.current = false;
      /** 火苗初始位置 */
      const rect = target.getBoundingClientRect();
      /** 火苗终点位置 */
      const emptyFire = document.querySelector(`#${flyTargetFireId}`);
      // 判断显示哪种飞入动画
      haveCompletedWeekTargetRef.current = !emptyFire;
      const targetRect = (
        emptyFire || document.querySelector(`#${flyTargetWrapperId}`)
      ).getBoundingClientRect();
      if (targetRect && rect) {
        setStyle({
          top: `${rect.top}px`,
          left: `${rect.left}px`,
          toTop: `${targetRect.top + targetRect.height / 2}px`,
          toLeft: `${targetRect.left + targetRect.width / 2}px`,
        });
      }
    } catch (error) {
      console.error(error);
    }
  }

  return {
    style,
    classNames,
    nextLessonInfo,
    targetRef,
    haveCompletedWeekTargetRef,
    maskLoading,
    setMaskLoading,
    setStyle,
    setClassNames,
    setNextLessonInfo,
    recordInitPosition,
  };
}
