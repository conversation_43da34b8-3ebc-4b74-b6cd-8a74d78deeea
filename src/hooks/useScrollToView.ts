import { useRef } from "react";

export const useScrollToView = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  // 默认滚动到结果组件
  function scrollToView(config?: boolean | ScrollIntoViewOptions) {
    if (containerRef.current) {
      containerRef.current.scrollIntoView(
        config || {
          behavior: "smooth",
        },
      );
    }
  }
  return { containerRef, scrollToView };
};
