import { useRef } from "react";

/**
 * 当 API 串联时，前后接口可能会有版本依赖问题，导致后续接口请求不符合预期
 * 故当接口请求时，通过调用 upLevel(n) 来对更新这一层的请求次数（n 从 1 开始），其会返回完整依赖的 getLevelKey()
 * 当接口数据返回时，可以通过 getLevelKey() 来判断前置依赖的接口是否有重新请求，如果不一致，则抛弃结果
 * @param {number} totalLevel
 */
export function useAPILevel(totalLevel: number = 3) {
  const levelRef = useRef(Array.from({ length: totalLevel }).map((it) => 1));
  const getLevelKey = (level: number) =>
    levelRef.current.slice(0, level).join("_");
  const upLevel = (level: number) => {
    if (level > 0 && level <= totalLevel) {
      levelRef.current[level - 1]++;
    }
    return getLevelKey(level);
  };
  return {
    upLevel,
    getLevelKey,
  };
}
