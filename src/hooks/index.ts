import { useState, useRef, useEffect } from "react";

export * from "./useAuth";
export * from "./useVisibilitychange";
export * from "./useSubjectList";
export * from "./useIntersectionObs";
export * from "./useScrollToView";
import { useLocation } from "react-router-dom";

// 获取浏览器 ? 后面的参数
export function useQuery() {
  const query: { [x: string]: string } = {};
  const search = useLocation().search;
  const params = new URLSearchParams(search);
  params.forEach((val, key) => {
    query[key] = val;
  });
  return query;
}

/**
 * @description 同步更新状态
 * @param { any } initState 要更新的内容
 * @returns { any } 返回最新结果
 */

export const useSyncState = <T>(
  initState?: T,
): [{ current: T }, (data: T) => void] => {
  const [state, setStateValue] = useState<T>(initState as T);
  const stateRef = useRef<T>(state);
  const setState = (value: T) => {
    stateRef.current = value;
    setStateValue(value);
  };
  return [stateRef, setState];
};

/**
 * 监听页面显示隐藏的 hooks
 * @returns {boolean} 页面是否可见
 */
export function usePageVisibility() {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const handleVisibilityChange = () => {
      setIsVisible(!document.hidden);
    };
    document.addEventListener("visibilitychange", handleVisibilityChange);
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, []);
  return isVisible;
}
export * from "./useAPILevel";
