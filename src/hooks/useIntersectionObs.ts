/** 观察目标 dom 是否可见 */

import * as React from "react";

interface IUseIntersectionObs {
  ids: string[];
  cb;
}

export function useIntersectionObs(props: IUseIntersectionObs) {
  const { ids, cb } = props;
  const observeInstanceRef = React.useRef<IntersectionObserver>();
  React.useEffect(() => {
    /** 不支持该 api 的浏览器直接返回 */
    if (!window.IntersectionObserver) {
      return;
    }
    if (!observeInstanceRef.current) {
      observeInstanceRef.current = new window.IntersectionObserver(cb, {
        threshold: [0],
      });
    }

    ids.forEach((id) => {
      const target = document.querySelector(`#${id}`);
      if (!target) {
        return;
      }
      observeInstanceRef.current.observe(target);
    });
    return () => {
      if (observeInstanceRef.current?.unobserve) {
        ids.forEach((id) => {
          const target = document.querySelector(`#${id}`);
          if (!target) {
            return;
          }
          observeInstanceRef.current.unobserve(target);
        });
      }
    };
  }, []);
}
