/** 页面可见监听 hook，在播放视频，关闭后需要监听 */

import * as React from "react";

interface IUseVisibilitychange {
  // TODO: 防止闭包陷阱
  handleVisibilitychange: (visible: boolean) => any;
}

export function useVisibilitychange(props: IUseVisibilitychange) {
  const { handleVisibilitychange } = props;
  React.useEffect(() => {
    function onVisibilitychange() {
      handleVisibilitychange(document.visibilityState === "visible");
    }
    document.addEventListener("visibilitychange", onVisibilitychange);
    return () => {
      console.log("remove listener");
      document.removeEventListener("visibilitychange", onVisibilitychange);
    };
  }, []);
}
