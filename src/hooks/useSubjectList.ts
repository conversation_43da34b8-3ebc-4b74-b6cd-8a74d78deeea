/** 获取科目列表 */

import * as React from "react";

import { getSubjectListApi } from "@/service/home";
import type { ISubjectItem } from "@/service/home";

interface IUseSubjectList {
  /** 接口非 200 的时候，是否要向外部透出 */
  showError?: boolean;
}

export function useSubjectList(props?: IUseSubjectList) {
  const { showError } = props || {};
  /** 是否获取科目列表异常 */
  const [isError, setIsError] = React.useState<boolean>();
  const [subjectList, setSubjectList] = React.useState<ISubjectItem[]>();
  const [loading, setLoading] = React.useState<boolean>();
  async function getSubjectList() {
    try {
      setLoading(true);
      setIsError(false);
      /** 该接口是弱依赖，允许异常，不给出异常提示信息 */
      const { data } = await getSubjectListApi({ ignoreError: true });
      setSubjectList(data?.categorySubjectList || []);
    } catch (err) {
      showError && setIsError(true);
      setSubjectList([]);
      console.error(err);
    } finally {
      setLoading(false);
    }
  }
  React.useEffect(() => {
    getSubjectList();
  }, []);
  return { loading, subjectList, isError, getSubjectList };
}
