/** 判断当前用户是否有权限访问自主学习计划 */

import { useState, useEffect } from "react";

import { getAuth as getAuthApi } from "@/service/auth";

interface IAuth {
  loading: boolean;
  hasAuth: boolean;
}

export function useAuth(): IAuth {
  const [hasAuth, setHasAuth] = useState<boolean>();
  const [loading, setLoading] = useState<boolean>(true);
  async function getAuth() {
    try {
      setLoading(true);
      const { data } = await getAuthApi();
      setHasAuth(data);
    } catch (e) {
      console.error(e);
      setHasAuth(false);
    } finally {
      setLoading(false);
    }
  }
  useEffect(() => {
    getAuth();
  }, []);
  return { hasAuth, loading };
}
