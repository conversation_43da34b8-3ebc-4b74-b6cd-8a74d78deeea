declare module "*.module.scss" {
  const classes: { [key: string]: string };
  export default classes;
}
declare module "*.modules.scss" {
  const classes: { [key: string]: string };
  export default classes;
}

declare module "*.jpg";
declare module "*.png";
declare module "*.gif";
declare module "bendH5/app";

interface Window {
  ARMS_GLOBAL_CONFIG_SAMPLE?: number;
  __MST_LABEL__?: string;
}

declare const __BUILD_TIME__: string;
declare const __APP_DIR__: string;
declare var mstJsBridge: any;
declare var ewt_goBack: () => boolean;
