{"name": "ewt-customer-h5", "version": "1.0.0", "description": "site study business domain project(self learning plan and member)", "main": "", "scripts": {"start": "yarn dev", "dev": "ewt dev", "dev:dev": "cross-env DEPLOYMENT_ENV=dev ewt dev", "dev:test": "cross-env DEPLOYMENT_ENV=sit ewt dev", "dev:pre": "cross-env DEPLOYMENT_ENV=pre ewt dev", "build": "ewt build", "build:analyzer": "cross-env USE_ANALYZER=analyzer ewt build", "build:no_minimize": "cross-env NO_MINIMIZE=true ewt build", "es-check": "es-check es5 ./dist/**/*.js"}, "author": "mst-team", "license": "ISC", "dependencies": {"@arms/js-sdk": "1.8.35", "@arms/rum-browser": "^0.0.34", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@ewt/request": "^2.2.2", "@ewt/sls-web-track": "^1.1.5", "@tip/fs": "^0.2.5", "abortcontroller-polyfill": "^1.7.5", "ahooks": "3.7.8", "antd-mobile": "^5.36.1", "axios": "^0.18.0", "dayjs": "^1.11.13", "html2canvas": "^1.4.1", "js-cookie": "^2.2.0", "lodash": "^4.17.21", "lottie-react": "^2.4.0", "lottie-web": "5.12.2", "moment": "^2.24.0", "mst-analytics": "1.3.4", "mst-customer-api": "1.1.5", "mst-js-bridge": "2.0.5", "query-string": "^5.1.1", "raf": "^3.4.1", "react": "18.2.0", "react-dom": "18.2.0", "react-error-boundary": "^6.0.0", "react-router": "^6.23.1", "react-router-dom": "^6.23.1", "react-window": "^1.8.11", "solarlunar": "^2.0.7", "swiper": "8.4.4", "uuid": "^11.1.0", "web-animations-js": "2.3.2"}, "devDependencies": {"@ewt/cli": "^1.0.6", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.16", "@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@typescript-eslint/parser": "6.15.0", "babel-plugin-import": "^1.11.2", "cross-env": "^5.1.4", "crossorigin-webpack-plugin": "^1.0.0", "cz-conventional-changelog": "^2.1.0", "es-check": "7.1.1", "eslint": "8.56.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.2", "eslint-plugin-react-hooks": "^1.6.0", "postcss-import": "^12.0.1", "postcss-px-to-viewport": "^1.1.1", "postcss-pxtorem": "^4.0.1", "prettier": "^3.3.2", "react-refresh": "^0.17.0", "typescript": "5.3.3", "webpack-bundle-analyzer": "^4.10.2"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "browserslist": ["> 0.1% and not dead", "last 2 versions", "Chrome >= 37"]}