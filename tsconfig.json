{
  "compilerOptions": {
    "target": "ESNext",
    "module": "ESNext",
    "allowSyntheticDefaultImports": true,
    "moduleResolution": "Node",
    "lib": ["DOM", "DOM.Iterable", "ESNext"],
    "jsx": "react",
    "baseUrl": "./",
    "paths": {
      "@/*": ["./src/*"],
      "~/*": ["./src/*"],
    },
    "esModuleInterop": true,
    "resolveJsonModule": true
  },
  "include": ["./src/**/*", "./global.d.ts"],
  "exclude": ["node_modules", "./.eslintrc.js", "./dist"]
}
