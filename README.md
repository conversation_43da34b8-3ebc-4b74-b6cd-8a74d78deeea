# 构建说明


## 目录结构

```
react-scaffold
└── src
    └── assets
    └── components
    └── containers
    └── layouts
    └── routes
    └── service
    └── store
    └── styles
    └── utils
    └── index.html
    └── index.js
├── .editorconfig
├── .eslintrc
├── .gitignore
├── .webpackrc.js
├── package.json
├── README.md
```

## 第一次运行项目

**业务需求登录，请帮定hosts 配置如下**
```bash
127.0.0.1 local.mistong.com
```
.webpackrc.js 设置host选项
```js
host: 'local.mistong.com'
```

```
  cd ~/react-scaffold
  npm install
  npm start
```

## 构建命令
npm start 启动项目开发
<p style="font-size:14px;color:red">tip:ie8不支持内联模式热更新,开发环境需要ie访问请修改.webpackrc.js中 inline:false</p>
npm build 项目打包

## 构建依赖
Node http://nodejs.cn/
Npm
webpack  https://www.runoob.com/w3cnote/webpack-tutorial.html

## 基础配置

### 入口文件
```
└── src
  └── index.html
  └── index.js
```

### 常用配置

打开以下目录的文件
```
└── .webpackrc.js
```
配置项
```
  {
    devtool: 'cheap-module-eval-source-map',
    entry: './src/index.js',
    output: {
        publicPath: '/',
    },
    dev: {
        output: {
            publicPath: '/',
        },
    },
    html: {
        template: './src/index.html'
    },
    babel: {
        plugins: []
    },
    externals: {
        react: 'React',
        'react-dom': 'ReactDOM',
    },
    resolve: {
        alias: {
            'components': './src/components',
            'common': './src/common',
            'img': './src/img'
        }
    },
    proxy: {
        "/api": {
            target: "http://teacher.235.mistong.com/",
            changeOrigin: true,
            secure: false,
            // pathRewrite: {"^/api" : ""},
        },
        // login用于登录验证千万别去掉啊
        '/login': {
          target: 'http://teacher.235.mistong.com',
          changeOrigin: true,
          secure: false,
        },
    },
    host: 'local.mistong.com',
    port: 3001,
    open: true,
    inline: true
  }
```

### lint配置
编辑器代码格式化工具
```
  └── .editorconfig 配置文件
```
webstorm 不需要安装 ATOM vscode sublime 需要安装editorConfig插件


